## 部署中间件

1. 安装[宝塔](https://www.bt.cn/new/download.html),查看通用命令安装
2. 通过宝塔安装mysql![img_6.png](img/img_6.png)![img_7.png](img/img_7.png)新建wanhe数据和wanhe_config数据，创建完成后通过数据库工具分别导入项目中sql目录下2个sql文件![img_8.png](img/img_8.png)更改配置![img_9.png](img/img_9.png)
3. 通过宝塔安装redis![img_11.png](img/img_11.png)![img_13.png](img/img_13.png)
4. 通过宝塔安装nacos![img_1.png](img/img_1.png)![img_2.png](img/img_2.png)![img_3.png](img/img_3.png)配置nacos![img_15.png](img/img_15.png)![img_4.png](img/img_4.png)导入配置文件[nacos_prod.zip](sql%2Fnacos_prod.zip)![img_5.png](img/img_5.png)
5. 通过docker安装emqx![img_10.png](img/img_10.png)
6. 通过docker安装kafka![img_12.png](img/img_12.png)
7. 通过decker安装influxdb![img_14.png](img/img_14.png)

## 部署jar

1. 可以通过配置密钥来配置使用sbin目录下脚本密码上传或打开宝塔面板拖拽上传
![img_16.png](img/img_16.png)
2. pdf需要的logo地址/www/wwwroot/jar/logo1.png
3. pdf需要的字体地址/usr/share/fonts/STSONG.TTF

## 宝塔地址

* service
  * 外网面板地址: http://*************:28282/8cffdfbe
  * 内网面板地址: http://************:28282/8cffdfbe
  * username: xh309w6h
  * password: 5304e385


* mysql-redis
  * 外网面板地址: http://**********:28282/f591f0b5
  * 内网面板地址: http://************:28282/f591f0b5
  * username: gp4p5xxc
  * password: 7a7f66d1


* emqx-kafaka
  * 外网面板地址: http://*************:28282/e3fe3e60
  * 内网面板地址: http://************:28282/e3fe3e60
  * username: khgfh1ez
  * password: 225b4f99


* influxdb
  * 外网面板地址: http://3.72.224.36:28282/c7c64705
  * 内网面板地址: http://172.31.15.207:28282/c7c64705
  * username: y01smcm6
  * password: d1a14e03


## 中间件外网地址账号密码

* nacos http://*************:8848/nacos nacos wanhe@123
* mysql **********:3306/wanhe wanhe 55kPKFMaYjW5aaYb
* mysql **********:3306/wanhe_config wanhe_config XLGTJKRFCK5hnSfc
* emqx *************:1883 wanhe wanhe@123
* emqx-ui *************:18083 admin wanhe@123
* redis **********:6379 jtfJ91QMFG5LRFNhrvH
* kafka *************:9092

## Api文档地址
https://apifox.com/apidoc/shared/468386c2-79f8-40b5-a5db-60dd3ff46864/
