<h1 align="center" style="margin: 30px 0 30px; font-weight: bold;">万禾架构</h1>
<h4 align="center">基于 Vue/Element UI 和 Spring Boot/Spring Cloud 前后端分离的分布式微服务架构</h4>

## 平台简介

万禾架构是基于若依微服务架构改造而来

* 采用前后端分离的模式，微服务版本前端(基于 [RuoYi-Vue](https://gitee.com/y_project/RuoYi-Vue))。
* 后端采用Spring Boot、Spring Cloud
* 注册中心、配置中心选型Nacos，权限认证使用Redis。
* 使用设备数据存储使用influxdb
* 业务数据存储使用mysql
* 设备和系统数据传输使用emqx和kafka

#### 友情链接 [若依/RuoYi-Cloud](https://gitee.com/zhangmrit/ruoyi-cloud) Ant Design版本。

## 系统模块

~~~
com.ruoyi
├── wanhe-gateway         // 网关模块 [8080]
├── wanhe-auth            // 认证中心 [9200]
├── wanhe-api             // 接口模块
│       └── ruoyi-api-system                          // 系统接口
├── wanhe-common          // 通用模块
│       └── wanhe-common-core                         // 核心模块
│       └── wanhe-common-i18n                         // 国际化
│       └── wanhe-common-log                          // 日志记录
│       └── wanhe-common-mail                         // 邮件发送
│       └── wanhe-common-ocr                          // 图片识别
│       └── wanhe-common-redis                        // 缓存服务
│       └── wanhe-common-security                     // 安全模块
│       └── wanhe-common-sms                          // 短信服务
│       └── wanhe-common-swagger                      // 系统接口
├── wanhe-modules         // 业务模块
│       └── wanhe-module-file                         // 文件服务 [9300]
│       └── wanhe-module-gen                          // 代码生成 [9202]
│       └── wanhe-module-job                          // 定时任务 [9203]
│       └── wanhe-module-system                       // 系统模块 [9201]
│       └── wanhe_biz_data                            // 数据模块 [9208]
│       └── wanhe_business                            // 业务模块 [9206]
│       └── wanhe_mqtt                                // 通讯模块 [9207]
├──pom.xml                // 公共依赖
~~~

## 整体架构图

系统采用分层架构设计，将业务逻辑、数据访问和表现层分离，同时融入微服务架构理念，实现服务的独立部署、扩展和管理。架构主要包括以下几个部分：

* 前端层：负责与用户进行交互，通过 HTTP/HTTPS 协议与网关进行通信。
* 网关层：作为系统的统一入口，负责请求路由、权限校验等功能。
* 微服务层：由多个独立的微服务组成，每个微服务负责特定的业务功能，如用户管理、系统配置、日志管理等。
* 数据层：负责数据的存储和管理，包括关系型数据库和非关系型数据库。

## 技术架构图

* 服务框架 springcloud
* 配置、注册、发现：Nacos
* 网关：Spring Cloud Gateway
* 认证授权: 使用redis+jwt
* 服务调度：Feign
* 数据库：mysql，influxdb
* 消息队列：eqmx，kafka

## 功能模块

* 通过接口文档查看系统功能
 ![img_18.png](img/img_18.png)