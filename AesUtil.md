```java
package com.chestnut;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * AES util
 *
 * <AUTHOR>
 * @date 2025/04/28
 */
public class AesUtil {
    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/ECB/PKCS5Padding";
    private static final String KEY = "whyn2025@!123456";

    /**
     * 加密
     *
     * @param plaintext 明文
     * @param key       密钥
     * @return {@link String }
     * @throws Exception 异常
     */
    public static String encrypt(String plaintext, String key) throws Exception {
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), ALGORITHM);
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);
        byte[] encryptedBytes = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    /**
     * 解密
     *
     * @param ciphertext 密文
     * @param key        密钥
     * @return {@link String }
     * @throws Exception 异常
     */
    public static String decrypt(String ciphertext, String key) throws Exception {
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), ALGORITHM);
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.DECRYPT_MODE, secretKey);
        byte[] decodedBytes = Base64.getDecoder().decode(ciphertext);
        byte[] decryptedBytes = cipher.doFinal(decodedBytes);
        return new String(decryptedBytes, StandardCharsets.UTF_8);
    }

    public static void main(String[] args) {
        try {
            String plaintext = "{\n" +
                    "  \"mid\": \"20310002-1250-1111-0002-18446744071578701893\",\n" +
                    "  \"responseId\": \"3cfd7ec4-bb54-4ca1-88e1-d7d1d73eda1f\",\n" +
                    "  \"timeStamp\": 1745892016,\n" +
                    "  \"sn\": \"20310002125011110002\",\n" +
                    "  \"status\": 1,\n" +
                    "  \"progress\": 39,\n" +
                    "  \"errorCode\": 0\n" +
                    "}";

            String encrypted = encrypt(plaintext, KEY);

            String test = "Bm3VOLenJGh4T8x4Jmh6KIsP8rzZTI4Hz/wxN4ii0sg=";

            String decrypted = decrypt(encrypted, KEY);

            System.out.println("原文: " + plaintext);
            System.out.println("加密后: " + encrypted);
            System.out.println("解密后: " + decrypted);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

```

