package com.wanhe.common.mail;

import com.ruoyi.common.core.utils.SpringUtils;
import jakarta.mail.internet.MimeMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.mail.MailProperties;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;

import java.io.ByteArrayOutputStream;

/**
 * <AUTHOR>
 * @date 2024/12/23
 */
@Slf4j
public class MailUtil {

    private static final JavaMailSender SENDER = SpringUtils.getBean(JavaMailSender.class);
    private static final MailProperties PROPERTIES = SpringUtils.getBean(MailProperties.class);

    /**
     * 发送pdf文件
     * @param to 接收邮箱
     * @param title 标题
     * @param content 内容
     * @param stream 字节流
     */
    public static void sendFile(String to, String title, String content, String fileName, ByteArrayOutputStream stream) {
        sendFile(to, title, false, content, fileName, stream);
    }

    /**
     * 发送pdf文件
     *
     * @param to       接收邮箱
     * @param title    标题
     * @param isHtml   是html
     * @param content  内容
     * @param fileName 文件名
     * @param stream   字节流
     */
    public static void sendFile(String to, String title, Boolean isHtml, String content, String fileName, ByteArrayOutputStream stream) {
        try {
            MimeMessage message = SENDER.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            helper.setFrom(PROPERTIES.getUsername());
            helper.setSubject(title);
            helper.setTo(to);
            helper.setText(content, isHtml);
            helper.addAttachment(fileName, new ByteArrayResource(stream.toByteArray()));
            SENDER.send(message);
        } catch (Exception e) {
            log.error("邮件发送失败", e);
        }
    }

    public static void send(String to, String title, String content) {
        send(to, title, content, false);
    }

    public static void send(String to, String title, String content, Boolean isHtml) {
        try {
            MimeMessage message = SENDER.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            helper.setFrom(PROPERTIES.getUsername());
            helper.setSubject(title);
            helper.setTo(to);
            helper.setText(content, isHtml);
            SENDER.send(message);
        } catch (Exception e) {
            log.error("邮件发送失败", e);
        }
    }

}
