#i18n.common_de_DE.properties
#Wed Feb 12 18:37:55 CST 2025
请求地址不允许访问=Zugriff auf angeforderte Adresse nicht erlaubt
文件sheet不存在=Datei-Sheet existiert nicht
验证码不能为空=Bestätigungscode darf nicht leer sein
用户名不能为空=Benutzername darf nicht leer sein
缺失数据或数据格式错误=Fehlende Daten oder falsches Datenformat
未知设备类型=Unbekannter Gerätetyp
参数存在SQL注入风险=Parameter enthält SQL-Injektionsrisiko
参数不符合规范，不能进行查询=Parameter entspricht nicht den Spezifikationen, Abfrage nicht möglich
数据不存在=Daten nicht vorhanden
翻译失败=Übersetzung fehlgeschlagen
没有内部访问权限，不允许访问=Keine internen Zugriffsberechtigungen, Zugriff nicht erlaubt
验证码错误=Bestätigungscode falsch
令牌不能为空=Token darf nicht leer sein
序列号只能输入数字和大写字母=Seriennummer darf nur Zahlen und Großbuchstaben enthalten
令牌验证失败=Token-Überprüfung fehlgeschlagen
服务未找到=Dienst nicht gefunden
email.auth=Sehr geehrte(r) {0}\: Hallo\!\nSie führen gerade eine Wartung der Kontobasisinformationen durch. Bitte geben Sie den Prüfcode ein\: {1}, um den Vorgang abzuschließen.
设备生产批次错误=Fehler in der Geräteproduktionscharge
没有设置用户信息，不允许访问=Keine Benutzerinformationen eingerichtet, Zugriff nicht erlaubt
内部服务器错误=Interner Serverfehler
验证码已失效=Bestätigungscode abgelaufen
万禾悦能邮箱验证=Wanhe Yueneng E-Mail-Verifizierung
设备流水号错误=Geräteseriennummer falsch
请求超过最大数，请稍候再试=Anfrage überschreitet Höchstzahl, bitte später erneut versuchen
序列号长度必须为20位=Seriennummer muss 20 Stellen lang sein
用户名或密码错误=Benutzername oder Passwort falsch
参数已超过最大限制，不能进行查询=Parameter überschreitet Höchstgrenze, Abfrage nicht möglich
客户端类型错误=Client-Typ falsch
登录状态已过期=Anmeldestatus abgelaufen
没有访问权限，请联系管理员授权=Keine Zugriffsberechtigungen, bitte Administrator für Autorisierung kontaktieren
数据存在重复=Daten bereits vorhanden
令牌已过期或验证不正确=Token abgelaufen oder Überprüfung falsch
设备故障通知=Gerätefehlermeldung
文件异常，无法获取数据=Datei fehlerhaft. Keine daten gefunden