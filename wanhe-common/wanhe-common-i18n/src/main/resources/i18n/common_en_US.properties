#i18n.common_en_US.properties
#Wed Feb 12 18:37:55 CST 2025
请求地址不允许访问=Requested address cannot be accessed
文件sheet不存在=File sheet does not exist
验证码不能为空=Verification code cannot be empty
用户名不能为空=Username cannot be empty
缺失数据或数据格式错误=Missing data or data format error
未知设备类型=Unknown device type
参数存在SQL注入风险=Parameters have a risk of SQL injection
参数不符合规范，不能进行查询=Parameter error. Query failed
数据不存在=Data does not exist
翻译失败=Translation failed
没有内部访问权限，不允许访问=No internal access permission. Access denied
验证码错误=Verification code error
令牌不能为空=Token cannot be empty
序列号只能输入数字和大写字母=SN can only contain numbers and uppercase letters
令牌验证失败=Token verification failed
服务未找到=Service not found
email.auth=Dear {0}: \n You are currently maintaining the basic account information. Please enter\: {1} in the verification code input box to complete the operation.
设备生产批次错误=Device production batch error
没有设置用户信息，不允许访问=User info not set. Access denied
内部服务器错误=Internal server error
验证码已失效=Verification code expired
万禾悦能邮箱验证=OneHo email verification
设备流水号错误=Device SN error
请求超过最大数，请稍候再试=Max requests exceeded. Try again later
序列号长度必须为20位=SN must be 20 digits in length
用户名或密码错误=Wrong username or password
参数已超过最大限制，不能进行查询=Parameters exceed max limit. Query failed
客户端类型错误=Client type error
登录状态已过期=Login status expired
没有访问权限，请联系管理员授权=No access permission. Contact admin
数据存在重复=Duplicate data
令牌已过期或验证不正确=Token expired or verification error
设备故障通知=Device fault notice
文件异常，无法获取数据=The file is abnormal, and data cannot be obtained
