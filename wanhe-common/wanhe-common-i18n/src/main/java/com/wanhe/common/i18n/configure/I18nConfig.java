package com.wanhe.common.i18n.configure;

import org.springframework.context.annotation.Bean;
import org.springframework.web.servlet.LocaleResolver;

import java.util.TimeZone;

/**
 * <AUTHOR>
 * @date 2025/1/18
 */
public class I18nConfig {

    @Bean
    public LocaleResolver localeResolver() {
//        TimeZone.setDefault(TimeZone.getTimeZone("Europe/Berlin"));
        return new MyLocaleResolver();
    }

}
