package com.wanhe.common.i18n.util;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.setting.dialect.Props;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.enums.LanguageEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Set;

/**
 * 百度翻译api
 * <AUTHOR>
 * @date 2025/1/18
 */
@Slf4j
public class TransApi {

    private static final RestTemplate REST_TEMPLATE = new RestTemplate();
    private static final String TRANS_API_HOST = "https://fanyi-api.baidu.com/api/trans/vip/translate";
    private static final String APP_ID = "";
    private static final String SECURITY_KEY = "";

    public static String get(String phrase, String to) {
//        return get(phrase, "zh", to);
        return phrase + to;
    }

    public static String get(String phrase, String from, String to) {
        if (StrUtil.isEmpty(APP_ID)) {
            return phrase;
        }
        try {
            Thread.sleep(1000L);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        MultiValueMap<String, String> paramsMap = new LinkedMultiValueMap<>();
        paramsMap.add("q", phrase);
        paramsMap.add("from", from);
        paramsMap.add("to", to);
        paramsMap.add("appid", APP_ID);
        String salt = String.valueOf(System.currentTimeMillis());
        paramsMap.add("salt", salt);
        paramsMap.add("sign", SecureUtil.md5(APP_ID + phrase + salt + SECURITY_KEY));
        JSONObject json;
        try {
            json = REST_TEMPLATE.postForObject(TRANS_API_HOST, paramsMap, JSONObject.class);
        } catch (Exception e) {
            log.error("翻译失败", e);
            return "";
        }
        if (json == null) {
            return "";
        }
        JSONArray array = json.getJSONArray("trans_result");
        if (array == null) {
            log.error(json.toString());
            return "";
        }
        if (array.size() == 1) {
            return array.getJSONObject(0).getString("dst");
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < array.size(); i++) {
            sb.append(array.getJSONObject(i).getString("dst"));
        }
        return sb.toString();
    }

    /**
     * 翻译本地文件
     * @param filePath 需要翻译的模块名称
     * @param isCover 是否覆盖已经翻译的文件
     * @throws Exception 异常
     */
    private static void trans(String filePath, String name, List<String> i18nFiles, boolean isCover) throws Exception {
        int countDe = 0;
        int countEn = 0;
        int countFr = 0;
        int countZh = 0;
        for (String i18nFile : i18nFiles) {
            System.out.println(filePath + "开始翻译" + i18nFile);
            String path = System.getProperty("user.dir") + filePath + "\\src\\main\\resources\\i18n\\";
            Props msg = new Props(path + i18nFile + ".properties", StandardCharsets.UTF_8);
            Props msgDe = new Props(path + i18nFile + "_de_DE.properties", StandardCharsets.UTF_8);
            Props msgEn = new Props(path + i18nFile + "_en_US.properties", StandardCharsets.UTF_8);
            Props msgFr = new Props(path + i18nFile + "_fr_FR.properties", StandardCharsets.UTF_8);
            Props msgZh = new Props(path + i18nFile + "_zh_CN.properties", StandardCharsets.UTF_8);
            Set<Object> keys = msg.keySet();
            int count = keys.size();
            for (Object key : keys) {
                String k = key.toString();
                System.out.println("剩余" + count-- + "个需要翻译 原文：" + k);
                if (isCover || ObjUtil.isEmpty(msgDe.get(k))) {
                    msgDe.put(k, get(k, LanguageEnum.DE.getBaidu()));
                    System.out.println("德语=" + msgDe.get(k));
                    countDe++;
                }
                if (isCover || ObjUtil.isEmpty(msgEn.get(k))) {
                    msgEn.put(k, get(k, LanguageEnum.EN.getBaidu()));
                    System.out.println("英语=" + msgEn.get(k));
                    countEn++;
                }
                if (isCover || ObjUtil.isEmpty(msgFr.get(k))) {
                    msgFr.put(k, get(k, LanguageEnum.FR.getBaidu()));
                    System.out.println("法语=" + msgFr.get(k));
                    countFr++;
                }
                if (isCover || ObjUtil.isEmpty(msgZh.get(k))) {
                    msgZh.put(k, k);
                    countZh++;
                }
            }
            if (countDe > 0 || countEn > 0 || countFr > 0 || countZh > 0) {
                msg.store(new OutputStreamWriter(new FileOutputStream(path + i18nFile + ".properties"), StandardCharsets.UTF_8), name + ".common.properties");
            }
            if (countDe > 0) msgDe.store(new OutputStreamWriter(new FileOutputStream(path + i18nFile + "_de_DE.properties"), StandardCharsets.UTF_8), name + ".common_de_DE.properties");
            if (countEn > 0) msgEn.store(new OutputStreamWriter(new FileOutputStream(path + i18nFile + "_en_US.properties"), StandardCharsets.UTF_8), name + ".common_en_US.properties");
            if (countFr > 0) msgFr.store(new OutputStreamWriter(new FileOutputStream(path + i18nFile + "_fr_FR.properties"), StandardCharsets.UTF_8), name + ".common_fr_FR.properties");
            if (countZh > 0) msgZh.store(new OutputStreamWriter(new FileOutputStream(path + i18nFile + "_zh_CN.properties"), StandardCharsets.UTF_8), name + ".common_zh_CN.properties");
        }
    }

    public static void main(String[] args) throws Exception {
        trans("\\wanhe-auth", "auth", List.of("message"), false);
        trans("\\wanhe-modules\\wanhe_business", "business", List.of("message", "export"), false);
        trans("\\wanhe-common\\wanhe-common-i18n", "i18n", List.of("common"), false);
        trans("\\wanhe-modules\\wanhe_biz_data", "data", List.of("message", "command", "export"), false);
        trans("\\wanhe-modules\\ruoyi-system", "system", List.of("message", "export"), false);
    }

}
