package com.ruoyi.common.security.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.core.enums.LanguageEnum;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.redis.RedisUtil;
import com.ruoyi.system.api.RemoteDictService;
import com.ruoyi.system.api.domain.SysDictData;
import com.ruoyi.system.api.dto.DictDataVO;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 字典工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class DictUtils {

    public static Map<String, String> getDeviceType() {
        return DictUtils.getDictCacheMap("device_type");
    }

    /**
     * 设置字典缓存
     *
     * @param key       参数键
     * @param dictDataList 字典数据列表
     */
    public static void setDictCache(String key, List<DictDataVO> dictDataList) {
        if (dictDataList == null || dictDataList.isEmpty()) {
            return;
        }
        List<DictDataVO> dictDataClone = dictDataList.parallelStream().map(data -> BeanUtil.copyProperties(data, DictDataVO.class)).toList();
        RedisUtil.transaction(redis -> {
            redis.opsForValue().set(getCacheKey(key, LanguageEnum.ZH.getBaidu()), JSON.toJSONString(dictDataClone));

            dictDataClone.forEach(data -> data.setDictLabel(data.getNameDe()));
            redis.opsForValue().set(getCacheKey(key, LanguageEnum.DE.getBaidu()), JSON.toJSONString(dictDataClone));

            dictDataClone.forEach(data -> data.setDictLabel(data.getNameEn()));
            redis.opsForValue().set(getCacheKey(key, LanguageEnum.EN.getBaidu()), JSON.toJSONString(dictDataClone));

            dictDataClone.forEach(data -> data.setDictLabel(data.getNameFra()));
            redis.opsForValue().set(getCacheKey(key, LanguageEnum.FR.getBaidu()), JSON.toJSONString(dictDataClone));
        });
    }

    /**
     * 获取字典缓存
     *
     * @param key 参数键
     * @return dictDatas 字典数据列表
     */
    public static List<DictDataVO> getDictCache(String key) {
        List<DictDataVO> list = RedisUtil.getList(getCacheKey(key, LanguageEnum.getLang()), DictDataVO.class);

        if (CollUtil.isEmpty(list)) {
            list = loadDictDataFromDb(key);
        }
        return list;
    }

    /**
     * 字典服务获取dict缓存
     *
     * @param key key
     * @return {@link List }<{@link DictDataVO }>
     */
    public static List<DictDataVO> getDictCacheByDict(String key) {
        return RedisUtil.getList(getCacheKey(key, LanguageEnum.getLang()), DictDataVO.class);
    }

    public static Map<String, String> getDictCacheMap(String key) {
        List<DictDataVO> list = getDictCache(key);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
    }

    public static Map<String, String> getDictCacheMapLV(String key) {
        List<DictDataVO> list = getDictCache(key);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(SysDictData::getDictLabel, SysDictData::getDictValue));
    }

    /**
     * 删除指定字典缓存
     *
     * @param key 字典键
     */
    public static void removeDictCache(String key) {
        List<String> list = Arrays.stream(LanguageEnum.values()).map(l -> getCacheKey(key, l.getBaidu())).toList();
        RedisUtil.delete(list);
    }

    /**
     * 设置cache key
     *
     * @param configKey 参数键
     * @return 缓存键key
     */
    public static String getCacheKey(String configKey, String language) {
        return "sys_dict:" + configKey + ":" + language;
    }

    /**
     * 从数据库加载字典数据
     *
     * @param dictType dict 类型
     * @return {@link List }<{@link DictDataVO }>
     */
    public static List<DictDataVO> loadDictDataFromDb(String dictType) {
        try {
            RemoteDictService dictService = SpringUtils.getBean(RemoteDictService.class);
            if (dictService != null) {
                List<DictDataVO> dictDataList = dictService.getDictDataByType(dictType).getData();
                if (CollUtil.isNotEmpty(dictDataList)) {
                    languageHandle(dictDataList);
                    return dictDataList;
                }
            }
        } catch (Exception e) {
            log.error("Failed to load dict data from database: {}", e.getMessage());
        }
        return Collections.emptyList();
    }

    /**
     * 多语言处理
     *
     * @param dictDataList 字典数据列表
     */
    public static void languageHandle(List<DictDataVO> dictDataList) {
        String lang = LanguageEnum.getLang();
        try {
            if (!LanguageEnum.ZH.getBaidu().equals(lang)) {
                String fieldName = "name" + StrUtil.upperFirst(lang);
                Class<DictDataVO> dictDataVOClass = DictDataVO.class;
                Field field = dictDataVOClass.getDeclaredField(fieldName);
                field.setAccessible(true);
                dictDataList.forEach(data -> {
                    String dictLabel;
                    try {
                        dictLabel = (String) field.get(data);
                    } catch (IllegalAccessException e) {
                        throw new RuntimeException(e);
                    }
                    data.setDictLabel(dictLabel);
                });
            }
        } catch (Exception e) {
            log.error("Analyze com.ruoyi.system.api.dto.DictDataVO exception: {}", e.getMessage());
        }
    }
}
