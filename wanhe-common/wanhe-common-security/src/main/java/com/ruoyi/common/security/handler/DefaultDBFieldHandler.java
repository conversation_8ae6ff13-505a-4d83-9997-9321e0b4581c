package com.ruoyi.common.security.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.model.LoginUser;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class DefaultDBField<PERSON>andler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        // 获取当前用户信息
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser != null) {
            SysUser user = loginUser.getSysUser();
            this.setFieldValByName("createBy", user.getUserName(), metaObject);
            this.setFieldValByName("updateBy", user.getUserName(), metaObject);
            this.setFieldValByName("createId", user.getUserId(), metaObject);
            if (this.getFieldValByName("deptId", metaObject) == null) {
                this.setFieldValByName("deptId", user.getDeptId(), metaObject);
            }
        }
        LocalDateTime now = LocalDateTime.now();
        this.setFieldValByName("createTime", now, metaObject);
        this.setFieldValByName("updateTime", now, metaObject);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        // 更新时间为空，则以当前时间为更新时间
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser != null) {
            this.setFieldValByName("updateBy", loginUser.getSysUser().getUserName(), metaObject);
        } else {
            this.setFieldValByName("updateBy", "操作人", metaObject);
        }
        this.setFieldValByName("updateTime", LocalDateTime.now(), metaObject);
    }
}