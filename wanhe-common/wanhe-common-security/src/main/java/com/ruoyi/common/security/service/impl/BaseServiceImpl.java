package com.ruoyi.common.security.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.security.service.BaseService;
import com.ruoyi.common.security.service.IBaseService;
import com.ruoyi.common.security.utils.Sql;
import com.ruoyi.system.api.domain.SysRole;
import com.ruoyi.system.api.domain.SysUser;
import org.springframework.dao.DuplicateKeyException;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2023/2/24
 */
public abstract class BaseServiceImpl<M extends BaseMapper<T>, T> extends ServiceImpl<M, T> implements IBaseService<T>, BaseService {

    public LambdaQueryChainWrapper<T> lambdaQueryAll(SFunction<T, ?> column) {
        String stationIdSql = dataScopeSqlId();
        //角色不是admin就要查询该部门及子部门的数据
        LambdaQueryChainWrapper<T> wrapper = new LambdaQueryChainWrapper<>(getBaseMapper(), getEntityClass());
        if (!stationIdSql.isEmpty()) {
            if (stationIdSql.equals(FALSE)) {
                wrapper.eq(column, 0);
            } else {
                wrapper.inSql(column, stationIdSql);
            }
        }
        return wrapper;
    }

    @Override
    public LambdaQueryChainWrapper<T> lambdaQueryTenant(SFunction<T, ?> tenantId, SFunction<T, ?> deptId, SFunction<T, ?> createId) {
        SysUser user = getSysUser();
        LambdaQueryChainWrapper<T> wrapper = new LambdaQueryChainWrapper<>(getBaseMapper(), getEntityClass());
        if (CollUtil.isEmpty(user.getRoles())) {
            return wrapper.eq(tenantId, 0);
        }
        //角色不是超级管理员就要按数据权限查询
        //数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限 5：仅本人数据权限）
        Set<Integer> dataScopes = user.getRoles().stream().map(SysRole::getDataScope).collect(Collectors.toSet());
        if (dataScopes.contains(1) || (dataScopes.contains(4) && user.getTenantId().equals(user.getDeptId()))) {
            return wrapper.inSql(isNotAdminRole(user.getRoleIds()), tenantId, dataScope(user.getTenantId()));
        }
        if (dataScopes.contains(2)) {
            //自定义部门ID
            Set<Long> zdyDeptIds = user.getRoles().stream().filter(role -> role.getDataScope() == 2)
                    .flatMap(role -> role.getCustomDeptIds().stream()).collect(Collectors.toSet());
            if (dataScopes.contains(4)) {
                //查询当前机构全部部门
                List<Long> deptIds = Sql.getDeptIds(user.getTenantId());
                //移除掉需要查询的部门
                deptIds.remove(user.getDeptId());
                //异常需要查询的部门，剩余的就是不需要查询的部门
                deptIds.removeAll(zdyDeptIds);
                //查询全部数据不查询自定义之外的部门
                return wrapper.inSql(tenantId, dataScope(user.getTenantId())).notIn(!deptIds.isEmpty(), deptId, deptIds);
            }
            //只查询本部门加自定义部门
            if (dataScopes.contains(3)) {
                zdyDeptIds.add(user.getDeptId());
            }
            return wrapper.in(deptId, zdyDeptIds);
        }
        if (dataScopes.contains(4)) {
            //查询当前机构全部部门
            List<Long> deptIds = Sql.getDeptIds(user.getTenantId());
            deptIds.remove(user.getDeptId());
            //查询全部数据当前部门
            return wrapper.inSql(tenantId, dataScope(user.getTenantId())).notIn(!deptIds.isEmpty(), deptId, deptIds);
        }
        if (dataScopes.contains(3)) {
            return wrapper.eq(deptId, user.getDeptId());
        }
        return wrapper.eq(tenantId, user.getTenantId()).eq(createId, user.getUserId());
    }

    @Override
    public T getByIdThrow(Serializable id) {
        return getOptById(id).orElseThrow(() -> new ServiceException("数据不存在"));
    }

    @Override
    public boolean save(T entity) {
        try {
            return super.save(entity);
        } catch (DuplicateKeyException e) {
            throw new ServiceException("数据存在重复");
        }
    }

    @Override
    public boolean saveBatch(Collection<T> entityList) {
        try {
            return super.saveBatch(entityList);
        } catch (DuplicateKeyException e) {
            throw new ServiceException("数据存在重复");
        }
    }

    @Override
    public boolean updateById(T entity) {
        try {
            return super.updateById(entity);
        } catch (DuplicateKeyException e) {
            throw new ServiceException("数据存在重复");
        }
    }

    @Override
    public boolean updateBatchById(Collection<T> entityList) {
        try {
            return super.updateBatchById(entityList);
        } catch (DuplicateKeyException e) {
            throw new ServiceException("数据存在重复");
        }
    }

    @Override
    public boolean saveOrUpdateBatch(Collection<T> entityList) {
        try {
            return super.saveOrUpdateBatch(entityList);
        } catch (DuplicateKeyException e) {
            throw new ServiceException("数据存在重复");
        }
    }
}
