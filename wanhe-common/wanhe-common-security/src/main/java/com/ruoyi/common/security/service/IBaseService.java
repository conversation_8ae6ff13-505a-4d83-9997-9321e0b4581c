package com.ruoyi.common.security.service;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.IService;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2023/2/24
 */
public interface IBaseService<T> extends IService<T> {

    /**
     * 查询当前部门已经下级部门数据
     * @param column 字段名称
     * @return 链式调用
     */
    LambdaQueryChainWrapper<T> lambdaQueryAll(SFunction<T, ?> column);

    /**
     * 查询当前租户数据
     * @return 链式调用
     */
    LambdaQueryChainWrapper<T> lambdaQueryTenant(SFunction<T, ?> tenant, SFunction<T, ?> createId, SFunction<T, ?> deptId);

    /**
     * 通过id查询对象，不存在对象就会抛异常
     * @param id 主键
     * @return 对象
     */
    T getByIdThrow(Serializable id);

}
