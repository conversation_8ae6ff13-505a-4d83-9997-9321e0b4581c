package com.ruoyi.common.security.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.redis.RedisUtil;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.common.security.utils.Sql;
import com.ruoyi.system.api.domain.SysRole;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.dto.UserDistrict;
import com.ruoyi.system.api.model.LoginUser;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/11
 */
public interface BaseService {

    long ADMIN_TENANT_ID = 100;
    long DIY_TENANT_ID = 110;
    long ADMIN_ROLE_ID = 1;
    long DIY_ROLE_ID = 2;
    long OWNER_ROLE_ID = 3;

    String FALSE = "false";

    default boolean isAdminRole(List<Long> roleIds) {
        return roleIds != null && roleIds.contains(ADMIN_ROLE_ID);
    }

    default boolean isNotAdminRole(List<Long> roleIds) {
        return roleIds == null || !roleIds.contains(ADMIN_ROLE_ID);
    }

    default boolean isDiyTenantId(long tenantId) {
        return DIY_TENANT_ID == tenantId;
    }

    /**
     * 是否使用数据权限 不等于100
     */
    @Deprecated
    default boolean isUseDataScope(long tenantId) {
        return tenantId != ADMIN_TENANT_ID;
    }

    /**
     * 数据权限
     *
     * @param tenantId 租户id
     */
    default String dataScope(Long tenantId) {
        return "select tenant_id from sys_dept where type in(1,2,3) and del_flag = '0' and full_path like concat((select full_path from sys_dept where dept_id = " + tenantId + "), '%')";
    }

    default UserDistrict getUd(Long userId) {
        //是否使用diy数据权限
        UserDistrict ud = RedisUtil.get(CacheConstants.USER_DISTRICT_KEY + userId, UserDistrict.class);
        if (ud == null) {
            ud = Sql.getUserDistrict(userId);
            RedisUtil.set(CacheConstants.USER_DISTRICT_KEY + userId, JSON.toJSONString(ud));
        }
        return ud;
    }

    default String dataScopeSqlSuper() {
        return dataScopeSqlSuper("power_station_id", null);
    }

    default String dataScopeSqlSuperId() {
        return dataScopeSqlSuper("id", null);
    }

    /**
     * 数据权限
     * sid 查询电站编号
     */
    default String dataScopeSqlSuper(String name, Integer powerStationId) {
        if (powerStationId != null) {
            return " and %s = %d".formatted(name, powerStationId);
        }
        SysUser user = getSysUser();
        //业主
        if (SysUser.isProprietor(user.getUserType())) {
            return " and %s in(select power_station_id from sys_user_station where user_id = %d)".formatted(name, user.getUserId());
        }
        //区域
        String districtScopes = getDistrictScopes(user.getUserId());
        //角色不是超级管理员就要按数据权限查询
        //数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限 5：仅本人数据权限）
        Set<Integer> dataScopes = user.getRoles().stream().map(SysRole::getDataScope).collect(Collectors.toSet());
        if (dataScopes.contains(1) || (dataScopes.contains(4) && user.getTenantId().equals(user.getDeptId()))) {
            if (isAdminRole(user.getRoleIds())) {
                if (districtScopes.isEmpty()) {
                    return "";
                }
                return " and %s in(select id from wh_power_station where deleted = 0%s)".formatted(name, districtScopes);
            } else {
                return " and %s in(select id from wh_power_station where tenant_id in(%s) and deleted = 0%s)".formatted(name, dataScope(user.getTenantId()), districtScopes);
            }
        } else if (dataScopes.contains(2)) {
            //自定义部门ID
            Set<Long> zdyDeptIds = user.getRoles().stream().filter(role -> role.getDataScope() == 2)
                    .flatMap(role -> role.getCustomDeptIds().stream()).collect(Collectors.toSet());
            if (dataScopes.contains(4)) {
                //查询当前机构全部部门
                List<Long> deptIds = Sql.getDeptIds(user.getTenantId());
                //移除掉需要查询的部门
                deptIds.remove(user.getDeptId());
                //异常需要查询的部门，剩余的就是不需要查询的部门
                deptIds.removeAll(zdyDeptIds);
                //查询全部数据不查询自定义之外的部门
                if (deptIds.isEmpty()) {
                    return " and %s in(select id from wh_power_station where tenant_id in(%s) and deleted = 0%s)".formatted(name, dataScope(user.getTenantId()), districtScopes);
                } else {
                    return " and %s in(select id from wh_power_station where tenant_id in(%s) and dept_id not in(%s) and deleted = 0%s)".formatted(name, dataScope(user.getTenantId()), join(deptIds), districtScopes);
                }
            } else if (dataScopes.contains(3)) {
                //只查询本部门加自定义部门
                zdyDeptIds.add(user.getDeptId());
                return " and %s in(select id from wh_power_station where dept_id in(%s) and deleted = 0%s)".formatted(name, join(zdyDeptIds), districtScopes);
            } else {
                return " and %s in(select id from wh_power_station where dept_id in(%s) and deleted = 0%s)".formatted(name, join(zdyDeptIds), districtScopes);
            }
        } else if (dataScopes.contains(4)) {
            //查询当前机构全部部门
            List<Long> deptIds = Sql.getDeptIds(user.getTenantId());
            deptIds.remove(user.getDeptId());
            //查询全部数据当前部门
            if (deptIds.isEmpty()) {
                return " and %s in(select id from wh_power_station where tenant_id in(%s) and deleted = 0%s)".formatted(name, dataScope(user.getTenantId()), districtScopes);
            } else {
                return " and %s in(select id from wh_power_station where tenant_id in(%s) and dept_id not in(%s) and deleted = 0%s)".formatted(name, dataScope(user.getTenantId()), join(deptIds), districtScopes);
            }
        } else if (dataScopes.contains(3)) {
            return " and %s in(select id from wh_power_station where dept_id = %d and deleted = 0%s)".formatted(name, user.getDeptId(), districtScopes);
        } else {
            return " and %s in(select id from wh_power_station where tenant_id = %d and create_id = %d and deleted = 0%s)".formatted(name, user.getTenantId(), user.getUserId(), districtScopes);
        }
    }

    /**
     * 数据权限
     * sid 查询电站编号
     */
    default String dataScopeSqlId() {
        SysUser user = getSysUser();
        //业主
        if (SysUser.isProprietor(user.getUserType())) {
            return "select power_station_id as id from sys_user_station where user_id = " + user.getUserId();
        }
        //区域
        String districtScopes = getDistrictScopes(user.getUserId());
        //角色不是超级管理员就要按数据权限查询
        //数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限 5：仅本人数据权限）
        Set<Integer> dataScopes = user.getRoles().stream().map(SysRole::getDataScope).collect(Collectors.toSet());
        if (dataScopes.contains(1) || (dataScopes.contains(4) && user.getTenantId().equals(user.getDeptId()))) {
            if (isAdminRole(user.getRoleIds())) {
                if (districtScopes.isEmpty()) {
                    return "";
                }
                return "select id from wh_power_station where deleted = 0%s".formatted(districtScopes);
            } else {
                return "select id from wh_power_station where tenant_id in(%s) and deleted = 0%s".formatted(dataScope(user.getTenantId()), districtScopes);
            }
        } else if (dataScopes.contains(2)) {
            //自定义部门ID
            Set<Long> zdyDeptIds = user.getRoles().stream().filter(role -> role.getDataScope() == 2)
                    .flatMap(role -> role.getCustomDeptIds().stream()).collect(Collectors.toSet());
            if (dataScopes.contains(4)) {
                //查询当前机构全部部门
                List<Long> deptIds = Sql.getDeptIds(user.getTenantId());
                //移除掉需要查询的部门
                deptIds.remove(user.getDeptId());
                //异常需要查询的部门，剩余的就是不需要查询的部门
                deptIds.removeAll(zdyDeptIds);
                //查询全部数据不查询自定义之外的部门
                if (deptIds.isEmpty()) {
                    return "select id from wh_power_station where tenant_id in(%s) and deleted = 0%s".formatted(dataScope(user.getTenantId()), districtScopes);
                } else {
                    return "select id from wh_power_station where tenant_id in(%s) and dept_id not in(%s) and deleted = 0%s".formatted(dataScope(user.getTenantId()), join(deptIds), districtScopes);
                }
            } else if (dataScopes.contains(3)) {
                //只查询本部门加自定义部门
                zdyDeptIds.add(user.getDeptId());
                return "select id from wh_power_station where dept_id in(%s) and deleted = 0%s".formatted(join(zdyDeptIds), districtScopes);
            } else {
                return "select id from wh_power_station where dept_id in(%s) and deleted = 0%s".formatted(join(zdyDeptIds), districtScopes);
            }
        } else if (dataScopes.contains(4)) {
            //查询当前机构全部部门
            List<Long> deptIds = Sql.getDeptIds(user.getTenantId());
            deptIds.remove(user.getDeptId());
            //查询全部数据当前部门
            if (deptIds.isEmpty()) {
                return "select id from wh_power_station where tenant_id in(%s) and deleted = 0%s".formatted(dataScope(user.getTenantId()), districtScopes);
            } else {
                return "select id from wh_power_station where tenant_id in(%s) and dept_id not in(%s) and deleted = 0%s".formatted(dataScope(user.getTenantId()), join(deptIds), districtScopes);
            }
        } else if (dataScopes.contains(3)) {
            return "select id from wh_power_station where dept_id = %d and deleted = 0%s".formatted(user.getDeptId(), districtScopes);
        } else {
            return "select id from wh_power_station where tenant_id = %d and create_id = %d and deleted = 0%s".formatted(user.getTenantId(), user.getUserId(), districtScopes);
        }
    }

    private String getDistrictScopes(Long userId) {
        UserDistrict ud = getUd(userId);
        if (ud.getAllCountry()) {
            return "";
        } else if (!ud.getCountryIdStr().isEmpty() && !ud.getProvinceIdStr().isEmpty()) {
            return " and (country_id in(%s) or province_id in(%s))".formatted(ud.getCountryIdStr(), ud.getProvinceIdStr());
        } else if (!ud.getCountryIdStr().isEmpty()) {
            return " and country_id in(%s)".formatted(ud.getCountryIdStr());
        } else if (!ud.getProvinceIdStr().isEmpty()) {
            return " and province_id in(%s)".formatted(ud.getProvinceIdStr());
        } else {
            return " and false";
        }
    }

    default <T> T nullThrow(T obj) {
        if (ObjUtil.isEmpty(obj)) {
            throw new ServiceException("数据不存在");
        }
        return obj;
    }

    default String join(Collection<?> ids) {
        return CollUtil.join(ids, ",");
    }

    default String joinStr(Collection<String> ids) {
        return CollUtil.join(ids, ",", "'", "'");
    }

    default LoginUser getLoginUser() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) {
            throw new ServiceException("登录状态已过期");
        }
        return loginUser;
    }

    default SysUser getSysUser() {
        return getLoginUser().getSysUser();
    }

    default Long getUserId() {
        return getSysUser().getUserId();
    }

    default Long getTenantId() {
        return getSysUser().getTenantId();
    }

    default Long getDeptId() {
        return getSysUser().getDeptId();
    }

    default <S> S getThrow(S obj) {
        return Optional.ofNullable(obj).orElseThrow(() -> new ServiceException("数据不存在"));
    }

}
