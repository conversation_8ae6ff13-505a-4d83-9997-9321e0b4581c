package com.ruoyi.common.security.utils;

import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.TimeZoneUtil;
import com.ruoyi.common.redis.RedisUtil;
import com.ruoyi.system.api.dto.UserDistrict;
import org.springframework.jdbc.core.JdbcTemplate;

import java.time.ZoneId;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/8
 */
public class Sql {

    private static final JdbcTemplate jdbcTemplate = SpringUtils.getBean(JdbcTemplate.class);

    public static List<Long> getDeptIds(Long tenantId) {
        String sql = "select dept_id from sys_dept where tenant_id = %d and type = 4".formatted(tenantId);
        return jdbcTemplate.query(sql, (rs, rowNum) -> rs.getLong("dept_id"));
    }

    public static UserDistrict getUserDistrict(Long userId) {
        String sql = "select is_all_country,country_ids,province_ids from sys_user_district where user_id = " + userId;
        Map<String, Object> map = jdbcTemplate.queryForMap(sql);
        return UserDistrict.to(map);
    }

    public static List<Integer> getStationId(String dataScopeSql) {
        return jdbcTemplate.query(dataScopeSql, (rs, rowNum) -> rs.getInt("id"));
    }

    public static ZoneId getZoneId(Integer stationId) {
        String tz = RedisUtil.get(CacheConstants.TIMEZONE + stationId);
        if (tz == null) {
            String sql = "select time_zone from wh_power_station where id = " + stationId;
            tz = jdbcTemplate.queryForObject(sql, String.class);
            if (tz == null) {
                throw new ServiceException("数据不存在");
            }
            RedisUtil.set(CacheConstants.TIMEZONE + stationId, tz);
        }
        return TimeZoneUtil.getZoneId(tz);
    }

}
