package com.ruoyi.common.core.web.domain.base;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Entity基类
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public abstract class ITCUDDEntity extends ICUEntity {

    @Schema(description = "租户id")
    protected Long tenantId;

    @Schema(description = "租户名称|机构名称")
    @TableField(exist = false)
    protected String tenantName;

    @Schema(description = "逻辑删除（0:正常1:删除）", hidden = true)
    @TableField(value = "is_delete", select = false)
    @JsonIgnore
    protected Boolean delete;

    @Schema(description = "逻辑删除（0正常 非0删除) 唯一索引使用", hidden = true)
    @TableField(select = false)
    @TableLogic
    @JsonIgnore
    protected Integer deleted;

}
