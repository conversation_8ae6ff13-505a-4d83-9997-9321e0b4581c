package com.ruoyi.common.core.utils;

import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
public class CommonUtil implements EnvironmentAware {

    private static Environment environment;

    /**
     * 当前运行环境 dev|test|pro
     */
    public static String PROFILE = "pro";

    /**
     * excel导出最大条数
     */
    public static Long MAX_EXPORT_EXCEL_ROW_COUNT = 10000L;


    /**
     * 获取Date的时间戳（秒）
     */
    public static Long getSecondsFromDate(Date date) {
        return date != null ? date.getTime() / 1000L : null;
    }

    /**
     * 根据秒级时间戳获取Date对象
     */
    public static Date getDateFromSeconds(long seconds) {
        return new Date(seconds * 1000);
    }

    /**
     * 获取当前运行环境配置(spring.profiles.active | spring.datasource.url | ...)
     */
    public static String getEnvStr(String name) {
        if (environment != null) {
            return environment.getProperty(name);
        }
        return "";
    }


    @Override
    public void setEnvironment(Environment environment) {
        CommonUtil.environment = environment;
        PROFILE = getEnvStr("spring.profiles.active");
    }

}
