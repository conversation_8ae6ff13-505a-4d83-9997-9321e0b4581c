package com.ruoyi.common.core.exception;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 业务异常
 *
 * <AUTHOR>
 */
@Setter
@Getter
@NoArgsConstructor
public final class ServiceException extends RuntimeException {

    private Object[] args;

    public ServiceException(String message) {
        super(message);
    }

    public ServiceException(String message, Object... args) {
        super(message);
        this.args = args;
    }

}