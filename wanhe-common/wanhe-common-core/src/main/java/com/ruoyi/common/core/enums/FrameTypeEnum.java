package com.ruoyi.common.core.enums;

import java.util.HashMap;
import java.util.Map;

public enum FrameTypeEnum {

    TIMED_UPLOAD("0x03", "参数定时上传"),

    ;

    private static final Map<String, FrameTypeEnum> indexMap = new HashMap<>();

    static {
        for (FrameTypeEnum type : values()) {
            indexMap.put(type.index, type);
        }
    }

    private final String index;
    private final String remark;

    FrameTypeEnum(String index, String remark) {
        this.index = index;
        this.remark = remark;
    }

    public static FrameTypeEnum getFrameTypeEnum(String index) {
        return indexMap.get(index);
    }

    public String getIndex() {
        return index;
    }

    public String getRemark() {
        return remark;
    }
}
