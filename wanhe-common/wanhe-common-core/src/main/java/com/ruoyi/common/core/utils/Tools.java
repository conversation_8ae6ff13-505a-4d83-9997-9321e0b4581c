package com.ruoyi.common.core.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.ArrTotal;
import com.ruoyi.common.core.domain.DataUnit;
import com.ruoyi.common.core.exception.ServiceException;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2024/12/20
 */
public class Tools {

    public static final Pattern PURE_NUMBER = Pattern.compile("^\\d+$");
    private static final Map<String, String> AREA;

    static {
        JSONArray array = JSONArray.parseArray("[{'iso2':'af','value':'+93'},{'iso2':'al','value':'+355'},{'iso2':'dz','value':'+213'},{'iso2':'as','value':'+1684'},{'iso2':'ad','value':'+376'},{'iso2':'ao','value':'+244'},{'iso2':'ai','value':'+1264'},{'iso2':'ag','value':'+1268'},{'iso2':'ar','value':'+54'},{'iso2':'am','value':'+374'},{'iso2':'aw','value':'+297'},{'iso2':'au','value':'+61'},{'iso2':'at','value':'+43'},{'iso2':'az','value':'+994'},{'iso2':'bs','value':'+1242'},{'iso2':'bh','value':'+973'},{'iso2':'bd','value':'+880'},{'iso2':'bb','value':'+1246'},{'iso2':'by','value':'+375'},{'iso2':'be','value':'+32'},{'iso2':'bz','value':'+501'},{'iso2':'bj','value':'+229'},{'iso2':'bm','value':'+1441'},{'iso2':'bt','value':'+975'},{'iso2':'bo','value':'+591'},{'iso2':'ba','value':'+387'},{'iso2':'bw','value':'+267'},{'iso2':'br','value':'+55'},{'iso2':'io','value':'+246'},{'iso2':'vg','value':'+1284'},{'iso2':'bn','value':'+673'},{'iso2':'bg','value':'+359'},{'iso2':'bf','value':'+226'},{'iso2':'bi','value':'+257'},{'iso2':'kh','value':'+855'},{'iso2':'cm','value':'+237'},{'iso2':'ca','value':'+204'},{'iso2':'cv','value':'+238'},{'iso2':'bq','value':'+599'},{'iso2':'ky','value':'+1345'},{'iso2':'cf','value':'+236'},{'iso2':'td','value':'+235'},{'iso2':'cl','value':'+56'},{'iso2':'cn','value':'+86'},{'iso2':'cx','value':'+61'},{'iso2':'cc','value':'+61'},{'iso2':'co','value':'+57'},{'iso2':'km','value':'+269'},{'iso2':'cd','value':'+243'},{'iso2':'cg','value':'+242'},{'iso2':'ck','value':'+682'},{'iso2':'cr','value':'+506'},{'iso2':'ci','value':'+225'},{'iso2':'hr','value':'+385'},{'iso2':'cu','value':'+53'},{'iso2':'cw','value':'+599'},{'iso2':'cy','value':'+357'},{'iso2':'cz','value':'+420'},{'iso2':'dk','value':'+45'},{'iso2':'dj','value':'+253'},{'iso2':'dm','value':'+1767'},{'iso2':'do','value':'+809'},{'iso2':'ec','value':'+593'},{'iso2':'eg','value':'+20'},{'iso2':'sv','value':'+503'},{'iso2':'gq','value':'+240'},{'iso2':'er','value':'+291'},{'iso2':'ee','value':'+372'},{'iso2':'et','value':'+251'},{'iso2':'fk','value':'+500'},{'iso2':'fo','value':'+298'},{'iso2':'fj','value':'+679'},{'iso2':'fi','value':'+358'},{'iso2':'fr','value':'+33'},{'iso2':'gf','value':'+594'},{'iso2':'pf','value':'+689'},{'iso2':'ga','value':'+241'},{'iso2':'gm','value':'+220'},{'iso2':'ge','value':'+995'},{'iso2':'de','value':'+49'},{'iso2':'gh','value':'+233'},{'iso2':'gi','value':'+350'},{'iso2':'gr','value':'+30'},{'iso2':'gl','value':'+299'},{'iso2':'gd','value':'+1473'},{'iso2':'gp','value':'+590'},{'iso2':'gu','value':'+1671'},{'iso2':'gt','value':'+502'},{'iso2':'gg','value':'+44'},{'iso2':'gn','value':'+224'},{'iso2':'gw','value':'+245'},{'iso2':'gy','value':'+592'},{'iso2':'ht','value':'+509'},{'iso2':'hn','value':'+504'},{'iso2':'hk','value':'+852'},{'iso2':'hu','value':'+36'},{'iso2':'is','value':'+354'},{'iso2':'in','value':'+91'},{'iso2':'id','value':'+62'},{'iso2':'ir','value':'+98'},{'iso2':'iq','value':'+964'},{'iso2':'ie','value':'+353'},{'iso2':'im','value':'+44'},{'iso2':'il','value':'+972'},{'iso2':'it','value':'+39'},{'iso2':'jm','value':'+876'},{'iso2':'jp','value':'+81'},{'iso2':'je','value':'+44'},{'iso2':'jo','value':'+962'},{'iso2':'kz','value':'+7'},{'iso2':'ke','value':'+254'},{'iso2':'ki','value':'+686'},{'iso2':'xk','value':'+383'},{'iso2':'kw','value':'+965'},{'iso2':'kg','value':'+996'},{'iso2':'la','value':'+856'},{'iso2':'lv','value':'+371'},{'iso2':'lb','value':'+961'},{'iso2':'ls','value':'+266'},{'iso2':'lr','value':'+231'},{'iso2':'ly','value':'+218'},{'iso2':'li','value':'+423'},{'iso2':'lt','value':'+370'},{'iso2':'lu','value':'+352'},{'iso2':'mo','value':'+853'},{'iso2':'mk','value':'+389'},{'iso2':'mg','value':'+261'},{'iso2':'mw','value':'+265'},{'iso2':'my','value':'+60'},{'iso2':'mv','value':'+960'},{'iso2':'ml','value':'+223'},{'iso2':'mt','value':'+356'},{'iso2':'mh','value':'+692'},{'iso2':'mq','value':'+596'},{'iso2':'mr','value':'+222'},{'iso2':'mu','value':'+230'},{'iso2':'yt','value':'+262'},{'iso2':'mx','value':'+52'},{'iso2':'fm','value':'+691'},{'iso2':'md','value':'+373'},{'iso2':'mc','value':'+377'},{'iso2':'mn','value':'+976'},{'iso2':'me','value':'+382'},{'iso2':'ms','value':'+1664'},{'iso2':'ma','value':'+212'},{'iso2':'mz','value':'+258'},{'iso2':'mm','value':'+95'},{'iso2':'na','value':'+264'},{'iso2':'nr','value':'+674'},{'iso2':'np','value':'+977'},{'iso2':'nl','value':'+31'},{'iso2':'nc','value':'+687'},{'iso2':'nz','value':'+64'},{'iso2':'ni','value':'+505'},{'iso2':'ne','value':'+227'},{'iso2':'ng','value':'+234'},{'iso2':'nu','value':'+683'},{'iso2':'nf','value':'+672'},{'iso2':'kp','value':'+850'},{'iso2':'mp','value':'+1670'},{'iso2':'no','value':'+47'},{'iso2':'om','value':'+968'},{'iso2':'pk','value':'+92'},{'iso2':'pw','value':'+680'},{'iso2':'ps','value':'+970'},{'iso2':'pa','value':'+507'},{'iso2':'pg','value':'+675'},{'iso2':'py','value':'+595'},{'iso2':'pe','value':'+51'},{'iso2':'ph','value':'+63'},{'iso2':'pl','value':'+48'},{'iso2':'pt','value':'+351'},{'iso2':'pr','value':'+787'},{'iso2':'qa','value':'+974'},{'iso2':'re','value':'+262'},{'iso2':'ro','value':'+40'},{'iso2':'ru','value':'+7'},{'iso2':'rw','value':'+250'},{'iso2':'bl','value':'+590'},{'iso2':'sh','value':'+290'},{'iso2':'kn','value':'+1869'},{'iso2':'lc','value':'+1758'},{'iso2':'mf','value':'+590'},{'iso2':'pm','value':'+508'},{'iso2':'vc','value':'+1784'},{'iso2':'ws','value':'+685'},{'iso2':'sm','value':'+378'},{'iso2':'st','value':'+239'},{'iso2':'sa','value':'+966'},{'iso2':'sn','value':'+221'},{'iso2':'rs','value':'+381'},{'iso2':'sc','value':'+248'},{'iso2':'sl','value':'+232'},{'iso2':'sg','value':'+65'},{'iso2':'sx','value':'+1721'},{'iso2':'sk','value':'+421'},{'iso2':'si','value':'+386'},{'iso2':'sb','value':'+677'},{'iso2':'so','value':'+252'},{'iso2':'za','value':'+27'},{'iso2':'kr','value':'+82'},{'iso2':'ss','value':'+211'},{'iso2':'es','value':'+34'},{'iso2':'lk','value':'+94'},{'iso2':'sd','value':'+249'},{'iso2':'sr','value':'+597'},{'iso2':'sj','value':'+47'},{'iso2':'sz','value':'+268'},{'iso2':'se','value':'+46'},{'iso2':'ch','value':'+41'},{'iso2':'sy','value':'+963'},{'iso2':'tw','value':'+886'},{'iso2':'tj','value':'+992'},{'iso2':'tz','value':'+255'},{'iso2':'th','value':'+66'},{'iso2':'tl','value':'+670'},{'iso2':'tg','value':'+228'},{'iso2':'tk','value':'+690'},{'iso2':'to','value':'+676'},{'iso2':'tt','value':'+1868'},{'iso2':'tn','value':'+216'},{'iso2':'tr','value':'+90'},{'iso2':'tm','value':'+993'},{'iso2':'tc','value':'+1649'},{'iso2':'tv','value':'+688'},{'iso2':'vi','value':'+1340'},{'iso2':'ug','value':'+256'},{'iso2':'ua','value':'+380'},{'iso2':'ae','value':'+971'},{'iso2':'gb','value':'+44'},{'iso2':'us','value':'+1'},{'iso2':'uy','value':'+598'},{'iso2':'uz','value':'+998'},{'iso2':'vu','value':'+678'},{'iso2':'va','value':'+39'},{'iso2':'ve','value':'+58'},{'iso2':'vn','value':'+84'},{'iso2':'wf','value':'+681'},{'iso2':'eh','value':'+212'},{'iso2':'ye','value':'+967'},{'iso2':'zm','value':'+260'},{'iso2':'zw','value':'+263'},{'iso2':'ax','value':'+358'},{'iso2':'aq','value':'+672'},{'iso2':'bv','value':'+47'},{'iso2':'gs','value':'+500'},{'iso2':'hm','value':'+672'},{'iso2':'pn','value':'+64'},{'iso2':'tf','value':'+262'},{'iso2':'um','value':'+1'}]");
        AREA = new HashMap<>(array.size());
        for (int i = 0; i < array.size(); i++) {
            JSONObject obj = array.getJSONObject(i);
            AREA.put(obj.getString("iso2"), obj.getString("value"));
        }
    }

    public static String getArea(String iso2) {
        return AREA.getOrDefault(iso2, StrUtil.EMPTY);
    }

    /**
     * 校验是否为纯数字
     */
    public static boolean isNum(String number) {
        return StrUtil.isNotBlank(number) && ReUtil.isMatch(PURE_NUMBER, number);
    }

    public static String snModel(String sn) {
        if (sn.length() < 8) {
            return sn;
        }
        return sn.substring(0, 8);
    }

    public static LocalDate snCheck(String sn) {
        if (sn.length() != 20) {
            throw new ServiceException("序列号长度必须为20位");
        }
        if (!ReUtil.isMatch("^[A-Z0-9]{20}$", sn)) {
            throw new ServiceException("序列号只能输入数字和大写字母");
        }
        try {
            Integer.parseInt(sn.substring(16));
        } catch (Exception e) {
            throw new ServiceException("设备流水号错误");
        }
        try {
            return LocalDate.parse(sn.substring(9, 15), TimeZoneUtil.yyMMdd);
        } catch (Exception e) {
            throw new ServiceException("设备生产批次错误");
        }
    }

    public static List<Object> getTimeData(Object time, BigDecimal data) {
        List<Object> list = new ArrayList<>(2);
        list.add(time);
        list.add(Objects.requireNonNullElse(data, "-"));
        return list;
    }

    public static List<Object> getTimeDataGtZero(Object time, BigDecimal data) {
        List<Object> list = new ArrayList<>(2);
        list.add(time);
        if (data == null) {
            list.add("-");
        } else {
            if (data.compareTo(BigDecimal.ZERO) < 0) {
                list.add(BigDecimal.ZERO);
            } else {
                list.add(data);
            }
        }
        return list;
    }

    public static List<Object> getTimeData(Object time, BigDecimal data, boolean fillZero) {
        List<Object> list = new ArrayList<>(2);
        list.add(time);
        if (fillZero) {
            list.add(Objects.requireNonNullElse(data, BigDecimal.ZERO));
        } else {
            list.add(Objects.requireNonNullElse(data, "-"));
        }
        return list;
    }

    public static String covertUnit(List<List<Object>> listList) {
        BigDecimal min = listList.stream().filter(item -> item.get(1) instanceof BigDecimal d && d.compareTo(BigDecimal.ZERO) > 0)
                .map(item -> (BigDecimal) item.get(1)).min(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
        BigDecimal absMin = min.abs();
        if (absMin.compareTo(NumUtil.THOUSAND) < 0) {
            return "kWh";
        } else if (absMin.compareTo(NumUtil.MILLION) < 0) {
            for (List<Object> list : listList) {
                if (list.get(1) instanceof BigDecimal d && d.compareTo(BigDecimal.ZERO) > 0) {
                    list.set(1, d.divide(NumUtil.THOUSAND, 2, RoundingMode.HALF_UP));
                }
            }
            return "MWh";
        } else {
            for (List<Object> list : listList) {
                if (list.get(1) instanceof BigDecimal d && d.compareTo(BigDecimal.ZERO) > 0) {
                    list.set(1, d.divide(NumUtil.MILLION, 2, RoundingMode.HALF_UP));
                }
            }
            return "GWh";
        }
    }

    public static String covertPowerWUnit(List<List<Object>> listList) {
        BigDecimal min = listList.stream().filter(item -> item.get(1) instanceof BigDecimal d && d.compareTo(BigDecimal.ZERO) > 0)
                .map(item -> (BigDecimal) item.get(1)).min(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
        BigDecimal absMin = min.abs();
        if (absMin.compareTo(NumUtil.THOUSAND) < 0) {
            return "W";
        } else if (absMin.compareTo(NumUtil.MILLION) < 0) {
            for (List<Object> list : listList) {
                if (list.get(1) instanceof BigDecimal d && d.compareTo(BigDecimal.ZERO) > 0) {
                    list.set(1, d.divide(NumUtil.THOUSAND, 2, RoundingMode.HALF_UP));
                }
            }
            return "kWh";
        } else if (absMin.compareTo(NumUtil.BILLION) < 0) {
            for (List<Object> list : listList) {
                if (list.get(1) instanceof BigDecimal d && d.compareTo(BigDecimal.ZERO) > 0) {
                    list.set(1, d.divide(NumUtil.MILLION, 2, RoundingMode.HALF_UP));
                }
            }
            return "MWh";
        } else {
            for (List<Object> list : listList) {
                if (list.get(1) instanceof BigDecimal d && d.compareTo(BigDecimal.ZERO) > 0) {
                    list.set(1, d.divide(NumUtil.BILLION, 2, RoundingMode.HALF_UP));
                }
            }
            return "GWh";
        }
    }

    public static String covertKwhUnit(List<BigDecimal> list) {
        BigDecimal min = list.stream().filter(d -> d.compareTo(BigDecimal.ZERO) > 0).min(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
        BigDecimal absMin = min.abs();
        if (absMin.compareTo(NumUtil.THOUSAND) < 0) {
            return "kWh";
        } else if (absMin.compareTo(NumUtil.MILLION) < 0) {
            for (int i = 0; i < list.size(); i++) {
                BigDecimal data = list.get(i);
                if (data.compareTo(BigDecimal.ZERO) > 0) {
                    list.set(i, data.divide(NumUtil.THOUSAND, 2, RoundingMode.HALF_UP));
                }
            }
            return "MWh";
        } else {
            for (int i = 0; i < list.size(); i++) {
                BigDecimal data = list.get(i);
                if (data.compareTo(BigDecimal.ZERO) > 0) {
                    list.set(i, data.divide(NumUtil.MILLION, 2, RoundingMode.HALF_UP));
                }
            }
            return "GWh";
        }
    }

    //转换功率单位
    public static DataUnit covertPowerUnit(BigDecimal power) {
        BigDecimal absPower = power.abs();
        if (absPower.compareTo(NumUtil.THOUSAND) < 0) {
            return new DataUnit(power, "W");
        } else if (absPower.compareTo(NumUtil.MILLION) < 0) {
            return new DataUnit(power.divide(NumUtil.THOUSAND, 2, RoundingMode.HALF_UP), "kW");
        } else if (absPower.compareTo(NumUtil.BILLION) < 0) {
            return new DataUnit(power.divide(NumUtil.MILLION, 2, RoundingMode.HALF_UP), "MW");
        } else {
            return new DataUnit(power.divide(NumUtil.BILLION, 2, RoundingMode.HALF_UP), "GW");
        }
    }

    public static String covertPowerUnitStr(BigDecimal power) {
        if (power == null) {
            return null;
        }
        BigDecimal absPower = power.abs();
        if (absPower.compareTo(NumUtil.THOUSAND) < 0) {
            return power.stripTrailingZeros().toPlainString() + "W";
        } else if (absPower.compareTo(NumUtil.MILLION) < 0) {
            return power.divide(NumUtil.THOUSAND, 2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString() + "kW";
        } else if (absPower.compareTo(NumUtil.BILLION) < 0) {
            return power.divide(NumUtil.MILLION, 2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString() + "MW";
        } else {
            return power.divide(NumUtil.BILLION, 2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString() + "GW";
        }
    }

    //转换发电量单位
    public static DataUnit covertKwhUnit(BigDecimal kwh) {
        BigDecimal absKwh = kwh.abs();
        if (absKwh.compareTo(NumUtil.THOUSAND) < 0) {
            return new DataUnit(kwh, "kWh");
        } else if (absKwh.compareTo(NumUtil.MILLION) < 0) {
            return new DataUnit(kwh.divide(NumUtil.THOUSAND, 2, RoundingMode.HALF_UP), "MWh");
        } else {
            return new DataUnit(kwh.divide(NumUtil.MILLION, 2, RoundingMode.HALF_UP), "GWh");
        }
    }

    public static String covertKwhUnitStr(BigDecimal kwh) {
        if (kwh == null) {
            return null;
        }
        BigDecimal absKwh = kwh.abs();
        if (absKwh.compareTo(NumUtil.THOUSAND) < 0) {
            return kwh.stripTrailingZeros().toPlainString() + "kWh";
        } else if (absKwh.compareTo(NumUtil.MILLION) < 0) {
            return kwh.divide(NumUtil.THOUSAND, 2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString() + "MWh";
        } else {
            return kwh.divide(NumUtil.MILLION, 2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString() + "GWh";
        }
    }

    /**
     * 版本号解析 101011f
     */
    public static String getVersion(String version) {
        if (version == null || version.isEmpty()) {
            return version;
        }
        int value;
        try {
            value = Integer.parseInt(version);
        } catch (Exception e) {
            return version;
        }
        String hex = Integer.toHexString(value);
        int length = hex.length();
        return switch (length) {
            case 1, 2 -> "0.0." + Integer.parseInt(hex, 16);
            case 3 -> "0." + parseInt(hex, 0, 1) + "." + parseInt(hex, 1);
            case 4 -> "0." + parseInt(hex, 0, 2) + "." + parseInt(hex, 2);
            case 5 -> parseInt(hex, 0, 1) + "." + parseInt(hex, 1, 3) + "." + parseInt(hex, 3);
            case 6 -> parseInt(hex, 0, 2) + "." + parseInt(hex, 2, 4) + "." + parseInt(hex, 4);
            case 7 -> parseInt(hex, 1, 3) + "." + parseInt(hex, 3, 5) + "." + parseInt(hex, 5);
            default -> parseInt(hex.substring(length - 6, length - 4)) + "." + parseInt(hex.substring(length - 4, length - 2)) + "." + parseInt(hex.substring(length - 2));
        };
    }

    private static int parseInt(String hex) {
        return Integer.parseInt(hex, 16);
    }

    private static int parseInt(String hex, int beginIndex) {
        return Integer.parseInt(hex.substring(beginIndex), 16);
    }

    private static int parseInt(String hex, int beginIndex, int endIndex) {
        return Integer.parseInt(hex.substring(beginIndex, endIndex), 16);
    }


    public static int floatToIntBits(BigDecimal data) {
        return Float.floatToIntBits(data.floatValue());
    }

    public static BigDecimal intBitsToFloat(int data) {
        return BigDecimal.valueOf(Float.intBitsToFloat(data));
    }

    public static Map<String, BigDecimal> getMap(List<JSONObject> list) {
        if (list == null) {
            return Collections.emptyMap();
        }
        Map<String, BigDecimal> map = new HashMap<>(list.size());
        for (JSONObject json : list) {
            map.put(json.getString("time"), json.getBigDecimal("data"));
        }
        return map;
    }

    public static Map<String, ArrTotal<BigDecimal>> getListMap(List<JSONObject> list) {
        if (list == null) {
            return Collections.emptyMap();
        }
        Map<String, ArrTotal<BigDecimal>> map = new HashMap<>(list.size());
        for (JSONObject json : list) {
            List<BigDecimal> list1 = CollUtil.list(true, json.getBigDecimal("pvTempPower"), json.getBigDecimal("pv2TempPower"),
                    json.getBigDecimal("pv3TempPower"), json.getBigDecimal("pv4TempPower"),
                    json.getBigDecimal("pv5TempPower"), json.getBigDecimal("pv6TempPower"),
                    json.getBigDecimal("pv7TempPower"), json.getBigDecimal("pv8TempPower"));
            map.put(json.getString("time"), new ArrTotal<>(list1, json.getBigDecimal("pvTotalPower")));
        }
        return map;
    }

    public static ArrTotal<BigDecimal> getArrTotal(JSONObject json) {
        if (json == null) {
            return new ArrTotal<>(CollUtil.list(true, null, null, null, null, null, null, null, null), null);
        }
        List<BigDecimal> list1 = CollUtil.list(true, json.getBigDecimal("pvEnergy"), json.getBigDecimal("pv2Energy"),
                json.getBigDecimal("pv3Energy"), json.getBigDecimal("pv4Energy"),
                json.getBigDecimal("pv5Energy"), json.getBigDecimal("pv6Energy"),
                json.getBigDecimal("pv7Energy"), json.getBigDecimal("pv8Energy"));

        BigDecimal pvEnergy = json.getBigDecimal("pvEnergy") == null ? BigDecimal.ZERO : json.getBigDecimal("pvEnergy");
        BigDecimal pv2Energy = json.getBigDecimal("pv2Energy") == null ? BigDecimal.ZERO : json.getBigDecimal("pv2Energy");
        BigDecimal pv3Energy = json.getBigDecimal("pv3Energy") == null ? BigDecimal.ZERO : json.getBigDecimal("pv3Energy");
        BigDecimal pv4Energy = json.getBigDecimal("pv4Energy") == null ? BigDecimal.ZERO : json.getBigDecimal("pv4Energy");
        BigDecimal pv5Energy = json.getBigDecimal("pv5Energy") == null ? BigDecimal.ZERO : json.getBigDecimal("pv5Energy");
        BigDecimal pv6Energy = json.getBigDecimal("pv6Energy") == null ? BigDecimal.ZERO : json.getBigDecimal("pv6Energy");
        BigDecimal pv7Energy = json.getBigDecimal("pv7Energy") == null ? BigDecimal.ZERO : json.getBigDecimal("pv7Energy");
        BigDecimal pv8Energy = json.getBigDecimal("pv8Energy") == null ? BigDecimal.ZERO : json.getBigDecimal("pv8Energy");

        return new ArrTotal<>(list1, pvEnergy.add(pv2Energy).add(pv3Energy).add(pv4Energy).add(pv5Energy).add(pv6Energy).add(pv7Energy).add(pv8Energy));
    }

    public static BigDecimal halfUp(BigDecimal data) {
        if (data == null) {
            return null;
        }
        return data.setScale(2, RoundingMode.HALF_UP);
    }

    public static BigDecimal add(BigDecimal a, BigDecimal b) {
        if (a != null && b != null) {
            return a.add(b);
        }
        if (a != null) {
            return a;
        }
        return b;
    }

    public static BigDecimal degree(BigDecimal ydAll, BigDecimal fdAll) {
        if (ydAll.compareTo(BigDecimal.ZERO) == 0 || fdAll.compareTo(ydAll) >= 0) {
            return BigDecimal.ZERO;
        } else {
            return NumUtil.HUNDRED.subtract(fdAll.multiply(NumUtil.HUNDRED).divide(ydAll, 2, RoundingMode.HALF_UP));
        }
    }

    /**
     * 设置状态转平台设备状态
     */
    public static int convertDeviceStatus(int sysStatus) {
        // 设备状态 0离线 1在线 2告警 3故障 4停机 5自检 6预启动
        // 系统状态 1自检ok 2停机 4预启动 8运行 16故障 32告警
        return switch (sysStatus) {
            case 1 -> 5;
            case 2 -> 4;
            case 4 -> 6;
            case 8 -> 1;
            case 16 -> 3;
            default -> 2;
        };
    }

    /**
     * 设备状态 0离线 1在线 2告警 3故障 4停机 5自检 6预启动
     * 电站状态 （0建设中 1在线 2离线 3告警）
     */
    public static int convertStationStatus(List<Integer> statusList) {
        if (CollUtil.isEmpty(statusList)) {
            return 0;
        }
        if (statusList.contains(0)) {
            return 2;
        } else if (statusList.contains(2) || statusList.contains(3)) {
            return 3;
        } else {
            return 1;
        }
    }

    public static String getFaultKey(String code, int bit) {
        return code + "_" + bit;
    }

    public static String joinKey(String key) {
        return "'" + key + "'";
    }

}
