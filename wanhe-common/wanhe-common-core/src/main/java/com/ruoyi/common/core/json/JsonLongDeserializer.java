package com.ruoyi.common.core.json;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;

/**
 * <AUTHOR>
 * @description
 * @date 2023/2/23
 */
public class JsonLongDeserializer extends JsonDeserializer<Long> {

    @Override
    public Long deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        if (p.isExpectedNumberIntToken()) {
            return p.getLongValue();
        }
        String value = p.getText();
        if ("".equals(value)) {
            return null;
        }
        return Long.valueOf(value);
    }

}
