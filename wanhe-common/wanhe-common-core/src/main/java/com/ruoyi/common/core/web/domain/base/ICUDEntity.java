package com.ruoyi.common.core.web.domain.base;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Entity基类
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public abstract class ICUDEntity extends ICUEntity {

    @Schema(description = "逻辑删除（0:正常1:删除）", hidden = true)
    @TableField(value = "is_delete", select = false)
    @TableLogic
    @JsonIgnore
    protected Boolean delete;

}
