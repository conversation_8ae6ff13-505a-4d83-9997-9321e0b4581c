package com.ruoyi.common.core.utils;


/**
 * <AUTHOR>
 * @since 2025/5/17 16:18
 */
public class DataHandleUtil {

    public static Number getNumber(String valueType, Long value){
        String hexString = Long.toHexString(value);

        switch (valueType) {
            case "TYPE_I8":
                // 对于 TYPE_I8，取低 8 位并转换为 byte
                return (byte) (value & 0xFF);
            case "TYPE_U8":
                // 对于 TYPE_U8，取低 8 位并转换为 short
                return (short) (value & 0xFF);
            case "TYPE_I16", "TYPE_BIT16":
                // 对于 TYPE_I16，取低 16 位并转换为 int
                return (short) (value & 0xFFFF);
            case "TYPE_U16":
                // 对于 TYPE_U16，取低 16 位并转换为 long
                return value & 0xFFFFL;
            case "TYPE_U32":
                // 对于 TYPE_U32，取低 32 位并转换为 long
                return value & 0xFFFFFFFFL;
            case "TYPE_F32":
                long longBits = Long.parseLong(hexString, 16);
                int intBits = (int) longBits;
                return Float.intBitsToFloat(intBits);
            default:
                // 默认情况可以根据实际需求调整
                return (int) (value & 0xFFFF);
        }
    }
}
