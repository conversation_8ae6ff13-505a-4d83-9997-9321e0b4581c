package com.ruoyi.common.core.domain;

import com.ruoyi.common.core.constant.Constants;
import lombok.Data;

import java.io.Serializable;

/**
 * 响应信息主体
 *
 * <AUTHOR>
 */
@Data
public class R<T> implements Serializable {

    /** 成功 */
    public static final int SUCCESS = Constants.SUCCESS;

    /** 失败 */
    public static final int FAIL = Constants.FAIL;

    private int code;

    private String msg;

    private T data;

    public static <T> R<T> ok() {
        return restResult(null, SUCCESS, null);
    }

    public static R<Boolean> ok(boolean b) {
        if (b) {
            return restResult(true, SUCCESS, "操作成功");
        }
        return restResult(false, FAIL, "操作失败");
    }

    public static R<Boolean> ok(int b) {
        if (b > 0) {
            return restResult(true, SUCCESS, "操作成功");
        }
        return restResult(false, FAIL, "操作失败");
    }

    public static <T> R<T> ok(T data) {
        return restResult(data, SUCCESS, null);
    }

    public static <T> R<T> ok(T data, String msg) {
        return restResult(data, SUCCESS, msg);
    }

    public static <T> R<T> fail() {
        return restResult(null, FAIL, null);
    }

    public static <T> R<T> fail(String msg) {
        return restResult(null, FAIL, msg);
    }

    public static <T> R<T> fail(T data) {
        return restResult(data, FAIL, null);
    }

    public static <T> R<T> fail(T data, String msg) {
        return restResult(data, FAIL, msg);
    }

    public static <T> R<T> fail(int code, String msg) {
        return restResult(null, code, msg);
    }

    private static <T> R<T> restResult(T data, int code, String msg) {
        R<T> apiResult = new R<>();
        apiResult.setCode(code);
        apiResult.setData(data);
        if (msg == null) {
            if (code == SUCCESS) {
                apiResult.setMsg("success");
            }
        } else {
            apiResult.setMsg(msg);
        }
        return apiResult;
    }

    public static <T> Boolean isError(R<T> ret) {
        return !isSuccess(ret);
    }

    public static <T> Boolean isSuccess(R<T> ret) {
        return R.SUCCESS == ret.getCode();
    }
}
