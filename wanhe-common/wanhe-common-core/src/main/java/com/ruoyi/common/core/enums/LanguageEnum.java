package com.ruoyi.common.core.enums;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.utils.ServletUtils;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum LanguageEnum {

    ZH("zh-Hans", "zh", new Locale("zh","CN")),
    EN("en", "en", new Locale("en","US")),
    DE("de", "de", new Locale("de","DE")),
    FR("fr", "fra", new Locale("fr","FR"));

    private final String language;
    private final String baidu;
    private final Locale locale;

    public static final List<LanguageEnum> TRANS = List.of(EN, DE, FR);
    private static final Map<String, Locale> MAP = Arrays.stream(LanguageEnum.values()).collect(Collectors.toMap(LanguageEnum::getLanguage, LanguageEnum::getLocale));

    public static Locale getLocal(String language) {
        return MAP.get(language);
    }

    public static Locale getLocal() {
        return getLocal(ServletUtils.getRequest().getHeader(SecurityConstants.LANGUAGE));
    }

    public static Locale getLocal(HttpServletRequest request) {
        return getLocal(request.getHeader(SecurityConstants.LANGUAGE));
    }

    public static String getLang(HttpServletRequest request) {
        String language = request.getHeader(SecurityConstants.LANGUAGE);
        return switch (language) {
            case "zh-Hans" -> "zh";
            case "fr" -> "fra";
            default -> language;
        };
    }

    public static String getLang() {
        return getLang(ServletUtils.getRequest());
    }
}
