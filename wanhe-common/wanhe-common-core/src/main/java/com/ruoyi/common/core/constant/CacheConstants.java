package com.ruoyi.common.core.constant;

/**
 * 缓存常量信息
 *
 * <AUTHOR>
 */
public class CacheConstants {

    //emu在线key缓存
    public static final String TIMEZONE = "timezone:";
    //emu在线key缓存
    public static final String EMU_ONLINE_KEY = "emu_online:";
    //发电量key
    public static final String ENERGY_FAULT_KEY = "energy_fault:";
    //故障库缓存key
    public static final String FAULT_DATA_KEY = "faultData:";
    //电表发电数据缓存key
    public static final String METER_DATA_KEY = "meterData:";

    //设备缓存数据
    public static final String EMU_INV = "emu_inv:";
    public static final String EMU_METER = "emu_meter:";
    public static final String INV = "inv:";

    //用户区域权限
    public static final String USER_DISTRICT_KEY = "userDistrict:";

    //下发命令
    public static final String TEMP_KEY = "temp:";

    //微逆发电数据缓存key
    @Deprecated
    public static final String INV_DATA_KEY = "invData:";

    //电站最后上报数据缓存key
    @Deprecated
    public static final String STATION_DATA_KEY = "stationData:";

    //故障通知缓存key
    public static final String FAULT_NOTICE_KEY = "fault_notice:";

    /** 缓存有效期，默认720（分钟） */
    public static final long EXPIRATION = 720;

    /** 缓存刷新时间，默认120（分钟） */
    public static final long REFRESH_TIME = 120;

    /** 密码最大错误次数 */
    public static final int PASSWORD_MAX_RETRY_COUNT = 5;

    /** 密码锁定时间，默认10（分钟） */
    public static final long PASSWORD_LOCK_TIME = 10;

    /** 权限缓存前缀 */
    public static final String LOGIN_TOKEN_KEY = "login_tokens:";

    /** 验证码 redis key */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";

    /** 参数管理 cache key */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /** 登录账户密码错误次数 redis key */
    public static final String PWD_ERR_CNT_KEY = "pwd_err_cnt:";

    /** 登录IP黑名单 cache key */
    public static final String SYS_LOGIN_BLACKIPLIST = SYS_CONFIG_KEY + "sys.login.blackIPList";

    /** 下发命令 */
    public static final String COMMAND = "command:";

    /** 下发命令  回调 */
    public static final String COMMAND_RESULT = "command_result:";
}
