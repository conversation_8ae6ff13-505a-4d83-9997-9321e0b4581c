package com.ruoyi.common.core.configuration;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;

import java.util.TimeZone;

/**
 * <AUTHOR>
 * @date 2025/7/15
 */
@Slf4j
public class TimeZoneInitializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {
    @Override
    public void initialize(ConfigurableApplicationContext applicationContext) {
        Environment environment = applicationContext.getEnvironment();
        // 从环境中读取配置，并提供一个安全的默认值
        String timezoneId = environment.getProperty("spring.app.timezone", "UTC");

        log.info("[TimeZoneInitializer] JVM default timezone has been set to: " + timezoneId);

        TimeZone.setDefault(TimeZone.getTimeZone(timezoneId));

        // 使用 System.out 是因为此时日志系统可能还未完全配置好
        System.out.println("[TimeZoneInitializer] JVM default timezone has been set to: " + timezoneId);
    }
}
