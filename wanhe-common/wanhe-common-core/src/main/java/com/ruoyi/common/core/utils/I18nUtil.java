package com.ruoyi.common.core.utils;

import com.ruoyi.common.core.enums.LanguageEnum;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;

import java.util.Locale;

@Slf4j
public class I18nUtil {

    private static final MessageSource MS = SpringUtils.getBean(MessageSource.class);

    public static String get(String key, HttpServletRequest request, Object[] args) {
        return MS.getMessage(key, args, LanguageEnum.getLocal(request));
    }

    public static String get(String key) {
        return MS.getMessage(key, null, LanguageEnum.getLocal());
    }

    public static String get(String key, Locale locale, Object... args) {
        return MS.getMessage(key, args, locale);
    }

}
