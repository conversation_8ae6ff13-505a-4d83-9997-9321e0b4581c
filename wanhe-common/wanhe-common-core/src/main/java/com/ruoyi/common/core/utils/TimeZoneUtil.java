package com.ruoyi.common.core.utils;

import cn.hutool.core.date.DatePattern;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/12/1
 */
public class TimeZoneUtil {

    public static final DateTimeFormatter HHmm = DatePattern.createFormatter("HH:mm");
    public static final DateTimeFormatter yyyyMMdd = DatePattern.createFormatter("yyyy-MM-dd");
    public static final DateTimeFormatter MMdd = DatePattern.createFormatter("MM-dd");
    public static final DateTimeFormatter yyMMdd = DatePattern.createFormatter("yyMMdd");
    public static final DateTimeFormatter yyyyMMddHHmmss = DatePattern.createFormatter("yyyy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter yyyyMMddHHmm = DatePattern.createFormatter("yyyy-MM-dd HH:mm");
    public static final DateTimeFormatter MMddHHmm = DatePattern.createFormatter("MM-dd HH:mm");
    public static final DateTimeFormatter yyyyMM = DatePattern.createFormatter("yyyy-MM");
    public static final DateTimeFormatter yyyyM = DatePattern.createFormatter("yyyy-M");
    public static final DateTimeFormatter yyyy = DatePattern.createFormatter("yyyy");
    public static final DateTimeFormatter M = DatePattern.createFormatter("M");
    public static final DateTimeFormatter d = DatePattern.createFormatter("d");
    public static final LocalTime TIME_23_59_59 = LocalTime.of(23, 59, 59);
    public static final LocalTime TIME_23_59 = LocalTime.of(23, 59);
    private static final Map<String, ZoneId> TIME_ZONE;

    static {
        TIME_ZONE = new HashMap<>();
        TIME_ZONE.put("UTC+0", ZoneId.of("Etc/GMT-0"));
        TIME_ZONE.put("UTC+1", ZoneId.of("Etc/GMT-1"));
        TIME_ZONE.put("UTC+2", ZoneId.of("Etc/GMT-2"));
        TIME_ZONE.put("UTC+3", ZoneId.of("Etc/GMT-3"));
        TIME_ZONE.put("UTC+4", ZoneId.of("Etc/GMT-4"));
        TIME_ZONE.put("UTC+5", ZoneId.of("Etc/GMT-5"));
        TIME_ZONE.put("UTC+6", ZoneId.of("Etc/GMT-6"));
        TIME_ZONE.put("UTC+7", ZoneId.of("Etc/GMT-7"));
        TIME_ZONE.put("UTC+8", ZoneId.of("Etc/GMT-8"));
        TIME_ZONE.put("UTC+9", ZoneId.of("Etc/GMT-9"));
        TIME_ZONE.put("UTC+10", ZoneId.of("Etc/GMT-10"));
        TIME_ZONE.put("UTC+11", ZoneId.of("Etc/GMT-11"));
        TIME_ZONE.put("UTC+12", ZoneId.of("Etc/GMT-12"));
        TIME_ZONE.put("UTC-1", ZoneId.of("Etc/GMT+1"));
        TIME_ZONE.put("UTC-2", ZoneId.of("Etc/GMT+2"));
        TIME_ZONE.put("UTC-3", ZoneId.of("Etc/GMT+3"));
        TIME_ZONE.put("UTC-4", ZoneId.of("Etc/GMT+4"));
        TIME_ZONE.put("UTC-5", ZoneId.of("Etc/GMT+5"));
        TIME_ZONE.put("UTC-6", ZoneId.of("Etc/GMT+6"));
        TIME_ZONE.put("UTC-7", ZoneId.of("Etc/GMT+7"));
        TIME_ZONE.put("UTC-8", ZoneId.of("Etc/GMT+8"));
        TIME_ZONE.put("UTC-9", ZoneId.of("Etc/GMT+9"));
        TIME_ZONE.put("UTC-10", ZoneId.of("Etc/GMT+10"));
        TIME_ZONE.put("UTC-11", ZoneId.of("Etc/GMT+11"));
        TIME_ZONE.put("UTC-12", ZoneId.of("Etc/GMT+12"));
    }

    public static ZoneId getZoneId(String timeZone) {
        return TIME_ZONE.get(timeZone);
    }

    /**
     * 获取当天的起始时间
     */
    public static ZonedDateTime getZonedEarlyTime(ZoneId zoneId) {
        ZonedDateTime now = ZonedDateTime.now(zoneId);
        return ZonedDateTime.of(now.getYear(), now.getMonthValue(), now.getDayOfMonth(), 0, 0, 0, 0, zoneId);
    }

    /**
     * 获取当天的起始时间
     */
    public static ZonedDateTime getZonedEarlyTime(ZoneId zoneId, ZonedDateTime now) {
        return ZonedDateTime.of(now.getYear(), now.getMonthValue(), now.getDayOfMonth(), 0, 0, 0, 0, zoneId);
    }

    /**
     * 转时间
     */
    public static LocalDateTime getZonedTime(LocalDateTime time, ZoneId zoneId) {
        if (time == null) {
            return null;
        }
        long second = time.atZone(ZoneId.systemDefault()).toEpochSecond();
        return Instant.ofEpochSecond(second).atZone(zoneId).toLocalDateTime();
    }

    /**
     * 转时间
     */
    public static LocalDateTime getZonedTime(LocalDateTime time, String tz) {
        long second = time.atZone(ZoneId.systemDefault()).toEpochSecond();
        return Instant.ofEpochSecond(second).atZone(getZoneId(tz)).toLocalDateTime();
    }

}
