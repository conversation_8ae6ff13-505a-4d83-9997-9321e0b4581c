package com.ruoyi.common.core.enums;

import com.ruoyi.common.core.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/11/21
 */
@Getter
@AllArgsConstructor
public enum DeviceTypeEnum {

    EMU(0, "EMU"),
    W<PERSON>( 1, "微逆"),
    DB( 2, "电表"),
    <PERSON><PERSON>( 3, "组件"),
    DC( 4, "电池"),
    CDZ(5, "充电桩");

    private final int val;

    private final String desc;

    public static DeviceTypeEnum of(int val) {
        for (DeviceTypeEnum deviceTypeEnum : DeviceTypeEnum.values()) {
            if (deviceTypeEnum.getVal() == val) {
                return deviceTypeEnum;
            }
        }
        throw new ServiceException("未知设备类型");
    }

}
