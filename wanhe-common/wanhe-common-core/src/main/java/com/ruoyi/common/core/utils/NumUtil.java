package com.ruoyi.common.core.utils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/12/20
 */
public class NumUtil {

    public static final BigDecimal B = new BigDecimal(1024);
    public static final BigDecimal MB = new BigDecimal(1024 * 1024);

    public static final BigDecimal HUNDRED = new BigDecimal(100);
    public static final BigDecimal THOUSAND = new BigDecimal(1000);
    public static final BigDecimal MILLION = new BigDecimal(1000000);
    public static final BigDecimal BILLION = new BigDecimal(1000000000);

    // 1千度电=0.078吨汽油=0.785吨二氧化碳=36棵树
    public static final BigDecimal GASOLINE = new BigDecimal("0.078");
    public static final BigDecimal COZ = new BigDecimal("0.785");
    public static final BigDecimal TREE = new BigDecimal(36);

    // 1度电=0.123kg煤
    public static final BigDecimal COAL = new BigDecimal(0.123);

}
