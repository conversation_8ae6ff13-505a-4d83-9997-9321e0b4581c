package com.ruoyi.common.core.web.domain.base;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * Entity基类
 *
 * <AUTHOR>
 */
@Setter
@Getter
public abstract class ITCUDEntity extends ICUDEntity {

    @Schema(description = "租户id")
    protected Long tenantId;

    @Schema(description = "租户名称|机构名称")
    @TableField(exist = false)
    protected String tenantName;

}
