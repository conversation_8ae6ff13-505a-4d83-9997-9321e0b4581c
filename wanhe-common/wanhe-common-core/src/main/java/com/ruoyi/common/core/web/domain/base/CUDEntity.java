package com.ruoyi.common.core.web.domain.base;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Entity基类
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public abstract class CUDEntity extends CUEntity {

    @Schema(description = "逻辑删除（0:正常2:删除）", hidden = true)
    @TableField(value = "del_flag", select = false)
    @TableLogic(value = "0", delval = "2")
    @JsonIgnore
    protected String delFlag;

}
