package com.ruoyi.common.core.configuration;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

import java.util.TimeZone;

/**
 * <AUTHOR>
 * @date 2025/7/15
 */
@Slf4j
@EnableConfigurationProperties(TimezoneProperties.class)
public class TimezoneConfiguration {

    public TimezoneConfiguration(TimezoneProperties timezoneProperties) {
        log.info("时区配置: {}", timezoneProperties.getTimezone());
        TimeZone.setDefault(TimeZone.getTimeZone(timezoneProperties.getTimezone()));
    }

}
