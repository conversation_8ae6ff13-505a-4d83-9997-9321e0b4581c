package com.ruoyi.common.core.enums;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.exception.ServiceException;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/12/19
 */
@Getter
@AllArgsConstructor
public enum ClientTypeEnum {

    //120 * 60 * 1000
    PC("1", 720L, 7200000L),
    //半年 30 * 24 * 60 * 60 * 1000
    INSTALL_APP("2", 259200L, 2592000000L),
    USER_APP("3", 259200L, 2592000000L);

    private final String val;
    //缓存分钟
    private final long expireTime;
    //剩余多少毫秒自动刷新token
    private final long refreshTime;

    public static ClientTypeEnum of(String val) {
        if (val == null) {
            throw new ServiceException("客户端类型不能空");
        }
        return switch (val) {
            case "1" -> PC;
            case "2" -> INSTALL_APP;
            case "3" -> USER_APP;
            default -> throw new ServiceException("客户端类型错误");
        };
    }

    public static String getVal(HttpServletRequest request) {
        return request.getHeader(SecurityConstants.CLIENT_TYPE);
    }

}
