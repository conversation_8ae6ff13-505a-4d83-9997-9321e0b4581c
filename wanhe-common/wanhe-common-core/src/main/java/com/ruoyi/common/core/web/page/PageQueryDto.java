package com.ruoyi.common.core.web.page;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.ruoyi.common.core.utils.StringUtils;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;


@Data
public class PageQueryDto {

   //(value = "当前页码", example = "1")
    private Integer pageNum = 1;
   //(value = "分页大小", example = "10")
    private Integer pageSize = 10;
   //(value = "是否导出excel", example = "false")
    private Boolean exportExcel;

    //@formatter:off
    /** 排序列 */
    private String orderByColumn;
    /** 排序的方向desc或者asc */
    private String isAsc = "asc";
    /** 分页参数合理化 */
    private Boolean reasonable = true;
    //@formatter:on

    public String getOrderBy() {
        if (StringUtils.isEmpty(orderByColumn)) {
            return "";
        }
        return StringUtils.toUnderScoreCase(orderByColumn) + " " + isAsc;
    }

    public void setIsAsc(String isAsc) {
        if (StringUtils.isNotEmpty(isAsc)) {
            // 兼容前端排序类型
            if ("ascending".equals(isAsc)) {
                isAsc = "asc";
            } else if ("descending".equals(isAsc)) {
                isAsc = "desc";
            }
            this.isAsc = isAsc;
        }
    }

    public Boolean getReasonable() {
        if (StringUtils.isNull(reasonable)) {
            return Boolean.TRUE;
        }
        return reasonable;
    }
    /**
     * 请求参数
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Map<String, Object> params;

    public Map<String, Object> getParams() {
        if (params == null) {
            params = new HashMap<>();
        }
        return params;
    }

    public void setParams(Map<String, Object> params) {
        this.params = params;
    }
}
