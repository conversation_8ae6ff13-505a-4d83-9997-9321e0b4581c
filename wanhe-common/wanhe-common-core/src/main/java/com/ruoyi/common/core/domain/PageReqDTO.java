package com.ruoyi.common.core.domain;

import lombok.Setter;


/**
 * <AUTHOR>
 * @date 2024/10/26
 */
@Setter
public class PageReqDTO {

    /**
     * 查询页数
     */
    private Integer pageNum;

    /**
     * 每页显示条数
     */
    private Integer pageSize;

    public Integer getPageNum() {
        if (pageNum == null || pageNum < 1) {
            return 1;
        }
        return pageNum;
    }

    public Integer getPageSize() {
        if (pageSize == null || pageSize < 1) {
            return 10;
        }
        if (pageSize > 200) {
            return 200;
        }
        return pageSize;
    }

}
