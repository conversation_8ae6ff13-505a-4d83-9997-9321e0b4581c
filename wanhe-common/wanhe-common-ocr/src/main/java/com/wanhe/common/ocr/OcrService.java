package com.wanhe.common.ocr;

import cn.hutool.core.collection.CollUtil;
import com.aliyun.ocr_api20210707.Client;
import com.aliyun.ocr_api20210707.models.RecognizeAllTextRequest;
import com.aliyun.ocr_api20210707.models.RecognizeAllTextResponse;
import com.aliyun.ocr_api20210707.models.RecognizeAllTextResponseBody;
import com.aliyun.teaopenapi.models.Config;
import com.ruoyi.common.core.exception.ServiceException;
import com.wanhe.common.ocr.config.OcrConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.AffineTransform;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @date 2024/12/24
 */
@Slf4j
public class OcrService {

    private final Client client;

    public OcrService(OcrConfig ocrConfig) throws Exception {
        Config config = new Config();
        config.setAccessKeyId(ocrConfig.getAccessKeyId());
        config.setAccessKeySecret(ocrConfig.getAccessKeySecret());
        config.setEndpoint(ocrConfig.getEndpoint());
        client = new Client(config);
    }

    public List<String> barCode(MultipartFile file) throws IOException {
        InputStream stream = file.getInputStream();
        String[] split = file.getOriginalFilename().split("\\.");
        String formatName = split[split.length - 1];
        byte[] imageBytes;
        try {
            // 将输入流完整读入内存，因为它需要被多次使用
            imageBytes = stream.readAllBytes();
        } catch (IOException e) {
            throw new ServiceException("图片处理失败");
        }

        List<RecognizeAllTextResponseBody.RecognizeAllTextResponseBodyDataSubImages> subImages = recognizeAndFilter(new ByteArrayInputStream(imageBytes));
        if (CollUtil.isEmpty(subImages)) {
            return Collections.emptyList();
        }

        List<String> list = subImages.stream()
                .flatMap(item -> item.getBlockInfo().getBlockDetails().stream()
                        .map(RecognizeAllTextResponseBody.RecognizeAllTextResponseBodyDataSubImagesBlockInfoBlockDetails::getBlockContent)
                        .filter(sn -> sn != null && sn.length() > 15 && sn.matches("^[a-zA-Z0-9]+$")))
                .distinct()
                .sorted()
                .toList();

        if (list.isEmpty()) {
            Integer degrees = getImageDegrees(subImages);
            byte[] rotatedImageBytes = rotateImage(imageBytes, degrees, formatName);
            ByteArrayInputStream inputStream = new ByteArrayInputStream(rotatedImageBytes);

            // 将旋转后的图片保存到本地以便查看
//            ByteArrayInputStream inputStream1 = new ByteArrayInputStream(rotatedImageBytes);
//            File outputFile = new File("corrected_image.jpg");
//            java.nio.file.Files.copy(inputStream1, outputFile.toPath(), java.nio.file.StandardCopyOption.REPLACE_EXISTING);

            list = recognizeAndFilter(inputStream).stream()
                    .flatMap(item -> item.getBlockInfo().getBlockDetails().stream()
                            .map(RecognizeAllTextResponseBody.RecognizeAllTextResponseBodyDataSubImagesBlockInfoBlockDetails::getBlockContent)
                            .filter(sn -> sn != null && sn.length() > 15 && sn.matches("^[a-zA-Z0-9]+$")))
                    .distinct()
                    .sorted()
                    .toList();
        }
        return list;
    }

    /**
     * 获取图像旋转度数
     *
     * @param subImages 识别所有文本响应体数据子图像
     * @return {@link Integer }
     */
    private Integer getImageDegrees(List<RecognizeAllTextResponseBody.RecognizeAllTextResponseBodyDataSubImages> subImages) {
        List<RecognizeAllTextResponseBody.RecognizeAllTextResponseBodyDataSubImagesBlockInfoBlockDetails> blockDetails = subImages.get(0).getBlockInfo().getBlockDetails();
        if (CollUtil.isEmpty(blockDetails)) {
            return -90;
        }

        AtomicReference<Integer> absX = new AtomicReference<>(0);
        AtomicReference<Integer> absY = new AtomicReference<>(0);
        blockDetails.forEach(item -> {
            RecognizeAllTextResponseBody.RecognizeAllTextResponseBodyDataSubImagesBlockInfoBlockDetailsBlockPoints startPoint = item.getBlockPoints().get(0);
            RecognizeAllTextResponseBody.RecognizeAllTextResponseBodyDataSubImagesBlockInfoBlockDetailsBlockPoints endPoint = item.getBlockPoints().get(2);

            absX.set(Math.max(Math.abs(startPoint.getX() - endPoint.getX()), absX.get()));
            absY.set(Math.max(Math.abs(startPoint.getY() - endPoint.getY()), absY.get()));
        });

        if (absX.get() > absY.get()) {
            return 180;
        } else {
            return -90;
        }
    }

    /**
     * 封装了单次调用OCR和解析结果的逻辑
     * @param inputStream 包含图片数据的输入流
     * @return 解析和过滤后的字符串列表
     */
    private List<RecognizeAllTextResponseBody.RecognizeAllTextResponseBodyDataSubImages> recognizeAndFilter(InputStream inputStream) {
        RecognizeAllTextRequest request = new RecognizeAllTextRequest();
        request.setBody(inputStream);
        request.setType("Advanced");
        request.setOutputCoordinate("points");

        RecognizeAllTextResponse response;
        try {
            response = client.recognizeAllText(request);
        } catch (Exception e) {
            log.error("图片识别失败：{}", e);
            throw new ServiceException("图片识别失败");
        }
        if (response.getStatusCode() == 200) {
            return response.getBody().getData().getSubImages();
        }
        return Collections.emptyList();
    }

    /**
     * 旋转图片字节数组
     *
     * @param imageBytes 原始图片字节
     * @param degrees    旋转角度，负数为向左（逆时针），正数为向右（顺时针）
     * @param formatName 格式名称
     * @return 旋转后的图片字节
     * @throws IOException IOException
     */
    private byte[] rotateImage(byte[] imageBytes, double degrees, String formatName) throws IOException {
        ByteArrayInputStream bis = new ByteArrayInputStream(imageBytes);
        BufferedImage originalImage = ImageIO.read(bis);
        if (originalImage == null) {
            throw new IOException("无法将字节解码为图片，可能格式不支持或数据已损坏。");
        }

        final double rads = Math.toRadians(degrees);
        final double sin = Math.abs(Math.sin(rads));
        final double cos = Math.abs(Math.cos(rads));
        final int w = originalImage.getWidth();
        final int h = originalImage.getHeight();
        final int newW = (int) Math.floor(w * cos + h * sin);
        final int newH = (int) Math.floor(h * cos + w * sin);

        BufferedImage rotatedImage = new BufferedImage(newW, newH, originalImage.getType());
        Graphics2D g2d = rotatedImage.createGraphics();
        AffineTransform at = new AffineTransform();
        at.translate((newW - w) / 2.0, (newH - h) / 2.0);
        at.rotate(rads, w / 2.0, h / 2.0);
        g2d.setTransform(at);
        g2d.drawImage(originalImage, 0, 0, null);
        g2d.dispose();

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(rotatedImage, formatName, baos);

        return baos.toByteArray();
    }

}
