package com.wanhe.common.ocr.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 * <AUTHOR>
 * @date 2024/12/24
 */
@Data
@RefreshScope
@ConfigurationProperties(prefix = "ali.ocr")
public class OcrConfig {
    //应用key
    private String accessKeyId;
    //应用密钥
    private String accessKeySecret;
    //地域
    private String endpoint;
}
