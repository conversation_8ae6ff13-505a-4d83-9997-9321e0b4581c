<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.ruoyi</groupId>
        <artifactId>wanhe_java_cloud</artifactId>
        <version>3.6.4</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <modules>
        <module>wanhe-common-log</module>
        <module>wanhe-common-core</module>
        <module>wanhe-common-redis</module>
        <module>wanhe-common-swagger</module>
        <module>wanhe-common-security</module>
        <module>wanhe-common-i18n</module>
        <module>wanhe-common-mail</module>
        <module>wanhe-common-ocr</module>
        <module>wanhe-common-sms</module>
    </modules>

    <artifactId>wanhe-common</artifactId>
    <packaging>pom</packaging>

    <description>
        wanhe-common通用模块
    </description>

</project>
