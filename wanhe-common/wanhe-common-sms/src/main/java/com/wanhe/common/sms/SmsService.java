package com.wanhe.common.sms;

import cn.hutool.core.text.StrPool;
import com.alibaba.fastjson2.JSON;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.teaopenapi.models.Config;
import com.wanhe.common.sms.config.SmsConfig;
import com.wanhe.common.sms.dto.FaultNotifiedDTO;
import com.wanhe.common.sms.dto.SendDTO;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/12/24
 */
@Slf4j
public class SmsService {

    private final Client client;
    private final SmsConfig smsConfig;

    public SmsService(SmsConfig smsConfig) throws Exception {
        Config config = new Config();
        config.setAccessKeyId(smsConfig.getAccessKeyId());
        config.setAccessKeySecret(smsConfig.getAccessKeySecret());
        config.setEndpoint(smsConfig.getEndpoint());
        this.smsConfig = smsConfig;
        this.client = new Client(config);
    }

    private void send(SendDTO dto) {
        SendSmsRequest request = new SendSmsRequest();
        if (dto.getPhone() != null) {
            request.setPhoneNumbers(dto.getPhone());
        } else if (dto.getPhones() != null) {
            request.setPhoneNumbers(String.join(StrPool.COMMA, dto.getPhones()));
        }
        request.setSignName(smsConfig.getSignName());
        request.setTemplateCode(dto.getTemplateCode());
        request.setTemplateParam(JSON.toJSONString(dto));
        try {
            SendSmsResponse response = client.sendSms(request);
            if (!response.getBody().getCode().equals("OK")) {
                log.error("短信发送失败:{}", JSON.toJSONString(response));
            }
        } catch (Exception e) {
            log.error("短信发送失败", e);
        }
    }

    /**
     * 尊敬的${userName}:您好!您所使用的电站${stationName}，
     * 出现如下故障:
     * 电站ID:${stationId}
     * 设备SN:${deviceSN}
     * 故障名称:S{faultName}
     * 若您有任何疑问，可随时拨打我们的客服热线${phoneNumber}。
     * 感谢您一直以来对我们的支持与信任，由此给您带来的不便，我们深表歉意!
     */
    public void sendFaultNotified(FaultNotifiedDTO dto) {
        dto.setTemplateCode("SMS_476840325");
        send(dto);
    }

}
