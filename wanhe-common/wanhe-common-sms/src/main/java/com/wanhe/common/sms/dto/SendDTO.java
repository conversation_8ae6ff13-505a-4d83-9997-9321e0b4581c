package com.wanhe.common.sms.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/25
 */
@Data
public class SendDTO {

    // 手机号
    @JSONField(serialize = false)
    private String phone;

    // 手机号
    @JSONField(serialize = false)
    private List<String> phones;

    @JSONField(serialize = false)
    private String templateCode;

}
