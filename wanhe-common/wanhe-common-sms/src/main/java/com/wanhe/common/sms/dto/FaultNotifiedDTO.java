package com.wanhe.common.sms.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * 尊敬的${userName}：
 * 您好！您所使用的电站${stationName} ,出现故障，相关信息如下：
 * 电站ID：${stationId}
 * 设备SN：${deviceSN}
 * 故障名称：${faultDescription}
 * 处理建议：${processingMethod}
 * 若您有任何疑问，可随时拨打我们的客服热线${phoneNumber}。
 * 感谢您一直以来对我们的支持与信任，由此给您带来的不便，我们深表歉意！
 * <AUTHOR>
 * @date 2024/12/25
 */
@Setter
@Getter
public class FaultNotifiedDTO extends SendDTO {

    //用户姓名
    private String userName;
    //电站名称
    private String stationName;
    //电站ID
    private Integer stationId;
    //设备SN
    private String deviceSN;
    //故障名称
    private String faultName;
    //客服热线
    private String phoneNumber;

}
