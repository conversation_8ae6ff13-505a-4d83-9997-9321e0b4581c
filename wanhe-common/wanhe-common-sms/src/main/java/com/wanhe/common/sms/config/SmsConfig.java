package com.wanhe.common.sms.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 * <AUTHOR>
 * @date 2024/12/24
 */
@Data
@RefreshScope
@ConfigurationProperties(prefix = "ali.sms")
public class SmsConfig {

    //应用key
    private String accessKeyId;
    //应用密钥
    private String accessKeySecret;
    //地域
    private String endpoint;
    //签名
    private String signName;

}
