package com.ruoyi.common.redis.service;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.utils.CommonUtil;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * spring redis 工具类
 *
 **/
@SuppressWarnings(value = {"unchecked", "rawtypes"})
@Component
public class RedisCache {

    @Resource
    public RedisTemplate redisTemplate;

    private String pre = "pre";

    private String getPre() {
        if ("pre".equals(pre)) {
            pre = CommonUtil.getEnvStr("ruoyi.name");
        }
        if (StrUtil.isEmpty(pre)) {
            pre = "pre";
        }
        return pre;
    }

    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key   缓存的键值
     * @param value 缓存的值
     */
    public <T> void setCacheObject(final String key, final T value) {
        redisTemplate.opsForValue().set(getPre() + key, value);
    }

    /**
     * 获得缓存的基本对象。
     *
     * @param key 缓存键值
     * @return 缓存键值对应的数据
     */
    public <T> T getCacheObject(final String key) {
        ValueOperations<String, T> operation = redisTemplate.opsForValue();
        return operation.get(getPre() + key);
    }

    /**
     * 删除单个对象
     *
     */
    public boolean deleteObject(final String key) {
        return redisTemplate.delete(getPre() + key);
    }

    /**
     * 删除集合对象
     *
     * @param collection 多个对象
     */
    public boolean deleteObject(final Collection collection) {
        Collection<String> newCollec = new ArrayList<>();
        for (String object : (Iterable<String>) collection) {
            newCollec.add(getPre() + object);
        }
        return redisTemplate.delete(newCollec) > 0;
    }

    /**
     * 获得缓存的list对象
     *
     * @param key 缓存的键值
     * @return 缓存键值对应的数据
     */
    public <T> List<T> getCacheList(final String key) {
        return redisTemplate.opsForList().range(getPre() + key, 0, -1);
    }

    /**
     * 获得缓存的基本对象列表
     *
     * @param pattern 字符串前缀
     * @return 对象列表
     */
    public Collection<String> keys(final String pattern) {
        return redisTemplate.keys(getPre() + pattern);
    }
}
