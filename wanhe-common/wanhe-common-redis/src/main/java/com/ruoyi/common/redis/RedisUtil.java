package com.ruoyi.common.redis;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.ruoyi.common.core.utils.SpringUtils;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.SessionCallback;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.time.Duration;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @date 2024/11/21
 */
public class RedisUtil {

    private static final StringRedisTemplate REDIS = SpringUtils.getBean(StringRedisTemplate.class);

    public static StringRedisTemplate redis() {
        return REDIS;
    }

    public static List<Object> transaction(Consumer<RedisOperations<String, String>> consumer) {
        return REDIS.execute(new SessionCallback<>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<Object> execute(RedisOperations redis) throws DataAccessException {
                try {
                    redis.multi();
                    consumer.accept(redis);
                    return redis.exec();
                } catch (RuntimeException e) {
                    redis.discard();
                    throw e;
                }
            }
        });
    }

    public static String get(String key) {
        return REDIS.opsForValue().get(key);
    }

    @SuppressWarnings("unchecked")
    public static <T> T getAndSet(String key, T obj) {
        String val = REDIS.opsForValue().getAndSet(key, JSON.toJSONString(obj, JSONWriter.Feature.WriteBigDecimalAsPlain));
        if (val == null) {
            return null;
        }
        return (T) JSON.parseObject(val, obj.getClass());
    }

    public static <T> List<T> getList(String key, Class<T> tClass) {
        return JSON.parseArray(get(key), tClass);
    }

    public static JSONObject getJSONObject(String key) {
        return JSON.parseObject(get(key));
    }

    public static JSONArray getJSONArray(String key) {
        return JSON.parseArray(get(key));
    }

    public static <T> T get(String key, Class<T> tClass) {
        return JSON.parseObject(get(key), tClass);
    }

    public static Map<String, String> entries(String key) {
        return REDIS.<String, String>opsForHash().entries(key);
    }

    public static void putAll(String key, Map<String, String> map) {
        REDIS.<String, String>opsForHash().putAll(key, map);
    }

    public static List<String> multiGet(Collection<String> keys) {
        return REDIS.opsForValue().multiGet(keys);
    }

    public static void set(Map<String, String> map) {
        REDIS.opsForValue().multiSet(map);
    }

    public static void set(String key, String value) {
        REDIS.opsForValue().set(key, value);
    }

    public static Boolean setIfAbsent(String key, String value, Duration timeout) {
        return REDIS.opsForValue().setIfAbsent(key, value, timeout);
    }

    public static void set(String key, Object value, Duration duration) {
        if (value == null) {
            return;
        }
        if (value instanceof String str) {
            REDIS.opsForValue().set(key, str, duration);
        } else if (value instanceof Number number) {
            REDIS.opsForValue().set(key, number.toString(), duration);
        } else {
            REDIS.opsForValue().set(key, JSON.toJSONString(value, JSONWriter.Feature.WriteBigDecimalAsPlain), duration);
        }
    }

    public static void delete(String key) {
        if (key.contains(Constants.ASTERISK)) {
            Set<String> keys = keys(key);
            if (CollUtil.isNotEmpty(keys)) {
                REDIS.delete(keys);
            }
        } else {
            REDIS.delete(key);
        }
    }

    public static void delete(Collection<String> keys) {
        REDIS.delete(keys);
    }

    public static Set<String> keys(String key) {
        return REDIS.keys(key);
    }

    public static Long getExpire(String key) {
        return REDIS.getExpire(key);
    }

    public static void expire(String key, long timeout, TimeUnit unit) {
        REDIS.expire(key, timeout, unit);
    }

    public static Long incr(String key) {
        return incr(key, 1L);
    }

    public static Long incr(String key, long delta) {
        return REDIS.opsForValue().increment(key, delta);
    }
}
