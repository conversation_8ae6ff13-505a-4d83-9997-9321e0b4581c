/*
 Navicat Premium Data Transfer

 Source Server         : wanhe_config
 Source Server Type    : MySQL
 Source Server Version : 80036
 Source Host           : **********:3306
 Source Schema         : wanhe_config

 Target Server Type    : MySQL
 Target Server Version : 80036
 File Encoding         : 65001

 Date: 27/03/2025 16:30:10
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for config_info
-- ----------------------------
DROP TABLE IF EXISTS `config_info`;
CREATE TABLE `config_info`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL,
  `content` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'content',
  `md5` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'md5',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `src_user` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL COMMENT 'source user',
  `src_ip` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'source ip',
  `app_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL,
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT '' COMMENT '租户字段',
  `c_desc` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL,
  `c_use` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL,
  `effect` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL,
  `type` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL,
  `c_schema` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL,
  `encrypted_data_key` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL COMMENT '秘钥',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_configinfo_datagrouptenant`(`data_id`, `group_id`, `tenant_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 132 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_bin COMMENT = 'config_info' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of config_info
-- ----------------------------
INSERT INTO `config_info` VALUES (72, 'application-local.yml', 'DEFAULT_GROUP', 'spring:\n  autoconfigure:\n    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure\n# feign 配置\nfeign:\n  sentinel:\n    enabled: true\n  okhttp:\n    enabled: true\n  httpclient:\n    enabled: false\n  client:\n    config:\n      default:\n        connectTimeout: 600000\n        readTimeout: 600000\n  compression:\n    request:\n      enabled: true\n      min-request-size: 8192\n    response:\n      enabled: true\n\n# 暴露监控端点\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: \'*\'\n\nmybatis-plus:\n  # 不支持多包, 如有需要可在注解配置 或 提升扫包等级\n  # 例如 com.**.**.mapper\n  mapperPackage: com.**.**.mapper\n  # 对应的 XML 文件位置\n  mapperLocations: classpath*:mapper/**/*Mapper.xml\n  # 实体扫描，多个package用逗号或者分号分隔\n  typeAliasesPackage: com.ruoyi.**.**,com.wanhe.**.**\n  # 启动时是否检查 MyBatis XML 文件的存在，默认不检查\n  checkConfigLocation: false\n  configuration:\n    map-underscore-to-camel-case: true\n    auto-mapping-behavior: full\n    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl\n    # 自动驼峰命名规则（camel case）映射\n    mapUnderscoreToCamelCase: true\n    # MyBatis 自动映射策略\n    # NONE：不启用 PARTIAL：只对非嵌套 resultMap 自动映射 FULL：对所有 resultMap 自动映射\n    autoMappingBehavior: PARTIAL\n    # MyBatis 自动映射时未知列或未知属性处理策\n    # NONE：不做处理 WARNING：打印相关警告 FAILING：抛出异常和详细信息\n    autoMappingUnknownColumnBehavior: NONE\n    # 更详细的日志输出 会有性能损耗 org.apache.ibatis.logging.stdout.StdOutImpl\n    # 关闭日志记录 (可单纯使用 p6spy 分析) org.apache.ibatis.logging.nologging.NoLoggingImpl\n    # 默认日志输出 org.apache.ibatis.logging.slf4j.Slf4jImpl\n    # logImpl: org.apache.ibatis.logging.nologging.NoLoggingImpl\n  globalConfig:\n    # 是否打印 Logo banner\n    banner: true\n    enableSqlRunner: true\n    dbConfig:\n      # 主键类型\n      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID\n      idType: ASSIGN_ID\n      # 逻辑已删除值\n      logicDeleteValue: 1\n      # 逻辑未删除值\n      logicNotDeleteValue: 0\n      # 字段验证策略之 insert,在 insert 的时候的字段验证策略\n      # IGNORED 忽略 NOT_NULL 非NULL NOT_EMPTY 非空 DEFAULT 默认 NEVER 不加入 SQL\n      insertStrategy: NOT_NULL\n      # 字段验证策略之 update,在 update 的时候的字段验证策略\n      updateStrategy: NOT_NULL\n      # 字段验证策略之 select,在 select 的时候的字段验证策略既 wrapper 根据内部 entity 生成的 where 条件\n      where-strategy: NOT_NULL\nmybatis-plus-join:\n  banner: false\n  sub-table-logic: false\nlogging:\n  file:\n    name: ./logs/${spring.application.name}/info.log', 'a1d13c887ec6389349835b4913cfd6f4', '2024-11-14 14:51:25', '2025-03-10 15:26:06', NULL, '***************', '', 'f201b8d5-2edd-4645-ae26-ff14b13093da', '', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (73, 'ruoyi-auth-local.yml', 'DEFAULT_GROUP', 'spring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8', '7a323f4fbaa688d1ad4dd901f7aaddd3', '2024-11-14 14:51:25', '2025-02-19 15:43:19', NULL, '************', '', 'f201b8d5-2edd-4645-ae26-ff14b13093da', '', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (74, 'ruoyi-file-local.yml', 'DEFAULT_GROUP', '#系统文件上传模式\nsys:\n  file:\n    # 图片上传模式 local/fdfs/minio/aliOss \n    sysFileType: aliOss  \n    compression: 0.35\n\nspring:\n  servlet:\n    multipart:\n      #单个文件的最大大小\n      max-file-size: 10MB\n\nali:\n  oss:\n    #应用key\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    #应用密钥\n    accessKeySecret: ******************************\n    #访问域名\n    host: http://whyn.oss-cn-beijing.aliyuncs.com\n    #地域\n    endpoint: oss-cn-beijing.aliyuncs.com\n    #存储桶名称\n    bucketName: whyn\n    #默认路径\n    defaultFile: wh\n    #过期时间\n    expireTime: 3600000\n    #最大长度\n    maxLength: 1048576000\n  ocr:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: ocr-api.cn-hangzhou.aliyuncs.com\n\n# 本地文件上传    \nfile:\n    domain: \n    path:   /uploadPath\n    prefix: /uploadPath\n\n# FastDFS配置\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minio配置\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test', 'c3459b710353b44aac6471bb0f3063ba', '2024-11-14 14:51:25', '2024-12-24 18:07:27', NULL, '***************', '', 'f201b8d5-2edd-4645-ae26-ff14b13093da', '', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (75, 'ruoyi-gateway-local.yml', 'DEFAULT_GROUP', 'spring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: ruoyi-auth\n          uri: lb://ruoyi-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestFilter\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 代码生成\n        - id: ruoyi-gen\n          uri: lb://ruoyi-gen\n          predicates:\n            - Path=/code/**\n          filters:\n            - StripPrefix=1\n        # 定时任务\n        - id: ruoyi-job\n          uri: lb://ruoyi-job\n          predicates:\n            - Path=/schedule/**\n          filters:\n            - StripPrefix=1\n        # 系统模块\n        - id: ruoyi-system\n          uri: lb://ruoyi-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: ruoyi-file\n          uri: lb://ruoyi-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 业务服务\n        - id: wanhe-business\n          uri: lb://wanhe-business\n          predicates:\n            - Path=/business/**\n          filters:\n            - StripPrefix=1\n        # mqtt服务\n        - id: wanhe-mqtt\n          uri: lb://wanhe-mqtt\n          predicates:\n            - Path=/mqtt/**\n          filters:\n            - StripPrefix=1\n        # 数据处理服务\n        - id: wanhe-biz-data\n          uri: lb://wanhe-biz-data\n          predicates:\n            - Path=/data/**\n          filters:\n            - StripPrefix=1\n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/*\n      - /system/dict/data/type/time_zone\n      - /system/config/configKey/*\n      - /data/inv/data/device/status\n      - /business/fault/all\n      - /business/station/device/check/sn\n      - /*/v2/api-docs\n      - /*/v3/api-docs\n      - /csrf\n\n# springdoc配置\nspringdoc:\n  webjars:\n    # 访问前缀\n    prefix:\n', '4b7d65b4cb9bfaf0b7cf10727139e2fa', '2024-11-14 14:51:25', '2025-03-07 15:21:24', NULL, '**************', '', 'f201b8d5-2edd-4645-ae26-ff14b13093da', '', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (76, 'ruoyi-gen-local.yml', 'DEFAULT_GROUP', '# spring配置\nspring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ***********************************************************************************************************************************************************    username: wanhe_cloud\n    password: EM8YTsC5QBQfGa1Y\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.gen.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'代码生成接口文档\'\n    # 描述\n    description: \'代码生成接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\n# 代码生成\ngen:\n  # 作者\n  author: ruoyi\n  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool\n  packageName: com.ruoyi.system\n  # 自动去除表前缀，默认是false\n  autoRemovePre: false\n  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）\n  tablePrefix: sys_\n', 'da929d417d96dd9b0a1430e93fa65247', '2024-11-14 14:51:25', '2025-03-03 10:13:33', NULL, '**************', '', 'f201b8d5-2edd-4645-ae26-ff14b13093da', '', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (77, 'ruoyi-job-local.yml', 'DEFAULT_GROUP', '# spring配置\nspring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ***********************************************************************************************************************************************************    username: wanhe_cloud\n    password: EM8YTsC5QBQfGa1Y\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.job.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'定时任务接口文档\'\n    # 描述\n    description: \'定时任务接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '581f9cc7d52fe02706ac319f78b1399c', '2024-11-14 14:51:25', '2025-03-03 10:24:22', NULL, '**************', '', 'f201b8d5-2edd-4645-ae26-ff14b13093da', '', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (78, 'ruoyi-monitor-local.yml', 'DEFAULT_GROUP', '# spring\nspring:\n  security:\n    user:\n      name: ruoyi\n      password: 123456\n  boot:\n    admin:\n      ui:\n        title: 若依服务状态监控\n', '6f122fd2bfb8d45f858e7d6529a9cd44', '2024-11-14 14:51:25', '2024-11-14 14:51:25', '', '************', '', 'f201b8d5-2edd-4645-ae26-ff14b13093da', NULL, NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (79, 'ruoyi-system-local.yml', 'DEFAULT_GROUP', '# spring配置\nspring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: ***********************************************************************************************************************************************************            username: wanhe_cloud\n            password: EM8YTsC5QBQfGa1Y\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\n# mybatis:\n#     # 搜索指定包别名\n#     typeAliasesPackage: com.ruoyi.system\n#     # 配置mapper的扫描，找到所有的mapper.xml映射文件\n#     mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', 'e15d6f8f416f694780ed5b6151549105', '2024-11-14 14:51:25', '2025-03-03 10:14:50', NULL, '************', '', 'f201b8d5-2edd-4645-ae26-ff14b13093da', '', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (80, 'sentinel-ruoyi-gateway', 'DEFAULT_GROUP', '[\r\n    {\r\n        \"resource\": \"ruoyi-auth\",\r\n        \"count\": 500,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-system\",\r\n        \"count\": 1000,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-gen\",\r\n        \"count\": 200,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-job\",\r\n        \"count\": 300,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    }\r\n]', '9f3a3069261598f74220bc47958ec252', '2024-11-14 14:51:25', '2024-11-14 14:51:25', '', '************', '', 'f201b8d5-2edd-4645-ae26-ff14b13093da', NULL, NULL, NULL, 'text', NULL, '');
INSERT INTO `config_info` VALUES (81, 'wanhe-biz-data-local.yml', 'DEFAULT_GROUP', '# spring配置\nspring: \n  influx:\n    url: http://**************:8086\n    user: influxdb\n    password: 9RNmQc7F4_7IeZG6OzbZK7ijgDJ1BvLOP4n03mXd6KWZ_cMkx-Wur9xYr6uUTTDdEJ6lr5DGrPeNw4GZytfXtA==\n    database: wan_he_device_data\n    timeZone: tz(\'Asia/Shanghai\')\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: ***********************************************************************************************************************************************************            username: wanhe_cloud\n            password: EM8YTsC5QBQfGa1Y\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\nemq:\n  # 70d64bdd0ec3cd94:2kNaHFwHuPOJinKQ8xeT8DqjUPgkiiT9BRGQGhMGPW1L\n  basic: Basic NzBkNjRiZGQwZWMzY2Q5NDoya05hSEZ3SHVQT0ppbktROHhlVDhEcWpVUGdraWlUOUJSR1FHaE1HUFcxTA==\n  url: http://**************:18083\nfault:\n  language: zh\nali:\n  sms:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: dysmsapi.aliyuncs.com\n    signName: 万禾悦能\nkafka:\n  #分区数量1个\n  partition-number: 5\n  bootstrap:\n    servers: ************:9093\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n  sasl:\n    mechanism: PLAIN\n    username: alikafka_pre-cn-jmp3vby9t005\n    password: bHqQ5pptpKYLgOHBzMCHGyA3ArG2kSHg\n  ssl:\n    truststore:\n      location: \"D:\\\\home\\\\wanhe\\\\ca\\\\only.4096.client.truststore.jks\"\n  security:\n    auth:\n      login:\n        config: \"D:\\\\home\\\\wanhe\\\\ca\\\\kafka_client_jaas.conf\"\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '9f20264f98461838d828bb694eb5691c', '2024-11-14 14:51:25', '2025-03-08 09:28:00', NULL, '**************', '', 'f201b8d5-2edd-4645-ae26-ff14b13093da', '', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (82, 'wanhe-business-local.yml', 'DEFAULT_GROUP', '# spring配置\nspring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: ***********************************************************************************************************************************************************            username: wanhe_cloud\n            password: EM8YTsC5QBQfGa1Y\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n  task:\n    execution:\n      pool:\n        core-size: 8\n        max-size: 200\n        keep-alive: 1200s\n      thread-name-prefix: task-pool-\nemq:\n  # 70d64bdd0ec3cd94:2kNaHFwHuPOJinKQ8xeT8DqjUPgkiiT9BRGQGhMGPW1L\n  basic: Basic NzBkNjRiZGQwZWMzY2Q5NDoya05hSEZ3SHVQT0ppbktROHhlVDhEcWpVUGdraWlUOUJSR1FHaE1HUFcxTA==\n  url: http://**************:18083\n\nfault:\n  language: zh\n# mybatis配置\n# mybatis:\n#     # 搜索指定包别名\n#     typeAliasesPackage: com.ruoyi.system\n#     # 配置mapper的扫描，找到所有的mapper.xml映射文件\n#     mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip', '1b2c2d8394e74da1d5b9aa77e0b04c97', '2024-11-14 14:51:25', '2025-03-03 18:42:01', NULL, '**************', '', 'f201b8d5-2edd-4645-ae26-ff14b13093da', '', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (83, 'wanhe-mqtt-local.yml', 'DEFAULT_GROUP', '# spring配置\nspring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: ***********************************************************************************************************************************************************            username: wanhe_cloud\n            password: EM8YTsC5QBQfGa1Y\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mqtt:\n    username: wanhe                       # 账号（仅用于后端自认证）\n    password: \"wanhe\"                       # 密码（仅用于后端自认证）\n    host-url: tcp://**************:1883         # 连接 Emqx 消息服务器地址\n    client-id: wanhe-iot-${random.int}                # 客户端Id，不能相同，采用随机数 ${random.value}\n    default-topic: test                     # 默认主题\n    timeout: 30                             # 超时时间\n    keepalive: 30                           # 保持连接\n    clearSession: false\n    maxInflight: 32  \n\nkafka:\n  #分区数量3个\n  partition-number: 5\n  bootstrap:\n    servers: ************:9093\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n  sasl:\n    mechanism: PLAIN\n    username: alikafka_pre-cn-jmp3vby9t005\n    password: bHqQ5pptpKYLgOHBzMCHGyA3ArG2kSHg\n  ssl:\n    truststore:\n      location: \"D:\\\\home\\\\wanhe\\\\ca\\\\only.4096.client.truststore.jks\"\n  security:\n    auth:\n      login:\n        config: \"D:\\\\home\\\\wanhe\\\\ca\\\\kafka_client_jaas.conf\"\n\n\n\n\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '536330803477e7c83154c804ec20b8f4', '2024-11-14 14:51:25', '2025-03-08 09:29:41', NULL, '**************', '', 'f201b8d5-2edd-4645-ae26-ff14b13093da', '', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (84, 'application-local.yml', 'DEFAULT_GROUP', 'spring:\n  autoconfigure:\n    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure\n# feign 配置\nfeign:\n  sentinel:\n    enabled: true\n  okhttp:\n    enabled: true\n  httpclient:\n    enabled: false\n  client:\n    config:\n      default:\n        connectTimeout: 600000\n        readTimeout: 600000\n  compression:\n    request:\n      enabled: true\n      min-request-size: 8192\n    response:\n      enabled: true\n\n# 暴露监控端点\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: \'*\'\n\nmybatis-plus:\n  # 不支持多包, 如有需要可在注解配置 或 提升扫包等级\n  # 例如 com.**.**.mapper\n  mapperPackage: com.**.**.mapper\n  # 对应的 XML 文件位置\n  mapperLocations: classpath*:mapper/**/*Mapper.xml\n  # 实体扫描，多个package用逗号或者分号分隔\n  typeAliasesPackage: com.ruoyi.**.**,com.wanhe.**.**\n  # 启动时是否检查 MyBatis XML 文件的存在，默认不检查\n  checkConfigLocation: false\n  configuration:\n    map-underscore-to-camel-case: true\n    auto-mapping-behavior: full\n    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl\n    # 自动驼峰命名规则（camel case）映射\n    mapUnderscoreToCamelCase: true\n    # MyBatis 自动映射策略\n    # NONE：不启用 PARTIAL：只对非嵌套 resultMap 自动映射 FULL：对所有 resultMap 自动映射\n    autoMappingBehavior: PARTIAL\n    # MyBatis 自动映射时未知列或未知属性处理策\n    # NONE：不做处理 WARNING：打印相关警告 FAILING：抛出异常和详细信息\n    autoMappingUnknownColumnBehavior: NONE\n    # 更详细的日志输出 会有性能损耗 org.apache.ibatis.logging.stdout.StdOutImpl\n    # 关闭日志记录 (可单纯使用 p6spy 分析) org.apache.ibatis.logging.nologging.NoLoggingImpl\n    # 默认日志输出 org.apache.ibatis.logging.slf4j.Slf4jImpl\n    # logImpl: org.apache.ibatis.logging.nologging.NoLoggingImpl\n  globalConfig:\n    # 是否打印 Logo banner\n    banner: true\n    enableSqlRunner: true\n    dbConfig:\n      # 主键类型\n      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID\n      idType: ASSIGN_ID\n      # 逻辑已删除值\n      logicDeleteValue: 1\n      # 逻辑未删除值\n      logicNotDeleteValue: 0\n      # 字段验证策略之 insert,在 insert 的时候的字段验证策略\n      # IGNORED 忽略 NOT_NULL 非NULL NOT_EMPTY 非空 DEFAULT 默认 NEVER 不加入 SQL\n      insertStrategy: NOT_NULL\n      # 字段验证策略之 update,在 update 的时候的字段验证策略\n      updateStrategy: NOT_NULL\n      # 字段验证策略之 select,在 select 的时候的字段验证策略既 wrapper 根据内部 entity 生成的 where 条件\n      where-strategy: NOT_NULL\nmybatis-plus-join:\n  banner: false\n  sub-table-logic: false\nlogging:\n  file:\n    name: ./logs/${spring.application.name}/info.log', 'a1d13c887ec6389349835b4913cfd6f4', '2025-03-24 16:32:56', '2025-03-24 16:32:56', '', '************', '', '0d8f014d-b030-4467-be18-f86b1c11a27b', NULL, NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (85, 'ruoyi-auth-local.yml', 'DEFAULT_GROUP', 'spring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8', '769ae644a7bc2fc8291edc31ded394c8', '2025-03-24 16:32:56', '2025-03-24 16:32:56', '', '************', '', '0d8f014d-b030-4467-be18-f86b1c11a27b', NULL, NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (86, 'ruoyi-file-local.yml', 'DEFAULT_GROUP', '#系统文件上传模式\nsys:\n  file:\n    # 图片上传模式 local/fdfs/minio/aliOss \n    sysFileType: aliOss  \n    compression: 0.35\n\nspring:\n  servlet:\n    multipart:\n      #单个文件的最大大小\n      max-file-size: 10MB\n\nali:\n  oss:\n    #应用key\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    #应用密钥\n    accessKeySecret: ******************************\n    #访问域名\n    host: http://whyn.oss-cn-beijing.aliyuncs.com\n    #地域\n    endpoint: oss-cn-beijing.aliyuncs.com\n    #存储桶名称\n    bucketName: whyn\n    #默认路径\n    defaultFile: wh\n    #过期时间\n    expireTime: 3600000\n    #最大长度\n    maxLength: 1048576000\n  ocr:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: ocr-api.cn-hangzhou.aliyuncs.com\n\n\n# 本地文件上传    \nfile:\n    domain: \n    path:   /uploadPath\n    prefix: /uploadPath\n\n# FastDFS配置\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minio配置\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test', '301ab63ccdf42fca66c0ee3e6ec2d08a', '2025-03-24 16:32:56', '2025-03-24 16:32:56', '', '************', '', '0d8f014d-b030-4467-be18-f86b1c11a27b', NULL, NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (87, 'ruoyi-gateway-local.yml', 'DEFAULT_GROUP', 'spring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: ruoyi-auth\n          uri: lb://ruoyi-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestFilter\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 代码生成\n        - id: ruoyi-gen\n          uri: lb://ruoyi-gen\n          predicates:\n            - Path=/code/**\n          filters:\n            - StripPrefix=1\n        # 定时任务\n        - id: ruoyi-job\n          uri: lb://ruoyi-job\n          predicates:\n            - Path=/schedule/**\n          filters:\n            - StripPrefix=1\n        # 系统模块\n        - id: ruoyi-system\n          uri: lb://ruoyi-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: ruoyi-file\n          uri: lb://ruoyi-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 业务服务\n        - id: wanhe-business\n          uri: lb://wanhe-business\n          predicates:\n            - Path=/business/**\n          filters:\n            - StripPrefix=1\n        # mqtt服务\n        - id: wanhe-mqtt\n          uri: lb://wanhe-mqtt\n          predicates:\n            - Path=/mqtt/**\n          filters:\n            - StripPrefix=1\n        # 数据处理服务\n        - id: wanhe-biz-data\n          uri: lb://wanhe-biz-data\n          predicates:\n            - Path=/data/**\n          filters:\n            - StripPrefix=1\n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/*\n      - /system/dict/data/type/time_zone\n      - /system/config/configKey/*\n      - /data/inv/data/device/status\n      - /business/fault/all\n      - /business/station/device/check/sn\n      - /*/v2/api-docs\n      - /*/v3/api-docs\n      - /csrf\n\n# springdoc配置\nspringdoc:\n  webjars:\n    # 访问前缀\n    prefix:\n', 'acdeafe385f475e3b0bc7069e8656ee8', '2025-03-24 16:32:56', '2025-03-24 16:32:56', '', '************', '', '0d8f014d-b030-4467-be18-f86b1c11a27b', NULL, NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (88, 'ruoyi-gen-local.yml', 'DEFAULT_GROUP', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n    username: wanhe\n    password: 55kPKFMaYjW5aaYb\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.gen.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'代码生成接口文档\'\n    # 描述\n    description: \'代码生成接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\n# 代码生成\ngen:\n  # 作者\n  author: ruoyi\n  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool\n  packageName: com.ruoyi.system\n  # 自动去除表前缀，默认是false\n  autoRemovePre: false\n  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）\n  tablePrefix: sys_\n', '4d3f4930cf461ae95701938f1f44ab07', '2025-03-24 16:32:56', '2025-03-24 16:32:56', '', '************', '', '0d8f014d-b030-4467-be18-f86b1c11a27b', NULL, NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (89, 'ruoyi-job-local.yml', 'DEFAULT_GROUP', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n    username: wanhe\n    password: 55kPKFMaYjW5aaYb\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.job.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'定时任务接口文档\'\n    # 描述\n    description: \'定时任务接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '312e536272fba0cd842861510ba9eb16', '2025-03-24 16:32:56', '2025-03-24 16:32:56', '', '************', '', '0d8f014d-b030-4467-be18-f86b1c11a27b', NULL, NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (90, 'ruoyi-monitor-local.yml', 'DEFAULT_GROUP', '# spring\nspring:\n  security:\n    user:\n      name: ruoyi\n      password: 123456\n  boot:\n    admin:\n      ui:\n        title: 若依服务状态监控\n', '6f122fd2bfb8d45f858e7d6529a9cd44', '2025-03-24 16:32:56', '2025-03-24 16:32:56', '', '************', '', '0d8f014d-b030-4467-be18-f86b1c11a27b', NULL, NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (91, 'ruoyi-system-local.yml', 'DEFAULT_GROUP', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\n# mybatis:\n#     # 搜索指定包别名\n#     typeAliasesPackage: com.ruoyi.system\n#     # 配置mapper的扫描，找到所有的mapper.xml映射文件\n#     mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', 'bf1d064c62947498ed54af7f70b6194a', '2025-03-24 16:32:56', '2025-03-24 16:32:56', '', '************', '', '0d8f014d-b030-4467-be18-f86b1c11a27b', NULL, NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (92, 'sentinel-ruoyi-gateway', 'DEFAULT_GROUP', '[\r\n    {\r\n        \"resource\": \"ruoyi-auth\",\r\n        \"count\": 500,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-system\",\r\n        \"count\": 1000,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-gen\",\r\n        \"count\": 200,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-job\",\r\n        \"count\": 300,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    }\r\n]', '9f3a3069261598f74220bc47958ec252', '2025-03-24 16:32:56', '2025-03-24 16:32:56', '', '************', '', '0d8f014d-b030-4467-be18-f86b1c11a27b', NULL, NULL, NULL, 'text', NULL, '');
INSERT INTO `config_info` VALUES (93, 'wanhe-biz-data-local.yml', 'DEFAULT_GROUP', '# spring配置\nspring: \n  influx:\n    url: http://***********:8086\n    user: influxdb\n    password: 6KE1sH8dFHq7CnW6o2I0BIiwcs3iPE-xEYsLNcnw3arcG26-5bb2rDr9RCw7tRw6i25KEBPjZ4YkRnA5ZiZLLA==\n    database: wan_he_device_data\n    # timeZone: tz(\'Asia/Shanghai\')\n    timeZone: tz(\'Europe/Berlin\')\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n  task:\n    execution:\n      pool:\n        core-size: 8\n        max-size: 200\n        keep-alive: 1200s\n      thread-name-prefix: task-pool-\nemq:\n  # 25c941e678c502a4:oGRUEeUlkWnPnQieNoqGUhXLfaewzQYiJwvA1QqSZWL\n  basic: Basic MjVjOTQxZTY3OGM1MDJhNDpvR1JVRWVVbGtXblBuUWllTm9xR1VoWExmYWV3elFZaUp3dkExUXFTWldM\n  url: http://*************:18083\nfault:\n  # language: zh\n  language: en\nali:\n  sms:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: dysmsapi.aliyuncs.com\n    signName: 万禾悦能\nkafka:\n  partition-number: 1\n  bootstrap:\n    servers: *************:9092\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', 'c0b209c71c40ff12a314079dfb7e3817', '2025-03-24 16:32:56', '2025-03-25 19:46:09', NULL, '************', '', '0d8f014d-b030-4467-be18-f86b1c11a27b', '', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (94, 'wanhe-business-local.yml', 'DEFAULT_GROUP', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\nemq:\n  # 25c941e678c502a4:oGRUEeUlkWnPnQieNoqGUhXLfaewzQYiJwvA1QqSZWL\n  basic: Basic MjVjOTQxZTY3OGM1MDJhNDpvR1JVRWVVbGtXblBuUWllTm9xR1VoWExmYWV3elFZaUp3dkExUXFTWldM\n  url: http://***********:18083\nfault:\n  language: en\n# mybatis配置\n# mybatis:\n#     # 搜索指定包别名\n#     typeAliasesPackage: com.ruoyi.system\n#     # 配置mapper的扫描，找到所有的mapper.xml映射文件\n#     mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip', '6c4a6878aaf481e40991dd0e296cf7c3', '2025-03-24 16:32:56', '2025-03-25 11:09:38', NULL, '**************', '', '0d8f014d-b030-4467-be18-f86b1c11a27b', '', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (95, 'wanhe-mqtt-local.yml', 'DEFAULT_GROUP', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mqtt:\n    username: admin                       # 账号（仅用于后端自认证）\n    password: public                       # 密码（仅用于后端自认证）\n    host-url: tcp://*************:1883         # 连接 Emqx 消息服务器地址\n    client-id: wanhe-iot-${random.int}                # 客户端Id，不能相同，采用随机数 ${random.value}\n    default-topic: test                     # 默认主题\n    timeout: 30                             # 超时时间\n    keepalive: 30                           # 保持连接\n    clearSession: false\n    maxInflight: 32\n\nkafka:\n  #分区数量1个\n  partition-number: 10\n  bootstrap:\n    servers: *************:9092\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', 'd5440661bcba249e8ebca3da96c458a8', '2025-03-24 16:32:56', '2025-03-25 11:40:02', NULL, '**************', '', '0d8f014d-b030-4467-be18-f86b1c11a27b', '', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (108, 'application-prod.yml', 'DEFAULT_GROUP', 'spring:\n  autoconfigure:\n    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure\n# feign 配置\nfeign:\n  sentinel:\n    enabled: true\n  okhttp:\n    enabled: true\n  httpclient:\n    enabled: false\n  client:\n    config:\n      default:\n        connectTimeout: 600000\n        readTimeout: 600000\n  compression:\n    request:\n      enabled: true\n      min-request-size: 8192\n    response:\n      enabled: true\n\n# 暴露监控端点\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: \'*\'\n\nmybatis-plus:\n  # 不支持多包, 如有需要可在注解配置 或 提升扫包等级\n  # 例如 com.**.**.mapper\n  mapperPackage: com.**.**.mapper\n  # 对应的 XML 文件位置\n  mapperLocations: classpath*:mapper/**/*Mapper.xml\n  # 实体扫描，多个package用逗号或者分号分隔\n  typeAliasesPackage: com.ruoyi.**.**,com.wanhe.**.**\n  # 启动时是否检查 MyBatis XML 文件的存在，默认不检查\n  checkConfigLocation: false\n  configuration:\n    map-underscore-to-camel-case: true\n    auto-mapping-behavior: full\n    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl\n    # 自动驼峰命名规则（camel case）映射\n    mapUnderscoreToCamelCase: true\n    # MyBatis 自动映射策略\n    # NONE：不启用 PARTIAL：只对非嵌套 resultMap 自动映射 FULL：对所有 resultMap 自动映射\n    autoMappingBehavior: PARTIAL\n    # MyBatis 自动映射时未知列或未知属性处理策\n    # NONE：不做处理 WARNING：打印相关警告 FAILING：抛出异常和详细信息\n    autoMappingUnknownColumnBehavior: NONE\n    # 更详细的日志输出 会有性能损耗 org.apache.ibatis.logging.stdout.StdOutImpl\n    # 关闭日志记录 (可单纯使用 p6spy 分析) org.apache.ibatis.logging.nologging.NoLoggingImpl\n    # 默认日志输出 org.apache.ibatis.logging.slf4j.Slf4jImpl\n    # logImpl: org.apache.ibatis.logging.nologging.NoLoggingImpl\n  globalConfig:\n    # 是否打印 Logo banner\n    banner: true\n    enableSqlRunner: true\n    dbConfig:\n      # 主键类型\n      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID\n      idType: ASSIGN_ID\n      # 逻辑已删除值\n      logicDeleteValue: 1\n      # 逻辑未删除值\n      logicNotDeleteValue: 0\n      # 字段验证策略之 insert,在 insert 的时候的字段验证策略\n      # IGNORED 忽略 NOT_NULL 非NULL NOT_EMPTY 非空 DEFAULT 默认 NEVER 不加入 SQL\n      insertStrategy: NOT_NULL\n      # 字段验证策略之 update,在 update 的时候的字段验证策略\n      updateStrategy: NOT_NULL\n      # 字段验证策略之 select,在 select 的时候的字段验证策略既 wrapper 根据内部 entity 生成的 where 条件\n      where-strategy: NOT_NULL\nmybatis-plus-join:\n  banner: false\n  sub-table-logic: false\nlogging:\n  file:\n    name: ./logs/${spring.application.name}/info.log', 'a1d13c887ec6389349835b4913cfd6f4', '2025-03-25 11:55:49', '2025-03-25 14:03:49', '', '***************', '', '', NULL, NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (109, 'ruoyi-auth-prod.yml', 'DEFAULT_GROUP', 'spring:\n  data:\n    redis:\n      host: ************\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8', '58771f8bd62a23faa39df80d7fb38758', '2025-03-25 11:55:49', '2025-03-25 14:03:49', '', '***************', '', '', NULL, NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (110, 'ruoyi-file-prod.yml', 'DEFAULT_GROUP', '#系统文件上传模式\nsys:\n  file:\n    # 图片上传模式 local/fdfs/minio/aliOss \n    sysFileType: aliOss  \n    compression: 0.35\n\nspring:\n  servlet:\n    multipart:\n      #单个文件的最大大小\n      max-file-size: 10MB\n\nali:\n  oss:\n    #应用key\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    #应用密钥\n    accessKeySecret: ******************************\n    #访问域名\n    host: http://whyn.oss-cn-beijing.aliyuncs.com\n    #地域\n    endpoint: oss-cn-beijing.aliyuncs.com\n    #存储桶名称\n    bucketName: whyn\n    #默认路径\n    defaultFile: wh\n    #过期时间\n    expireTime: 3600000\n    #最大长度\n    maxLength: 1048576000\n  ocr:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: ocr-api.cn-hangzhou.aliyuncs.com\n\n\n# 本地文件上传    \nfile:\n    domain: \n    path:   /uploadPath\n    prefix: /uploadPath\n\n# FastDFS配置\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minio配置\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test', '301ab63ccdf42fca66c0ee3e6ec2d08a', '2025-03-25 11:55:49', '2025-03-25 14:03:49', '', '***************', '', '', NULL, NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (111, 'ruoyi-gateway-prod.yml', 'DEFAULT_GROUP', 'spring:\n  data:\n    redis:\n      host: ************\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: ruoyi-auth\n          uri: lb://ruoyi-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestFilter\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 代码生成\n        - id: ruoyi-gen\n          uri: lb://ruoyi-gen\n          predicates:\n            - Path=/code/**\n          filters:\n            - StripPrefix=1\n        # 定时任务\n        - id: ruoyi-job\n          uri: lb://ruoyi-job\n          predicates:\n            - Path=/schedule/**\n          filters:\n            - StripPrefix=1\n        # 系统模块\n        - id: ruoyi-system\n          uri: lb://ruoyi-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: ruoyi-file\n          uri: lb://ruoyi-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 业务服务\n        - id: wanhe-business\n          uri: lb://wanhe-business\n          predicates:\n            - Path=/business/**\n          filters:\n            - StripPrefix=1\n        # mqtt服务\n        - id: wanhe-mqtt\n          uri: lb://wanhe-mqtt\n          predicates:\n            - Path=/mqtt/**\n          filters:\n            - StripPrefix=1\n        # 数据处理服务\n        - id: wanhe-biz-data\n          uri: lb://wanhe-biz-data\n          predicates:\n            - Path=/data/**\n          filters:\n            - StripPrefix=1\n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/*\n      - /system/dict/data/type/time_zone\n      - /system/config/configKey/*\n      - /data/inv/data/device/status\n      - /business/fault/all\n      - /business/station/device/check/sn\n      - /*/v2/api-docs\n      - /*/v3/api-docs\n      - /csrf\n\n# springdoc配置\nspringdoc:\n  webjars:\n    # 访问前缀\n    prefix:\n', '9d1319cef876fa71597055028cd25f27', '2025-03-25 11:55:49', '2025-03-25 14:03:49', '', '***************', '', '', NULL, NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (112, 'ruoyi-gen-prod.yml', 'DEFAULT_GROUP', '# spring配置\nspring:\n  data:\n    redis:\n      host: ************\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ***************************************************************************************************************************************************    username: wanhe\n    password: 55kPKFMaYjW5aaYb\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.gen.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'代码生成接口文档\'\n    # 描述\n    description: \'代码生成接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\n# 代码生成\ngen:\n  # 作者\n  author: ruoyi\n  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool\n  packageName: com.ruoyi.system\n  # 自动去除表前缀，默认是false\n  autoRemovePre: false\n  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）\n  tablePrefix: sys_\n', 'eb19e9793239c67179706cc3737ab7e6', '2025-03-25 11:55:49', '2025-03-25 14:03:49', '', '***************', '', '', NULL, NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (113, 'ruoyi-job-prod.yml', 'DEFAULT_GROUP', '# spring配置\nspring:\n  data:\n    redis:\n      host: ************\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ***************************************************************************************************************************************************    username: wanhe\n    password: 55kPKFMaYjW5aaYb\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.job.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'定时任务接口文档\'\n    # 描述\n    description: \'定时任务接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '26708a7b335fd28285ab34c3b0a0b657', '2025-03-25 11:55:49', '2025-03-25 14:03:49', '', '***************', '', '', NULL, NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (114, 'ruoyi-monitor-prod.yml', 'DEFAULT_GROUP', '# spring\nspring:\n  security:\n    user:\n      name: ruoyi\n      password: 123456\n  boot:\n    admin:\n      ui:\n        title: 若依服务状态监控\n', '6f122fd2bfb8d45f858e7d6529a9cd44', '2025-03-25 11:55:49', '2025-03-25 14:03:49', '', '***************', '', '', NULL, NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (115, 'ruoyi-system-prod.yml', 'DEFAULT_GROUP', '# spring配置\nspring:\n  data:\n    redis:\n      host: ************\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: ***************************************************************************************************************************************************            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\n# mybatis:\n#     # 搜索指定包别名\n#     typeAliasesPackage: com.ruoyi.system\n#     # 配置mapper的扫描，找到所有的mapper.xml映射文件\n#     mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '69f7e0054cb2906810078a6ba0ada07a', '2025-03-25 11:55:49', '2025-03-25 14:03:49', '', '***************', '', '', NULL, NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (116, 'sentinel-ruoyi-gateway', 'DEFAULT_GROUP', '[\r\n    {\r\n        \"resource\": \"ruoyi-auth\",\r\n        \"count\": 500,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-system\",\r\n        \"count\": 1000,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-gen\",\r\n        \"count\": 200,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-job\",\r\n        \"count\": 300,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    }\r\n]', '9f3a3069261598f74220bc47958ec252', '2025-03-25 11:55:49', '2025-03-25 14:03:49', '', '***************', '', '', NULL, NULL, NULL, 'text', NULL, '');
INSERT INTO `config_info` VALUES (117, 'wanhe-biz-data-prod.yml', 'DEFAULT_GROUP', '# spring配置\nspring: \n  influx:\n    url: http://*************:8086\n    user: influxdb\n    password: 6KE1sH8dFHq7CnW6o2I0BIiwcs3iPE-xEYsLNcnw3arcG26-5bb2rDr9RCw7tRw6i25KEBPjZ4YkRnA5ZiZLLA==\n    database: wan_he_device_data\n    # timeZone: tz(\'Asia/Shanghai\')\n    timeZone: tz(\'Europe/Berlin\')\n  data:\n    redis:\n      host: ************\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: ***************************************************************************************************************************************************            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n  task:\n    execution:\n      pool:\n        core-size: 8\n        max-size: 200\n        keep-alive: 1200s\n      thread-name-prefix: task-pool-\nemq:\n  # 25c941e678c502a4:oGRUEeUlkWnPnQieNoqGUhXLfaewzQYiJwvA1QqSZWL\n  basic: Basic MjVjOTQxZTY3OGM1MDJhNDpvR1JVRWVVbGtXblBuUWllTm9xR1VoWExmYWV3elFZaUp3dkExUXFTWldM\n  url: http://************:18083\nfault:\n  # language: zh\n  language: en\nali:\n  sms:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: dysmsapi.aliyuncs.com\n    signName: 万禾悦能\nkafka:\n  partition-number: 1\n  bootstrap:\n    servers: ************:9092\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '1af97495376f369d2979cc1a03e07318', '2025-03-25 11:55:49', '2025-03-25 19:50:57', NULL, '***************', '', '', '', '', '', 'yaml', '', '');
INSERT INTO `config_info` VALUES (118, 'wanhe-business-prod.yml', 'DEFAULT_GROUP', '# spring配置\nspring:\n  data:\n    redis:\n      host: ************\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: ***************************************************************************************************************************************************            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\nemq:\n  # 25c941e678c502a4:oGRUEeUlkWnPnQieNoqGUhXLfaewzQYiJwvA1QqSZWL\n  basic: Basic MjVjOTQxZTY3OGM1MDJhNDpvR1JVRWVVbGtXblBuUWllTm9xR1VoWExmYWV3elFZaUp3dkExUXFTWldM\n  url: http://************:18083\nfault:\n  language: en\n# mybatis配置\n# mybatis:\n#     # 搜索指定包别名\n#     typeAliasesPackage: com.ruoyi.system\n#     # 配置mapper的扫描，找到所有的mapper.xml映射文件\n#     mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip', 'c54f97f6aadbf4ea4a829f175f8b8aea', '2025-03-25 11:55:49', '2025-03-25 14:03:49', '', '***************', '', '', NULL, NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` VALUES (119, 'wanhe-mqtt-prod.yml', 'DEFAULT_GROUP', '# spring配置\nspring:\n  data:\n    redis:\n      host: ************\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: ***************************************************************************************************************************************************            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mqtt:\n    username: admin                       # 账号（仅用于后端自认证）\n    password: public                       # 密码（仅用于后端自认证）\n    host-url: tcp://************:1883         # 连接 Emqx 消息服务器地址\n    client-id: wanhe-iot-${random.int}                # 客户端Id，不能相同，采用随机数 ${random.value}\n    default-topic: test                     # 默认主题\n    timeout: 30                             # 超时时间\n    keepalive: 30                           # 保持连接\n    clearSession: false\n    maxInflight: 32\n\nkafka:\n  #分区数量1个\n  partition-number: 10\n  bootstrap:\n    servers: ************:9092\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '26bf32935a837ca09cb876ce87f31982', '2025-03-25 11:55:49', '2025-03-25 14:59:17', NULL, '***************', '', '', '', '', '', 'yaml', '', '');

-- ----------------------------
-- Table structure for config_info_aggr
-- ----------------------------
DROP TABLE IF EXISTS `config_info_aggr`;
CREATE TABLE `config_info_aggr`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'group_id',
  `datum_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'datum_id',
  `content` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '内容',
  `gmt_modified` datetime NOT NULL COMMENT '修改时间',
  `app_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL,
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT '' COMMENT '租户字段',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_configinfoaggr_datagrouptenantdatum`(`data_id`, `group_id`, `tenant_id`, `datum_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_bin COMMENT = '增加租户字段' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of config_info_aggr
-- ----------------------------

-- ----------------------------
-- Table structure for config_info_beta
-- ----------------------------
DROP TABLE IF EXISTS `config_info_beta`;
CREATE TABLE `config_info_beta`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'group_id',
  `app_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'app_name',
  `content` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'content',
  `beta_ips` varchar(1024) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'betaIps',
  `md5` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'md5',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `src_user` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL COMMENT 'source user',
  `src_ip` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'source ip',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT '' COMMENT '租户字段',
  `encrypted_data_key` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL COMMENT '秘钥',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_configinfobeta_datagrouptenant`(`data_id`, `group_id`, `tenant_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_bin COMMENT = 'config_info_beta' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of config_info_beta
-- ----------------------------

-- ----------------------------
-- Table structure for config_info_tag
-- ----------------------------
DROP TABLE IF EXISTS `config_info_tag`;
CREATE TABLE `config_info_tag`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'group_id',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT '' COMMENT 'tenant_id',
  `tag_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'tag_id',
  `app_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'app_name',
  `content` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'content',
  `md5` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'md5',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `src_user` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL COMMENT 'source user',
  `src_ip` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'source ip',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_configinfotag_datagrouptenanttag`(`data_id`, `group_id`, `tenant_id`, `tag_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_bin COMMENT = 'config_info_tag' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of config_info_tag
-- ----------------------------

-- ----------------------------
-- Table structure for config_tags_relation
-- ----------------------------
DROP TABLE IF EXISTS `config_tags_relation`;
CREATE TABLE `config_tags_relation`  (
  `id` bigint NOT NULL COMMENT 'id',
  `tag_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'tag_name',
  `tag_type` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'tag_type',
  `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'group_id',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT '' COMMENT 'tenant_id',
  `nid` bigint NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`nid`) USING BTREE,
  UNIQUE INDEX `uk_configtagrelation_configidtag`(`id`, `tag_name`, `tag_type`) USING BTREE,
  INDEX `idx_tenant_id`(`tenant_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_bin COMMENT = 'config_tag_relation' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of config_tags_relation
-- ----------------------------

-- ----------------------------
-- Table structure for group_capacity
-- ----------------------------
DROP TABLE IF EXISTS `group_capacity`;
CREATE TABLE `group_capacity`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `group_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'Group ID，空字符表示整个集群',
  `quota` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '配额，0表示使用默认值',
  `usage` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '使用量',
  `max_size` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '单个配置大小上限，单位为字节，0表示使用默认值',
  `max_aggr_count` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '聚合子配置最大个数，，0表示使用默认值',
  `max_aggr_size` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '单个聚合数据的子配置大小上限，单位为字节，0表示使用默认值',
  `max_history_count` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '最大变更历史数量',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_group_id`(`group_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_bin COMMENT = '集群、各Group容量信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of group_capacity
-- ----------------------------

-- ----------------------------
-- Table structure for his_config_info
-- ----------------------------
DROP TABLE IF EXISTS `his_config_info`;
CREATE TABLE `his_config_info`  (
  `id` bigint UNSIGNED NOT NULL,
  `nid` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `group_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `app_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'app_name',
  `content` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `md5` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL,
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `src_user` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL,
  `src_ip` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL,
  `op_type` char(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL,
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT '' COMMENT '租户字段',
  `encrypted_data_key` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL COMMENT '秘钥',
  PRIMARY KEY (`nid`) USING BTREE,
  INDEX `idx_gmt_create`(`gmt_create`) USING BTREE,
  INDEX `idx_gmt_modified`(`gmt_modified`) USING BTREE,
  INDEX `idx_did`(`data_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 447 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_bin COMMENT = '多租户改造' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of his_config_info
-- ----------------------------
INSERT INTO `his_config_info` VALUES (32, 323, 'wanhe-biz-data-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring: \n  influx:\n    url: http://*************:28086\n    user: iot\n    password: bREY6tktBXkPWWvij150LsjU47p4CePALxKa0D1Airvrg8hUgc7Xr0jyXIKQ6jW1gaTIQ_rxMdtqTvbdKgO6LQ==\n    database: wan_he_device_data\n    timeZone: tz(\'Asia/Shanghai\')\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **********************************************************************************************************************************************************            username: wanhe_cloud\n            password: eXdkxzAsKyKzcaT5\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\nemq:\n  # 70d64bdd0ec3cd94:zeFvtO9BKsaPMAlOwxesOSqvSJqwy1xmK4uhzuGzzalP\n  basic: basic NzBkNjRiZGQwZWMzY2Q5NDp6ZUZ2dE85QktzYVBNQWxPd3hlc09TcXZTSnF3eTF4bUs0dWh6dUd6emFsUA==\n  url: http://*************:18083\nali:\n  sms:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: dysmsapi.aliyuncs.com\n    signName: 万禾悦能\nkafka:\n  #分区数量1个\n  partition-number: 5\n  bootstrap:\n    servers: *************:9093\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n  sasl:\n    mechanism: PLAIN\n    username: alikafka_pre-cn-jmp3vby9t005\n    password: bHqQ5pptpKYLgOHBzMCHGyA3ArG2kSHg\n  ssl:\n    truststore:\n      location: \"D:\\\\home\\\\wanhe\\\\ca\\\\only.4096.client.truststore.jks\"\n  security:\n    auth:\n      login:\n        config: \"D:\\\\home\\\\wanhe\\\\ca\\\\kafka_client_jaas.conf\"\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '60e9311909787014709d07cc9d481c88', '2025-02-26 10:46:38', '2025-02-26 02:46:39', NULL, '***************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (33, 324, 'wanhe-mqtt-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **********************************************************************************************************************************************************            username: wanhe_cloud\n            password: eXdkxzAsKyKzcaT5\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mqtt:\n    username: wanhe                       # 账号（仅用于后端自认证）\n    password: \"wanhe\"                       # 密码（仅用于后端自认证）\n    host-url: tcp://*************:1883         # 连接 Emqx 消息服务器地址\n    client-id: wanhe-iot-${random.int}                # 客户端Id，不能相同，采用随机数 ${random.value}\n    default-topic: test                     # 默认主题\n    timeout: 30                             # 超时时间\n    keepalive: 30                           # 保持连接\n    clearSession: false\n    maxInflight: 32  \n\nkafka:\n  #分区数量3个\n  partition-number: 5\n  bootstrap:\n    servers: *************:9093\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n  sasl:\n    mechanism: PLAIN\n    username: alikafka_pre-cn-jmp3vby9t005\n    password: bHqQ5pptpKYLgOHBzMCHGyA3ArG2kSHg\n  ssl:\n    truststore:\n      location: \"D:\\\\home\\\\wanhe\\\\ca\\\\only.4096.client.truststore.jks\"\n  security:\n    auth:\n      login:\n        config: \"D:\\\\home\\\\wanhe\\\\ca\\\\kafka_client_jaas.conf\"\n\n\n\n\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', 'a77afc59bf86d887ddf04746c10cf11a', '2025-02-26 10:47:04', '2025-02-26 02:47:05', NULL, '***************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (32, 325, 'wanhe-biz-data-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring: \n  influx:\n    url: http://*************:28086\n    user: iot\n    password: bREY6tktBXkPWWvij150LsjU47p4CePALxKa0D1Airvrg8hUgc7Xr0jyXIKQ6jW1gaTIQ_rxMdtqTvbdKgO6LQ==\n    database: wan_he_device_data\n    timeZone: tz(\'Asia/Shanghai\')\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **********************************************************************************************************************************************************            username: wanhe_cloud\n            password: eXdkxzAsKyKzcaT5\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\nemq:\n  # 70d64bdd0ec3cd94:zeFvtO9BKsaPMAlOwxesOSqvSJqwy1xmK4uhzuGzzalP\n  basic: basic NzBkNjRiZGQwZWMzY2Q5NDp6ZUZ2dE85QktzYVBNQWxPd3hlc09TcXZTSnF3eTF4bUs0dWh6dUd6emFsUA==\n  url: http://*************:18083\nali:\n  sms:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: dysmsapi.aliyuncs.com\n    signName: 万禾悦能\nkafka:\n  #分区数量1个\n  partition-number: 1\n  bootstrap:\n    servers: *************:9093\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n  sasl:\n    mechanism: PLAIN\n    username: alikafka_pre-cn-jmp3vby9t005\n    password: bHqQ5pptpKYLgOHBzMCHGyA3ArG2kSHg\n  ssl:\n    truststore:\n      location: \"D:\\\\home\\\\wanhe\\\\ca\\\\only.4096.client.truststore.jks\"\n  security:\n    auth:\n      login:\n        config: \"D:\\\\home\\\\wanhe\\\\ca\\\\kafka_client_jaas.conf\"\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '172b19d7eb46722fa5210e8b834f6f0f', '2025-02-26 10:49:37', '2025-02-26 02:49:38', NULL, '************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (33, 326, 'wanhe-mqtt-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **********************************************************************************************************************************************************            username: wanhe_cloud\n            password: eXdkxzAsKyKzcaT5\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mqtt:\n    username: wanhe                       # 账号（仅用于后端自认证）\n    password: \"wanhe\"                       # 密码（仅用于后端自认证）\n    host-url: tcp://*************:1883         # 连接 Emqx 消息服务器地址\n    client-id: wanhe-iot-${random.int}                # 客户端Id，不能相同，采用随机数 ${random.value}\n    default-topic: test                     # 默认主题\n    timeout: 30                             # 超时时间\n    keepalive: 30                           # 保持连接\n    clearSession: false\n    maxInflight: 32  \n\nkafka:\n  #分区数量1个\n  partition-number: 1\n  bootstrap:\n    servers: *************:9093\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n  sasl:\n    mechanism: PLAIN\n    username: alikafka_pre-cn-jmp3vby9t005\n    password: bHqQ5pptpKYLgOHBzMCHGyA3ArG2kSHg\n  ssl:\n    truststore:\n      location: \"D:\\\\home\\\\wanhe\\\\ca\\\\only.4096.client.truststore.jks\"\n  security:\n    auth:\n      login:\n        config: \"D:\\\\home\\\\wanhe\\\\ca\\\\kafka_client_jaas.conf\"\n\n\n\n\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', 'ebb687d14c3d28a583b661cd346520be', '2025-02-26 10:49:50', '2025-02-26 02:49:51', NULL, '************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (81, 327, 'wanhe-biz-data-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring: \n  influx:\n    url: http://*************:28086\n    user: iot\n    password: bREY6tktBXkPWWvij150LsjU47p4CePALxKa0D1Airvrg8hUgc7Xr0jyXIKQ6jW1gaTIQ_rxMdtqTvbdKgO6LQ==\n    database: wan_he_device_data\n    timeZone: tz(\'Asia/Shanghai\')\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **********************************************************************************************************************************************************            username: wanhe_cloud\n            password: eXdkxzAsKyKzcaT5\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\nemq:\n  # 70d64bdd0ec3cd94:zeFvtO9BKsaPMAlOwxesOSqvSJqwy1xmK4uhzuGzzalP\n  basic: basic NzBkNjRiZGQwZWMzY2Q5NDp6ZUZ2dE85QktzYVBNQWxPd3hlc09TcXZTSnF3eTF4bUs0dWh6dUd6emFsUA==\n  url: http://*************:18083\nali:\n  sms:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: dysmsapi.aliyuncs.com\n    signName: 万禾悦能\nkafka:\n  #分区数量1个\n  partition-number: 5\n  bootstrap:\n    servers: *************:9093\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n  sasl:\n    mechanism: PLAIN\n    username: alikafka_pre-cn-jmp3vby9t005\n    password: bHqQ5pptpKYLgOHBzMCHGyA3ArG2kSHg\n  ssl:\n    truststore:\n      location: \"D:\\\\home\\\\wanhe\\\\ca\\\\only.4096.client.truststore.jks\"\n  security:\n    auth:\n      login:\n        config: \"D:\\\\home\\\\wanhe\\\\ca\\\\kafka_client_jaas.conf\"\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '60e9311909787014709d07cc9d481c88', '2025-02-26 15:05:28', '2025-02-26 07:05:28', NULL, '***************', 'U', 'f201b8d5-2edd-4645-ae26-ff14b13093da', '');
INSERT INTO `his_config_info` VALUES (82, 328, 'wanhe-business-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **********************************************************************************************************************************************************            username: wanhe_cloud\n            password: eXdkxzAsKyKzcaT5\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n\n\nemq:\n  # 70d64bdd0ec3cd94:zeFvtO9BKsaPMAlOwxesOSqvSJqwy1xmK4uhzuGzzalP\n  basic: basic NzBkNjRiZGQwZWMzY2Q5NDp6ZUZ2dE85QktzYVBNQWxPd3hlc09TcXZTSnF3eTF4bUs0dWh6dUd6emFsUA==\n  url: http://*************:18083\n\n# mybatis配置\n# mybatis:\n#     # 搜索指定包别名\n#     typeAliasesPackage: com.ruoyi.system\n#     # 配置mapper的扫描，找到所有的mapper.xml映射文件\n#     mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip', 'c0b704d4c04fd29cf6f97d87f40948cb', '2025-02-26 15:05:42', '2025-02-26 07:05:42', NULL, '************', 'U', 'f201b8d5-2edd-4645-ae26-ff14b13093da', '');
INSERT INTO `his_config_info` VALUES (31, 329, 'wanhe-business-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **********************************************************************************************************************************************************            username: wanhe_cloud\n            password: eXdkxzAsKyKzcaT5\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\nemq:\n  # 70d64bdd0ec3cd94:zeFvtO9BKsaPMAlOwxesOSqvSJqwy1xmK4uhzuGzzalP\n  basic: basic NzBkNjRiZGQwZWMzY2Q5NDp6ZUZ2dE85QktzYVBNQWxPd3hlc09TcXZTSnF3eTF4bUs0dWh6dUd6emFsUA==\n  url: http://*************:18083\n# mybatis配置\n# mybatis:\n#     # 搜索指定包别名\n#     typeAliasesPackage: com.ruoyi.system\n#     # 配置mapper的扫描，找到所有的mapper.xml映射文件\n#     mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip', '287c7276e90c2878ea41cd3d91b36da7', '2025-02-26 15:06:00', '2025-02-26 07:06:00', NULL, '***************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (32, 330, 'wanhe-biz-data-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring: \n  influx:\n    url: http://*************:28086\n    user: iot\n    password: bREY6tktBXkPWWvij150LsjU47p4CePALxKa0D1Airvrg8hUgc7Xr0jyXIKQ6jW1gaTIQ_rxMdtqTvbdKgO6LQ==\n    database: wan_he_device_data\n    timeZone: tz(\'Asia/Shanghai\')\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **********************************************************************************************************************************************************            username: wanhe_cloud\n            password: eXdkxzAsKyKzcaT5\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\nemq:\n  # 70d64bdd0ec3cd94:zeFvtO9BKsaPMAlOwxesOSqvSJqwy1xmK4uhzuGzzalP\n  basic: basic NzBkNjRiZGQwZWMzY2Q5NDp6ZUZ2dE85QktzYVBNQWxPd3hlc09TcXZTSnF3eTF4bUs0dWh6dUd6emFsUA==\n  url: http://*************:18083\nali:\n  sms:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: dysmsapi.aliyuncs.com\n    signName: 万禾悦能\nkafka:\n  #分区数量1个\n  partition-number: 5\n  bootstrap:\n    servers: *************:9093\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n  sasl:\n    mechanism: PLAIN\n    username: alikafka_pre-cn-jmp3vby9t005\n    password: bHqQ5pptpKYLgOHBzMCHGyA3ArG2kSHg\n  ssl:\n    truststore:\n      location: \"D:\\\\home\\\\wanhe\\\\ca\\\\only.4096.client.truststore.jks\"\n  security:\n    auth:\n      login:\n        config: \"D:\\\\home\\\\wanhe\\\\ca\\\\kafka_client_jaas.conf\"\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '60e9311909787014709d07cc9d481c88', '2025-02-26 15:06:11', '2025-02-26 07:06:11', NULL, '************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (32, 331, 'wanhe-biz-data-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring: \n  influx:\n    url: http://*************:28086\n    user: iot\n    password: bREY6tktBXkPWWvij150LsjU47p4CePALxKa0D1Airvrg8hUgc7Xr0jyXIKQ6jW1gaTIQ_rxMdtqTvbdKgO6LQ==\n    database: wan_he_device_data\n    timeZone: tz(\'Asia/Shanghai\')\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **********************************************************************************************************************************************************            username: wanhe_cloud\n            password: eXdkxzAsKyKzcaT5\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\nemq:\n  # 70d64bdd0ec3cd94:zeFvtO9BKsaPMAlOwxesOSqvSJqwy1xmK4uhzuGzzalP\n  basic: basic NzBkNjRiZGQwZWMzY2Q5NDp6ZUZ2dE85QktzYVBNQWxPd3hlc09TcXZTSnF3eTF4bUs0dWh6dUd6emFsUA==\n  url: http://*************:18083\nfault:\n  language: zh\nali:\n  sms:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: dysmsapi.aliyuncs.com\n    signName: 万禾悦能\nkafka:\n  #分区数量1个\n  partition-number: 5\n  bootstrap:\n    servers: *************:9093\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n  sasl:\n    mechanism: PLAIN\n    username: alikafka_pre-cn-jmp3vby9t005\n    password: bHqQ5pptpKYLgOHBzMCHGyA3ArG2kSHg\n  ssl:\n    truststore:\n      location: \"D:\\\\home\\\\wanhe\\\\ca\\\\only.4096.client.truststore.jks\"\n  security:\n    auth:\n      login:\n        config: \"D:\\\\home\\\\wanhe\\\\ca\\\\kafka_client_jaas.conf\"\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', 'f23ccdeaa41802ac5e06898ef050458f', '2025-02-27 14:30:40', '2025-02-27 06:30:40', NULL, '************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (82, 332, 'wanhe-business-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **********************************************************************************************************************************************************            username: wanhe_cloud\n            password: eXdkxzAsKyKzcaT5\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n\n\nemq:\n  # 70d64bdd0ec3cd94:zeFvtO9BKsaPMAlOwxesOSqvSJqwy1xmK4uhzuGzzalP\n  basic: basic NzBkNjRiZGQwZWMzY2Q5NDp6ZUZ2dE85QktzYVBNQWxPd3hlc09TcXZTSnF3eTF4bUs0dWh6dUd6emFsUA==\n  url: http://*************:18083\n\nfault:\n  language: zh\n# mybatis配置\n# mybatis:\n#     # 搜索指定包别名\n#     typeAliasesPackage: com.ruoyi.system\n#     # 配置mapper的扫描，找到所有的mapper.xml映射文件\n#     mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip', '75a2bca8d93cbdfa54cf641976472111', '2025-02-27 14:31:14', '2025-02-27 06:31:15', NULL, '************', 'U', 'f201b8d5-2edd-4645-ae26-ff14b13093da', '');
INSERT INTO `his_config_info` VALUES (76, 333, 'ruoyi-gen-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: *************\n      port: 6379\n      password: hsx123456\n      database: 5\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **********************************************************************************************************************************************************    username: wanhe_cloud\n    password: eXdkxzAsKyKzcaT5\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.gen.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'代码生成接口文档\'\n    # 描述\n    description: \'代码生成接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\n# 代码生成\ngen:\n  # 作者\n  author: ruoyi\n  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool\n  packageName: com.ruoyi.system\n  # 自动去除表前缀，默认是false\n  autoRemovePre: false\n  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）\n  tablePrefix: sys_\n', '1e6b6955be46ec86e8e9b56e041c59e4', '2025-03-03 10:13:32', '2025-03-03 02:13:33', NULL, '**************', 'U', 'f201b8d5-2edd-4645-ae26-ff14b13093da', '');
INSERT INTO `his_config_info` VALUES (77, 334, 'ruoyi-job-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: *************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **********************************************************************************************************************************************************    username: wanhe_cloud\n    password: eXdkxzAsKyKzcaT5\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.job.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'定时任务接口文档\'\n    # 描述\n    description: \'定时任务接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '87ba74e6d30e873cc67cca5c8de7a0d8', '2025-03-03 10:14:23', '2025-03-03 02:14:24', NULL, '**************', 'U', 'f201b8d5-2edd-4645-ae26-ff14b13093da', '');
INSERT INTO `his_config_info` VALUES (79, 335, 'ruoyi-system-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **********************************************************************************************************************************************************            username: wanhe_cloud\n            password: eXdkxzAsKyKzcaT5\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\n# mybatis:\n#     # 搜索指定包别名\n#     typeAliasesPackage: com.ruoyi.system\n#     # 配置mapper的扫描，找到所有的mapper.xml映射文件\n#     mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '7b680027499de9b885636409ec896ebd', '2025-03-03 10:14:50', '2025-03-03 02:14:50', NULL, '************', 'U', 'f201b8d5-2edd-4645-ae26-ff14b13093da', '');
INSERT INTO `his_config_info` VALUES (81, 336, 'wanhe-biz-data-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring: \n  influx:\n    url: http://*************:28086\n    user: iot\n    password: bREY6tktBXkPWWvij150LsjU47p4CePALxKa0D1Airvrg8hUgc7Xr0jyXIKQ6jW1gaTIQ_rxMdtqTvbdKgO6LQ==\n    database: wan_he_device_data\n    timeZone: tz(\'Asia/Shanghai\')\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **********************************************************************************************************************************************************            username: wanhe_cloud\n            password: eXdkxzAsKyKzcaT5\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\nemq:\n  # 70d64bdd0ec3cd94:zeFvtO9BKsaPMAlOwxesOSqvSJqwy1xmK4uhzuGzzalP\n  basic: basic NzBkNjRiZGQwZWMzY2Q5NDp6ZUZ2dE85QktzYVBNQWxPd3hlc09TcXZTSnF3eTF4bUs0dWh6dUd6emFsUA==\n  url: http://*************:18083\nfault:\n  language: zh\nali:\n  sms:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: dysmsapi.aliyuncs.com\n    signName: 万禾悦能\nkafka:\n  #分区数量1个\n  partition-number: 5\n  bootstrap:\n    servers: *************:9093\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n  sasl:\n    mechanism: PLAIN\n    username: alikafka_pre-cn-jmp3vby9t005\n    password: bHqQ5pptpKYLgOHBzMCHGyA3ArG2kSHg\n  ssl:\n    truststore:\n      location: \"D:\\\\home\\\\wanhe\\\\ca\\\\only.4096.client.truststore.jks\"\n  security:\n    auth:\n      login:\n        config: \"D:\\\\home\\\\wanhe\\\\ca\\\\kafka_client_jaas.conf\"\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', 'f23ccdeaa41802ac5e06898ef050458f', '2025-03-03 10:19:45', '2025-03-03 02:19:45', NULL, '**************', 'U', 'f201b8d5-2edd-4645-ae26-ff14b13093da', '');
INSERT INTO `his_config_info` VALUES (82, 337, 'wanhe-business-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **********************************************************************************************************************************************************            username: wanhe_cloud\n            password: eXdkxzAsKyKzcaT5\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n  task:\n    execution:\n      pool:\n        core-size: 8\n        max-size: 200\n        keep-alive: 1200s\n      thread-name-prefix: task-pool-\nemq:\n  # 70d64bdd0ec3cd94:zeFvtO9BKsaPMAlOwxesOSqvSJqwy1xmK4uhzuGzzalP\n  basic: basic NzBkNjRiZGQwZWMzY2Q5NDp6ZUZ2dE85QktzYVBNQWxPd3hlc09TcXZTSnF3eTF4bUs0dWh6dUd6emFsUA==\n  url: http://*************:18083\n\nfault:\n  language: zh\n# mybatis配置\n# mybatis:\n#     # 搜索指定包别名\n#     typeAliasesPackage: com.ruoyi.system\n#     # 配置mapper的扫描，找到所有的mapper.xml映射文件\n#     mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip', 'bbc5e182c2b2bfc73c9849a0461c617b', '2025-03-03 10:20:13', '2025-03-03 02:20:14', NULL, '************', 'U', 'f201b8d5-2edd-4645-ae26-ff14b13093da', '');
INSERT INTO `his_config_info` VALUES (82, 338, 'wanhe-business-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: ***********************************************************************************************************************************************************            username: wanhe_cloud\n            password: EM8YTsC5QBQfGa1Y\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n  task:\n    execution:\n      pool:\n        core-size: 8\n        max-size: 200\n        keep-alive: 1200s\n      thread-name-prefix: task-pool-\nemq:\n  # 70d64bdd0ec3cd94:zeFvtO9BKsaPMAlOwxesOSqvSJqwy1xmK4uhzuGzzalP\n  basic: basic NzBkNjRiZGQwZWMzY2Q5NDp6ZUZ2dE85QktzYVBNQWxPd3hlc09TcXZTSnF3eTF4bUs0dWh6dUd6emFsUA==\n  url: http://*************:18083\n\nfault:\n  language: zh\n# mybatis配置\n# mybatis:\n#     # 搜索指定包别名\n#     typeAliasesPackage: com.ruoyi.system\n#     # 配置mapper的扫描，找到所有的mapper.xml映射文件\n#     mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip', '73f1d542aa7b447b977224068bcbfc69', '2025-03-03 10:20:37', '2025-03-03 02:20:37', NULL, '************', 'U', 'f201b8d5-2edd-4645-ae26-ff14b13093da', '');
INSERT INTO `his_config_info` VALUES (83, 339, 'wanhe-mqtt-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **********************************************************************************************************************************************************            username: wanhe_cloud\n            password: eXdkxzAsKyKzcaT5\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mqtt:\n    username: wanhe                       # 账号（仅用于后端自认证）\n    password: \"wanhe\"                       # 密码（仅用于后端自认证）\n    host-url: tcp://*************:1883         # 连接 Emqx 消息服务器地址\n    client-id: wanhe-iot-${random.int}                # 客户端Id，不能相同，采用随机数 ${random.value}\n    default-topic: test                     # 默认主题\n    timeout: 30                             # 超时时间\n    keepalive: 30                           # 保持连接\n    clearSession: false\n    maxInflight: 32  \n\nkafka:\n  #分区数量3个\n  partition-number: 5\n  bootstrap:\n    servers: *************:9093\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n  sasl:\n    mechanism: PLAIN\n    username: alikafka_pre-cn-jmp3vby9t005\n    password: bHqQ5pptpKYLgOHBzMCHGyA3ArG2kSHg\n  ssl:\n    truststore:\n      location: \"D:\\\\home\\\\wanhe\\\\ca\\\\only.4096.client.truststore.jks\"\n  security:\n    auth:\n      login:\n        config: \"D:\\\\home\\\\wanhe\\\\ca\\\\kafka_client_jaas.conf\"\n\n\n\n\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', 'a77afc59bf86d887ddf04746c10cf11a', '2025-03-03 10:21:24', '2025-03-03 02:21:24', NULL, '**************', 'U', 'f201b8d5-2edd-4645-ae26-ff14b13093da', '');
INSERT INTO `his_config_info` VALUES (77, 340, 'ruoyi-job-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ***********************************************************************************************************************************************************    username: wanhe_cloud\n    password: EM8YTsC5QBQfGa1Y\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.job.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'定时任务接口文档\'\n    # 描述\n    description: \'定时任务接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '9f66a766d0f3dcd6298ccf794f4e0279', '2025-03-03 10:24:22', '2025-03-03 02:24:22', NULL, '**************', 'U', 'f201b8d5-2edd-4645-ae26-ff14b13093da', '');
INSERT INTO `his_config_info` VALUES (5, 341, 'ruoyi-system-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **********************************************************************************************************************************************************            username: wanhe_cloud\n            password: eXdkxzAsKyKzcaT5\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\n# mybatis:\n#     # 搜索指定包别名\n#     typeAliasesPackage: com.ruoyi.system\n#     # 配置mapper的扫描，找到所有的mapper.xml映射文件\n#     mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '7b680027499de9b885636409ec896ebd', '2025-03-03 10:27:43', '2025-03-03 02:27:43', NULL, '************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (6, 342, 'ruoyi-gen-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: *************\n      port: 6379\n      password: hsx123456\n      database: 5\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **********************************************************************************************************************************************************    username: wanhe_cloud\n    password: eXdkxzAsKyKzcaT5\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.gen.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'代码生成接口文档\'\n    # 描述\n    description: \'代码生成接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\n# 代码生成\ngen:\n  # 作者\n  author: ruoyi\n  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool\n  packageName: com.ruoyi.system\n  # 自动去除表前缀，默认是false\n  autoRemovePre: false\n  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）\n  tablePrefix: sys_\n', '1e6b6955be46ec86e8e9b56e041c59e4', '2025-03-03 10:28:18', '2025-03-03 02:28:19', NULL, '************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (7, 343, 'ruoyi-job-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: *************\n      port: 6379\n      password: hsx123456\n      database: 5\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **********************************************************************************************************************************************************    username: wanhe_cloud\n    password: eXdkxzAsKyKzcaT5\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.job.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'定时任务接口文档\'\n    # 描述\n    description: \'定时任务接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', 'c7d6c2bafd86b4bf8b947aba426b3722', '2025-03-03 10:28:56', '2025-03-03 02:28:57', NULL, '************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (31, 344, 'wanhe-business-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **********************************************************************************************************************************************************            username: wanhe_cloud\n            password: eXdkxzAsKyKzcaT5\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\nemq:\n  # 70d64bdd0ec3cd94:zeFvtO9BKsaPMAlOwxesOSqvSJqwy1xmK4uhzuGzzalP\n  basic: basic NzBkNjRiZGQwZWMzY2Q5NDp6ZUZ2dE85QktzYVBNQWxPd3hlc09TcXZTSnF3eTF4bUs0dWh6dUd6emFsUA==\n  url: http://*************:18083\nfault:\n  language: zh\n# mybatis配置\n# mybatis:\n#     # 搜索指定包别名\n#     typeAliasesPackage: com.ruoyi.system\n#     # 配置mapper的扫描，找到所有的mapper.xml映射文件\n#     mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip', 'cad412fb0fede3fbca8bd3622d7badc0', '2025-03-03 10:30:02', '2025-03-03 02:30:03', NULL, '************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (32, 345, 'wanhe-biz-data-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring: \n  influx:\n    url: http://*************:28086\n    user: iot\n    password: bREY6tktBXkPWWvij150LsjU47p4CePALxKa0D1Airvrg8hUgc7Xr0jyXIKQ6jW1gaTIQ_rxMdtqTvbdKgO6LQ==\n    database: wan_he_device_data\n    timeZone: tz(\'Asia/Shanghai\')\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **********************************************************************************************************************************************************            username: wanhe_cloud\n            password: eXdkxzAsKyKzcaT5\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n  task:\n    execution:\n      pool:\n        core-size: 8\n        max-size: 200\n        keep-alive: 1200s\n      thread-name-prefix: task-pool-\nemq:\n  # 70d64bdd0ec3cd94:zeFvtO9BKsaPMAlOwxesOSqvSJqwy1xmK4uhzuGzzalP\n  basic: basic NzBkNjRiZGQwZWMzY2Q5NDp6ZUZ2dE85QktzYVBNQWxPd3hlc09TcXZTSnF3eTF4bUs0dWh6dUd6emFsUA==\n  url: http://*************:18083\nfault:\n  language: zh\nali:\n  sms:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: dysmsapi.aliyuncs.com\n    signName: 万禾悦能\nkafka:\n  #分区数量1个\n  partition-number: 5\n  bootstrap:\n    servers: *************:9093\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n  sasl:\n    mechanism: PLAIN\n    username: alikafka_pre-cn-jmp3vby9t005\n    password: bHqQ5pptpKYLgOHBzMCHGyA3ArG2kSHg\n  ssl:\n    truststore:\n      location: \"D:\\\\home\\\\wanhe\\\\ca\\\\only.4096.client.truststore.jks\"\n  security:\n    auth:\n      login:\n        config: \"D:\\\\home\\\\wanhe\\\\ca\\\\kafka_client_jaas.conf\"\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '2a7dfd30c3b6d5c9550204feb31f87a4', '2025-03-03 10:31:46', '2025-03-03 02:31:47', NULL, '************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (33, 346, 'wanhe-mqtt-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **********************************************************************************************************************************************************            username: wanhe_cloud\n            password: eXdkxzAsKyKzcaT5\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mqtt:\n    username: wanhe                       # 账号（仅用于后端自认证）\n    password: \"wanhe\"                       # 密码（仅用于后端自认证）\n    host-url: tcp://*************:1883         # 连接 Emqx 消息服务器地址\n    client-id: wanhe-iot-${random.int}                # 客户端Id，不能相同，采用随机数 ${random.value}\n    default-topic: test                     # 默认主题\n    timeout: 30                             # 超时时间\n    keepalive: 30                           # 保持连接\n    clearSession: false\n    maxInflight: 32  \n\nkafka:\n  #分区数量1个\n  partition-number: 5\n  bootstrap:\n    servers: *************:9093\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n  sasl:\n    mechanism: PLAIN\n    username: alikafka_pre-cn-jmp3vby9t005\n    password: bHqQ5pptpKYLgOHBzMCHGyA3ArG2kSHg\n  ssl:\n    truststore:\n      location: \"D:\\\\home\\\\wanhe\\\\ca\\\\only.4096.client.truststore.jks\"\n  security:\n    auth:\n      login:\n        config: \"D:\\\\home\\\\wanhe\\\\ca\\\\kafka_client_jaas.conf\"\n\n\n\n\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '2ae08a7dfe038dfe449fbcedd61b6ca5', '2025-03-03 10:32:35', '2025-03-03 02:32:36', NULL, '************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (81, 347, 'wanhe-biz-data-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring: \n  influx:\n    url: http://**************:8086\n    user: influxdb\n    password: 9RNmQc7F4_7IeZG6OzbZK7ijgDJ1BvLOP4n03mXd6KWZ_cMkx-Wur9xYr6uUTTDdEJ6lr5DGrPeNw4GZytfXtA==\n    database: wan_he_device_data\n    timeZone: tz(\'Asia/Shanghai\')\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: ***********************************************************************************************************************************************************            username: wanhe_cloud\n            password: EM8YTsC5QBQfGa1Y\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\nemq:\n  # 70d64bdd0ec3cd94:2kNaHFwHuPOJinKQ8xeT8DqjUPgkiiT9BRGQGhMGPW1L\n  basic: NzBkNjRiZGQwZWMzY2Q5NDoya05hSEZ3SHVQT0ppbktROHhlVDhEcWpVUGdraWlUOUJSR1FHaE1HUFcxTA==\n  url: http://**************:18083\nfault:\n  language: zh\nali:\n  sms:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: dysmsapi.aliyuncs.com\n    signName: 万禾悦能\nkafka:\n  #分区数量1个\n  partition-number: 5\n  bootstrap:\n    servers: *************:9093\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n  sasl:\n    mechanism: PLAIN\n    username: alikafka_pre-cn-jmp3vby9t005\n    password: bHqQ5pptpKYLgOHBzMCHGyA3ArG2kSHg\n  ssl:\n    truststore:\n      location: \"D:\\\\home\\\\wanhe\\\\ca\\\\only.4096.client.truststore.jks\"\n  security:\n    auth:\n      login:\n        config: \"D:\\\\home\\\\wanhe\\\\ca\\\\kafka_client_jaas.conf\"\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', 'a57dc408916cd7b166ea6b3b7b6cf133', '2025-03-03 18:41:44', '2025-03-03 10:41:44', NULL, '**************', 'U', 'f201b8d5-2edd-4645-ae26-ff14b13093da', '');
INSERT INTO `his_config_info` VALUES (82, 348, 'wanhe-business-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: ***********************************************************************************************************************************************************            username: wanhe_cloud\n            password: EM8YTsC5QBQfGa1Y\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n  task:\n    execution:\n      pool:\n        core-size: 8\n        max-size: 200\n        keep-alive: 1200s\n      thread-name-prefix: task-pool-\nemq:\n  # 70d64bdd0ec3cd94:2kNaHFwHuPOJinKQ8xeT8DqjUPgkiiT9BRGQGhMGPW1L\n  basic: NzBkNjRiZGQwZWMzY2Q5NDoya05hSEZ3SHVQT0ppbktROHhlVDhEcWpVUGdraWlUOUJSR1FHaE1HUFcxTA==\n  url: http://**************:18083\n\nfault:\n  language: zh\n# mybatis配置\n# mybatis:\n#     # 搜索指定包别名\n#     typeAliasesPackage: com.ruoyi.system\n#     # 配置mapper的扫描，找到所有的mapper.xml映射文件\n#     mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip', '2234207d138fafcda1040c172bd44183', '2025-03-03 18:42:00', '2025-03-03 10:42:01', NULL, '**************', 'U', 'f201b8d5-2edd-4645-ae26-ff14b13093da', '');
INSERT INTO `his_config_info` VALUES (31, 349, 'wanhe-business-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: ***********************************************************************************************************************************************************            username: wanhe_cloud\n            password: EM8YTsC5QBQfGa1Y\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\nemq:\n  # 70d64bdd0ec3cd94:2kNaHFwHuPOJinKQ8xeT8DqjUPgkiiT9BRGQGhMGPW1L\n  basic: NzBkNjRiZGQwZWMzY2Q5NDoya05hSEZ3SHVQT0ppbktROHhlVDhEcWpVUGdraWlUOUJSR1FHaE1HUFcxTA==\n  url: http://**************:18083\nfault:\n  language: zh\n# mybatis配置\n# mybatis:\n#     # 搜索指定包别名\n#     typeAliasesPackage: com.ruoyi.system\n#     # 配置mapper的扫描，找到所有的mapper.xml映射文件\n#     mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip', '8cc4e3c13a4990b5f6d6ce67474000aa', '2025-03-03 18:42:32', '2025-03-03 10:42:32', NULL, '**************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (32, 350, 'wanhe-biz-data-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring: \n  influx:\n    url: http://**************:8086\n    user: influxdb\n    password: 9RNmQc7F4_7IeZG6OzbZK7ijgDJ1BvLOP4n03mXd6KWZ_cMkx-Wur9xYr6uUTTDdEJ6lr5DGrPeNw4GZytfXtA==\n    database: wan_he_device_data\n    timeZone: tz(\'Asia/Shanghai\')\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: ***********************************************************************************************************************************************************            username: wanhe_cloud\n            password: EM8YTsC5QBQfGa1Y\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n  task:\n    execution:\n      pool:\n        core-size: 8\n        max-size: 200\n        keep-alive: 1200s\n      thread-name-prefix: task-pool-\nemq:\n  # 70d64bdd0ec3cd94:2kNaHFwHuPOJinKQ8xeT8DqjUPgkiiT9BRGQGhMGPW1L\n  basic: NzBkNjRiZGQwZWMzY2Q5NDoya05hSEZ3SHVQT0ppbktROHhlVDhEcWpVUGdraWlUOUJSR1FHaE1HUFcxTA==\n  url: http://**************:18083\nfault:\n  language: zh\nali:\n  sms:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: dysmsapi.aliyuncs.com\n    signName: 万禾悦能\nkafka:\n  #分区数量1个\n  partition-number: 5\n  bootstrap:\n    servers: *************:9093\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n  sasl:\n    mechanism: PLAIN\n    username: alikafka_pre-cn-jmp3vby9t005\n    password: bHqQ5pptpKYLgOHBzMCHGyA3ArG2kSHg\n  ssl:\n    truststore:\n      location: \"D:\\\\home\\\\wanhe\\\\ca\\\\only.4096.client.truststore.jks\"\n  security:\n    auth:\n      login:\n        config: \"D:\\\\home\\\\wanhe\\\\ca\\\\kafka_client_jaas.conf\"\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', 'd7010083d164305f3cf0eee9cf6146d9', '2025-03-03 18:42:48', '2025-03-03 10:42:48', NULL, '************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (72, 351, 'application-local.yml', 'DEFAULT_GROUP', '', 'spring:\n  autoconfigure:\n    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure\n# feign 配置\nfeign:\n  sentinel:\n    enabled: true\n  okhttp:\n    enabled: true\n  httpclient:\n    enabled: false\n  client:\n    config:\n      default:\n        connectTimeout: 10000\n        readTimeout: 10000\n  compression:\n    request:\n      enabled: true\n      min-request-size: 8192\n    response:\n      enabled: true\n\n# 暴露监控端点\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: \'*\'\n\nmybatis-plus:\n  # 不支持多包, 如有需要可在注解配置 或 提升扫包等级\n  # 例如 com.**.**.mapper\n  mapperPackage: com.**.**.mapper\n  # 对应的 XML 文件位置\n  mapperLocations: classpath*:mapper/**/*Mapper.xml\n  # 实体扫描，多个package用逗号或者分号分隔\n  typeAliasesPackage: com.ruoyi.**.**,com.wanhe.**.**\n  # 启动时是否检查 MyBatis XML 文件的存在，默认不检查\n  checkConfigLocation: false\n  configuration:\n    map-underscore-to-camel-case: true\n    auto-mapping-behavior: full\n    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl\n    # 自动驼峰命名规则（camel case）映射\n    mapUnderscoreToCamelCase: true\n    # MyBatis 自动映射策略\n    # NONE：不启用 PARTIAL：只对非嵌套 resultMap 自动映射 FULL：对所有 resultMap 自动映射\n    autoMappingBehavior: PARTIAL\n    # MyBatis 自动映射时未知列或未知属性处理策\n    # NONE：不做处理 WARNING：打印相关警告 FAILING：抛出异常和详细信息\n    autoMappingUnknownColumnBehavior: NONE\n    # 更详细的日志输出 会有性能损耗 org.apache.ibatis.logging.stdout.StdOutImpl\n    # 关闭日志记录 (可单纯使用 p6spy 分析) org.apache.ibatis.logging.nologging.NoLoggingImpl\n    # 默认日志输出 org.apache.ibatis.logging.slf4j.Slf4jImpl\n    # logImpl: org.apache.ibatis.logging.nologging.NoLoggingImpl\n  globalConfig:\n    # 是否打印 Logo banner\n    banner: true\n    enableSqlRunner: true\n    dbConfig:\n      # 主键类型\n      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID\n      idType: ASSIGN_ID\n      # 逻辑已删除值\n      logicDeleteValue: 1\n      # 逻辑未删除值\n      logicNotDeleteValue: 0\n      # 字段验证策略之 insert,在 insert 的时候的字段验证策略\n      # IGNORED 忽略 NOT_NULL 非NULL NOT_EMPTY 非空 DEFAULT 默认 NEVER 不加入 SQL\n      insertStrategy: NOT_NULL\n      # 字段验证策略之 update,在 update 的时候的字段验证策略\n      updateStrategy: NOT_NULL\n      # 字段验证策略之 select,在 select 的时候的字段验证策略既 wrapper 根据内部 entity 生成的 where 条件\n      where-strategy: NOT_NULL\nlogging:\n  file:\n    name: ./logs/${spring.application.name}/info.log', 'f2a1f2084d669f9ae299d70e169cfbd8', '2025-03-06 14:55:45', '2025-03-06 06:55:46', NULL, '**************', 'U', 'f201b8d5-2edd-4645-ae26-ff14b13093da', '');
INSERT INTO `his_config_info` VALUES (1, 352, 'application-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  autoconfigure:\n    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure\n# feign 配置\nfeign:\n  sentinel:\n    enabled: true\n  okhttp:\n    enabled: true\n  httpclient:\n    enabled: false\n  client:\n    config:\n      default:\n        connectTimeout: 10000\n        readTimeout: 10000\n  compression:\n    request:\n      enabled: true\n      min-request-size: 8192\n    response:\n      enabled: true\n\n# 暴露监控端点\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: \'*\'\n\nmybatis-plus:\n  # 不支持多包, 如有需要可在注解配置 或 提升扫包等级\n  # 例如 com.**.**.mapper\n  mapperPackage: com.**.**.mapper\n  # 对应的 XML 文件位置\n  mapperLocations: classpath*:mapper/**/*Mapper.xml\n  # 实体扫描，多个package用逗号或者分号分隔\n  typeAliasesPackage: com.ruoyi.**.**,com.wanhe.**.**\n  # 启动时是否检查 MyBatis XML 文件的存在，默认不检查\n  checkConfigLocation: false\n  configuration:\n    map-underscore-to-camel-case: true\n    auto-mapping-behavior: full\n    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl\n    # 自动驼峰命名规则（camel case）映射\n    mapUnderscoreToCamelCase: true\n    # MyBatis 自动映射策略\n    # NONE：不启用 PARTIAL：只对非嵌套 resultMap 自动映射 FULL：对所有 resultMap 自动映射\n    autoMappingBehavior: PARTIAL\n    # MyBatis 自动映射时未知列或未知属性处理策\n    # NONE：不做处理 WARNING：打印相关警告 FAILING：抛出异常和详细信息\n    autoMappingUnknownColumnBehavior: NONE\n    # 更详细的日志输出 会有性能损耗 org.apache.ibatis.logging.stdout.StdOutImpl\n    # 关闭日志记录 (可单纯使用 p6spy 分析) org.apache.ibatis.logging.nologging.NoLoggingImpl\n    # 默认日志输出 org.apache.ibatis.logging.slf4j.Slf4jImpl\n    # logImpl: org.apache.ibatis.logging.nologging.NoLoggingImpl\n  globalConfig:\n    # 是否打印 Logo banner\n    banner: true\n    enableSqlRunner: true\n    dbConfig:\n      # 主键类型\n      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID\n      idType: ASSIGN_ID\n      # 逻辑已删除值\n      logicDeleteValue: 1\n      # 逻辑未删除值\n      logicNotDeleteValue: 0\n      # 字段验证策略之 insert,在 insert 的时候的字段验证策略\n      # IGNORED 忽略 NOT_NULL 非NULL NOT_EMPTY 非空 DEFAULT 默认 NEVER 不加入 SQL\n      insertStrategy: NOT_NULL\n      # 字段验证策略之 update,在 update 的时候的字段验证策略\n      updateStrategy: NOT_NULL\n      # 字段验证策略之 select,在 select 的时候的字段验证策略既 wrapper 根据内部 entity 生成的 where 条件\n      where-strategy: NOT_NULL\n\nlogging:\n  file:\n    name: ./logs/${spring.application.name}/info.log', '6296de499f534038b29a0558b6e9e291', '2025-03-06 14:56:03', '2025-03-06 06:56:04', NULL, '************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (75, 353, 'ruoyi-gateway-local.yml', 'DEFAULT_GROUP', '', 'spring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: ruoyi-auth\n          uri: lb://ruoyi-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestFilter\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 代码生成\n        - id: ruoyi-gen\n          uri: lb://ruoyi-gen\n          predicates:\n            - Path=/code/**\n          filters:\n            - StripPrefix=1\n        # 定时任务\n        - id: ruoyi-job\n          uri: lb://ruoyi-job\n          predicates:\n            - Path=/schedule/**\n          filters:\n            - StripPrefix=1\n        # 系统模块\n        - id: ruoyi-system\n          uri: lb://ruoyi-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: ruoyi-file\n          uri: lb://ruoyi-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 业务服务\n        - id: wanhe-business\n          uri: lb://wanhe-business\n          predicates:\n            - Path=/business/**\n          filters:\n            - StripPrefix=1\n        # mqtt服务\n        - id: wanhe-mqtt\n          uri: lb://wanhe-mqtt\n          predicates:\n            - Path=/mqtt/**\n          filters:\n            - StripPrefix=1\n        # 数据处理服务\n        - id: wanhe-biz-data\n          uri: lb://wanhe-biz-data\n          predicates:\n            - Path=/data/**\n          filters:\n            - StripPrefix=1\n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/*\n      - /system/dict/data/type/time_zone\n      - /system/config/configKey/*\n      - /data/inv/data/device/status\n      - /business/fault/all\n      - /*/v2/api-docs\n      - /*/v3/api-docs\n      - /csrf\n\n# springdoc配置\nspringdoc:\n  webjars:\n    # 访问前缀\n    prefix:\n', 'b52a35beacbc48e3973ebb34947d5b69', '2025-03-07 15:21:23', '2025-03-07 07:21:24', NULL, '**************', 'U', 'f201b8d5-2edd-4645-ae26-ff14b13093da', '');
INSERT INTO `his_config_info` VALUES (2, 354, 'ruoyi-gateway-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: ruoyi-auth\n          uri: lb://ruoyi-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestFilter\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 代码生成\n        - id: ruoyi-gen\n          uri: lb://ruoyi-gen\n          predicates:\n            - Path=/code/**\n          filters:\n            - StripPrefix=1\n        # 定时任务\n        - id: ruoyi-job\n          uri: lb://ruoyi-job\n          predicates:\n            - Path=/schedule/**\n          filters:\n            - StripPrefix=1\n        # 系统模块\n        - id: ruoyi-system\n          uri: lb://ruoyi-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: ruoyi-file\n          uri: lb://ruoyi-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 业务服务\n        - id: wanhe-business\n          uri: lb://wanhe-business\n          predicates:\n            - Path=/business/**\n          filters:\n            - StripPrefix=1\n        # mqtt服务\n        - id: wanhe-mqtt\n          uri: lb://wanhe-mqtt\n          predicates:\n            - Path=/mqtt/**\n          filters:\n            - StripPrefix=1\n        # 数据处理服务\n        - id: wanhe-biz-data\n          uri: lb://wanhe-biz-data\n          predicates:\n            - Path=/data/**\n          filters:\n            - StripPrefix=1\n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/*\n      - /system/dict/data/type/time_zone\n      - /system/config/configKey/*\n      - /data/inv/data/device/status\n      - /business/fault/all\n      - /*/v2/api-docs\n      - /*/v3/api-docs\n      - /csrf\n\n# springdoc配置\nspringdoc:\n  webjars:\n    # 访问前缀\n    prefix:\n', 'b52a35beacbc48e3973ebb34947d5b69', '2025-03-07 15:21:42', '2025-03-07 07:21:43', NULL, '**************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (81, 355, 'wanhe-biz-data-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring: \n  influx:\n    url: http://**************:8086\n    user: influxdb\n    password: 9RNmQc7F4_7IeZG6OzbZK7ijgDJ1BvLOP4n03mXd6KWZ_cMkx-Wur9xYr6uUTTDdEJ6lr5DGrPeNw4GZytfXtA==\n    database: wan_he_device_data\n    timeZone: tz(\'Asia/Shanghai\')\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: ***********************************************************************************************************************************************************            username: wanhe_cloud\n            password: EM8YTsC5QBQfGa1Y\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\nemq:\n  # 70d64bdd0ec3cd94:2kNaHFwHuPOJinKQ8xeT8DqjUPgkiiT9BRGQGhMGPW1L\n  basic: Basic NzBkNjRiZGQwZWMzY2Q5NDoya05hSEZ3SHVQT0ppbktROHhlVDhEcWpVUGdraWlUOUJSR1FHaE1HUFcxTA==\n  url: http://**************:18083\nfault:\n  language: zh\nali:\n  sms:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: dysmsapi.aliyuncs.com\n    signName: 万禾悦能\nkafka:\n  #分区数量1个\n  partition-number: 5\n  bootstrap:\n    servers: *************:9093\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n  sasl:\n    mechanism: PLAIN\n    username: alikafka_pre-cn-jmp3vby9t005\n    password: bHqQ5pptpKYLgOHBzMCHGyA3ArG2kSHg\n  ssl:\n    truststore:\n      location: \"D:\\\\home\\\\wanhe\\\\ca\\\\only.4096.client.truststore.jks\"\n  security:\n    auth:\n      login:\n        config: \"D:\\\\home\\\\wanhe\\\\ca\\\\kafka_client_jaas.conf\"\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '3c147bdeb9694d7680500ec5679d69a6', '2025-03-08 09:28:00', '2025-03-08 01:28:00', NULL, '**************', 'U', 'f201b8d5-2edd-4645-ae26-ff14b13093da', '');
INSERT INTO `his_config_info` VALUES (83, 356, 'wanhe-mqtt-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: ***********************************************************************************************************************************************************            username: wanhe_cloud\n            password: EM8YTsC5QBQfGa1Y\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mqtt:\n    username: wanhe                       # 账号（仅用于后端自认证）\n    password: \"wanhe\"                       # 密码（仅用于后端自认证）\n    host-url: tcp://**************:1883         # 连接 Emqx 消息服务器地址\n    client-id: wanhe-iot-${random.int}                # 客户端Id，不能相同，采用随机数 ${random.value}\n    default-topic: test                     # 默认主题\n    timeout: 30                             # 超时时间\n    keepalive: 30                           # 保持连接\n    clearSession: false\n    maxInflight: 32  \n\nkafka:\n  #分区数量3个\n  partition-number: 5\n  bootstrap:\n    servers: *************:9093\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n  sasl:\n    mechanism: PLAIN\n    username: alikafka_pre-cn-jmp3vby9t005\n    password: bHqQ5pptpKYLgOHBzMCHGyA3ArG2kSHg\n  ssl:\n    truststore:\n      location: \"D:\\\\home\\\\wanhe\\\\ca\\\\only.4096.client.truststore.jks\"\n  security:\n    auth:\n      login:\n        config: \"D:\\\\home\\\\wanhe\\\\ca\\\\kafka_client_jaas.conf\"\n\n\n\n\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', 'fe9074f6168a67c2d61c3eb24755bc77', '2025-03-08 09:29:41', '2025-03-08 01:29:41', NULL, '**************', 'U', 'f201b8d5-2edd-4645-ae26-ff14b13093da', '');
INSERT INTO `his_config_info` VALUES (32, 357, 'wanhe-biz-data-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring: \n  influx:\n    url: http://**************:8086\n    user: influxdb\n    password: 9RNmQc7F4_7IeZG6OzbZK7ijgDJ1BvLOP4n03mXd6KWZ_cMkx-Wur9xYr6uUTTDdEJ6lr5DGrPeNw4GZytfXtA==\n    database: wan_he_device_data\n    timeZone: tz(\'Asia/Shanghai\')\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: ***********************************************************************************************************************************************************            username: wanhe_cloud\n            password: EM8YTsC5QBQfGa1Y\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n  task:\n    execution:\n      pool:\n        core-size: 8\n        max-size: 200\n        keep-alive: 1200s\n      thread-name-prefix: task-pool-\nemq:\n  # 70d64bdd0ec3cd94:2kNaHFwHuPOJinKQ8xeT8DqjUPgkiiT9BRGQGhMGPW1L\n  basic: Basic NzBkNjRiZGQwZWMzY2Q5NDoya05hSEZ3SHVQT0ppbktROHhlVDhEcWpVUGdraWlUOUJSR1FHaE1HUFcxTA==\n  url: http://**************:18083\nfault:\n  language: zh\nali:\n  sms:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: dysmsapi.aliyuncs.com\n    signName: 万禾悦能\nkafka:\n  #分区数量1个\n  partition-number: 5\n  bootstrap:\n    servers: *************:9093\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n  sasl:\n    mechanism: PLAIN\n    username: alikafka_pre-cn-jmp3vby9t005\n    password: bHqQ5pptpKYLgOHBzMCHGyA3ArG2kSHg\n  ssl:\n    truststore:\n      location: \"D:\\\\home\\\\wanhe\\\\ca\\\\only.4096.client.truststore.jks\"\n  security:\n    auth:\n      login:\n        config: \"D:\\\\home\\\\wanhe\\\\ca\\\\kafka_client_jaas.conf\"\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', 'b6491a026fa3e8e9ee468da198501606', '2025-03-08 09:31:55', '2025-03-08 01:31:55', NULL, '**************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (33, 358, 'wanhe-mqtt-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: ***********************************************************************************************************************************************************            username: wanhe_cloud\n            password: EM8YTsC5QBQfGa1Y\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mqtt:\n    username: wanhe                       # 账号（仅用于后端自认证）\n    password: \"wanhe\"                       # 密码（仅用于后端自认证）\n    host-url: tcp://**************:1883         # 连接 Emqx 消息服务器地址\n    client-id: wanhe-iot-${random.int}                # 客户端Id，不能相同，采用随机数 ${random.value}\n    default-topic: test                     # 默认主题\n    timeout: 30                             # 超时时间\n    keepalive: 30                           # 保持连接\n    clearSession: false\n    maxInflight: 32  \n\nkafka:\n  #分区数量1个\n  partition-number: 5\n  bootstrap:\n    servers: *************:9093\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n  sasl:\n    mechanism: PLAIN\n    username: alikafka_pre-cn-jmp3vby9t005\n    password: bHqQ5pptpKYLgOHBzMCHGyA3ArG2kSHg\n  ssl:\n    truststore:\n      location: \"D:\\\\home\\\\wanhe\\\\ca\\\\only.4096.client.truststore.jks\"\n  security:\n    auth:\n      login:\n        config: \"D:\\\\home\\\\wanhe\\\\ca\\\\kafka_client_jaas.conf\"\n\n\n\n\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '50ac8eb1af051643861b066e19576d70', '2025-03-08 09:32:17', '2025-03-08 01:32:18', NULL, '**************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (72, 359, 'application-local.yml', 'DEFAULT_GROUP', '', 'spring:\n  autoconfigure:\n    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure\n# feign 配置\nfeign:\n  sentinel:\n    enabled: true\n  okhttp:\n    enabled: true\n  httpclient:\n    enabled: false\n  client:\n    config:\n      default:\n        connectTimeout: 600000\n        readTimeout: 600000\n  compression:\n    request:\n      enabled: true\n      min-request-size: 8192\n    response:\n      enabled: true\n\n# 暴露监控端点\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: \'*\'\n\nmybatis-plus:\n  # 不支持多包, 如有需要可在注解配置 或 提升扫包等级\n  # 例如 com.**.**.mapper\n  mapperPackage: com.**.**.mapper\n  # 对应的 XML 文件位置\n  mapperLocations: classpath*:mapper/**/*Mapper.xml\n  # 实体扫描，多个package用逗号或者分号分隔\n  typeAliasesPackage: com.ruoyi.**.**,com.wanhe.**.**\n  # 启动时是否检查 MyBatis XML 文件的存在，默认不检查\n  checkConfigLocation: false\n  configuration:\n    map-underscore-to-camel-case: true\n    auto-mapping-behavior: full\n    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl\n    # 自动驼峰命名规则（camel case）映射\n    mapUnderscoreToCamelCase: true\n    # MyBatis 自动映射策略\n    # NONE：不启用 PARTIAL：只对非嵌套 resultMap 自动映射 FULL：对所有 resultMap 自动映射\n    autoMappingBehavior: PARTIAL\n    # MyBatis 自动映射时未知列或未知属性处理策\n    # NONE：不做处理 WARNING：打印相关警告 FAILING：抛出异常和详细信息\n    autoMappingUnknownColumnBehavior: NONE\n    # 更详细的日志输出 会有性能损耗 org.apache.ibatis.logging.stdout.StdOutImpl\n    # 关闭日志记录 (可单纯使用 p6spy 分析) org.apache.ibatis.logging.nologging.NoLoggingImpl\n    # 默认日志输出 org.apache.ibatis.logging.slf4j.Slf4jImpl\n    # logImpl: org.apache.ibatis.logging.nologging.NoLoggingImpl\n  globalConfig:\n    # 是否打印 Logo banner\n    banner: true\n    enableSqlRunner: true\n    dbConfig:\n      # 主键类型\n      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID\n      idType: ASSIGN_ID\n      # 逻辑已删除值\n      logicDeleteValue: 1\n      # 逻辑未删除值\n      logicNotDeleteValue: 0\n      # 字段验证策略之 insert,在 insert 的时候的字段验证策略\n      # IGNORED 忽略 NOT_NULL 非NULL NOT_EMPTY 非空 DEFAULT 默认 NEVER 不加入 SQL\n      insertStrategy: NOT_NULL\n      # 字段验证策略之 update,在 update 的时候的字段验证策略\n      updateStrategy: NOT_NULL\n      # 字段验证策略之 select,在 select 的时候的字段验证策略既 wrapper 根据内部 entity 生成的 where 条件\n      where-strategy: NOT_NULL\nlogging:\n  file:\n    name: ./logs/${spring.application.name}/info.log', '6ea36abf99cb3a52978a75655ffc2cd6', '2025-03-08 10:33:10', '2025-03-08 02:33:11', NULL, '************', 'U', 'f201b8d5-2edd-4645-ae26-ff14b13093da', '');
INSERT INTO `his_config_info` VALUES (72, 360, 'application-local.yml', 'DEFAULT_GROUP', '', 'spring:\n  autoconfigure:\n    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure\n# feign 配置\nfeign:\n  sentinel:\n    enabled: true\n  okhttp:\n    enabled: true\n  httpclient:\n    enabled: false\n  client:\n    config:\n      default:\n        connectTimeout: 600000\n        readTimeout: 600000\n  compression:\n    request:\n      enabled: true\n      min-request-size: 8192\n    response:\n      enabled: true\n\n# 暴露监控端点\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: \'*\'\n\nmybatis-plus:\n  # 不支持多包, 如有需要可在注解配置 或 提升扫包等级\n  # 例如 com.**.**.mapper\n  mapperPackage: com.**.**.mapper\n  # 对应的 XML 文件位置\n  mapperLocations: classpath*:mapper/**/*Mapper.xml\n  # 实体扫描，多个package用逗号或者分号分隔\n  typeAliasesPackage: com.ruoyi.**.**,com.wanhe.**.**\n  # 启动时是否检查 MyBatis XML 文件的存在，默认不检查\n  checkConfigLocation: false\n  configuration:\n    map-underscore-to-camel-case: true\n    auto-mapping-behavior: full\n    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl\n    # 自动驼峰命名规则（camel case）映射\n    mapUnderscoreToCamelCase: true\n    # MyBatis 自动映射策略\n    # NONE：不启用 PARTIAL：只对非嵌套 resultMap 自动映射 FULL：对所有 resultMap 自动映射\n    autoMappingBehavior: PARTIAL\n    # MyBatis 自动映射时未知列或未知属性处理策\n    # NONE：不做处理 WARNING：打印相关警告 FAILING：抛出异常和详细信息\n    autoMappingUnknownColumnBehavior: NONE\n    # 更详细的日志输出 会有性能损耗 org.apache.ibatis.logging.stdout.StdOutImpl\n    # 关闭日志记录 (可单纯使用 p6spy 分析) org.apache.ibatis.logging.nologging.NoLoggingImpl\n    # 默认日志输出 org.apache.ibatis.logging.slf4j.Slf4jImpl\n    # logImpl: org.apache.ibatis.logging.nologging.NoLoggingImpl\n  globalConfig:\n    # 是否打印 Logo banner\n    banner: true\n    dbConfig:\n      # 主键类型\n      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID\n      idType: ASSIGN_ID\n      # 逻辑已删除值\n      logicDeleteValue: 1\n      # 逻辑未删除值\n      logicNotDeleteValue: 0\n      # 字段验证策略之 insert,在 insert 的时候的字段验证策略\n      # IGNORED 忽略 NOT_NULL 非NULL NOT_EMPTY 非空 DEFAULT 默认 NEVER 不加入 SQL\n      insertStrategy: NOT_NULL\n      # 字段验证策略之 update,在 update 的时候的字段验证策略\n      updateStrategy: NOT_NULL\n      # 字段验证策略之 select,在 select 的时候的字段验证策略既 wrapper 根据内部 entity 生成的 where 条件\n      where-strategy: NOT_NULL\nlogging:\n  file:\n    name: ./logs/${spring.application.name}/info.log', '60857a59d96f4fc8bf176673382d2aed', '2025-03-08 10:33:58', '2025-03-08 02:33:58', NULL, '************', 'U', 'f201b8d5-2edd-4645-ae26-ff14b13093da', '');
INSERT INTO `his_config_info` VALUES (72, 361, 'application-local.yml', 'DEFAULT_GROUP', '', 'spring:\n  autoconfigure:\n    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure\n# feign 配置\nfeign:\n  sentinel:\n    enabled: true\n  okhttp:\n    enabled: true\n  httpclient:\n    enabled: false\n  client:\n    config:\n      default:\n        connectTimeout: 600000\n        readTimeout: 600000\n  compression:\n    request:\n      enabled: true\n      min-request-size: 8192\n    response:\n      enabled: true\n\n# 暴露监控端点\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: \'*\'\n\nmybatis-plus:\n  # 不支持多包, 如有需要可在注解配置 或 提升扫包等级\n  # 例如 com.**.**.mapper\n  mapperPackage: com.**.**.mapper\n  # 对应的 XML 文件位置\n  mapperLocations: classpath*:mapper/**/*Mapper.xml\n  # 实体扫描，多个package用逗号或者分号分隔\n  typeAliasesPackage: com.ruoyi.**.**,com.wanhe.**.**\n  # 启动时是否检查 MyBatis XML 文件的存在，默认不检查\n  checkConfigLocation: false\n  configuration:\n    map-underscore-to-camel-case: true\n    auto-mapping-behavior: full\n    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl\n    # 自动驼峰命名规则（camel case）映射\n    mapUnderscoreToCamelCase: true\n    # MyBatis 自动映射策略\n    # NONE：不启用 PARTIAL：只对非嵌套 resultMap 自动映射 FULL：对所有 resultMap 自动映射\n    autoMappingBehavior: PARTIAL\n    # MyBatis 自动映射时未知列或未知属性处理策\n    # NONE：不做处理 WARNING：打印相关警告 FAILING：抛出异常和详细信息\n    autoMappingUnknownColumnBehavior: NONE\n    # 更详细的日志输出 会有性能损耗 org.apache.ibatis.logging.stdout.StdOutImpl\n    # 关闭日志记录 (可单纯使用 p6spy 分析) org.apache.ibatis.logging.nologging.NoLoggingImpl\n    # 默认日志输出 org.apache.ibatis.logging.slf4j.Slf4jImpl\n    # logImpl: org.apache.ibatis.logging.nologging.NoLoggingImpl\n  globalConfig:\n    # 是否打印 Logo banner\n    banner: true\n    enableSqlRunner: true\n    dbConfig:\n      # 主键类型\n      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID\n      idType: ASSIGN_ID\n      # 逻辑已删除值\n      logicDeleteValue: 1\n      # 逻辑未删除值\n      logicNotDeleteValue: 0\n      # 字段验证策略之 insert,在 insert 的时候的字段验证策略\n      # IGNORED 忽略 NOT_NULL 非NULL NOT_EMPTY 非空 DEFAULT 默认 NEVER 不加入 SQL\n      insertStrategy: NOT_NULL\n      # 字段验证策略之 update,在 update 的时候的字段验证策略\n      updateStrategy: NOT_NULL\n      # 字段验证策略之 select,在 select 的时候的字段验证策略既 wrapper 根据内部 entity 生成的 where 条件\n      where-strategy: NOT_NULL\nlogging:\n  file:\n    name: ./logs/${spring.application.name}/info.log', '6ea36abf99cb3a52978a75655ffc2cd6', '2025-03-10 15:26:05', '2025-03-10 07:26:06', NULL, '***************', 'U', 'f201b8d5-2edd-4645-ae26-ff14b13093da', '');
INSERT INTO `his_config_info` VALUES (1, 362, 'application-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  autoconfigure:\n    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure\n# feign 配置\nfeign:\n  sentinel:\n    enabled: true\n  okhttp:\n    enabled: true\n  httpclient:\n    enabled: false\n  client:\n    config:\n      default:\n        connectTimeout: 600000\n        readTimeout: 600000\n  compression:\n    request:\n      enabled: true\n      min-request-size: 8192\n    response:\n      enabled: true\n\n# 暴露监控端点\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: \'*\'\n\nmybatis-plus:\n  # 不支持多包, 如有需要可在注解配置 或 提升扫包等级\n  # 例如 com.**.**.mapper\n  mapperPackage: com.**.**.mapper\n  # 对应的 XML 文件位置\n  mapperLocations: classpath*:mapper/**/*Mapper.xml\n  # 实体扫描，多个package用逗号或者分号分隔\n  typeAliasesPackage: com.ruoyi.**.**,com.wanhe.**.**\n  # 启动时是否检查 MyBatis XML 文件的存在，默认不检查\n  checkConfigLocation: false\n  configuration:\n    map-underscore-to-camel-case: true\n    auto-mapping-behavior: full\n    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl\n    # 自动驼峰命名规则（camel case）映射\n    mapUnderscoreToCamelCase: true\n    # MyBatis 自动映射策略\n    # NONE：不启用 PARTIAL：只对非嵌套 resultMap 自动映射 FULL：对所有 resultMap 自动映射\n    autoMappingBehavior: PARTIAL\n    # MyBatis 自动映射时未知列或未知属性处理策\n    # NONE：不做处理 WARNING：打印相关警告 FAILING：抛出异常和详细信息\n    autoMappingUnknownColumnBehavior: NONE\n    # 更详细的日志输出 会有性能损耗 org.apache.ibatis.logging.stdout.StdOutImpl\n    # 关闭日志记录 (可单纯使用 p6spy 分析) org.apache.ibatis.logging.nologging.NoLoggingImpl\n    # 默认日志输出 org.apache.ibatis.logging.slf4j.Slf4jImpl\n    # logImpl: org.apache.ibatis.logging.nologging.NoLoggingImpl\n  globalConfig:\n    # 是否打印 Logo banner\n    banner: true\n    enableSqlRunner: true\n    dbConfig:\n      # 主键类型\n      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID\n      idType: ASSIGN_ID\n      # 逻辑已删除值\n      logicDeleteValue: 1\n      # 逻辑未删除值\n      logicNotDeleteValue: 0\n      # 字段验证策略之 insert,在 insert 的时候的字段验证策略\n      # IGNORED 忽略 NOT_NULL 非NULL NOT_EMPTY 非空 DEFAULT 默认 NEVER 不加入 SQL\n      insertStrategy: NOT_NULL\n      # 字段验证策略之 update,在 update 的时候的字段验证策略\n      updateStrategy: NOT_NULL\n      # 字段验证策略之 select,在 select 的时候的字段验证策略既 wrapper 根据内部 entity 生成的 where 条件\n      where-strategy: NOT_NULL\n\nlogging:\n  file:\n    name: ./logs/${spring.application.name}/info.log', 'f2d7aa5025fdfd65bb5069b64ac33871', '2025-03-10 15:26:20', '2025-03-10 07:26:21', NULL, '***************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (1, 363, 'application-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  autoconfigure:\n    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure\n# feign 配置\nfeign:\n  sentinel:\n    enabled: true\n  okhttp:\n    enabled: true\n  httpclient:\n    enabled: false\n  client:\n    config:\n      default:\n        connectTimeout: 600000\n        readTimeout: 600000\n  compression:\n    request:\n      enabled: true\n      min-request-size: 8192\n    response:\n      enabled: true\n\n# 暴露监控端点\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: \'*\'\n\nmybatis-plus:\n  # 不支持多包, 如有需要可在注解配置 或 提升扫包等级\n  # 例如 com.**.**.mapper\n  mapperPackage: com.**.**.mapper\n  # 对应的 XML 文件位置\n  mapperLocations: classpath*:mapper/**/*Mapper.xml\n  # 实体扫描，多个package用逗号或者分号分隔\n  typeAliasesPackage: com.ruoyi.**.**,com.wanhe.**.**\n  # 启动时是否检查 MyBatis XML 文件的存在，默认不检查\n  checkConfigLocation: false\n  configuration:\n    map-underscore-to-camel-case: true\n    auto-mapping-behavior: full\n    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl\n    # 自动驼峰命名规则（camel case）映射\n    mapUnderscoreToCamelCase: true\n    # MyBatis 自动映射策略\n    # NONE：不启用 PARTIAL：只对非嵌套 resultMap 自动映射 FULL：对所有 resultMap 自动映射\n    autoMappingBehavior: PARTIAL\n    # MyBatis 自动映射时未知列或未知属性处理策\n    # NONE：不做处理 WARNING：打印相关警告 FAILING：抛出异常和详细信息\n    autoMappingUnknownColumnBehavior: NONE\n    # 更详细的日志输出 会有性能损耗 org.apache.ibatis.logging.stdout.StdOutImpl\n    # 关闭日志记录 (可单纯使用 p6spy 分析) org.apache.ibatis.logging.nologging.NoLoggingImpl\n    # 默认日志输出 org.apache.ibatis.logging.slf4j.Slf4jImpl\n    # logImpl: org.apache.ibatis.logging.nologging.NoLoggingImpl\n  globalConfig:\n    # 是否打印 Logo banner\n    banner: true\n    enableSqlRunner: true\n    dbConfig:\n      # 主键类型\n      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID\n      idType: ASSIGN_ID\n      # 逻辑已删除值\n      logicDeleteValue: 1\n      # 逻辑未删除值\n      logicNotDeleteValue: 0\n      # 字段验证策略之 insert,在 insert 的时候的字段验证策略\n      # IGNORED 忽略 NOT_NULL 非NULL NOT_EMPTY 非空 DEFAULT 默认 NEVER 不加入 SQL\n      insertStrategy: NOT_NULL\n      # 字段验证策略之 update,在 update 的时候的字段验证策略\n      updateStrategy: NOT_NULL\n      # 字段验证策略之 select,在 select 的时候的字段验证策略既 wrapper 根据内部 entity 生成的 where 条件\n      where-strategy: NOT_NULL\nmybatis-plus-join:\n  banner: false\n  sub-table-logic: false\nlogging:\n  file:\n    name: ./logs/${spring.application.name}/info.log', 'a1d13c887ec6389349835b4913cfd6f4', '2025-03-24 16:17:31', '2025-03-24 08:17:32', NULL, '************', 'D', '', '');
INSERT INTO `his_config_info` VALUES (2, 364, 'ruoyi-gateway-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: ruoyi-auth\n          uri: lb://ruoyi-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestFilter\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 代码生成\n        - id: ruoyi-gen\n          uri: lb://ruoyi-gen\n          predicates:\n            - Path=/code/**\n          filters:\n            - StripPrefix=1\n        # 定时任务\n        - id: ruoyi-job\n          uri: lb://ruoyi-job\n          predicates:\n            - Path=/schedule/**\n          filters:\n            - StripPrefix=1\n        # 系统模块\n        - id: ruoyi-system\n          uri: lb://ruoyi-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: ruoyi-file\n          uri: lb://ruoyi-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 业务服务\n        - id: wanhe-business\n          uri: lb://wanhe-business\n          predicates:\n            - Path=/business/**\n          filters:\n            - StripPrefix=1\n        # mqtt服务\n        - id: wanhe-mqtt\n          uri: lb://wanhe-mqtt\n          predicates:\n            - Path=/mqtt/**\n          filters:\n            - StripPrefix=1\n        # 数据处理服务\n        - id: wanhe-biz-data\n          uri: lb://wanhe-biz-data\n          predicates:\n            - Path=/data/**\n          filters:\n            - StripPrefix=1\n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/*\n      - /system/dict/data/type/time_zone\n      - /system/config/configKey/*\n      - /data/inv/data/device/status\n      - /business/fault/all\n      - /business/station/device/check/sn\n      - /*/v2/api-docs\n      - /*/v3/api-docs\n      - /csrf\n\n# springdoc配置\nspringdoc:\n  webjars:\n    # 访问前缀\n    prefix:\n', '4b7d65b4cb9bfaf0b7cf10727139e2fa', '2025-03-24 16:17:34', '2025-03-24 08:17:34', NULL, '************', 'D', '', '');
INSERT INTO `his_config_info` VALUES (3, 365, 'ruoyi-auth-dev.yml', 'DEFAULT_GROUP', '', 'spring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n\n', 'aedce3073793dae44b9ea3bbe76cdc63', '2025-03-24 16:17:38', '2025-03-24 08:17:38', NULL, '************', 'D', '', '');
INSERT INTO `his_config_info` VALUES (4, 366, 'ruoyi-monitor-dev.yml', 'DEFAULT_GROUP', '', '# spring\nspring:\n  security:\n    user:\n      name: ruoyi\n      password: 123456\n  boot:\n    admin:\n      ui:\n        title: 若依服务状态监控\n', '6f122fd2bfb8d45f858e7d6529a9cd44', '2025-03-24 16:17:41', '2025-03-24 08:17:42', NULL, '************', 'D', '', '');
INSERT INTO `his_config_info` VALUES (5, 367, 'ruoyi-system-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: ***********************************************************************************************************************************************************            username: wanhe_cloud\n            password: EM8YTsC5QBQfGa1Y\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\n# mybatis:\n#     # 搜索指定包别名\n#     typeAliasesPackage: com.ruoyi.system\n#     # 配置mapper的扫描，找到所有的mapper.xml映射文件\n#     mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', 'e15d6f8f416f694780ed5b6151549105', '2025-03-24 16:17:44', '2025-03-24 08:17:44', NULL, '************', 'D', '', '');
INSERT INTO `his_config_info` VALUES (6, 368, 'ruoyi-gen-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ***********************************************************************************************************************************************************    username: wanhe_cloud\n    password: EM8YTsC5QBQfGa1Y\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.gen.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'代码生成接口文档\'\n    # 描述\n    description: \'代码生成接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\n# 代码生成\ngen:\n  # 作者\n  author: ruoyi\n  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool\n  packageName: com.ruoyi.system\n  # 自动去除表前缀，默认是false\n  autoRemovePre: false\n  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）\n  tablePrefix: sys_\n', 'da929d417d96dd9b0a1430e93fa65247', '2025-03-24 16:17:46', '2025-03-24 08:17:47', NULL, '************', 'D', '', '');
INSERT INTO `his_config_info` VALUES (7, 369, 'ruoyi-job-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ***********************************************************************************************************************************************************    username: wanhe_cloud\n    password: EM8YTsC5QBQfGa1Y\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.job.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'定时任务接口文档\'\n    # 描述\n    description: \'定时任务接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '7e104fbf450615141bf90d9bf5d8cc87', '2025-03-24 16:17:49', '2025-03-24 08:17:50', NULL, '************', 'D', '', '');
INSERT INTO `his_config_info` VALUES (8, 370, 'ruoyi-file-dev.yml', 'DEFAULT_GROUP', '', '#系统文件上传模式\nsys:\n  file:\n    # 图片上传模式 local/fdfs/minio/aliOss \n    sysFileType: aliOss  \n    compression: 0.35\n\nspring:\n  servlet:\n    multipart:\n      #单个文件的最大大小\n      max-file-size: 10MB\n\nali:\n  oss:\n    #应用key\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    #应用密钥\n    accessKeySecret: ******************************\n    #访问域名\n    host: http://whyn.oss-cn-beijing.aliyuncs.com\n    #地域\n    endpoint: oss-cn-beijing.aliyuncs.com\n    #存储桶名称\n    bucketName: whyn\n    #默认路径\n    defaultFile: wh\n    #过期时间\n    expireTime: 3600000\n    #最大长度\n    maxLength: 1048576000\n  ocr:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: ocr-api.cn-hangzhou.aliyuncs.com\n\n\n# 本地文件上传    \nfile:\n    domain: \n    path:   /uploadPath\n    prefix: /uploadPath\n\n# FastDFS配置\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minio配置\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test', '301ab63ccdf42fca66c0ee3e6ec2d08a', '2025-03-24 16:17:53', '2025-03-24 08:17:53', NULL, '***************', 'D', '', '');
INSERT INTO `his_config_info` VALUES (9, 371, 'sentinel-ruoyi-gateway', 'DEFAULT_GROUP', '', '[\r\n    {\r\n        \"resource\": \"ruoyi-auth\",\r\n        \"count\": 500,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-system\",\r\n        \"count\": 1000,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-gen\",\r\n        \"count\": 200,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-job\",\r\n        \"count\": 300,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    }\r\n]', '9f3a3069261598f74220bc47958ec252', '2025-03-24 16:17:57', '2025-03-24 08:17:57', NULL, '***************', 'D', '', '');
INSERT INTO `his_config_info` VALUES (31, 372, 'wanhe-business-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: ***********************************************************************************************************************************************************            username: wanhe_cloud\n            password: EM8YTsC5QBQfGa1Y\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\nemq:\n  # 70d64bdd0ec3cd94:2kNaHFwHuPOJinKQ8xeT8DqjUPgkiiT9BRGQGhMGPW1L\n  basic: Basic NzBkNjRiZGQwZWMzY2Q5NDoya05hSEZ3SHVQT0ppbktROHhlVDhEcWpVUGdraWlUOUJSR1FHaE1HUFcxTA==\n  url: http://**************:18083\nfault:\n  language: zh\n# mybatis配置\n# mybatis:\n#     # 搜索指定包别名\n#     typeAliasesPackage: com.ruoyi.system\n#     # 配置mapper的扫描，找到所有的mapper.xml映射文件\n#     mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip', 'a58ce5349f86eed7231b9c20f9b5fe17', '2025-03-24 16:18:00', '2025-03-24 08:18:00', NULL, '***************', 'D', '', '');
INSERT INTO `his_config_info` VALUES (32, 373, 'wanhe-biz-data-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring: \n  influx:\n    url: http://**************:8086\n    user: influxdb\n    password: 9RNmQc7F4_7IeZG6OzbZK7ijgDJ1BvLOP4n03mXd6KWZ_cMkx-Wur9xYr6uUTTDdEJ6lr5DGrPeNw4GZytfXtA==\n    database: wan_he_device_data\n    timeZone: tz(\'Asia/Shanghai\')\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: ***********************************************************************************************************************************************************            username: wanhe_cloud\n            password: EM8YTsC5QBQfGa1Y\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n  task:\n    execution:\n      pool:\n        core-size: 8\n        max-size: 200\n        keep-alive: 1200s\n      thread-name-prefix: task-pool-\nemq:\n  # 70d64bdd0ec3cd94:2kNaHFwHuPOJinKQ8xeT8DqjUPgkiiT9BRGQGhMGPW1L\n  basic: Basic NzBkNjRiZGQwZWMzY2Q5NDoya05hSEZ3SHVQT0ppbktROHhlVDhEcWpVUGdraWlUOUJSR1FHaE1HUFcxTA==\n  url: http://**************:18083\nfault:\n  language: zh\nali:\n  sms:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: dysmsapi.aliyuncs.com\n    signName: 万禾悦能\nkafka:\n  #分区数量1个\n  partition-number: 5\n  bootstrap:\n    servers: ************:9093\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n  sasl:\n    mechanism: PLAIN\n    username: alikafka_pre-cn-jmp3vby9t005\n    password: bHqQ5pptpKYLgOHBzMCHGyA3ArG2kSHg\n  ssl:\n    truststore:\n      location: \"D:\\\\home\\\\wanhe\\\\ca\\\\only.4096.client.truststore.jks\"\n  security:\n    auth:\n      login:\n        config: \"D:\\\\home\\\\wanhe\\\\ca\\\\kafka_client_jaas.conf\"\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', 'c7fa272441c33f6f0987ba0309085790', '2025-03-24 16:18:02', '2025-03-24 08:18:03', NULL, '************', 'D', '', '');
INSERT INTO `his_config_info` VALUES (33, 374, 'wanhe-mqtt-dev.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **************\n      port: 6379\n      password: hsx123456\n      database: 5\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: ***********************************************************************************************************************************************************            username: wanhe_cloud\n            password: EM8YTsC5QBQfGa1Y\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mqtt:\n    username: wanhe                       # 账号（仅用于后端自认证）\n    password: \"wanhe\"                       # 密码（仅用于后端自认证）\n    host-url: tcp://**************:1883         # 连接 Emqx 消息服务器地址\n    client-id: wanhe-iot-${random.int}                # 客户端Id，不能相同，采用随机数 ${random.value}\n    default-topic: test                     # 默认主题\n    timeout: 30                             # 超时时间\n    keepalive: 30                           # 保持连接\n    clearSession: false\n    maxInflight: 32  \n\nkafka:\n  #分区数量1个\n  partition-number: 5\n  bootstrap:\n    servers: ************:9093\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n  sasl:\n    mechanism: PLAIN\n    username: alikafka_pre-cn-jmp3vby9t005\n    password: bHqQ5pptpKYLgOHBzMCHGyA3ArG2kSHg\n  ssl:\n    truststore:\n      location: \"D:\\\\home\\\\wanhe\\\\ca\\\\only.4096.client.truststore.jks\"\n  security:\n    auth:\n      login:\n        config: \"D:\\\\home\\\\wanhe\\\\ca\\\\kafka_client_jaas.conf\"\n\n\n\n\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', 'cb6ae51d3cf9bcc5e42d1a4101551889', '2025-03-24 16:18:05', '2025-03-24 08:18:05', NULL, '************', 'D', '', '');
INSERT INTO `his_config_info` VALUES (0, 375, 'application-local.yml', 'DEFAULT_GROUP', '', 'spring:\n  autoconfigure:\n    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure\n# feign 配置\nfeign:\n  sentinel:\n    enabled: true\n  okhttp:\n    enabled: true\n  httpclient:\n    enabled: false\n  client:\n    config:\n      default:\n        connectTimeout: 600000\n        readTimeout: 600000\n  compression:\n    request:\n      enabled: true\n      min-request-size: 8192\n    response:\n      enabled: true\n\n# 暴露监控端点\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: \'*\'\n\nmybatis-plus:\n  # 不支持多包, 如有需要可在注解配置 或 提升扫包等级\n  # 例如 com.**.**.mapper\n  mapperPackage: com.**.**.mapper\n  # 对应的 XML 文件位置\n  mapperLocations: classpath*:mapper/**/*Mapper.xml\n  # 实体扫描，多个package用逗号或者分号分隔\n  typeAliasesPackage: com.ruoyi.**.**,com.wanhe.**.**\n  # 启动时是否检查 MyBatis XML 文件的存在，默认不检查\n  checkConfigLocation: false\n  configuration:\n    map-underscore-to-camel-case: true\n    auto-mapping-behavior: full\n    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl\n    # 自动驼峰命名规则（camel case）映射\n    mapUnderscoreToCamelCase: true\n    # MyBatis 自动映射策略\n    # NONE：不启用 PARTIAL：只对非嵌套 resultMap 自动映射 FULL：对所有 resultMap 自动映射\n    autoMappingBehavior: PARTIAL\n    # MyBatis 自动映射时未知列或未知属性处理策\n    # NONE：不做处理 WARNING：打印相关警告 FAILING：抛出异常和详细信息\n    autoMappingUnknownColumnBehavior: NONE\n    # 更详细的日志输出 会有性能损耗 org.apache.ibatis.logging.stdout.StdOutImpl\n    # 关闭日志记录 (可单纯使用 p6spy 分析) org.apache.ibatis.logging.nologging.NoLoggingImpl\n    # 默认日志输出 org.apache.ibatis.logging.slf4j.Slf4jImpl\n    # logImpl: org.apache.ibatis.logging.nologging.NoLoggingImpl\n  globalConfig:\n    # 是否打印 Logo banner\n    banner: true\n    enableSqlRunner: true\n    dbConfig:\n      # 主键类型\n      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID\n      idType: ASSIGN_ID\n      # 逻辑已删除值\n      logicDeleteValue: 1\n      # 逻辑未删除值\n      logicNotDeleteValue: 0\n      # 字段验证策略之 insert,在 insert 的时候的字段验证策略\n      # IGNORED 忽略 NOT_NULL 非NULL NOT_EMPTY 非空 DEFAULT 默认 NEVER 不加入 SQL\n      insertStrategy: NOT_NULL\n      # 字段验证策略之 update,在 update 的时候的字段验证策略\n      updateStrategy: NOT_NULL\n      # 字段验证策略之 select,在 select 的时候的字段验证策略既 wrapper 根据内部 entity 生成的 where 条件\n      where-strategy: NOT_NULL\nmybatis-plus-join:\n  banner: false\n  sub-table-logic: false\nlogging:\n  file:\n    name: ./logs/${spring.application.name}/info.log', 'a1d13c887ec6389349835b4913cfd6f4', '2025-03-24 16:32:56', '2025-03-24 08:32:56', '', '************', 'I', '0d8f014d-b030-4467-be18-f86b1c11a27b', '');
INSERT INTO `his_config_info` VALUES (0, 376, 'ruoyi-auth-local.yml', 'DEFAULT_GROUP', '', 'spring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8', '769ae644a7bc2fc8291edc31ded394c8', '2025-03-24 16:32:56', '2025-03-24 08:32:56', '', '************', 'I', '0d8f014d-b030-4467-be18-f86b1c11a27b', '');
INSERT INTO `his_config_info` VALUES (0, 377, 'ruoyi-file-local.yml', 'DEFAULT_GROUP', '', '#系统文件上传模式\nsys:\n  file:\n    # 图片上传模式 local/fdfs/minio/aliOss \n    sysFileType: aliOss  \n    compression: 0.35\n\nspring:\n  servlet:\n    multipart:\n      #单个文件的最大大小\n      max-file-size: 10MB\n\nali:\n  oss:\n    #应用key\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    #应用密钥\n    accessKeySecret: ******************************\n    #访问域名\n    host: http://whyn.oss-cn-beijing.aliyuncs.com\n    #地域\n    endpoint: oss-cn-beijing.aliyuncs.com\n    #存储桶名称\n    bucketName: whyn\n    #默认路径\n    defaultFile: wh\n    #过期时间\n    expireTime: 3600000\n    #最大长度\n    maxLength: 1048576000\n  ocr:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: ocr-api.cn-hangzhou.aliyuncs.com\n\n\n# 本地文件上传    \nfile:\n    domain: \n    path:   /uploadPath\n    prefix: /uploadPath\n\n# FastDFS配置\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minio配置\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test', '301ab63ccdf42fca66c0ee3e6ec2d08a', '2025-03-24 16:32:56', '2025-03-24 08:32:56', '', '************', 'I', '0d8f014d-b030-4467-be18-f86b1c11a27b', '');
INSERT INTO `his_config_info` VALUES (0, 378, 'ruoyi-gateway-local.yml', 'DEFAULT_GROUP', '', 'spring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: ruoyi-auth\n          uri: lb://ruoyi-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestFilter\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 代码生成\n        - id: ruoyi-gen\n          uri: lb://ruoyi-gen\n          predicates:\n            - Path=/code/**\n          filters:\n            - StripPrefix=1\n        # 定时任务\n        - id: ruoyi-job\n          uri: lb://ruoyi-job\n          predicates:\n            - Path=/schedule/**\n          filters:\n            - StripPrefix=1\n        # 系统模块\n        - id: ruoyi-system\n          uri: lb://ruoyi-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: ruoyi-file\n          uri: lb://ruoyi-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 业务服务\n        - id: wanhe-business\n          uri: lb://wanhe-business\n          predicates:\n            - Path=/business/**\n          filters:\n            - StripPrefix=1\n        # mqtt服务\n        - id: wanhe-mqtt\n          uri: lb://wanhe-mqtt\n          predicates:\n            - Path=/mqtt/**\n          filters:\n            - StripPrefix=1\n        # 数据处理服务\n        - id: wanhe-biz-data\n          uri: lb://wanhe-biz-data\n          predicates:\n            - Path=/data/**\n          filters:\n            - StripPrefix=1\n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/*\n      - /system/dict/data/type/time_zone\n      - /system/config/configKey/*\n      - /data/inv/data/device/status\n      - /business/fault/all\n      - /business/station/device/check/sn\n      - /*/v2/api-docs\n      - /*/v3/api-docs\n      - /csrf\n\n# springdoc配置\nspringdoc:\n  webjars:\n    # 访问前缀\n    prefix:\n', 'acdeafe385f475e3b0bc7069e8656ee8', '2025-03-24 16:32:56', '2025-03-24 08:32:56', '', '************', 'I', '0d8f014d-b030-4467-be18-f86b1c11a27b', '');
INSERT INTO `his_config_info` VALUES (0, 379, 'ruoyi-gen-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n    username: wanhe\n    password: 55kPKFMaYjW5aaYb\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.gen.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'代码生成接口文档\'\n    # 描述\n    description: \'代码生成接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\n# 代码生成\ngen:\n  # 作者\n  author: ruoyi\n  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool\n  packageName: com.ruoyi.system\n  # 自动去除表前缀，默认是false\n  autoRemovePre: false\n  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）\n  tablePrefix: sys_\n', '4d3f4930cf461ae95701938f1f44ab07', '2025-03-24 16:32:56', '2025-03-24 08:32:56', '', '************', 'I', '0d8f014d-b030-4467-be18-f86b1c11a27b', '');
INSERT INTO `his_config_info` VALUES (0, 380, 'ruoyi-job-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n    username: wanhe\n    password: 55kPKFMaYjW5aaYb\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.job.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'定时任务接口文档\'\n    # 描述\n    description: \'定时任务接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '312e536272fba0cd842861510ba9eb16', '2025-03-24 16:32:56', '2025-03-24 08:32:56', '', '************', 'I', '0d8f014d-b030-4467-be18-f86b1c11a27b', '');
INSERT INTO `his_config_info` VALUES (0, 381, 'ruoyi-monitor-local.yml', 'DEFAULT_GROUP', '', '# spring\nspring:\n  security:\n    user:\n      name: ruoyi\n      password: 123456\n  boot:\n    admin:\n      ui:\n        title: 若依服务状态监控\n', '6f122fd2bfb8d45f858e7d6529a9cd44', '2025-03-24 16:32:56', '2025-03-24 08:32:56', '', '************', 'I', '0d8f014d-b030-4467-be18-f86b1c11a27b', '');
INSERT INTO `his_config_info` VALUES (0, 382, 'ruoyi-system-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\n# mybatis:\n#     # 搜索指定包别名\n#     typeAliasesPackage: com.ruoyi.system\n#     # 配置mapper的扫描，找到所有的mapper.xml映射文件\n#     mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', 'bf1d064c62947498ed54af7f70b6194a', '2025-03-24 16:32:56', '2025-03-24 08:32:56', '', '************', 'I', '0d8f014d-b030-4467-be18-f86b1c11a27b', '');
INSERT INTO `his_config_info` VALUES (0, 383, 'sentinel-ruoyi-gateway', 'DEFAULT_GROUP', '', '[\r\n    {\r\n        \"resource\": \"ruoyi-auth\",\r\n        \"count\": 500,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-system\",\r\n        \"count\": 1000,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-gen\",\r\n        \"count\": 200,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-job\",\r\n        \"count\": 300,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    }\r\n]', '9f3a3069261598f74220bc47958ec252', '2025-03-24 16:32:56', '2025-03-24 08:32:56', '', '************', 'I', '0d8f014d-b030-4467-be18-f86b1c11a27b', '');
INSERT INTO `his_config_info` VALUES (0, 384, 'wanhe-biz-data-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring: \n  influx:\n    url: http://***********:8086\n    user: influxdb\n    password: riAi9Y1aefJonscBxFqvcNUIubsCchw39dqwIKGnfjPR5IdskAYSjqLRy4vy7Kgiak16t_o6dt1pP-ROJnXGfg==\n    database: wan_he_device_data\n    timeZone: tz(\'Asia/Shanghai\')\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n  task:\n    execution:\n      pool:\n        core-size: 8\n        max-size: 200\n        keep-alive: 1200s\n      thread-name-prefix: task-pool-\nemq:\n  # 25c941e678c502a4:v3e71a3Bw0k2WKgkwvhbyOYrDm4omsq7TQlImta01xP\n  basic: Basic MjVjOTQxZTY3OGM1MDJhNDp2M2U3MWEzQncwazJXS2drd3ZoYnlPWXJEbTRvbXNxN1RRbEltdGEwMXhQ\n  url: http://***********:18083\nfault:\n  language: zh\nali:\n  sms:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: dysmsapi.aliyuncs.com\n    signName: 万禾悦能\nkafka:\n  #分区数量1个\n  partition-number: 5\n  bootstrap:\n    servers: ************:9093\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n  sasl:\n    mechanism: PLAIN\n    username: alikafka_pre-cn-jmp3vby9t005\n    password: bHqQ5pptpKYLgOHBzMCHGyA3ArG2kSHg\n  ssl:\n    truststore:\n      location: \"D:\\\\home\\\\wanhe\\\\ca\\\\only.4096.client.truststore.jks\"\n  security:\n    auth:\n      login:\n        config: \"D:\\\\home\\\\wanhe\\\\ca\\\\kafka_client_jaas.conf\"\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '5076280b268760c8d7e9bc28971bc608', '2025-03-24 16:32:56', '2025-03-24 08:32:56', '', '************', 'I', '0d8f014d-b030-4467-be18-f86b1c11a27b', '');
INSERT INTO `his_config_info` VALUES (0, 385, 'wanhe-business-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\nemq:\n  # 25c941e678c502a4:v3e71a3Bw0k2WKgkwvhbyOYrDm4omsq7TQlImta01xP\n  basic: Basic MjVjOTQxZTY3OGM1MDJhNDp2M2U3MWEzQncwazJXS2drd3ZoYnlPWXJEbTRvbXNxN1RRbEltdGEwMXhQ\n  url: http://***********:18083\nfault:\n  language: zh\n# mybatis配置\n# mybatis:\n#     # 搜索指定包别名\n#     typeAliasesPackage: com.ruoyi.system\n#     # 配置mapper的扫描，找到所有的mapper.xml映射文件\n#     mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip', 'a9d119631319af8dca1ae74ce39d5919', '2025-03-24 16:32:56', '2025-03-24 08:32:56', '', '************', 'I', '0d8f014d-b030-4467-be18-f86b1c11a27b', '');
INSERT INTO `his_config_info` VALUES (0, 386, 'wanhe-mqtt-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mqtt:\n    username: admin                       # 账号（仅用于后端自认证）\n    password: public                       # 密码（仅用于后端自认证）\n    host-url: tcp://************:1883         # 连接 Emqx 消息服务器地址\n    client-id: wanhe-iot-${random.int}                # 客户端Id，不能相同，采用随机数 ${random.value}\n    default-topic: test                     # 默认主题\n    timeout: 30                             # 超时时间\n    keepalive: 30                           # 保持连接\n    clearSession: false\n    maxInflight: 32\n\nkafka:\n  #分区数量1个\n  partition-number: 5\n  bootstrap:\n    servers: ************:9093\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n  sasl:\n    mechanism: PLAIN\n    username: alikafka_pre-cn-jmp3vby9t005\n    password: bHqQ5pptpKYLgOHBzMCHGyA3ArG2kSHg\n  ssl:\n    truststore:\n      location: \"D:\\\\home\\\\wanhe\\\\ca\\\\only.4096.client.truststore.jks\"\n  security:\n    auth:\n      login:\n        config: \"D:\\\\home\\\\wanhe\\\\ca\\\\kafka_client_jaas.conf\"\n\n\n\n\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '7cf47a40a6da6ed2bf145ed823148b5c', '2025-03-24 16:32:56', '2025-03-24 08:32:56', '', '************', 'I', '0d8f014d-b030-4467-be18-f86b1c11a27b', '');
INSERT INTO `his_config_info` VALUES (0, 387, 'application-local.yml', 'DEFAULT_GROUP', '', 'spring:\n  autoconfigure:\n    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure\n# feign 配置\nfeign:\n  sentinel:\n    enabled: true\n  okhttp:\n    enabled: true\n  httpclient:\n    enabled: false\n  client:\n    config:\n      default:\n        connectTimeout: 600000\n        readTimeout: 600000\n  compression:\n    request:\n      enabled: true\n      min-request-size: 8192\n    response:\n      enabled: true\n\n# 暴露监控端点\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: \'*\'\n\nmybatis-plus:\n  # 不支持多包, 如有需要可在注解配置 或 提升扫包等级\n  # 例如 com.**.**.mapper\n  mapperPackage: com.**.**.mapper\n  # 对应的 XML 文件位置\n  mapperLocations: classpath*:mapper/**/*Mapper.xml\n  # 实体扫描，多个package用逗号或者分号分隔\n  typeAliasesPackage: com.ruoyi.**.**,com.wanhe.**.**\n  # 启动时是否检查 MyBatis XML 文件的存在，默认不检查\n  checkConfigLocation: false\n  configuration:\n    map-underscore-to-camel-case: true\n    auto-mapping-behavior: full\n    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl\n    # 自动驼峰命名规则（camel case）映射\n    mapUnderscoreToCamelCase: true\n    # MyBatis 自动映射策略\n    # NONE：不启用 PARTIAL：只对非嵌套 resultMap 自动映射 FULL：对所有 resultMap 自动映射\n    autoMappingBehavior: PARTIAL\n    # MyBatis 自动映射时未知列或未知属性处理策\n    # NONE：不做处理 WARNING：打印相关警告 FAILING：抛出异常和详细信息\n    autoMappingUnknownColumnBehavior: NONE\n    # 更详细的日志输出 会有性能损耗 org.apache.ibatis.logging.stdout.StdOutImpl\n    # 关闭日志记录 (可单纯使用 p6spy 分析) org.apache.ibatis.logging.nologging.NoLoggingImpl\n    # 默认日志输出 org.apache.ibatis.logging.slf4j.Slf4jImpl\n    # logImpl: org.apache.ibatis.logging.nologging.NoLoggingImpl\n  globalConfig:\n    # 是否打印 Logo banner\n    banner: true\n    enableSqlRunner: true\n    dbConfig:\n      # 主键类型\n      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID\n      idType: ASSIGN_ID\n      # 逻辑已删除值\n      logicDeleteValue: 1\n      # 逻辑未删除值\n      logicNotDeleteValue: 0\n      # 字段验证策略之 insert,在 insert 的时候的字段验证策略\n      # IGNORED 忽略 NOT_NULL 非NULL NOT_EMPTY 非空 DEFAULT 默认 NEVER 不加入 SQL\n      insertStrategy: NOT_NULL\n      # 字段验证策略之 update,在 update 的时候的字段验证策略\n      updateStrategy: NOT_NULL\n      # 字段验证策略之 select,在 select 的时候的字段验证策略既 wrapper 根据内部 entity 生成的 where 条件\n      where-strategy: NOT_NULL\nmybatis-plus-join:\n  banner: false\n  sub-table-logic: false\nlogging:\n  file:\n    name: ./logs/${spring.application.name}/info.log', 'a1d13c887ec6389349835b4913cfd6f4', '2025-03-24 16:35:52', '2025-03-24 08:35:52', '', '************', 'I', '', '');
INSERT INTO `his_config_info` VALUES (0, 388, 'ruoyi-auth-local.yml', 'DEFAULT_GROUP', '', 'spring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8', '769ae644a7bc2fc8291edc31ded394c8', '2025-03-24 16:35:52', '2025-03-24 08:35:52', '', '************', 'I', '', '');
INSERT INTO `his_config_info` VALUES (0, 389, 'ruoyi-file-local.yml', 'DEFAULT_GROUP', '', '#系统文件上传模式\nsys:\n  file:\n    # 图片上传模式 local/fdfs/minio/aliOss \n    sysFileType: aliOss  \n    compression: 0.35\n\nspring:\n  servlet:\n    multipart:\n      #单个文件的最大大小\n      max-file-size: 10MB\n\nali:\n  oss:\n    #应用key\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    #应用密钥\n    accessKeySecret: ******************************\n    #访问域名\n    host: http://whyn.oss-cn-beijing.aliyuncs.com\n    #地域\n    endpoint: oss-cn-beijing.aliyuncs.com\n    #存储桶名称\n    bucketName: whyn\n    #默认路径\n    defaultFile: wh\n    #过期时间\n    expireTime: 3600000\n    #最大长度\n    maxLength: 1048576000\n  ocr:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: ocr-api.cn-hangzhou.aliyuncs.com\n\n\n# 本地文件上传    \nfile:\n    domain: \n    path:   /uploadPath\n    prefix: /uploadPath\n\n# FastDFS配置\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minio配置\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test', '301ab63ccdf42fca66c0ee3e6ec2d08a', '2025-03-24 16:35:52', '2025-03-24 08:35:52', '', '************', 'I', '', '');
INSERT INTO `his_config_info` VALUES (0, 390, 'ruoyi-gateway-local.yml', 'DEFAULT_GROUP', '', 'spring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: ruoyi-auth\n          uri: lb://ruoyi-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestFilter\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 代码生成\n        - id: ruoyi-gen\n          uri: lb://ruoyi-gen\n          predicates:\n            - Path=/code/**\n          filters:\n            - StripPrefix=1\n        # 定时任务\n        - id: ruoyi-job\n          uri: lb://ruoyi-job\n          predicates:\n            - Path=/schedule/**\n          filters:\n            - StripPrefix=1\n        # 系统模块\n        - id: ruoyi-system\n          uri: lb://ruoyi-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: ruoyi-file\n          uri: lb://ruoyi-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 业务服务\n        - id: wanhe-business\n          uri: lb://wanhe-business\n          predicates:\n            - Path=/business/**\n          filters:\n            - StripPrefix=1\n        # mqtt服务\n        - id: wanhe-mqtt\n          uri: lb://wanhe-mqtt\n          predicates:\n            - Path=/mqtt/**\n          filters:\n            - StripPrefix=1\n        # 数据处理服务\n        - id: wanhe-biz-data\n          uri: lb://wanhe-biz-data\n          predicates:\n            - Path=/data/**\n          filters:\n            - StripPrefix=1\n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/*\n      - /system/dict/data/type/time_zone\n      - /system/config/configKey/*\n      - /data/inv/data/device/status\n      - /business/fault/all\n      - /business/station/device/check/sn\n      - /*/v2/api-docs\n      - /*/v3/api-docs\n      - /csrf\n\n# springdoc配置\nspringdoc:\n  webjars:\n    # 访问前缀\n    prefix:\n', 'acdeafe385f475e3b0bc7069e8656ee8', '2025-03-24 16:35:52', '2025-03-24 08:35:52', '', '************', 'I', '', '');
INSERT INTO `his_config_info` VALUES (0, 391, 'ruoyi-gen-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n    username: wanhe\n    password: 55kPKFMaYjW5aaYb\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.gen.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'代码生成接口文档\'\n    # 描述\n    description: \'代码生成接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\n# 代码生成\ngen:\n  # 作者\n  author: ruoyi\n  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool\n  packageName: com.ruoyi.system\n  # 自动去除表前缀，默认是false\n  autoRemovePre: false\n  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）\n  tablePrefix: sys_\n', '4d3f4930cf461ae95701938f1f44ab07', '2025-03-24 16:35:52', '2025-03-24 08:35:52', '', '************', 'I', '', '');
INSERT INTO `his_config_info` VALUES (0, 392, 'ruoyi-job-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n    username: wanhe\n    password: 55kPKFMaYjW5aaYb\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.job.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'定时任务接口文档\'\n    # 描述\n    description: \'定时任务接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '312e536272fba0cd842861510ba9eb16', '2025-03-24 16:35:52', '2025-03-24 08:35:52', '', '************', 'I', '', '');
INSERT INTO `his_config_info` VALUES (0, 393, 'ruoyi-monitor-local.yml', 'DEFAULT_GROUP', '', '# spring\nspring:\n  security:\n    user:\n      name: ruoyi\n      password: 123456\n  boot:\n    admin:\n      ui:\n        title: 若依服务状态监控\n', '6f122fd2bfb8d45f858e7d6529a9cd44', '2025-03-24 16:35:52', '2025-03-24 08:35:52', '', '************', 'I', '', '');
INSERT INTO `his_config_info` VALUES (0, 394, 'ruoyi-system-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\n# mybatis:\n#     # 搜索指定包别名\n#     typeAliasesPackage: com.ruoyi.system\n#     # 配置mapper的扫描，找到所有的mapper.xml映射文件\n#     mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', 'bf1d064c62947498ed54af7f70b6194a', '2025-03-24 16:35:52', '2025-03-24 08:35:52', '', '************', 'I', '', '');
INSERT INTO `his_config_info` VALUES (0, 395, 'sentinel-ruoyi-gateway', 'DEFAULT_GROUP', '', '[\r\n    {\r\n        \"resource\": \"ruoyi-auth\",\r\n        \"count\": 500,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-system\",\r\n        \"count\": 1000,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-gen\",\r\n        \"count\": 200,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-job\",\r\n        \"count\": 300,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    }\r\n]', '9f3a3069261598f74220bc47958ec252', '2025-03-24 16:35:52', '2025-03-24 08:35:52', '', '************', 'I', '', '');
INSERT INTO `his_config_info` VALUES (0, 396, 'wanhe-biz-data-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring: \n  influx:\n    url: http://***********:8086\n    user: influxdb\n    password: riAi9Y1aefJonscBxFqvcNUIubsCchw39dqwIKGnfjPR5IdskAYSjqLRy4vy7Kgiak16t_o6dt1pP-ROJnXGfg==\n    database: wan_he_device_data\n    timeZone: tz(\'Asia/Shanghai\')\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n  task:\n    execution:\n      pool:\n        core-size: 8\n        max-size: 200\n        keep-alive: 1200s\n      thread-name-prefix: task-pool-\nemq:\n  # 25c941e678c502a4:v3e71a3Bw0k2WKgkwvhbyOYrDm4omsq7TQlImta01xP\n  basic: Basic MjVjOTQxZTY3OGM1MDJhNDp2M2U3MWEzQncwazJXS2drd3ZoYnlPWXJEbTRvbXNxN1RRbEltdGEwMXhQ\n  url: http://***********:18083\nfault:\n  language: zh\nali:\n  sms:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: dysmsapi.aliyuncs.com\n    signName: 万禾悦能\nkafka:\n  #分区数量1个\n  partition-number: 5\n  bootstrap:\n    servers: ************:9093\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n  sasl:\n    mechanism: PLAIN\n    username: alikafka_pre-cn-jmp3vby9t005\n    password: bHqQ5pptpKYLgOHBzMCHGyA3ArG2kSHg\n  ssl:\n    truststore:\n      location: \"D:\\\\home\\\\wanhe\\\\ca\\\\only.4096.client.truststore.jks\"\n  security:\n    auth:\n      login:\n        config: \"D:\\\\home\\\\wanhe\\\\ca\\\\kafka_client_jaas.conf\"\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '5076280b268760c8d7e9bc28971bc608', '2025-03-24 16:35:52', '2025-03-24 08:35:52', '', '************', 'I', '', '');
INSERT INTO `his_config_info` VALUES (0, 397, 'wanhe-business-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\nemq:\n  # 25c941e678c502a4:v3e71a3Bw0k2WKgkwvhbyOYrDm4omsq7TQlImta01xP\n  basic: Basic MjVjOTQxZTY3OGM1MDJhNDp2M2U3MWEzQncwazJXS2drd3ZoYnlPWXJEbTRvbXNxN1RRbEltdGEwMXhQ\n  url: http://***********:18083\nfault:\n  language: zh\n# mybatis配置\n# mybatis:\n#     # 搜索指定包别名\n#     typeAliasesPackage: com.ruoyi.system\n#     # 配置mapper的扫描，找到所有的mapper.xml映射文件\n#     mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip', 'a9d119631319af8dca1ae74ce39d5919', '2025-03-24 16:35:52', '2025-03-24 08:35:52', '', '************', 'I', '', '');
INSERT INTO `his_config_info` VALUES (0, 398, 'wanhe-mqtt-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mqtt:\n    username: admin                       # 账号（仅用于后端自认证）\n    password: public                       # 密码（仅用于后端自认证）\n    host-url: tcp://************:1883         # 连接 Emqx 消息服务器地址\n    client-id: wanhe-iot-${random.int}                # 客户端Id，不能相同，采用随机数 ${random.value}\n    default-topic: test                     # 默认主题\n    timeout: 30                             # 超时时间\n    keepalive: 30                           # 保持连接\n    clearSession: false\n    maxInflight: 32\n\nkafka:\n  #分区数量1个\n  partition-number: 5\n  bootstrap:\n    servers: ************:9093\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n  sasl:\n    mechanism: PLAIN\n    username: alikafka_pre-cn-jmp3vby9t005\n    password: bHqQ5pptpKYLgOHBzMCHGyA3ArG2kSHg\n  ssl:\n    truststore:\n      location: \"D:\\\\home\\\\wanhe\\\\ca\\\\only.4096.client.truststore.jks\"\n  security:\n    auth:\n      login:\n        config: \"D:\\\\home\\\\wanhe\\\\ca\\\\kafka_client_jaas.conf\"\n\n\n\n\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '7cf47a40a6da6ed2bf145ed823148b5c', '2025-03-24 16:35:52', '2025-03-24 08:35:52', '', '************', 'I', '', '');
INSERT INTO `his_config_info` VALUES (96, 399, 'application-local.yml', 'DEFAULT_GROUP', '', 'spring:\n  autoconfigure:\n    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure\n# feign 配置\nfeign:\n  sentinel:\n    enabled: true\n  okhttp:\n    enabled: true\n  httpclient:\n    enabled: false\n  client:\n    config:\n      default:\n        connectTimeout: 600000\n        readTimeout: 600000\n  compression:\n    request:\n      enabled: true\n      min-request-size: 8192\n    response:\n      enabled: true\n\n# 暴露监控端点\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: \'*\'\n\nmybatis-plus:\n  # 不支持多包, 如有需要可在注解配置 或 提升扫包等级\n  # 例如 com.**.**.mapper\n  mapperPackage: com.**.**.mapper\n  # 对应的 XML 文件位置\n  mapperLocations: classpath*:mapper/**/*Mapper.xml\n  # 实体扫描，多个package用逗号或者分号分隔\n  typeAliasesPackage: com.ruoyi.**.**,com.wanhe.**.**\n  # 启动时是否检查 MyBatis XML 文件的存在，默认不检查\n  checkConfigLocation: false\n  configuration:\n    map-underscore-to-camel-case: true\n    auto-mapping-behavior: full\n    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl\n    # 自动驼峰命名规则（camel case）映射\n    mapUnderscoreToCamelCase: true\n    # MyBatis 自动映射策略\n    # NONE：不启用 PARTIAL：只对非嵌套 resultMap 自动映射 FULL：对所有 resultMap 自动映射\n    autoMappingBehavior: PARTIAL\n    # MyBatis 自动映射时未知列或未知属性处理策\n    # NONE：不做处理 WARNING：打印相关警告 FAILING：抛出异常和详细信息\n    autoMappingUnknownColumnBehavior: NONE\n    # 更详细的日志输出 会有性能损耗 org.apache.ibatis.logging.stdout.StdOutImpl\n    # 关闭日志记录 (可单纯使用 p6spy 分析) org.apache.ibatis.logging.nologging.NoLoggingImpl\n    # 默认日志输出 org.apache.ibatis.logging.slf4j.Slf4jImpl\n    # logImpl: org.apache.ibatis.logging.nologging.NoLoggingImpl\n  globalConfig:\n    # 是否打印 Logo banner\n    banner: true\n    enableSqlRunner: true\n    dbConfig:\n      # 主键类型\n      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID\n      idType: ASSIGN_ID\n      # 逻辑已删除值\n      logicDeleteValue: 1\n      # 逻辑未删除值\n      logicNotDeleteValue: 0\n      # 字段验证策略之 insert,在 insert 的时候的字段验证策略\n      # IGNORED 忽略 NOT_NULL 非NULL NOT_EMPTY 非空 DEFAULT 默认 NEVER 不加入 SQL\n      insertStrategy: NOT_NULL\n      # 字段验证策略之 update,在 update 的时候的字段验证策略\n      updateStrategy: NOT_NULL\n      # 字段验证策略之 select,在 select 的时候的字段验证策略既 wrapper 根据内部 entity 生成的 where 条件\n      where-strategy: NOT_NULL\nmybatis-plus-join:\n  banner: false\n  sub-table-logic: false\nlogging:\n  file:\n    name: ./logs/${spring.application.name}/info.log', 'a1d13c887ec6389349835b4913cfd6f4', '2025-03-24 16:36:07', '2025-03-24 08:36:07', NULL, '************', 'D', '', '');
INSERT INTO `his_config_info` VALUES (97, 400, 'ruoyi-auth-local.yml', 'DEFAULT_GROUP', '', 'spring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8', '769ae644a7bc2fc8291edc31ded394c8', '2025-03-24 16:36:07', '2025-03-24 08:36:07', NULL, '************', 'D', '', '');
INSERT INTO `his_config_info` VALUES (98, 401, 'ruoyi-file-local.yml', 'DEFAULT_GROUP', '', '#系统文件上传模式\nsys:\n  file:\n    # 图片上传模式 local/fdfs/minio/aliOss \n    sysFileType: aliOss  \n    compression: 0.35\n\nspring:\n  servlet:\n    multipart:\n      #单个文件的最大大小\n      max-file-size: 10MB\n\nali:\n  oss:\n    #应用key\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    #应用密钥\n    accessKeySecret: ******************************\n    #访问域名\n    host: http://whyn.oss-cn-beijing.aliyuncs.com\n    #地域\n    endpoint: oss-cn-beijing.aliyuncs.com\n    #存储桶名称\n    bucketName: whyn\n    #默认路径\n    defaultFile: wh\n    #过期时间\n    expireTime: 3600000\n    #最大长度\n    maxLength: 1048576000\n  ocr:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: ocr-api.cn-hangzhou.aliyuncs.com\n\n\n# 本地文件上传    \nfile:\n    domain: \n    path:   /uploadPath\n    prefix: /uploadPath\n\n# FastDFS配置\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minio配置\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test', '301ab63ccdf42fca66c0ee3e6ec2d08a', '2025-03-24 16:36:07', '2025-03-24 08:36:07', NULL, '************', 'D', '', '');
INSERT INTO `his_config_info` VALUES (99, 402, 'ruoyi-gateway-local.yml', 'DEFAULT_GROUP', '', 'spring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: ruoyi-auth\n          uri: lb://ruoyi-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestFilter\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 代码生成\n        - id: ruoyi-gen\n          uri: lb://ruoyi-gen\n          predicates:\n            - Path=/code/**\n          filters:\n            - StripPrefix=1\n        # 定时任务\n        - id: ruoyi-job\n          uri: lb://ruoyi-job\n          predicates:\n            - Path=/schedule/**\n          filters:\n            - StripPrefix=1\n        # 系统模块\n        - id: ruoyi-system\n          uri: lb://ruoyi-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: ruoyi-file\n          uri: lb://ruoyi-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 业务服务\n        - id: wanhe-business\n          uri: lb://wanhe-business\n          predicates:\n            - Path=/business/**\n          filters:\n            - StripPrefix=1\n        # mqtt服务\n        - id: wanhe-mqtt\n          uri: lb://wanhe-mqtt\n          predicates:\n            - Path=/mqtt/**\n          filters:\n            - StripPrefix=1\n        # 数据处理服务\n        - id: wanhe-biz-data\n          uri: lb://wanhe-biz-data\n          predicates:\n            - Path=/data/**\n          filters:\n            - StripPrefix=1\n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/*\n      - /system/dict/data/type/time_zone\n      - /system/config/configKey/*\n      - /data/inv/data/device/status\n      - /business/fault/all\n      - /business/station/device/check/sn\n      - /*/v2/api-docs\n      - /*/v3/api-docs\n      - /csrf\n\n# springdoc配置\nspringdoc:\n  webjars:\n    # 访问前缀\n    prefix:\n', 'acdeafe385f475e3b0bc7069e8656ee8', '2025-03-24 16:36:07', '2025-03-24 08:36:07', NULL, '************', 'D', '', '');
INSERT INTO `his_config_info` VALUES (100, 403, 'ruoyi-gen-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n    username: wanhe\n    password: 55kPKFMaYjW5aaYb\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.gen.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'代码生成接口文档\'\n    # 描述\n    description: \'代码生成接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\n# 代码生成\ngen:\n  # 作者\n  author: ruoyi\n  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool\n  packageName: com.ruoyi.system\n  # 自动去除表前缀，默认是false\n  autoRemovePre: false\n  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）\n  tablePrefix: sys_\n', '4d3f4930cf461ae95701938f1f44ab07', '2025-03-24 16:36:07', '2025-03-24 08:36:07', NULL, '************', 'D', '', '');
INSERT INTO `his_config_info` VALUES (101, 404, 'ruoyi-job-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n    username: wanhe\n    password: 55kPKFMaYjW5aaYb\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.job.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'定时任务接口文档\'\n    # 描述\n    description: \'定时任务接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '312e536272fba0cd842861510ba9eb16', '2025-03-24 16:36:07', '2025-03-24 08:36:07', NULL, '************', 'D', '', '');
INSERT INTO `his_config_info` VALUES (102, 405, 'ruoyi-monitor-local.yml', 'DEFAULT_GROUP', '', '# spring\nspring:\n  security:\n    user:\n      name: ruoyi\n      password: 123456\n  boot:\n    admin:\n      ui:\n        title: 若依服务状态监控\n', '6f122fd2bfb8d45f858e7d6529a9cd44', '2025-03-24 16:36:07', '2025-03-24 08:36:07', NULL, '************', 'D', '', '');
INSERT INTO `his_config_info` VALUES (103, 406, 'ruoyi-system-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\n# mybatis:\n#     # 搜索指定包别名\n#     typeAliasesPackage: com.ruoyi.system\n#     # 配置mapper的扫描，找到所有的mapper.xml映射文件\n#     mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', 'bf1d064c62947498ed54af7f70b6194a', '2025-03-24 16:36:07', '2025-03-24 08:36:07', NULL, '************', 'D', '', '');
INSERT INTO `his_config_info` VALUES (104, 407, 'sentinel-ruoyi-gateway', 'DEFAULT_GROUP', '', '[\r\n    {\r\n        \"resource\": \"ruoyi-auth\",\r\n        \"count\": 500,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-system\",\r\n        \"count\": 1000,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-gen\",\r\n        \"count\": 200,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-job\",\r\n        \"count\": 300,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    }\r\n]', '9f3a3069261598f74220bc47958ec252', '2025-03-24 16:36:07', '2025-03-24 08:36:07', NULL, '************', 'D', '', '');
INSERT INTO `his_config_info` VALUES (105, 408, 'wanhe-biz-data-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring: \n  influx:\n    url: http://***********:8086\n    user: influxdb\n    password: riAi9Y1aefJonscBxFqvcNUIubsCchw39dqwIKGnfjPR5IdskAYSjqLRy4vy7Kgiak16t_o6dt1pP-ROJnXGfg==\n    database: wan_he_device_data\n    timeZone: tz(\'Asia/Shanghai\')\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n  task:\n    execution:\n      pool:\n        core-size: 8\n        max-size: 200\n        keep-alive: 1200s\n      thread-name-prefix: task-pool-\nemq:\n  # 25c941e678c502a4:v3e71a3Bw0k2WKgkwvhbyOYrDm4omsq7TQlImta01xP\n  basic: Basic MjVjOTQxZTY3OGM1MDJhNDp2M2U3MWEzQncwazJXS2drd3ZoYnlPWXJEbTRvbXNxN1RRbEltdGEwMXhQ\n  url: http://***********:18083\nfault:\n  language: zh\nali:\n  sms:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: dysmsapi.aliyuncs.com\n    signName: 万禾悦能\nkafka:\n  #分区数量1个\n  partition-number: 5\n  bootstrap:\n    servers: ************:9093\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n  sasl:\n    mechanism: PLAIN\n    username: alikafka_pre-cn-jmp3vby9t005\n    password: bHqQ5pptpKYLgOHBzMCHGyA3ArG2kSHg\n  ssl:\n    truststore:\n      location: \"D:\\\\home\\\\wanhe\\\\ca\\\\only.4096.client.truststore.jks\"\n  security:\n    auth:\n      login:\n        config: \"D:\\\\home\\\\wanhe\\\\ca\\\\kafka_client_jaas.conf\"\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '5076280b268760c8d7e9bc28971bc608', '2025-03-24 16:36:07', '2025-03-24 08:36:07', NULL, '************', 'D', '', '');
INSERT INTO `his_config_info` VALUES (106, 409, 'wanhe-business-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\nemq:\n  # 25c941e678c502a4:v3e71a3Bw0k2WKgkwvhbyOYrDm4omsq7TQlImta01xP\n  basic: Basic MjVjOTQxZTY3OGM1MDJhNDp2M2U3MWEzQncwazJXS2drd3ZoYnlPWXJEbTRvbXNxN1RRbEltdGEwMXhQ\n  url: http://***********:18083\nfault:\n  language: zh\n# mybatis配置\n# mybatis:\n#     # 搜索指定包别名\n#     typeAliasesPackage: com.ruoyi.system\n#     # 配置mapper的扫描，找到所有的mapper.xml映射文件\n#     mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip', 'a9d119631319af8dca1ae74ce39d5919', '2025-03-24 16:36:12', '2025-03-24 08:36:12', NULL, '************', 'D', '', '');
INSERT INTO `his_config_info` VALUES (107, 410, 'wanhe-mqtt-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mqtt:\n    username: admin                       # 账号（仅用于后端自认证）\n    password: public                       # 密码（仅用于后端自认证）\n    host-url: tcp://************:1883         # 连接 Emqx 消息服务器地址\n    client-id: wanhe-iot-${random.int}                # 客户端Id，不能相同，采用随机数 ${random.value}\n    default-topic: test                     # 默认主题\n    timeout: 30                             # 超时时间\n    keepalive: 30                           # 保持连接\n    clearSession: false\n    maxInflight: 32\n\nkafka:\n  #分区数量1个\n  partition-number: 5\n  bootstrap:\n    servers: ************:9093\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n  sasl:\n    mechanism: PLAIN\n    username: alikafka_pre-cn-jmp3vby9t005\n    password: bHqQ5pptpKYLgOHBzMCHGyA3ArG2kSHg\n  ssl:\n    truststore:\n      location: \"D:\\\\home\\\\wanhe\\\\ca\\\\only.4096.client.truststore.jks\"\n  security:\n    auth:\n      login:\n        config: \"D:\\\\home\\\\wanhe\\\\ca\\\\kafka_client_jaas.conf\"\n\n\n\n\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '7cf47a40a6da6ed2bf145ed823148b5c', '2025-03-24 16:36:12', '2025-03-24 08:36:12', NULL, '************', 'D', '', '');
INSERT INTO `his_config_info` VALUES (94, 411, 'wanhe-business-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\nemq:\n  # 25c941e678c502a4:v3e71a3Bw0k2WKgkwvhbyOYrDm4omsq7TQlImta01xP\n  basic: Basic MjVjOTQxZTY3OGM1MDJhNDp2M2U3MWEzQncwazJXS2drd3ZoYnlPWXJEbTRvbXNxN1RRbEltdGEwMXhQ\n  url: http://***********:18083\nfault:\n  language: zh\n# mybatis配置\n# mybatis:\n#     # 搜索指定包别名\n#     typeAliasesPackage: com.ruoyi.system\n#     # 配置mapper的扫描，找到所有的mapper.xml映射文件\n#     mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip', 'a9d119631319af8dca1ae74ce39d5919', '2025-03-24 17:04:04', '2025-03-24 09:04:04', NULL, '************', 'U', '0d8f014d-b030-4467-be18-f86b1c11a27b', '');
INSERT INTO `his_config_info` VALUES (95, 412, 'wanhe-mqtt-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mqtt:\n    username: admin                       # 账号（仅用于后端自认证）\n    password: public                       # 密码（仅用于后端自认证）\n    host-url: tcp://************:1883         # 连接 Emqx 消息服务器地址\n    client-id: wanhe-iot-${random.int}                # 客户端Id，不能相同，采用随机数 ${random.value}\n    default-topic: test                     # 默认主题\n    timeout: 30                             # 超时时间\n    keepalive: 30                           # 保持连接\n    clearSession: false\n    maxInflight: 32\n\nkafka:\n  #分区数量1个\n  partition-number: 5\n  bootstrap:\n    servers: ************:9093\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n  sasl:\n    mechanism: PLAIN\n    username: alikafka_pre-cn-jmp3vby9t005\n    password: bHqQ5pptpKYLgOHBzMCHGyA3ArG2kSHg\n  ssl:\n    truststore:\n      location: \"D:\\\\home\\\\wanhe\\\\ca\\\\only.4096.client.truststore.jks\"\n  security:\n    auth:\n      login:\n        config: \"D:\\\\home\\\\wanhe\\\\ca\\\\kafka_client_jaas.conf\"\n\n\n\n\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '7cf47a40a6da6ed2bf145ed823148b5c', '2025-03-24 17:16:12', '2025-03-24 09:16:13', NULL, '************', 'U', '0d8f014d-b030-4467-be18-f86b1c11a27b', '');
INSERT INTO `his_config_info` VALUES (93, 413, 'wanhe-biz-data-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring: \n  influx:\n    url: http://***********:8086\n    user: influxdb\n    password: riAi9Y1aefJonscBxFqvcNUIubsCchw39dqwIKGnfjPR5IdskAYSjqLRy4vy7Kgiak16t_o6dt1pP-ROJnXGfg==\n    database: wan_he_device_data\n    timeZone: tz(\'Asia/Shanghai\')\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n  task:\n    execution:\n      pool:\n        core-size: 8\n        max-size: 200\n        keep-alive: 1200s\n      thread-name-prefix: task-pool-\nemq:\n  # 25c941e678c502a4:v3e71a3Bw0k2WKgkwvhbyOYrDm4omsq7TQlImta01xP\n  basic: Basic MjVjOTQxZTY3OGM1MDJhNDp2M2U3MWEzQncwazJXS2drd3ZoYnlPWXJEbTRvbXNxN1RRbEltdGEwMXhQ\n  url: http://***********:18083\nfault:\n  language: zh\nali:\n  sms:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: dysmsapi.aliyuncs.com\n    signName: 万禾悦能\nkafka:\n  #分区数量1个\n  partition-number: 5\n  bootstrap:\n    servers: ************:9093\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n  sasl:\n    mechanism: PLAIN\n    username: alikafka_pre-cn-jmp3vby9t005\n    password: bHqQ5pptpKYLgOHBzMCHGyA3ArG2kSHg\n  ssl:\n    truststore:\n      location: \"D:\\\\home\\\\wanhe\\\\ca\\\\only.4096.client.truststore.jks\"\n  security:\n    auth:\n      login:\n        config: \"D:\\\\home\\\\wanhe\\\\ca\\\\kafka_client_jaas.conf\"\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '5076280b268760c8d7e9bc28971bc608', '2025-03-24 22:33:57', '2025-03-24 14:33:57', NULL, '**************', 'U', '0d8f014d-b030-4467-be18-f86b1c11a27b', '');
INSERT INTO `his_config_info` VALUES (94, 414, 'wanhe-business-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\nemq:\n  # 25c941e678c502a4:v3e71a3Bw0k2WKgkwvhbyOYrDm4omsq7TQlImta01xP\n  basic: Basic MjVjOTQxZTY3OGM1MDJhNDp2M2U3MWEzQncwazJXS2drd3ZoYnlPWXJEbTRvbXNxN1RRbEltdGEwMXhQ\n  url: http://***********:18083\nfault:\n  language: en\n# mybatis配置\n# mybatis:\n#     # 搜索指定包别名\n#     typeAliasesPackage: com.ruoyi.system\n#     # 配置mapper的扫描，找到所有的mapper.xml映射文件\n#     mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip', 'e008dc813c5e59cc7b2e08ed7f9a752c', '2025-03-25 11:09:37', '2025-03-25 03:09:38', NULL, '**************', 'U', '0d8f014d-b030-4467-be18-f86b1c11a27b', '');
INSERT INTO `his_config_info` VALUES (93, 415, 'wanhe-biz-data-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring: \n  influx:\n    url: http://***********:8086\n    user: influxdb\n    password: riAi9Y1aefJonscBxFqvcNUIubsCchw39dqwIKGnfjPR5IdskAYSjqLRy4vy7Kgiak16t_o6dt1pP-ROJnXGfg==\n    database: wan_he_device_data\n    # timeZone: tz(\'Asia/Shanghai\')\n    timeZone: tz(\'Europe/Berlin\')\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n  task:\n    execution:\n      pool:\n        core-size: 8\n        max-size: 200\n        keep-alive: 1200s\n      thread-name-prefix: task-pool-\nemq:\n  # 25c941e678c502a4:v3e71a3Bw0k2WKgkwvhbyOYrDm4omsq7TQlImta01xP\n  basic: Basic MjVjOTQxZTY3OGM1MDJhNDp2M2U3MWEzQncwazJXS2drd3ZoYnlPWXJEbTRvbXNxN1RRbEltdGEwMXhQ\n  url: http://***********:18083\nfault:\n  # language: zh\n  language: en\nali:\n  sms:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: dysmsapi.aliyuncs.com\n    signName: 万禾悦能\nkafka:\n  partition-number: 1\n  bootstrap:\n    servers: *************:9092\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', 'bd97f52d48d4b85d37ae7616ef612ade', '2025-03-25 11:10:02', '2025-03-25 03:10:03', NULL, '**************', 'U', '0d8f014d-b030-4467-be18-f86b1c11a27b', '');
INSERT INTO `his_config_info` VALUES (95, 416, 'wanhe-mqtt-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mqtt:\n    username: admin                       # 账号（仅用于后端自认证）\n    password: public                       # 密码（仅用于后端自认证）\n    host-url: tcp://*************:1883         # 连接 Emqx 消息服务器地址\n    client-id: wanhe-iot-${random.int}                # 客户端Id，不能相同，采用随机数 ${random.value}\n    default-topic: test                     # 默认主题\n    timeout: 30                             # 超时时间\n    keepalive: 30                           # 保持连接\n    clearSession: false\n    maxInflight: 32\n\nkafka:\n  #分区数量1个\n  partition-number: 5\n  bootstrap:\n    servers: *************:9093\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '94e41083340aed29f413717ee134f8f7', '2025-03-25 11:36:43', '2025-03-25 03:36:44', NULL, '**************', 'U', '0d8f014d-b030-4467-be18-f86b1c11a27b', '');
INSERT INTO `his_config_info` VALUES (95, 417, 'wanhe-mqtt-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mqtt:\n    username: admin                       # 账号（仅用于后端自认证）\n    password: public                       # 密码（仅用于后端自认证）\n    host-url: tcp://*************:1883         # 连接 Emqx 消息服务器地址\n    client-id: wanhe-iot-${random.int}                # 客户端Id，不能相同，采用随机数 ${random.value}\n    default-topic: test                     # 默认主题\n    timeout: 30                             # 超时时间\n    keepalive: 30                           # 保持连接\n    clearSession: false\n    maxInflight: 32\n\nkafka:\n  #分区数量1个\n  partition-number: 10\n  bootstrap:\n    servers: *************:9093\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', 'ed9fd403da6937ebbee5d3f564181918', '2025-03-25 11:40:02', '2025-03-25 03:40:02', NULL, '**************', 'U', '0d8f014d-b030-4467-be18-f86b1c11a27b', '');
INSERT INTO `his_config_info` VALUES (0, 418, 'application-prod.yml', 'DEFAULT_GROUP', '', 'spring:\n  autoconfigure:\n    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure\n# feign 配置\nfeign:\n  sentinel:\n    enabled: true\n  okhttp:\n    enabled: true\n  httpclient:\n    enabled: false\n  client:\n    config:\n      default:\n        connectTimeout: 600000\n        readTimeout: 600000\n  compression:\n    request:\n      enabled: true\n      min-request-size: 8192\n    response:\n      enabled: true\n\n# 暴露监控端点\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: \'*\'\n\nmybatis-plus:\n  # 不支持多包, 如有需要可在注解配置 或 提升扫包等级\n  # 例如 com.**.**.mapper\n  mapperPackage: com.**.**.mapper\n  # 对应的 XML 文件位置\n  mapperLocations: classpath*:mapper/**/*Mapper.xml\n  # 实体扫描，多个package用逗号或者分号分隔\n  typeAliasesPackage: com.ruoyi.**.**,com.wanhe.**.**\n  # 启动时是否检查 MyBatis XML 文件的存在，默认不检查\n  checkConfigLocation: false\n  configuration:\n    map-underscore-to-camel-case: true\n    auto-mapping-behavior: full\n    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl\n    # 自动驼峰命名规则（camel case）映射\n    mapUnderscoreToCamelCase: true\n    # MyBatis 自动映射策略\n    # NONE：不启用 PARTIAL：只对非嵌套 resultMap 自动映射 FULL：对所有 resultMap 自动映射\n    autoMappingBehavior: PARTIAL\n    # MyBatis 自动映射时未知列或未知属性处理策\n    # NONE：不做处理 WARNING：打印相关警告 FAILING：抛出异常和详细信息\n    autoMappingUnknownColumnBehavior: NONE\n    # 更详细的日志输出 会有性能损耗 org.apache.ibatis.logging.stdout.StdOutImpl\n    # 关闭日志记录 (可单纯使用 p6spy 分析) org.apache.ibatis.logging.nologging.NoLoggingImpl\n    # 默认日志输出 org.apache.ibatis.logging.slf4j.Slf4jImpl\n    # logImpl: org.apache.ibatis.logging.nologging.NoLoggingImpl\n  globalConfig:\n    # 是否打印 Logo banner\n    banner: true\n    enableSqlRunner: true\n    dbConfig:\n      # 主键类型\n      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID\n      idType: ASSIGN_ID\n      # 逻辑已删除值\n      logicDeleteValue: 1\n      # 逻辑未删除值\n      logicNotDeleteValue: 0\n      # 字段验证策略之 insert,在 insert 的时候的字段验证策略\n      # IGNORED 忽略 NOT_NULL 非NULL NOT_EMPTY 非空 DEFAULT 默认 NEVER 不加入 SQL\n      insertStrategy: NOT_NULL\n      # 字段验证策略之 update,在 update 的时候的字段验证策略\n      updateStrategy: NOT_NULL\n      # 字段验证策略之 select,在 select 的时候的字段验证策略既 wrapper 根据内部 entity 生成的 where 条件\n      where-strategy: NOT_NULL\nmybatis-plus-join:\n  banner: false\n  sub-table-logic: false\nlogging:\n  file:\n    name: ./logs/${spring.application.name}/info.log', 'a1d13c887ec6389349835b4913cfd6f4', '2025-03-25 11:55:48', '2025-03-25 03:55:49', '', '***************', 'I', '', '');
INSERT INTO `his_config_info` VALUES (0, 419, 'ruoyi-auth-prod.yml', 'DEFAULT_GROUP', '', 'spring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8', '769ae644a7bc2fc8291edc31ded394c8', '2025-03-25 11:55:48', '2025-03-25 03:55:49', '', '***************', 'I', '', '');
INSERT INTO `his_config_info` VALUES (0, 420, 'ruoyi-file-prod.yml', 'DEFAULT_GROUP', '', '#系统文件上传模式\nsys:\n  file:\n    # 图片上传模式 local/fdfs/minio/aliOss \n    sysFileType: aliOss  \n    compression: 0.35\n\nspring:\n  servlet:\n    multipart:\n      #单个文件的最大大小\n      max-file-size: 10MB\n\nali:\n  oss:\n    #应用key\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    #应用密钥\n    accessKeySecret: ******************************\n    #访问域名\n    host: http://whyn.oss-cn-beijing.aliyuncs.com\n    #地域\n    endpoint: oss-cn-beijing.aliyuncs.com\n    #存储桶名称\n    bucketName: whyn\n    #默认路径\n    defaultFile: wh\n    #过期时间\n    expireTime: 3600000\n    #最大长度\n    maxLength: 1048576000\n  ocr:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: ocr-api.cn-hangzhou.aliyuncs.com\n\n\n# 本地文件上传    \nfile:\n    domain: \n    path:   /uploadPath\n    prefix: /uploadPath\n\n# FastDFS配置\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minio配置\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test', '301ab63ccdf42fca66c0ee3e6ec2d08a', '2025-03-25 11:55:48', '2025-03-25 03:55:49', '', '***************', 'I', '', '');
INSERT INTO `his_config_info` VALUES (0, 421, 'ruoyi-gateway-prod.yml', 'DEFAULT_GROUP', '', 'spring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: ruoyi-auth\n          uri: lb://ruoyi-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestFilter\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 代码生成\n        - id: ruoyi-gen\n          uri: lb://ruoyi-gen\n          predicates:\n            - Path=/code/**\n          filters:\n            - StripPrefix=1\n        # 定时任务\n        - id: ruoyi-job\n          uri: lb://ruoyi-job\n          predicates:\n            - Path=/schedule/**\n          filters:\n            - StripPrefix=1\n        # 系统模块\n        - id: ruoyi-system\n          uri: lb://ruoyi-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: ruoyi-file\n          uri: lb://ruoyi-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 业务服务\n        - id: wanhe-business\n          uri: lb://wanhe-business\n          predicates:\n            - Path=/business/**\n          filters:\n            - StripPrefix=1\n        # mqtt服务\n        - id: wanhe-mqtt\n          uri: lb://wanhe-mqtt\n          predicates:\n            - Path=/mqtt/**\n          filters:\n            - StripPrefix=1\n        # 数据处理服务\n        - id: wanhe-biz-data\n          uri: lb://wanhe-biz-data\n          predicates:\n            - Path=/data/**\n          filters:\n            - StripPrefix=1\n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/*\n      - /system/dict/data/type/time_zone\n      - /system/config/configKey/*\n      - /data/inv/data/device/status\n      - /business/fault/all\n      - /business/station/device/check/sn\n      - /*/v2/api-docs\n      - /*/v3/api-docs\n      - /csrf\n\n# springdoc配置\nspringdoc:\n  webjars:\n    # 访问前缀\n    prefix:\n', 'acdeafe385f475e3b0bc7069e8656ee8', '2025-03-25 11:55:48', '2025-03-25 03:55:49', '', '***************', 'I', '', '');
INSERT INTO `his_config_info` VALUES (0, 422, 'ruoyi-gen-prod.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n    username: wanhe\n    password: 55kPKFMaYjW5aaYb\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.gen.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'代码生成接口文档\'\n    # 描述\n    description: \'代码生成接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\n# 代码生成\ngen:\n  # 作者\n  author: ruoyi\n  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool\n  packageName: com.ruoyi.system\n  # 自动去除表前缀，默认是false\n  autoRemovePre: false\n  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）\n  tablePrefix: sys_\n', '4d3f4930cf461ae95701938f1f44ab07', '2025-03-25 11:55:48', '2025-03-25 03:55:49', '', '***************', 'I', '', '');
INSERT INTO `his_config_info` VALUES (0, 423, 'ruoyi-job-prod.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n    username: wanhe\n    password: 55kPKFMaYjW5aaYb\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.job.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'定时任务接口文档\'\n    # 描述\n    description: \'定时任务接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '312e536272fba0cd842861510ba9eb16', '2025-03-25 11:55:48', '2025-03-25 03:55:49', '', '***************', 'I', '', '');
INSERT INTO `his_config_info` VALUES (0, 424, 'ruoyi-monitor-prod.yml', 'DEFAULT_GROUP', '', '# spring\nspring:\n  security:\n    user:\n      name: ruoyi\n      password: 123456\n  boot:\n    admin:\n      ui:\n        title: 若依服务状态监控\n', '6f122fd2bfb8d45f858e7d6529a9cd44', '2025-03-25 11:55:48', '2025-03-25 03:55:49', '', '***************', 'I', '', '');
INSERT INTO `his_config_info` VALUES (0, 425, 'ruoyi-system-prod.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\n# mybatis:\n#     # 搜索指定包别名\n#     typeAliasesPackage: com.ruoyi.system\n#     # 配置mapper的扫描，找到所有的mapper.xml映射文件\n#     mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', 'bf1d064c62947498ed54af7f70b6194a', '2025-03-25 11:55:48', '2025-03-25 03:55:49', '', '***************', 'I', '', '');
INSERT INTO `his_config_info` VALUES (0, 426, 'sentinel-ruoyi-gateway', 'DEFAULT_GROUP', '', '[\r\n    {\r\n        \"resource\": \"ruoyi-auth\",\r\n        \"count\": 500,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-system\",\r\n        \"count\": 1000,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-gen\",\r\n        \"count\": 200,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-job\",\r\n        \"count\": 300,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    }\r\n]', '9f3a3069261598f74220bc47958ec252', '2025-03-25 11:55:48', '2025-03-25 03:55:49', '', '***************', 'I', '', '');
INSERT INTO `his_config_info` VALUES (0, 427, 'wanhe-biz-data-prod.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring: \n  influx:\n    url: http://***********:8086\n    user: influxdb\n    password: riAi9Y1aefJonscBxFqvcNUIubsCchw39dqwIKGnfjPR5IdskAYSjqLRy4vy7Kgiak16t_o6dt1pP-ROJnXGfg==\n    database: wan_he_device_data\n    # timeZone: tz(\'Asia/Shanghai\')\n    timeZone: tz(\'Europe/Berlin\')\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n  task:\n    execution:\n      pool:\n        core-size: 8\n        max-size: 200\n        keep-alive: 1200s\n      thread-name-prefix: task-pool-\nemq:\n  # 25c941e678c502a4:oGRUEeUlkWnPnQieNoqGUhXLfaewzQYiJwvA1QqSZWL\n  basic: Basic MjVjOTQxZTY3OGM1MDJhNDpvR1JVRWVVbGtXblBuUWllTm9xR1VoWExmYWV3elFZaUp3dkExUXFTWldM\n  url: http://***********:18083\nfault:\n  # language: zh\n  language: en\nali:\n  sms:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: dysmsapi.aliyuncs.com\n    signName: 万禾悦能\nkafka:\n  partition-number: 1\n  bootstrap:\n    servers: *************:9092\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', 'b805091c682a9bbb1f824687d33da7bb', '2025-03-25 11:55:48', '2025-03-25 03:55:49', '', '***************', 'I', '', '');
INSERT INTO `his_config_info` VALUES (0, 428, 'wanhe-business-prod.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\nemq:\n  # 25c941e678c502a4:oGRUEeUlkWnPnQieNoqGUhXLfaewzQYiJwvA1QqSZWL\n  basic: Basic MjVjOTQxZTY3OGM1MDJhNDpvR1JVRWVVbGtXblBuUWllTm9xR1VoWExmYWV3elFZaUp3dkExUXFTWldM\n  url: http://***********:18083\nfault:\n  language: en\n# mybatis配置\n# mybatis:\n#     # 搜索指定包别名\n#     typeAliasesPackage: com.ruoyi.system\n#     # 配置mapper的扫描，找到所有的mapper.xml映射文件\n#     mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip', '6c4a6878aaf481e40991dd0e296cf7c3', '2025-03-25 11:55:48', '2025-03-25 03:55:49', '', '***************', 'I', '', '');
INSERT INTO `his_config_info` VALUES (0, 429, 'wanhe-mqtt-prod.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mqtt:\n    username: admin                       # 账号（仅用于后端自认证）\n    password: public                       # 密码（仅用于后端自认证）\n    host-url: tcp://*************:1883         # 连接 Emqx 消息服务器地址\n    client-id: wanhe-iot-${random.int}                # 客户端Id，不能相同，采用随机数 ${random.value}\n    default-topic: test                     # 默认主题\n    timeout: 30                             # 超时时间\n    keepalive: 30                           # 保持连接\n    clearSession: false\n    maxInflight: 32\n\nkafka:\n  #分区数量1个\n  partition-number: 10\n  bootstrap:\n    servers: *************:9092\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', 'd5440661bcba249e8ebca3da96c458a8', '2025-03-25 11:55:48', '2025-03-25 03:55:49', '', '***************', 'I', '', '');
INSERT INTO `his_config_info` VALUES (113, 430, 'ruoyi-job-prod.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n    username: wanhe\n    password: 55kPKFMaYjW5aaYb\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.job.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'定时任务接口文档\'\n    # 描述\n    description: \'定时任务接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '312e536272fba0cd842861510ba9eb16', '2025-03-25 14:03:49', '2025-03-25 06:03:49', '', '***************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (108, 431, 'application-prod.yml', 'DEFAULT_GROUP', '', 'spring:\n  autoconfigure:\n    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure\n# feign 配置\nfeign:\n  sentinel:\n    enabled: true\n  okhttp:\n    enabled: true\n  httpclient:\n    enabled: false\n  client:\n    config:\n      default:\n        connectTimeout: 600000\n        readTimeout: 600000\n  compression:\n    request:\n      enabled: true\n      min-request-size: 8192\n    response:\n      enabled: true\n\n# 暴露监控端点\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: \'*\'\n\nmybatis-plus:\n  # 不支持多包, 如有需要可在注解配置 或 提升扫包等级\n  # 例如 com.**.**.mapper\n  mapperPackage: com.**.**.mapper\n  # 对应的 XML 文件位置\n  mapperLocations: classpath*:mapper/**/*Mapper.xml\n  # 实体扫描，多个package用逗号或者分号分隔\n  typeAliasesPackage: com.ruoyi.**.**,com.wanhe.**.**\n  # 启动时是否检查 MyBatis XML 文件的存在，默认不检查\n  checkConfigLocation: false\n  configuration:\n    map-underscore-to-camel-case: true\n    auto-mapping-behavior: full\n    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl\n    # 自动驼峰命名规则（camel case）映射\n    mapUnderscoreToCamelCase: true\n    # MyBatis 自动映射策略\n    # NONE：不启用 PARTIAL：只对非嵌套 resultMap 自动映射 FULL：对所有 resultMap 自动映射\n    autoMappingBehavior: PARTIAL\n    # MyBatis 自动映射时未知列或未知属性处理策\n    # NONE：不做处理 WARNING：打印相关警告 FAILING：抛出异常和详细信息\n    autoMappingUnknownColumnBehavior: NONE\n    # 更详细的日志输出 会有性能损耗 org.apache.ibatis.logging.stdout.StdOutImpl\n    # 关闭日志记录 (可单纯使用 p6spy 分析) org.apache.ibatis.logging.nologging.NoLoggingImpl\n    # 默认日志输出 org.apache.ibatis.logging.slf4j.Slf4jImpl\n    # logImpl: org.apache.ibatis.logging.nologging.NoLoggingImpl\n  globalConfig:\n    # 是否打印 Logo banner\n    banner: true\n    enableSqlRunner: true\n    dbConfig:\n      # 主键类型\n      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID\n      idType: ASSIGN_ID\n      # 逻辑已删除值\n      logicDeleteValue: 1\n      # 逻辑未删除值\n      logicNotDeleteValue: 0\n      # 字段验证策略之 insert,在 insert 的时候的字段验证策略\n      # IGNORED 忽略 NOT_NULL 非NULL NOT_EMPTY 非空 DEFAULT 默认 NEVER 不加入 SQL\n      insertStrategy: NOT_NULL\n      # 字段验证策略之 update,在 update 的时候的字段验证策略\n      updateStrategy: NOT_NULL\n      # 字段验证策略之 select,在 select 的时候的字段验证策略既 wrapper 根据内部 entity 生成的 where 条件\n      where-strategy: NOT_NULL\nmybatis-plus-join:\n  banner: false\n  sub-table-logic: false\nlogging:\n  file:\n    name: ./logs/${spring.application.name}/info.log', 'a1d13c887ec6389349835b4913cfd6f4', '2025-03-25 14:03:49', '2025-03-25 06:03:49', '', '***************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (109, 432, 'ruoyi-auth-prod.yml', 'DEFAULT_GROUP', '', 'spring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8', '769ae644a7bc2fc8291edc31ded394c8', '2025-03-25 14:03:49', '2025-03-25 06:03:49', '', '***************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (110, 433, 'ruoyi-file-prod.yml', 'DEFAULT_GROUP', '', '#系统文件上传模式\nsys:\n  file:\n    # 图片上传模式 local/fdfs/minio/aliOss \n    sysFileType: aliOss  \n    compression: 0.35\n\nspring:\n  servlet:\n    multipart:\n      #单个文件的最大大小\n      max-file-size: 10MB\n\nali:\n  oss:\n    #应用key\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    #应用密钥\n    accessKeySecret: ******************************\n    #访问域名\n    host: http://whyn.oss-cn-beijing.aliyuncs.com\n    #地域\n    endpoint: oss-cn-beijing.aliyuncs.com\n    #存储桶名称\n    bucketName: whyn\n    #默认路径\n    defaultFile: wh\n    #过期时间\n    expireTime: 3600000\n    #最大长度\n    maxLength: 1048576000\n  ocr:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: ocr-api.cn-hangzhou.aliyuncs.com\n\n\n# 本地文件上传    \nfile:\n    domain: \n    path:   /uploadPath\n    prefix: /uploadPath\n\n# FastDFS配置\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minio配置\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test', '301ab63ccdf42fca66c0ee3e6ec2d08a', '2025-03-25 14:03:49', '2025-03-25 06:03:49', '', '***************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (111, 434, 'ruoyi-gateway-prod.yml', 'DEFAULT_GROUP', '', 'spring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: ruoyi-auth\n          uri: lb://ruoyi-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestFilter\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 代码生成\n        - id: ruoyi-gen\n          uri: lb://ruoyi-gen\n          predicates:\n            - Path=/code/**\n          filters:\n            - StripPrefix=1\n        # 定时任务\n        - id: ruoyi-job\n          uri: lb://ruoyi-job\n          predicates:\n            - Path=/schedule/**\n          filters:\n            - StripPrefix=1\n        # 系统模块\n        - id: ruoyi-system\n          uri: lb://ruoyi-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: ruoyi-file\n          uri: lb://ruoyi-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 业务服务\n        - id: wanhe-business\n          uri: lb://wanhe-business\n          predicates:\n            - Path=/business/**\n          filters:\n            - StripPrefix=1\n        # mqtt服务\n        - id: wanhe-mqtt\n          uri: lb://wanhe-mqtt\n          predicates:\n            - Path=/mqtt/**\n          filters:\n            - StripPrefix=1\n        # 数据处理服务\n        - id: wanhe-biz-data\n          uri: lb://wanhe-biz-data\n          predicates:\n            - Path=/data/**\n          filters:\n            - StripPrefix=1\n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/*\n      - /system/dict/data/type/time_zone\n      - /system/config/configKey/*\n      - /data/inv/data/device/status\n      - /business/fault/all\n      - /business/station/device/check/sn\n      - /*/v2/api-docs\n      - /*/v3/api-docs\n      - /csrf\n\n# springdoc配置\nspringdoc:\n  webjars:\n    # 访问前缀\n    prefix:\n', 'acdeafe385f475e3b0bc7069e8656ee8', '2025-03-25 14:03:49', '2025-03-25 06:03:49', '', '***************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (112, 435, 'ruoyi-gen-prod.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n    username: wanhe\n    password: 55kPKFMaYjW5aaYb\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.gen.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'代码生成接口文档\'\n    # 描述\n    description: \'代码生成接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\n# 代码生成\ngen:\n  # 作者\n  author: ruoyi\n  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool\n  packageName: com.ruoyi.system\n  # 自动去除表前缀，默认是false\n  autoRemovePre: false\n  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）\n  tablePrefix: sys_\n', '4d3f4930cf461ae95701938f1f44ab07', '2025-03-25 14:03:49', '2025-03-25 06:03:49', '', '***************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (119, 436, 'wanhe-mqtt-prod.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mqtt:\n    username: admin                       # 账号（仅用于后端自认证）\n    password: public                       # 密码（仅用于后端自认证）\n    host-url: tcp://*************:1883         # 连接 Emqx 消息服务器地址\n    client-id: wanhe-iot-${random.int}                # 客户端Id，不能相同，采用随机数 ${random.value}\n    default-topic: test                     # 默认主题\n    timeout: 30                             # 超时时间\n    keepalive: 30                           # 保持连接\n    clearSession: false\n    maxInflight: 32\n\nkafka:\n  #分区数量1个\n  partition-number: 10\n  bootstrap:\n    servers: *************:9092\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', 'd5440661bcba249e8ebca3da96c458a8', '2025-03-25 14:03:49', '2025-03-25 06:03:49', '', '***************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (114, 437, 'ruoyi-monitor-prod.yml', 'DEFAULT_GROUP', '', '# spring\nspring:\n  security:\n    user:\n      name: ruoyi\n      password: 123456\n  boot:\n    admin:\n      ui:\n        title: 若依服务状态监控\n', '6f122fd2bfb8d45f858e7d6529a9cd44', '2025-03-25 14:03:49', '2025-03-25 06:03:49', '', '***************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (115, 438, 'ruoyi-system-prod.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\n# mybatis:\n#     # 搜索指定包别名\n#     typeAliasesPackage: com.ruoyi.system\n#     # 配置mapper的扫描，找到所有的mapper.xml映射文件\n#     mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', 'bf1d064c62947498ed54af7f70b6194a', '2025-03-25 14:03:49', '2025-03-25 06:03:49', '', '***************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (116, 439, 'sentinel-ruoyi-gateway', 'DEFAULT_GROUP', '', '[\r\n    {\r\n        \"resource\": \"ruoyi-auth\",\r\n        \"count\": 500,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-system\",\r\n        \"count\": 1000,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-gen\",\r\n        \"count\": 200,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-job\",\r\n        \"count\": 300,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    }\r\n]', '9f3a3069261598f74220bc47958ec252', '2025-03-25 14:03:49', '2025-03-25 06:03:49', '', '***************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (117, 440, 'wanhe-biz-data-prod.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring: \n  influx:\n    url: http://***********:8086\n    user: influxdb\n    password: riAi9Y1aefJonscBxFqvcNUIubsCchw39dqwIKGnfjPR5IdskAYSjqLRy4vy7Kgiak16t_o6dt1pP-ROJnXGfg==\n    database: wan_he_device_data\n    # timeZone: tz(\'Asia/Shanghai\')\n    timeZone: tz(\'Europe/Berlin\')\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n  task:\n    execution:\n      pool:\n        core-size: 8\n        max-size: 200\n        keep-alive: 1200s\n      thread-name-prefix: task-pool-\nemq:\n  # 25c941e678c502a4:oGRUEeUlkWnPnQieNoqGUhXLfaewzQYiJwvA1QqSZWL\n  basic: Basic MjVjOTQxZTY3OGM1MDJhNDpvR1JVRWVVbGtXblBuUWllTm9xR1VoWExmYWV3elFZaUp3dkExUXFTWldM\n  url: http://***********:18083\nfault:\n  # language: zh\n  language: en\nali:\n  sms:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: dysmsapi.aliyuncs.com\n    signName: 万禾悦能\nkafka:\n  partition-number: 1\n  bootstrap:\n    servers: *************:9092\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', 'b805091c682a9bbb1f824687d33da7bb', '2025-03-25 14:03:49', '2025-03-25 06:03:49', '', '***************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (118, 441, 'wanhe-business-prod.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\nemq:\n  # 25c941e678c502a4:oGRUEeUlkWnPnQieNoqGUhXLfaewzQYiJwvA1QqSZWL\n  basic: Basic MjVjOTQxZTY3OGM1MDJhNDpvR1JVRWVVbGtXblBuUWllTm9xR1VoWExmYWV3elFZaUp3dkExUXFTWldM\n  url: http://***********:18083\nfault:\n  language: en\n# mybatis配置\n# mybatis:\n#     # 搜索指定包别名\n#     typeAliasesPackage: com.ruoyi.system\n#     # 配置mapper的扫描，找到所有的mapper.xml映射文件\n#     mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip', '6c4a6878aaf481e40991dd0e296cf7c3', '2025-03-25 14:03:49', '2025-03-25 06:03:49', '', '***************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (117, 442, 'wanhe-biz-data-prod.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring: \n  influx:\n    url: http://*************:8086\n    user: influxdb\n    password: riAi9Y1aefJonscBxFqvcNUIubsCchw39dqwIKGnfjPR5IdskAYSjqLRy4vy7Kgiak16t_o6dt1pP-ROJnXGfg==\n    database: wan_he_device_data\n    # timeZone: tz(\'Asia/Shanghai\')\n    timeZone: tz(\'Europe/Berlin\')\n  data:\n    redis:\n      host: ************\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: ***************************************************************************************************************************************************            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n  task:\n    execution:\n      pool:\n        core-size: 8\n        max-size: 200\n        keep-alive: 1200s\n      thread-name-prefix: task-pool-\nemq:\n  # 25c941e678c502a4:oGRUEeUlkWnPnQieNoqGUhXLfaewzQYiJwvA1QqSZWL\n  basic: Basic MjVjOTQxZTY3OGM1MDJhNDpvR1JVRWVVbGtXblBuUWllTm9xR1VoWExmYWV3elFZaUp3dkExUXFTWldM\n  url: http://************:18083\nfault:\n  # language: zh\n  language: en\nali:\n  sms:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: dysmsapi.aliyuncs.com\n    signName: 万禾悦能\nkafka:\n  partition-number: 1\n  bootstrap:\n    servers: *************:9092\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', 'e0f34bf194d6c8c18e84db8de1b133c0', '2025-03-25 14:49:57', '2025-03-25 06:49:58', NULL, '***************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (93, 443, 'wanhe-biz-data-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring: \n  influx:\n    url: http://***********:8086\n    user: influxdb\n    password: riAi9Y1aefJonscBxFqvcNUIubsCchw39dqwIKGnfjPR5IdskAYSjqLRy4vy7Kgiak16t_o6dt1pP-ROJnXGfg==\n    database: wan_he_device_data\n    # timeZone: tz(\'Asia/Shanghai\')\n    timeZone: tz(\'Europe/Berlin\')\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n  task:\n    execution:\n      pool:\n        core-size: 8\n        max-size: 200\n        keep-alive: 1200s\n      thread-name-prefix: task-pool-\nemq:\n  # 25c941e678c502a4:oGRUEeUlkWnPnQieNoqGUhXLfaewzQYiJwvA1QqSZWL\n  basic: Basic MjVjOTQxZTY3OGM1MDJhNDpvR1JVRWVVbGtXblBuUWllTm9xR1VoWExmYWV3elFZaUp3dkExUXFTWldM\n  url: http://***********:18083\nfault:\n  # language: zh\n  language: en\nali:\n  sms:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: dysmsapi.aliyuncs.com\n    signName: 万禾悦能\nkafka:\n  partition-number: 1\n  bootstrap:\n    servers: *************:9092\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', 'b805091c682a9bbb1f824687d33da7bb', '2025-03-25 14:55:59', '2025-03-25 06:55:59', NULL, '***************', 'U', '0d8f014d-b030-4467-be18-f86b1c11a27b', '');
INSERT INTO `his_config_info` VALUES (93, 444, 'wanhe-biz-data-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring: \n  influx:\n    url: http://***********:8086\n    user: influxdb\n    password: riAi9Y1aefJonscBxFqvcNUIubsCchw39dqwIKGnfjPR5IdskAYSjqLRy4vy7Kgiak16t_o6dt1pP-ROJnXGfg==\n    database: wan_he_device_data\n    # timeZone: tz(\'Asia/Shanghai\')\n    timeZone: tz(\'Europe/Berlin\')\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n  task:\n    execution:\n      pool:\n        core-size: 8\n        max-size: 200\n        keep-alive: 1200s\n      thread-name-prefix: task-pool-\nemq:\n  # 25c941e678c502a4:oGRUEeUlkWnPnQieNoqGUhXLfaewzQYiJwvA1QqSZWL\n  basic: Basic MjVjOTQxZTY3OGM1MDJhNDpvR1JVRWVVbGtXblBuUWllTm9xR1VoWExmYWV3elFZaUp3dkExUXFTWldM\n  url: http://*************:18083\nfault:\n  # language: zh\n  language: en\nali:\n  sms:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: dysmsapi.aliyuncs.com\n    signName: 万禾悦能\nkafka:\n  partition-number: 1\n  bootstrap:\n    servers: *************:9092\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', 'db1cb41e033ca27f3497f63454177c04', '2025-03-25 14:58:21', '2025-03-25 06:58:22', NULL, '***************', 'U', '0d8f014d-b030-4467-be18-f86b1c11a27b', '');
INSERT INTO `his_config_info` VALUES (119, 445, 'wanhe-mqtt-prod.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  data:\n    redis:\n      host: ************\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: ***************************************************************************************************************************************************            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mqtt:\n    username: admin                       # 账号（仅用于后端自认证）\n    password: public                       # 密码（仅用于后端自认证）\n    host-url: tcp://************:1883         # 连接 Emqx 消息服务器地址\n    client-id: wanhe-iot-${random.int}                # 客户端Id，不能相同，采用随机数 ${random.value}\n    default-topic: test                     # 默认主题\n    timeout: 30                             # 超时时间\n    keepalive: 30                           # 保持连接\n    clearSession: false\n    maxInflight: 32\n\nkafka:\n  #分区数量1个\n  partition-number: 10\n  bootstrap:\n    servers: ************:9092\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '26bf32935a837ca09cb876ce87f31982', '2025-03-25 14:59:17', '2025-03-25 06:59:17', NULL, '***************', 'U', '', '');
INSERT INTO `his_config_info` VALUES (93, 446, 'wanhe-biz-data-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring: \n  influx:\n    url: http://***********:8086\n    user: influxdb\n    password: riAi9Y1aefJonscBxFqvcNUIubsCchw39dqwIKGnfjPR5IdskAYSjqLRy4vy7Kgiak16t_o6dt1pP-ROJnXGfg==\n    database: wan_he_device_data\n    # timeZone: tz(\'Asia/Shanghai\')\n    timeZone: tz(\'Europe/Berlin\')\n  data:\n    redis:\n      host: **********\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**********:3306/wanhe?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n  task:\n    execution:\n      pool:\n        core-size: 8\n        max-size: 200\n        keep-alive: 1200s\n      thread-name-prefix: task-pool-\nemq:\n  # 25c941e678c502a4:oGRUEeUlkWnPnQieNoqGUhXLfaewzQYiJwvA1QqSZWL\n  basic: Basic MjVjOTQxZTY3OGM1MDJhNDpvR1JVRWVVbGtXblBuUWllTm9xR1VoWExmYWV3elFZaUp3dkExUXFTWldM\n  url: http://*************:18083\nfault:\n  # language: zh\n  language: en\nali:\n  sms:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: dysmsapi.aliyuncs.com\n    signName: 万禾悦能\nkafka:\n  partition-number: 1\n  bootstrap:\n    servers: *************:9092\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', 'db1cb41e033ca27f3497f63454177c04', '2025-03-25 19:46:08', '2025-03-25 11:46:09', NULL, '************', 'U', '0d8f014d-b030-4467-be18-f86b1c11a27b', '');
INSERT INTO `his_config_info` VALUES (117, 447, 'wanhe-biz-data-prod.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring: \n  influx:\n    url: http://*************:8086\n    user: influxdb\n    password: riAi9Y1aefJonscBxFqvcNUIubsCchw39dqwIKGnfjPR5IdskAYSjqLRy4vy7Kgiak16t_o6dt1pP-ROJnXGfg==\n    database: wan_he_device_data\n    # timeZone: tz(\'Asia/Shanghai\')\n    timeZone: tz(\'Europe/Berlin\')\n  data:\n    redis:\n      host: ************\n      port: 6379\n      password: jtfJ91QMFG5LRFNhrvH\n      database: 0\n      lettuce:\n        pool:\n          max-active: 64\n          max-idle: 64\n          min-idle: 8\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: ***************************************************************************************************************************************************            username: wanhe\n            password: 55kPKFMaYjW5aaYb\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n  mail:\n    default-encoding: utf-8\n    host: smtp.sparkspace.huaweicloud.com\n    port: 465\n    protocol: smtp\n    username: <EMAIL>\n    password: Xcwjsn5vfynBS5bm\n    properties:\n      mail:\n        smtp:\n          ssl:\n            enable: true\n          auth: true\n          starttls:\n            enable: true\n            required: true\n  task:\n    execution:\n      pool:\n        core-size: 8\n        max-size: 200\n        keep-alive: 1200s\n      thread-name-prefix: task-pool-\nemq:\n  # 25c941e678c502a4:oGRUEeUlkWnPnQieNoqGUhXLfaewzQYiJwvA1QqSZWL\n  basic: Basic MjVjOTQxZTY3OGM1MDJhNDpvR1JVRWVVbGtXblBuUWllTm9xR1VoWExmYWV3elFZaUp3dkExUXFTWldM\n  url: http://************:18083\nfault:\n  # language: zh\n  language: en\nali:\n  sms:\n    accessKeyId: LTAI5tQbaCMQ3A48n8dQY3SX\n    accessKeySecret: ******************************\n    endpoint: dysmsapi.aliyuncs.com\n    signName: 万禾悦能\nkafka:\n  partition-number: 1\n  bootstrap:\n    servers: ************:9092\n  topic: wan_he_device_data\n  group:\n    id: wan_he_service\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n', '94749e8f5abdf51e7e377ceb9f117f77', '2025-03-25 19:50:56', '2025-03-25 11:50:57', NULL, '***************', 'U', '', '');

-- ----------------------------
-- Table structure for permissions
-- ----------------------------
DROP TABLE IF EXISTS `permissions`;
CREATE TABLE `permissions`  (
  `role` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `resource` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `action` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  UNIQUE INDEX `uk_role_permission`(`role`, `resource`, `action`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of permissions
-- ----------------------------

-- ----------------------------
-- Table structure for roles
-- ----------------------------
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles`  (
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `role` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  UNIQUE INDEX `idx_user_role`(`username`, `role`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of roles
-- ----------------------------
INSERT INTO `roles` VALUES ('nacos', 'ROLE_ADMIN');

-- ----------------------------
-- Table structure for tenant_capacity
-- ----------------------------
DROP TABLE IF EXISTS `tenant_capacity`;
CREATE TABLE `tenant_capacity`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'Tenant ID',
  `quota` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '配额，0表示使用默认值',
  `usage` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '使用量',
  `max_size` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '单个配置大小上限，单位为字节，0表示使用默认值',
  `max_aggr_count` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '聚合子配置最大个数',
  `max_aggr_size` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '单个聚合数据的子配置大小上限，单位为字节，0表示使用默认值',
  `max_history_count` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '最大变更历史数量',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_tenant_id`(`tenant_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_bin COMMENT = '租户容量信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tenant_capacity
-- ----------------------------

-- ----------------------------
-- Table structure for tenant_info
-- ----------------------------
DROP TABLE IF EXISTS `tenant_info`;
CREATE TABLE `tenant_info`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `kp` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'kp',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT '' COMMENT 'tenant_id',
  `tenant_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT '' COMMENT 'tenant_name',
  `tenant_desc` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'tenant_desc',
  `create_source` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'create_source',
  `gmt_create` bigint NOT NULL COMMENT '创建时间',
  `gmt_modified` bigint NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_tenant_info_kptenantid`(`kp`, `tenant_id`) USING BTREE,
  INDEX `idx_tenant_id`(`tenant_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_bin COMMENT = 'tenant_info' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tenant_info
-- ----------------------------
INSERT INTO `tenant_info` VALUES (2, '1', '0d8f014d-b030-4467-be18-f86b1c11a27b', 'local', '本地', 'nacos', 1742804304750, 1742804304750);

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `password` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `enabled` tinyint(1) NOT NULL,
  PRIMARY KEY (`username`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of users
-- ----------------------------
INSERT INTO `users` VALUES ('HVQ4UW', '$2a$10$UMCrHLKHOS3hMs9eyb5IwuXBYZH0Byvi4LjR4q4Lk.wS7QLha.6LO', 1);
INSERT INTO `users` VALUES ('nacos', '$2a$10$EuWPZHzz32dJN7jexM34MOeYirDdFAZm2kuWj7VEOJhhZkDrxfvUu', 1);

SET FOREIGN_KEY_CHECKS = 1;
