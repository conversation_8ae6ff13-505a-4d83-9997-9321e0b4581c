# 一、屋顶光伏通信协议(EMU&App)

## 1、Mqtt配置

发送方：本地App
● 接收方:  网关/Wifi
● 是否回复：是
● 发送消息格式 

```json
{
	"frameType": 800,
	"mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
	"timeStamp": 1624982406963,
    "connect":{
        "url":"mqtt://**************:1883",
        "admin":"D0008",
        "password":"OH12345678"
    }
}

```

● 接收消息格式 （result为1代表配置成功，0代表配置失败）

```json
{
	"frameType": 800,
	"mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
    "responseId": 3549,
	"timeStamp": 1624982406963,
	"status": 1
}
```



## 2、联网模式配置

发送方：本地App
● 接收方:  网关/Wifi
● 是否回复：是
● 发送消息格式 

```json
{
	"frameType": 801,
	"mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
	"timeStamp": 1624982406963,
    // 通信模式，0：本地模式，1：服务器通信
	"type": 1
}

```

● 接收消息格式 （result为1代表配置成功，0代表配置失败）

```json
{
	"frameType": 801,
	"mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
    "responseId": 3549,
	"timeStamp": 1624982406963,
	"result": 1
}
```



## 3、通信配置

发送方：本地App
● 接收方:  网关/Wifi
● 是否回复：是
● 发送消息格式 

```json
{
	"frameType": 802,
	"mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
	"timeStamp": 1624982406963,
     // 通信方式，0:有线网，1：wifi,2:4G
	"commType": 0,
	"eth": {
         // 自动获取ip，0：否，1：是
         "auto": 0,
		"ip": "**************",
         "gateway": "************",
      	 "mask":"*************",
		 "dns": "***************",
  		 "dns2": "*******"	
	},
	"wifi": {
		"name": "OneHope",
		"passward": "WH@123456"
	},
	"4G": {}
}
```

回复消息格式

```json
{
	"frameType": 802,
	"mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
    "responseId": 3359,
	"timeStamp": 1624982406993,
    "commType": 0,
	"status":1,
     // 信号质量
    "quality":5
}
```



## 4、修改WIFI密码

发送方：本地App
● 接收方:  网关/Wifi
● 是否回复：是
● 发送消息格式 

```json
{
	"frameType": 803,
	"mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
	"timeStamp": 1624982406963,
	"commType": 0,
	"passward": "WH@123456"
}
```

回复消息格式

```json
{
	"frameType": 803,
	"mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
    "responseId": 3359,
	"timeStamp": 1624982406993,
	"status":1
}
```



## 5、设置时区、时间

●发送方：本地App
● 接收方:  网关/Wifi
● 是否回复：是
● 发布消息格式

```json
{
	"frameType": 804,
	"mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
	"timeStamp": 1624982416963,
	"timeCode": "UTC+8"，
    "time":1624982416963
}
```

● 回复消息格式 

```json
{
	"frameType": 804,
	"mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
	"reponseId": 3549,
	"timeStamp": 1624982416969,
  	"result": 1
}
```



## 6、读参数

●发送方：本地App
● 接收方:  网关
● 是否回复：是
● 发布消息格式

```json
{
	"frameType": 805,
    "mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
	"timeStamp": 1624982406918,
	"sn": "11000123456789"
}
```

● 回复消息格式 

```json
{
    // 功能码
	"frameType": 805, 
    // 数据id
	"mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
   "reponseId": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
    // 时间戳
	"timeStamp": 1624982406963,
	"gateWayData": {
       // EMU序列号
		"sn": "10000123456789",
        // 运行状态，1：正常
		"runningStatus": 1,
        // 联网模式，0：WIFI,1:有线网，2：4G, 3:无连接
        "commType":0,
        // 数据上传间隔时间，单位秒
		"uploadInterval": 15,
        // 连接微逆数量
		"monitorNum": 200,
        // 网关软件版本号
		"softVer": 10000100,
        // 网关硬件版本号
		"hardVer": 12000001
	},
	"invData": [
		{
            // 微逆序列号
			"sn": "11000123456789",         
			"data": {
                // PV瞬时功率
				"pvTempPower": 1,
                // AC瞬时功率
				"acTempPower": 1,
                // 电网频率
				"gridFreq": 230.5,
                // 系统状态 1自检ok 2停机 4运行 8故障 16告警
				"sysStatus": 40.2,
                //启机控制,0/停止  1/启动
				"sysCtrlCmd": 1,
                // PV电压1实际值
				"pvVolReal": 1,
                // PV电流1实际值
				"PvCurrReal": 230.5,
                // AC电压实际值
				"acVolReal": 1,
                // AC电流实际值
				"acCurrReal": 1,
                // AC MOS温度实际值
				"acMosTempReal": 230.5,
                // PV MOS温度实际值
				"pVMosTempReal": 1,
                // 日运行时间，小时
				"dayRunTime": 1,
                // 日发电量
                "dailyEnergy":200.5,
                // 微逆发电效率,%
                "efficiency":95.5
			},
            // 连接组件数量
			"pvCount": 2,
             // 和EMU的通信状态，0：通信异常，1：正常
           	"commStatus": 1,
            // 故障码
			"faultCode": {
              "faultCode1": 3,
              "faultCode2": 3,
              "faultCode3": 5
           },
            // 微逆软件版本号
			"softVer": 10000100,
            // 微逆硬件版本号
			"hardVer": 12000001,
            "timeCode": "UTC-8"
		},
		{
			"sn": "11000123456790",
			"data": {
				"pvTempPower": 1,
				"acTempPower": 1,
				"gridFreq": 230.5,
				"sysStatus": 40.2,
				"sysCtrlCmd": 1,
				"pvVolReal": 1,
				"PvCurrReal": 230.5,
				"acVolReal": 1,
				"acCurrReal": 1,
				"acMosTempReal": 230.5,
				"pVMosTempReal": 1,
				"dayRunTime": 1,
                "dailyEnergy":200.5,
                "efficiency":95.5
			},
			"pvCount": 2,
			"commStatus": 1,
			"faultCode": {
              "faultCode1": 3,
              "faultCode2": 3,
              "faultCode3": 5
           },
			"softVer": 10000100,
			"hardVer": 12000001,
           "timeCode": "UTC-8"
		}
	]
}
```



## 7、写参数

●发送方：本地App
● 接收方:  网关
● 是否回复：是
● 发布消息格式

```json
{
	"frameType": 806,
	"mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
	"timeStamp": 1624982406963,
	"sn": 10000123456789,
	"invData": [
		{
			"sn": 11000123456789,
			"data": [
				{
					"addr": 9,
					"value": 1
				},
				{
					"addr": 10,
					"value": 18
				}
			]
		},
		{
			"sn": 11000123456799,
			"data": [
				{
					"addr": 9,
					"value": 1
				},
				{
					"addr": 10,
					"value": 18
				}
			]
		}
	]
}
```

● 回复消息格式(写完毕后，将写的参数读上来完成组帧传递给服务器)

```json
{
	"frameType": 806,
	"mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
	"reponseId": 3349,
	"timeStamp": 1624982406983,
	"sn": "10000123456789",
    "status" : 1
}
```

 



## 8、读发电量文件

●发送方：本地App
● 接收方:  wifi
● 是否回复：是
● 发布消息格式

```json
{
	"frameType": 807,
	"mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
	"timeStamp": 1624982416998,
	"sn": "10000123456789"

}
```

回复消息(发电量文件内容需要再根据实际内容添加）

```json
{
  "frameType": 807,
  "mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
  "reponseId": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
  "timeStamp": 1624982416999,
  "sn": "10000123456789",
  "emuEnergy": {
    "total" : 123,
    "todayTotal": 45.22,
	"monthTotal": 123.4,
    "today24Hour": [100.2,302.41,123.4,125.4,455.6,423.1,236.5,100.2,302.41,123.4,125.4,455.6,423.1,236.5,100.2,302.41,123.4,125.4,455.6,423.1,236.5,100.2,302.41],
    "past30days": [100.2,302.41,123.4,125.4,455.6,423.1,236.5,100.2,302.41,123.4,125.4,455.6,423.1,236.5,100.2,302.41,123.4,125.4,455.6,423.1,236.5,100.2,302.41,123.4,125.4,455.6,423.1,236.5,235.6],
    "past12months": [100.2,302.41,123.4,125.4,455.6,423.1,236.5,100.2,302.41,123.4,125.4,455.6],
    "past5years": [32145.2,42125.5,32654.7,12345.8,4223.5]
  },
  "invEnergy": [
    {
      "sn": "11000123456789",
      "total" : 61,
      "todayTotal": 45.22,
	  "monthTotal": 52.4,
      "yearTotal": 59.3,
      "detailInfo":{
     "today24Hour": [100.2,302.41,123.4,125.4,455.6,423.1,236.5,100.2,302.41,123.4,125.4,455.6,423.1,236.5,100.2,302.41,123.4,125.4,455.6,423.1,236.5,100.2,302.41],
    "past30days": [100.2,302.41,123.4,125.4,455.6,423.1,236.5,100.2,302.41,123.4,125.4,455.6,423.1,236.5,100.2,302.41,123.4,125.4,455.6,423.1,236.5,100.2,302.41,123.4,125.4,455.6,423.1,236.5,235.6],
    "past12months": [100.2,302.41,123.4,125.4,455.6,423.1,236.5,100.2,302.41,123.4,125.4,455.6],
    "past5years": [32145.2,42125.5,32654.7,12345.8,4223.5]    
	}
    },
    {
      "sn": "11000123456799",
      "total" : 62,
      "todayTotal": 45.22,
	  "monthTotal": 52.4,
      "yearTotal": 59.3,
      "detailInfo":{
     "today24Hour": [100.2,302.41,123.4,125.4,455.6,423.1,236.5,100.2,302.41,123.4,125.4,455.6,423.1,236.5,100.2,302.41,123.4,125.4,455.6,423.1,236.5,100.2,302.41],
    "past30days": [100.2,302.41,123.4,125.4,455.6,423.1,236.5,100.2,302.41,123.4,125.4,455.6,423.1,236.5,100.2,302.41,123.4,125.4,455.6,423.1,236.5,100.2,302.41,123.4,125.4,455.6,423.1,236.5,235.6],
    "past12months": [100.2,302.41,123.4,125.4,455.6,423.1,236.5,100.2,302.41,123.4,125.4,455.6],
    "past5years": [32145.2,42125.5,32654.7,12345.8,4223.5]    
	}
    }
  ]
}
```



## 9、写EMU参数

●发送方：本地App
● 接收方:  EMU
● 是否回复：是
● 发布消息格式

```json
{
	"frameType": 808,
	"mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
	"timeStamp": 1624982406963,
	"sn": 10000123456789,
	"params": [
		 {
            "addr": 4000,
            "value": 1
        }
	]
}

```

● 回复消息格式(写完毕后，将写的参数读上来完成组帧传递给服务器)

```json
{
	"frameType": 808,
	"mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
	"reponseId": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
	"timeStamp": 1624982406983,
	"sn": "10000123456789",
	"status" : 1
}
```





# 二、阳台光伏通信协议(wifi&App)

## 1、Mqtt配置

发送方：本地App
● 接收方:  网关/Wifi
● 是否回复：是
● 发送消息格式 

```json
{
	"frameType": 900,
	"mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
	"timeStamp": 1624982406963,
    "connect":{
        "url":"mqtt://**************:1883",
        "admin":"D0008",
        "password":"OH12345678"
    }
}

```

● 接收消息格式 （result为1代表配置成功，0代表配置失败）

```json
{
	"frameType": 900,
	"mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
    "responseId": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
	"timeStamp": 1624982406963,
	"result": 1
}
```



## 2、联网模式配置

发送方：本地App
● 接收方:  网关/Wifi
● 是否回复：是
● 发送消息格式 

```json
{
	"frameType": 901,
	"mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
	"timeStamp": 1624982406963,
    // 通信模式，0：本地模式，1：服务器通信
	"type": 1
}

```

● 接收消息格式 （result为1代表配置成功，0代表配置失败）

```json
{
	"frameType": 901,
	"mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
    "responseId": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
	"timeStamp": 1624982406963,
	"result": 1
}
```



## 3、通信配置

发送方：本地App
● 接收方:  网关/Wifi
● 是否回复：是
● 发送消息格式 

```json
{
	"frameType": 902,
	"mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
	"timeStamp": 1624982406963,
     // 通信方式，1：wifi
	"commType": 1,
	"wifi": {
		"name": "OneHope",
		"passward": "WH@123456"
	}
}
```

回复消息格式

```json
{
	"frameType": 902,
	"mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
    "responseId": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
	"timeStamp": 1624982406993,
    "commType": 0,
	"status":1,
     // 信号质量
    "quality":5
}
```



## 4、修改WIFI密码

发送方：本地App
● 接收方:  网关/Wifi
● 是否回复：是
● 发送消息格式 

```json
{
	"frameType": 903,
	"mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
	"timeStamp": 1624982406963,
	"commType": 0,
	"passward": "WH@123456"
}
```

回复消息格式

```json
{
	"frameType": 903,
	"mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
    "responseId": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
	"timeStamp": 1624982406993,
	"status":1
}
```



## 5、设置时区、时间

●发送方：本地App
● 接收方:  网关/Wifi
● 是否回复：是
● 发布消息格式

```json
{
	"frameType": 904,
	"mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
	"timeStamp": 1624982416963,
	"timeCode": "UTC+8"，
    "time":1624982416963
}
```

● 回复消息格式 

```json
{
	"frameType": 904,
	"mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
	"reponseId": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
	"timeStamp": 1624982416969,
  	"result": 1
}
```



## 6、读参数

●发送方：本地App
● 接收方:  Wifi
● 是否回复：是
● 发布消息格式

```json
{
	"frameType": 905,
    "mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
	"timeStamp": 1624982406918,
	"sn": "11000123456789"
}
```

● 回复消息格式 

```json
{
	"frameType": 905,
	"mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
    "reponseId": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
	"timeStamp": 1624982406963,
	"sn": "11000123456789",
    "wifi": {
		"runningStatus": 1,
		"uploadInterval": 15,
        "softVer": 10000100,
        "hardVer": 12000001,
	},
	"invData": {
		"pvTempPower": 1,
        "acTempPower": 1,
        "gridFreq": 230.5,
        "sysStatus": 40.2,
        "sysCtrlCmd": 1,
        "pvVolReal": 1,
        "PvCurrReal": 230.5,
        "acVolReal": 1,
        "acCurrReal": 1,
        "acMosTempReal": 230.5,
        "pVMosTempReal": 1,
        "dayRunTime": 1,
        "dailyEnergy":200.5,
        "pvCount": 2,
        "commStatus": 1,
        "faultCode": {
              "faultCode1": 3,
              "faultCode2": 3,
              "faultCode3": 5
           },
        "softVer": 10000100,
        "hardVer": 12000001,
       "timeCode": "UTC-8"
	}
}
```





## 7、写参数

●发送方：本地App
● 接收方:  wifi
● 是否回复：是
● 发布消息格式

```json
{
	"frameType": 906,
	"mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
	"timeStamp": 1624982406963,
	"sn": 10000123456789,
	"invData": [
		{
			"addr": 9,
			"value": 1
		},
		{
			"addr": 10,
			"value": 18
		}
	]
}

```

● 回复消息格式(写完毕后，将写的参数读上来完成组帧传递给服务器)

```json
{
	"frameType": 906,
	"mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
	"reponseId": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
	"timeStamp": 1624982406983,
	"sn": "10000123456789",
	"status" : 1
}
```



## 8、读发电量文件

●发送方：本地App
● 接收方:  wifi
● 是否回复：是
● 发布消息格式

```json
{
	"frameType": 907,
	"mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
	"timeStamp": 1624982416998,
	"sn": "10000123456789"

}
```

回复消息(发电量文件内容需要再根据实际内容添加）

```json
{
	"frameType": 907,
	"mid": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
	"reponseId": "c53999c5-3600-dc21-15fe-ec68a5ecc166",
	"timeStamp": 1624982416999,
	"sn": "10000123456789",
	"invEnergy": {
      "sn": "11000123456789",
      "total" : 61,
      "todayTotal": 45.22,
	  "monthTotal": 52.4,
      "yearTotal": 59.3,
      "detailInfo":{
     "today24Hour": [100.2,302.41,123.4,125.4,455.6,423.1,236.5,100.2,302.41,123.4,125.4,455.6,423.1,236.5,100.2,302.41,123.4,125.4,455.6,423.1,236.5,100.2,302.41],
    "past30days": [100.2,302.41,123.4,125.4,455.6,423.1,236.5,100.2,302.41,123.4,125.4,455.6,423.1,236.5,100.2,302.41,123.4,125.4,455.6,423.1,236.5,100.2,302.41,123.4,125.4,455.6,423.1,236.5,235.6],
    "past12months": [100.2,302.41,123.4,125.4,455.6,423.1,236.5,100.2,302.41,123.4,125.4,455.6],
    "past5years": [32145.2,42125.5,32654.7,12345.8,4223.5]    
	}
    }  
    }
```



