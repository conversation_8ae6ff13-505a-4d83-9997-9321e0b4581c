package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024-11-15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("wh_grid_data")
public class GridData {

    @Excel(name = "地址")
    @Schema(description = "地址")
    @TableId(type = IdType.INPUT)
    private Integer addr;

    @Excel(name = "中文名")
    @Schema(description = "中文名")
    private String name;

    @Excel(name = "英文名")
    @Schema(description = "英文名")
    private String nameEn;

    @Excel(name = "数据类型")
    @Schema(description = "数据类型")
    private String type;

    @Excel(name = "是否下发")
    @Schema(description = "是否下发")
    @TableField("is_issue")
    private Boolean issue;

    @Excel(name = "值")
    @Schema(description = "值")
    private BigDecimal val;

    @Excel(name = "单位")
    @Schema(description = "单位")
    private String unit;

}
