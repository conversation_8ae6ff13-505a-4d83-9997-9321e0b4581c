package com.ruoyi.system.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/11/19
 */
@Data
public class PowerStationListDataVO {

    @Schema(description = "电站总数量")
    private Integer count = 0;

    @Schema(description = "EMU数量")
    private Integer emu = 0;

    @Schema(description = "微逆数量")
    private Integer wn = 0;

    @Schema(description = "日发电量")
    private BigDecimal dayKwh = BigDecimal.ZERO;
    private String dayKwhUnit;

    @Schema(description = "月发电量")
    private BigDecimal monthKwh = BigDecimal.ZERO;
    private String monthKwhUnit;

    @Schema(description = "年发电量")
    private BigDecimal yearKwh = BigDecimal.ZERO;
    private String yearKwhUnit;

    @Schema(description = "总发电量")
    private BigDecimal allKwh = BigDecimal.ZERO;
    private String allKwhUnit;

}
