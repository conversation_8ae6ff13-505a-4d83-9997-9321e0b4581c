package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/11/1
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("sys_user_station")
public class SysUserStation {

    @Schema(description = "电站编号")
    @NotNull
    @TableId
    private Integer powerStationId;

    @Schema(description = "用户编号")
    @NotNull
    private Long userId;

}
