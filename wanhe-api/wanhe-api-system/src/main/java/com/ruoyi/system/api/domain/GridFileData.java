package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.poi.ss.usermodel.HorizontalAlignment;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024-11-15
 */
@Data
@NoArgsConstructor
@TableName("wh_grid_file_data")
public class GridFileData {

    @Schema(description = "主键id")
    @TableId(type = IdType.AUTO)
    private Integer id;

    @Schema(description = "并网文件id")
    private Integer gridFileId;

    @Excel(name = "序号", width = 8)
    @Schema(description = "序号")
    @TableField(exist = false)
    private Integer num;

    @Excel(name = "英文名", align = HorizontalAlignment.LEFT, width = 36)
    @Schema(description = "英文名")
    @TableField(exist = false)
    private String nameEn;

    @Excel(name = "地址")
    @Schema(description = "地址")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Integer addr;

    @Excel(name = "数据类型")
    @Schema(description = "数据类型")
    @TableField(exist = false)
    private String type;

//    @Excel(name = "是否下发", readConverterExp = "true=1,false=0")
    @Schema(description = "是否下发")
    @TableField("is_issue")
    private Boolean issue;

    @Excel(name = "值")
    @Schema(description = "值")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal val;

    @Excel(name = "单位")
    @Schema(description = "单位")
    @TableField(exist = false)
    private String unit;

}
