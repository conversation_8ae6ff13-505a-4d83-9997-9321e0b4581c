package com.ruoyi.system.api.dto;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import lombok.Data;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/1/14
 */
@Data
public class UserDistrict {

    /** 是否全部国家 */
    private Boolean allCountry;

    /** 国家区域id（全选省的国家） */
    private String countryIdStr;
    private List<Integer> countryIds;

    /** 省级区域Id */
    private String provinceIdStr;
    private List<Integer> provinceIds;

    public static UserDistrict to(Map<String, Object> map) {
        UserDistrict ud = new UserDistrict();
        ud.setAllCountry((Boolean) map.get("is_all_country"));
        List<Integer> countryIds = JSON.parseArray((String) map.get("country_ids"), Integer.class);
        if (CollUtil.isEmpty(countryIds)) {
            ud.setCountryIdStr(StrUtil.EMPTY);
            ud.setCountryIds(Collections.emptyList());
        } else {
            ud.setCountryIdStr(CollUtil.join(countryIds, ","));
            ud.setCountryIds(countryIds);
        }
        List<Integer> provinceIds = JSON.parseArray((String) map.get("province_ids"), Integer.class);
        if (CollUtil.isEmpty(provinceIds)) {
            ud.setProvinceIdStr(StrUtil.EMPTY);
            ud.setProvinceIds(Collections.emptyList());
        } else {
            ud.setProvinceIdStr(CollUtil.join(provinceIds, ","));
            ud.setProvinceIds(provinceIds);
        }
        return ud;
    }

}
