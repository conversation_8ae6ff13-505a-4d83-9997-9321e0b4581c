package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.base.CUEntity;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 字典数据表 sys_dict_data
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("sys_dict_data")
public class SysDictData extends CUEntity {

    /** 字典编码 */
    @TableId(type = IdType.AUTO)
    private Long dictCode;

    /** 字典排序 */
    private Long dictSort;

    /** 字典标签 */
    @NotBlank
    @Size(max = 100)
    private String dictLabel;

    /** 字典键值 */
    @Excel(name = "字典键值")
    @NotBlank
    @Size(max = 100)
    private String dictValue;

    /** 字典类型 */
    @NotBlank
    @Size(max = 100)
    private String dictType;

    /** 样式属性（其他样式扩展） */
    private String cssClass;

    /** 表格字典样式 */
    private String listClass;

    /** 是否默认（Y是 N否） */
    @Pattern(regexp = "^Y|N$")
    private String isDefault;

    /** 状态（0正常 1停用） */
    @Pattern(regexp = "^0|1$")
    private String status;

    /** 备注 */
    private String remark;
}
