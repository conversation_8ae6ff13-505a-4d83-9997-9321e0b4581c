package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 国际化配置对象 sys_i18n
 *
 * <AUTHOR>
 * @date 2023-08-24
 */
@Data
@TableName("sys_i18n")
public class SysI18n {

    @Schema(description = "主键id")
    @EqualsAndHashCode.Include
    @TableId(type = IdType.AUTO)
    @Excel(name = "编号", sort = 0)
    private Integer id;

    //sys_i18n_source
    @Schema(description = "数据类型 1菜单 2字典数据 3产品分类 4故障描述 5故障处理意见")
    private Integer type;

    @Schema(description = "数据id")
    private Long dataId;

    @Excel(name = "中文", sort = 2)
    @Schema(description = "中文")
    private String nameZh;

    @Excel(name = "英文", sort = 3)
    @Schema(description = "英文")
    private String nameEn;

    @Excel(name = "德文", sort = 4)
    @Schema(description = "德文")
    private String nameDe;

    @Excel(name = "法文", sort = 5)
    @Schema(description = "法文")
    private String nameFra;

    @Schema(description = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Excel(name = "数据类型", sort = 1)
    @Schema(description = "数据类型")
    @TableField(exist = false)
    private String typeName;

}
