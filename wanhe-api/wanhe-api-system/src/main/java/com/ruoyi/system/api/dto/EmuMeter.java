package com.ruoyi.system.api.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/2/20
 */
@Data
public class EmuMeter {

    private Integer emuId;
    private String emuSn;
    private Integer stationId;
    private Long tenantId;
    private LocalDateTime stationCreateTime;
    private Map<String, Meter> meterMap;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Meter {

        private Integer meterId;
        private Integer powerGridSeat;

    }

}
