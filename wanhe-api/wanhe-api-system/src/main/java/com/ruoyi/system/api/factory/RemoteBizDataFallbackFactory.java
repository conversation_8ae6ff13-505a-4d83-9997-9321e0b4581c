package com.ruoyi.system.api.factory;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.api.RemoteBizDataService;
import com.ruoyi.system.api.dto.PowerStationListDataVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/10/31
 */
@Slf4j
@Component
public class RemoteBizDataFallbackFactory implements FallbackFactory<RemoteBizDataService> {

    @Override
    public RemoteBizDataService create(Throwable e) {
        log.error("数据服务调用失败", e);
        return new RemoteBizDataService() {
            @Override
            public R<Void> networking(Integer powerStationId) {
                return R.fail(e.getMessage());
            }

            @Override
            public R<Boolean> configMeter(Integer powerStationId) {
                return R.fail(e.getMessage());
            }

            @Override
            public R<PowerStationListDataVO> energy() {
                return R.fail(e.getMessage());
            }

            @Override
            public R<Void> syncData() {
                return R.fail(e.getMessage());
            }

            @Override
            public R<Void> generation() {
                return R.fail(e.getMessage());
            }
        };
    }

}
