package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.domain.base.CUDEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * 部门表 sys_dept
 *
 * <AUTHOR>
 */
@Data
@TableName("sys_dept")
@EqualsAndHashCode(callSuper = false)
public class SysDept extends CUDEntity {

    /** 部门ID */
    @TableId(type = IdType.AUTO)
    private Long deptId;

    /** 租户ID */
    private Long tenantId;

    /** 父部门ID 一级位0*/
    private Long parentId;

    /** 祖级列表 */
    private String ancestors;

    /** 全路径 */
    private String fullPath;

    /** 部门名称 */
    private String deptName;

    /** 机构类型 1 供应商 2 经销商 3 安装商 4部门 */
    private Integer type;

    /** 图标 */
    private String logo;

    /** 显示顺序 */
    private Integer orderNum;

    /** 区号 */
    private String areaCode;

    /** 联系电话 */
    private String phone;

    /** 邮箱 */
    private String email;

    /** 部门状态:0正常,1停用 */
    private String status;

    /** 备注 */
    private String remark;

    /** 创建人id */
    private Long createId;

    /** 父部门名称 */
    @TableField(exist = false)
    private String parentName;

    /** 子部门 */
    @TableField(exist = false)
    private List<SysDept> children = new ArrayList<>();

}

