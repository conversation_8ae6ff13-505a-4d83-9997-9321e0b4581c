package com.ruoyi.system.api.factory;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.api.RemoteDictService;
import com.ruoyi.system.api.dto.DictDataVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/10 17:36
 */
@Slf4j
@Component
public class RemoteDictFallbackFactory implements FallbackFactory<RemoteDictService> {

    @Override
    public RemoteDictService create(Throwable cause) {
        log.error("字典服务调用失败:{}", cause.getMessage());

        return new RemoteDictService() {
            @Override
            public R<List<DictDataVO>> getDictDataByType(String dictType) {
                return R.ok(Collections.emptyList());
            }
        };
    }
}
