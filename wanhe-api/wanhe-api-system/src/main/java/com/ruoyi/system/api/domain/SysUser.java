package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.core.web.domain.base.TCUDEntity;
import jakarta.validation.constraints.Email;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户对象 sys_user
 *
 * <AUTHOR>
 */
@Data
@TableName("sys_user")
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class SysUser extends TCUDEntity {

    public static final String STAFF_TYPE = "00";
    public static final String PROPRIETOR_TYPE = "01";

    /** 用户ID */
    @TableId(type = IdType.AUTO)
    private Long userId;

    /** 部门ID */
    private Long deptId;

    /** 用户账号 */
    private String userName;

    /** 姓名 */
    private String nickName;

    /** 用户类型（00 员工 01 业主） */
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private String userType;

    /** 用户邮箱 */
    @Email
    private String email;

    /** 区号 */
    private String areaCode;

    /** 手机号码 */
    private String phonenumber;

    /** 用户性别 */
    private String sex;

    /** 用户头像 */
    private String avatar;

    /** 密码 */
    private String password;

    /** 帐号状态（0正常 1停用） */
    private String status;

    /** 最后登录IP */
    private String loginIp;

    /** 最后登录时间 */
    private LocalDateTime loginDate;

    /** 备注 */
    private String remark;

    /** 创建人id */
    private Long createId;

    /** 最后操作的电站编号 */
    private Integer lastOperateId;

    /** 部门对象 */
    @TableField(exist = false)
    private SysDept dept;

    /** 角色对象 */
    @TableField(exist = false)
    private List<SysRole> roles;

    /** 角色id */
    @TableField(exist = false)
    private List<Long> roleIds;

    /** 角色名称 */
    @TableField(exist = false)
    private List<String> roleNames;

    //判断是否是员工
    public static boolean isStaff(String userType) {
        return STAFF_TYPE.equals(userType);
    }

    //判断是否是业主
    public static boolean isProprietor(String userType) {
        return PROPRIETOR_TYPE.equals(userType);
    }

}
