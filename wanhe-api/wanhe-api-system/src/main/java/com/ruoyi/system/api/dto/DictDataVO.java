package com.ruoyi.system.api.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.system.api.domain.SysDictData;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 字典数据表 sys_dict_data
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DictDataVO extends SysDictData {

    @Schema(description = "英文")
//    @JsonIgnore
//    @JSONField(deserialize = false, serialize = false)
    private String nameEn;

    @Schema(description = "德文")
//    @JsonIgnore
//    @JSONField(deserialize = false, serialize = false)
    private String nameDe;

    @Schema(description = "法文")
//    @JsonIgnore
//    @JSONField(deserialize = false, serialize = false)
    private String nameFra;

}
