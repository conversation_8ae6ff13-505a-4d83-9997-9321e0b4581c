package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.annotation.Excel.ColumnType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 系统访问记录表 sys_logininfor
 * 
 * <AUTHOR>
 */
@Data
@TableName("sys_logininfor")
public class SysLogininfor {

    /** ID */
    @TableId(type = IdType.AUTO)
    @Excel(name = "序号", cellType = ColumnType.NUMERIC)
    private Long infoId;

    /** 用户账号 */
    @Excel(name = "用户账号")
    private String userName;

    /** ip地址 */
    @Excel(name = "ip地址")
    private String ipaddr;

    /** 操作地址 */
    @Excel(name = "操作地址")
    private String operLocation;

    /** 状态 0成功 1失败 */
    @Excel(name = "状态", readConverterExp = "0=成功,1=失败")
    private String status;

    /** 描述 */
    @Excel(name = "描述")
    private String msg;

    /** 访问时间 */
    @Excel(name = "访问时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime accessTime;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "客户端类型 1管理端 2安装端 3客户端")
    private String clientType;

    @Schema(description = "租户名称|机构名称")
    @TableField(exist = false)
    private String tenantName;

    /** 创建人id */
    @TableField(fill = FieldFill.INSERT)
    private Long createId;

    /** 部门id */
    @TableField(fill = FieldFill.INSERT)
    private Long deptId;

}