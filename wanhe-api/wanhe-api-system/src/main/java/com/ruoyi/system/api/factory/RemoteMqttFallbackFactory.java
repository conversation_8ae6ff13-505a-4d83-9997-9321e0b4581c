package com.ruoyi.system.api.factory;

import com.ruoyi.system.api.RemotePubMqttService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 用户服务降级处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RemoteMqttFallbackFactory implements FallbackFactory<RemotePubMqttService> {

    @Override
    public RemotePubMqttService create(Throwable throwable) {
        log.error("用户服务调用失败:{}", throwable.getMessage());
        return (publishDto, source) -> Boolean.FALSE;
    }
}
