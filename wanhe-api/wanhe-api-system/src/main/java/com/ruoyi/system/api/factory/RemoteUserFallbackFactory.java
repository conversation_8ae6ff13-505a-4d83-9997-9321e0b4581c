package com.ruoyi.system.api.factory;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 用户服务降级处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RemoteUserFallbackFactory implements FallbackFactory<RemoteUserService> {

    @Override
    public RemoteUserService create(Throwable throwable) {
        log.error("用户服务调用失败", throwable);
        return new RemoteUserService() {
            @Override
            public R<SysUser> getByUsername(String username) {
                return R.fail(throwable.getMessage());
            }

            @Override
            public R<LoginUser> getUserInfo(String username, String clientType, String source) {
                return R.fail(throwable.getMessage());
            }

            @Override
            public R<SysUser> registerUserInfo(SysUser sysUser, String clientType, String source) {
                return R.fail(throwable.getMessage());
            }

            @Override
            public R<Boolean> updatePassword(SysUser sysUser, String clientType, String source) {
                return R.fail(throwable.getMessage());
            }

            @Override
            public R<Boolean> recordUserLogin(SysUser sysUser, String source) {
                return R.fail(throwable.getMessage());
            }
        };
    }
}
