package com.ruoyi.system.api;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.system.api.domain.PublishDTO;
import com.ruoyi.system.api.factory.RemoteMqttFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 用户服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remotePubMqttService", value = ServiceNameConstants.WAN_HE_MQTT, fallbackFactory = RemoteMqttFallbackFactory.class)
public interface RemotePubMqttService {

    /**
     * 发布
     */
    @PostMapping("/mqtt/send/publish")
    Boolean publish(@RequestBody PublishDTO publishDto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

}
