package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024-11-01
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@TableName("wh_power_station_extend")
@Schema(name = "PowerStationExtend", description = "电站信息扩展表")
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class PowerStationExtend {

    @Schema(description = "电站id")
    @EqualsAndHashCode.Include
    @TableId(type = IdType.AUTO)
    private Integer id;

    @Schema(description = "日发电量")
    private BigDecimal dayKwh;

    @Deprecated
    @Schema(description = "月发电量")
    private BigDecimal monthKwh;

    @Deprecated
    @Schema(description = "年发电量")
    private BigDecimal yearKwh;

    @Schema(description = "总发电量")
    private BigDecimal allKwh;

    @Schema(description = "当前功率")
    private BigDecimal kw;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    public PowerStationExtend(BigDecimal dayKwh, BigDecimal kw) {
        this.dayKwh = dayKwh;
        this.kw = kw;
    }

    public PowerStationExtend(BigDecimal kw) {
        this.kw = kw;
    }

}
