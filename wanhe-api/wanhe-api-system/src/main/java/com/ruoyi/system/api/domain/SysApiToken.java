package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.domain.base.CUEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-11-11
 */
@Data
@TableName(value = "sys_api_token", autoResultMap = true)
@Schema(name = "SysApiToken", description = "第三方调用该系统token")
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = false)
public class SysApiToken extends CUEntity {

    @Schema(description = "主键id")
    @EqualsAndHashCode.Include
    @TableId(type = IdType.INPUT)
    private Long id;

    @Schema(description = "租户名称")
    private String tenantName;

    @Schema(description = "姓名")
    private String nickName;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "token")
    private String token;

    @Schema(description = "是否有效")
    @TableField(value = "is_valid")
    private Boolean valid;

    @Schema(description = "电站编号")
    @TableField(exist = false)
    private List<Integer> stationIds;

    @Schema(description = "电站名称")
    @TableField(exist = false)
    private List<String> stationNames;

}
