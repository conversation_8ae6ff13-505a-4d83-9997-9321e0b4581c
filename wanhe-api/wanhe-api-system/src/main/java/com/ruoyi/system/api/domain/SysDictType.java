package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.domain.base.CUEntity;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 字典类型表 sys_dict_type
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_dict_type")
public class SysDictType extends CUEntity {

    /** 字典主键 */
    @TableId(type = IdType.AUTO)
    private Long dictId;

    /** 字典名称 */
    @NotBlank
    @Size(max = 100)
    private String dictName;

    /** 字典类型 */
    @NotBlank
    @Size(max = 100)
    @Pattern(regexp = "^[a-z][a-z0-9_]*$")
    private String dictType;

    /** 状态（0正常 1停用） */
    @NotBlank
    @Pattern(regexp = "^0|1$")
    private String status;

    /** 备注 */
    private String remark;
}
