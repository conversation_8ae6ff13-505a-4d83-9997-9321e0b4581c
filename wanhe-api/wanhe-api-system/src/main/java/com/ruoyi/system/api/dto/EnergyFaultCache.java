package com.ruoyi.system.api.dto;

import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/2/20
 */
@Data
public class EnergyFaultCache {

    //日发电量
    private BigDecimal dailyEnergy;

    //总发电量
//    private BigDecimal totalEnergy;

    // 更新时间
//    private long updateTime;

    // 时区码
//    private String timeCode;

    // 和EMU的通信状态，0：通信异常，1：正常
    private int commStatus;

    // 系统状态 1自检ok 2停机 4运行 8故障 16告警
    private int sysStatus;

    // 故障码
    private JSONObject faultCode;

}
