package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.web.domain.base.TCUDEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Set;

/**
 * 角色表 sys_role
 *
 * <AUTHOR>
 */
@Setter
@Getter
@NoArgsConstructor
@TableName(value = "sys_role", autoResultMap = true)
public class SysRole extends TCUDEntity {

    /** 角色ID */
    @TableId(type = IdType.AUTO)
    private Long roleId;

    /** 角色名称 */
    private String roleName;

    /** 角色权限 */
    private String roleKey;

    /** 角色排序 */
    private Integer roleSort;

    /** 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限 5：仅本人数据权限） */
    private Integer dataScope;

    /** 后台菜单树选择项是否关联显示（ 0：父子不互相关联显示 1：父子互相关联显示） */
    private boolean menuCheckStrictly;

    /** 安装端菜单树选择项是否关联显示（0：父子不互相关联显示 1：父子互相关联显示 ） */
    private boolean deptCheckStrictly;

    /** 角色状态（0正常 1停用） */
    private String status;

    /** 备注 */
    private String remark;

    /** 用户是否存在此角色标识 默认不存在 */
    @TableField(exist = false)
    private boolean flag = false;

    /** 菜单组 */
    @TableField(exist = false)
    private List<Long> menuIds;

    /** 部门组（数据权限） */
    @TableField(exist = false)
    private List<Long> appMenuIds;

    /** 角色菜单权限 */
    @TableField(exist = false)
    private Set<String> permissions;

    /** 创建人id */
    @TableField(fill = FieldFill.INSERT)
    private Long createId;

    /** 部门id */
    @TableField(fill = FieldFill.INSERT)
    private Long deptId;

    /** 自定义部门id */
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private List<Long> customDeptIds;

    /** 自定义部门是否关联显示（ 0：父子不互相关联显示 1：父子互相关联显示） */
    private Boolean customDeptCheckStrictly;

    @JsonIgnore
    @Deprecated
    public boolean isAdmin() {
        return roleId != null && 1L == roleId;
    }

}
