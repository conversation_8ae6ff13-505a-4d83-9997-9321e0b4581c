spring:
  cloud:
    nacos:
      username: nacos
      password: nacos
      discovery:
        # 服务注册地址
        server-addr: 47.105.115.215:8848
        # 命名空间
        namespace: public
      config:
        # 配置中心地址
        server-addr: 47.105.115.215:8848
        # 命名空间
        namespace: public
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
