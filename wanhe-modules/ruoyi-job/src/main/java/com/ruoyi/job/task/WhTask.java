package com.ruoyi.job.task;

import com.ruoyi.system.api.RemoteBizDataService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/12/12
 */
@Component("whTask")
public class WhTask {

    @Resource
    private RemoteBizDataService remoteBizDataService;

    /**
     * 同步发电量和功率
     */
    public void syncData() {
        remoteBizDataService.syncData();
    }

    /**
     * 发电量每日凌晨清零
     */
    public void generation() {
        remoteBizDataService.generation();
    }

}
