package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.domain.base.CUEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 联系我们
 *
 * <AUTHOR>
 * @date 2025/05/13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("sys_contact_us")
public class SysContactUs extends CUEntity {

    /** ID */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /** 地区 */
    private String region;

    /** 电话 */
    private String phone;

    /** 邮箱 */
    private String emil;

    /** 语言 */
    private String language;

    /** 排序 */
    private Integer sort;

}
