package com.ruoyi.system.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.ruoyi.system.domain.SysNotice;
import com.ruoyi.system.domain.SysUserNotice;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/10
 */
public interface SysUserNoticeMapper extends MPJBaseMapper<SysUserNotice> {

    @Select("select user_id, notice_id from sys_user_notice where user_id = #{userId} and notice_id = #{noticeId}")
    SysUserNotice get(Long userId, Integer noticeId);

    /**
     * 获取用户所有未读通知
     *
     * @param userId     用户id
     * @param createTime 创建时间
     * @return {@link SysNotice }
     */
    @Select("SELECT n.id FROM sys_notice n LEFT JOIN sys_user_notice un ON n.id = un.notice_id AND un.user_id = #{userId} where n.create_time > #{createTime} and n.is_enable = 1 and un.user_id is null")
    List<Integer> getUnreadNotice(Long userId, LocalDateTime createTime);

}
