package com.ruoyi.system.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Future;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/8
 */
@Data
public class ApiTokenDTO {

    @Schema(description = "租户名称")
    @NotBlank
    protected String tenantName;

    @Schema(description = "姓名")
    @NotBlank
    private String nickName;

    @Schema(description = "电站编号")
    @NotNull
    private List<Integer> stationIds;

    @Schema(description = "结束时间")
    @NotNull
    @Future
    private LocalDateTime endTime;

    @Schema(description = "是否有效")
    @NotNull
    private Boolean valid;

}
