package com.ruoyi.system.controller;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.Page;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.SysRole;
import com.ruoyi.system.pojo.SysRoleDTO;
import com.ruoyi.system.pojo.SysRolePageReqDTO;
import com.ruoyi.system.pojo.SysRoleStatusDTO;
import com.ruoyi.system.pojo.SysRoleUpdateDTO;
import com.ruoyi.system.service.ISysRoleService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 角色信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/role")
public class SysRoleController extends BaseController {

    @Resource
    private ISysRoleService roleService;

    /**
     * 新增角色 system:role:add
     */
    @RequiresPermissions("system:role:add")
    @Log(title = "角色管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@Validated @RequestBody SysRoleDTO dto) {
        if (dto.getDataScope() == 2 && CollUtil.isEmpty(dto.getCustomDeptIds())) {
            throw new ServiceException("自定义权限范围不能为空");
        }
        if (dto.getDataScope() != 2) {
            dto.setCustomDeptIds(null);
        }
        return R.ok(roleService.insertRole(dto));
    }

    /**
     * 修改保存角色 system:role:edit
     */
    @RequiresPermissions("system:role:edit")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@Validated @RequestBody SysRoleUpdateDTO dto) {
        if (dto.getDataScope() == 2 && CollUtil.isEmpty(dto.getCustomDeptIds())) {
            throw new ServiceException("自定义权限范围不能为空");
        }
        if (dto.getDataScope() != 2) {
            dto.setCustomDeptIds(null);
        }
        return R.ok(roleService.updateRole(dto));
    }

    /**
     * 分页查询 system:role:list
     */
    @RequiresPermissions("system:role:list")
    @GetMapping("/list")
    public TableDataInfo<SysRole> page(@Validated SysRolePageReqDTO dto) {
        Page<SysRole> page = startPage(dto);
        roleService.page(dto);
        return getDataTable(page);
    }

    /**
     * 根据角色编号获取详细信息 system:role:query
     */
    @RequiresPermissions("system:role:query")
    @GetMapping(value = "/{roleId}")
    public R<SysRole> getInfo(@PathVariable Long roleId) {
        return R.ok(roleService.selectRoleById(roleId));
    }

    /**
     * 状态修改 system:role:edit
     */
    @RequiresPermissions("system:role:edit")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public R<Boolean> changeStatus(@Validated @RequestBody SysRoleStatusDTO dto) {
        return R.ok(roleService.updateRoleStatus(dto));
    }

    /**
     * 删除角色 system:role:remove
     */
    @RequiresPermissions("system:role:remove")
    @Log(title = "角色管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{roleId}")
    public R<Boolean> remove(@PathVariable Long roleId) {
        return R.ok(roleService.deleteRoleById(roleId));
    }

    /**
     * 获取角色选择框列表
     */
    @GetMapping("/optionselect")
    public R<List<SysRole>> optionselect(@RequestParam Long tenantId) {
        return R.ok(roleService.selectRoleAll2(tenantId));
    }

}
