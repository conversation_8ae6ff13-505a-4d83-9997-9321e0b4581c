package com.ruoyi.system.service;

import com.ruoyi.common.security.service.IMPJBaseService;
import com.ruoyi.system.domain.SysNotice;
import com.ruoyi.system.pojo.SysNoticeDTO;
import com.ruoyi.system.pojo.SysNoticeMyPageReqDTO;
import com.ruoyi.system.pojo.SysNoticePageReqDTO;
import com.ruoyi.system.pojo.SysNoticeUpdateDTO;

import java.io.Serializable;

/**
 * 公告 服务层
 * 
 * <AUTHOR>
 */
public interface ISysNoticeService extends IMPJBaseService<SysNotice> {

    void page(SysNoticePageReqDTO dto);

    void page(SysNoticeMyPageReqDTO dto);

    /**
     * 已读所有通知
     */
    void readAllNotice();

    boolean add(SysNoticeDTO dto);

    boolean update(SysNoticeUpdateDTO dto);

    boolean enable(Serializable id);

    SysNotice get(Integer id);

}
