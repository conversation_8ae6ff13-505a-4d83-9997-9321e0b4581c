package com.ruoyi.system.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.ruoyi.system.domain.SysApiTokenStation;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/12
 */
public interface SysApiTokenStationMapper extends MPJBaseMapper<SysApiTokenStation> {

    @Select("select token_id, station_id from sys_api_token_station where token_id in(${tokenIds})")
    List<SysApiTokenStation> get(String tokenIds);

    @Select("select station_id from sys_api_token_station where token_id = ${tokenId}")
    List<Integer> getStationIds(Long tokenId);

}
