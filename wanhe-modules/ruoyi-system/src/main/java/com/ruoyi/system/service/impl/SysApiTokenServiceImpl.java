package com.ruoyi.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.IntStr;
import com.ruoyi.common.core.utils.JwtUtils;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.redis.RedisUtil;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.service.impl.MPJBaseServiceImpl;
import com.ruoyi.system.api.domain.SysApiToken;
import com.ruoyi.system.api.model.LoginUser;
import com.ruoyi.system.domain.SysApiTokenStation;
import com.ruoyi.system.mapper.SysApiTokenMapper;
import com.ruoyi.system.mapper.SysApiTokenStationMapper;
import com.ruoyi.system.pojo.ApiTokenDTO;
import com.ruoyi.system.pojo.ApiTokenUpdateDTO;
import com.ruoyi.system.pojo.SysApiTokenPageReqDTO;
import com.ruoyi.system.service.ISysApiTokenService;
import io.jsonwebtoken.Claims;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @date 2024-11-11
*/
@Service
public class SysApiTokenServiceImpl extends MPJBaseServiceImpl<SysApiTokenMapper, SysApiToken> implements ISysApiTokenService {

    @Resource
    private RedisService redisService;
    @Resource
    private SysApiTokenStationMapper tokenStationMapper;

    @Override
    public void page(SysApiTokenPageReqDTO dto) {
        List<SysApiToken> list = lambdaQuery()
                .like(StrUtil.isNotBlank(dto.getNickName()), SysApiToken::getNickName, dto.getNickName())
                .orderByDesc(SysApiToken::getId)
                .list();
        if (!list.isEmpty()) {
            List<Long> tokenIds = new ArrayList<>(list.size());
            Map<Long, SysApiToken> tokenMap = new HashMap<>(list.size());
            for (SysApiToken token : list) {
                token.setStationIds(new ArrayList<>());
                tokenIds.add(token.getId());
                tokenMap.put(token.getId(), token);
            }
            List<SysApiTokenStation> apiTokenStations = tokenStationMapper.get(join(tokenIds));
            Set<Integer> stationIds = new HashSet<>();
            for (SysApiTokenStation tokenStation : apiTokenStations) {
                tokenMap.get(tokenStation.getTokenId()).getStationIds().add(tokenStation.getStationId());
                stationIds.add(tokenStation.getStationId());
            }
            if (!stationIds.isEmpty()) {
                Map<Integer, String> stationMap = baseMapper.getStation(join(stationIds)).stream().collect(Collectors.toMap(IntStr::getK, IntStr::getV));
                list.forEach(token -> token.setStationNames(token.getStationIds().stream().map(stationMap::get).toList()));
            }
        }
    }

    /**
     * 刷新令牌有效期
     *
     * @param loginUser 登录信息
     */
    public void refreshToken(LoginUser loginUser, SysApiToken apiToken) {
        loginUser.setExpireTime(apiToken.getEndTime().atZone(ZoneId.systemDefault()).toEpochSecond() * 1000L);
        // 根据uuid将loginUser缓存
        String userKey = getTokenKey(loginUser.getToken());
        if (apiToken.getValid()) {
            long seconds = Duration.between(LocalDateTime.now(), apiToken.getEndTime()).getSeconds();
            if (seconds > 0) {
                redisService.setCacheObject(userKey, loginUser, seconds, TimeUnit.SECONDS);
            } else {
                redisService.deleteObject(userKey);
            }
        } else {
            redisService.deleteObject(userKey);
        }
    }

    private String getTokenKey(String token) {
        return CacheConstants.LOGIN_TOKEN_KEY + token;
    }

    @Override
    @Transactional
    public boolean add(ApiTokenDTO dto) {
        SysApiToken apiToken = BeanUtil.copyProperties(dto, SysApiToken.class);
        apiToken.setId(IdUtil.getSnowflakeNextId());
        LoginUser loginUser = new LoginUser();
        loginUser.setToken(IdUtils.fastUUID());
        loginUser.setUserid(apiToken.getId());
        loginUser.setUsername(dto.getNickName());
        refreshToken(loginUser, apiToken);
        // Jwt存储信息
        Map<String, Object> claimsMap = new HashMap<>();
        claimsMap.put(SecurityConstants.USER_KEY, loginUser.getToken());
        claimsMap.put(SecurityConstants.DETAILS_USER_ID, loginUser.getUserid());
        claimsMap.put(SecurityConstants.DETAILS_USERNAME, loginUser.getUsername());
        apiToken.setToken(JwtUtils.createToken(claimsMap));
        if (!dto.getStationIds().isEmpty()) {
            List<SysApiTokenStation> list = dto.getStationIds().stream().map(id -> new SysApiTokenStation(apiToken.getId(), id)).toList();
            tokenStationMapper.insert(list);
        }
        return save(apiToken);
    }

    @Override
    @Transactional
    public boolean update(ApiTokenUpdateDTO dto) {
        SysApiToken apiToken = getByIdThrow(dto.getId());
        BeanUtil.copyProperties(dto, apiToken);
        tokenStationMapper.delete(Wrappers.<SysApiTokenStation>lambdaQuery().eq(SysApiTokenStation::getTokenId, apiToken.getId()));
        if (!dto.getStationIds().isEmpty()) {
            List<SysApiTokenStation> list = dto.getStationIds().stream().map(id -> new SysApiTokenStation(apiToken.getId(), id)).toList();
            tokenStationMapper.insert(list);
        }
        boolean b = updateById(apiToken);
        Claims claims = JwtUtils.parseToken(apiToken.getToken());
        String userKey = claims.get(SecurityConstants.USER_KEY, String.class);
        String tokenKey = getTokenKey(userKey);
        LoginUser loginUser = redisService.getCacheObject(tokenKey);
        if (loginUser == null) {
            loginUser = new LoginUser();
            loginUser.setToken(userKey);
            loginUser.setUserid(claims.get(SecurityConstants.DETAILS_USER_ID, Long.class));
            loginUser.setUsername(claims.get(SecurityConstants.DETAILS_USERNAME, String.class));
        }
        refreshToken(loginUser, apiToken);
        return b;
    }

    @Override
    public SysApiToken get(Long apiTokenId) {
        SysApiToken token = getByIdThrow(apiTokenId);
        token.setStationIds(tokenStationMapper.getStationIds(apiTokenId));
        return token;
    }

    @Override
    @Transactional
    public boolean delete(Long apiTokenId) {
        SysApiToken token = getByIdThrow(apiTokenId);
        tokenStationMapper.delete(Wrappers.<SysApiTokenStation>lambdaQuery().eq(SysApiTokenStation::getTokenId, apiTokenId));
        boolean b = removeById(apiTokenId);
        Claims claims = JwtUtils.parseToken(token.getToken());
        String userKey = JwtUtils.getUserKey(claims);
        RedisUtil.delete(getTokenKey(userKey));
        return b;
    }

}
