package com.ruoyi.system.pojo;

import com.ruoyi.common.core.domain.PageReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/11/6
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysUserProprietorPageReqDTO extends PageReqDTO {

    @Schema(description = "电站编号")
    @NotNull
    private Integer powerStationId;

    @Schema(description = "用户名称或ID")
    private String name;

}
