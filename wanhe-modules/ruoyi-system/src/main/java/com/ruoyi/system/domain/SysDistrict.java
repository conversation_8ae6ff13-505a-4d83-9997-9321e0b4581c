package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.domain.base.ICUDEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-10-30
 */
@Data
@TableName("sys_district")
@NoArgsConstructor
@Schema(name = "SysDistrict", description = "行政区域")
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = false)
public class SysDistrict extends ICUDEntity {

    @Schema(description = "父级")
    private Integer pid;

    @Schema(description = "行政区名称")
    private String name;

    @Schema(description = "级别 0国家 1省 2市")
    private Integer grade;

    @Schema(description = "子集")
    @TableField(exist = false)
    private List<SysDistrict> children;

    public SysDistrict(Integer pid, String name, Integer grade) {
        this.pid = pid;
        this.name = name;
        this.grade = grade;
    }
}
