package com.ruoyi.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.domain.KV;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.I18nUtil;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.Tools;
import com.ruoyi.common.redis.RedisUtil;
import com.ruoyi.common.security.service.impl.MPJBaseServiceImpl;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.SysDept;
import com.ruoyi.system.api.domain.SysRole;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.domain.SysUserDistrict;
import com.ruoyi.system.domain.SysUserRole;
import com.ruoyi.system.api.domain.SysUserStation;
import com.ruoyi.system.mapper.*;
import com.ruoyi.system.pojo.*;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysUserService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysUserServiceImpl extends MPJBaseServiceImpl<SysUserMapper, SysUser> implements ISysUserService {

    @Resource
    private SysRoleMapper roleMapper;
    @Resource
    private SysUserRoleMapper userRoleMapper;
    @Resource
    private SysUserDistrictMapper sysUserDistrictMapper;
    @Resource
    private SysUserStationMapper userStationMapper;
    @Resource
    private ISysDeptService deptService;
    @Resource
    private ISysConfigService configService;

    @Override
    public boolean updatePassword(SysUser sysUser) {
        SysUser user = lambdaQuery().eq(SysUser::getUserName, sysUser.getUserName()).one();
        if (user == null) {
            throw new ServiceException("用户名不存在");
        }
        if (!user.getEmail().equals(sysUser.getEmail())) {
            throw new ServiceException("邮箱输入错误");
        }
        return lambdaUpdate().eq(SysUser::getUserName, sysUser.getUserName())
                .eq(SysUser::getEmail, sysUser.getEmail())
                .set(SysUser::getPassword, sysUser.getPassword())
                .update();
    }

    @Override
    public SysUser getByUsername(String username) {
        return lambdaQuery().eq(SysUser::getUserName, username).one();
    }

    @Override
    public List<SysUser> stationList(ProprietorSearchReqDTO dto) {
        MPJLambdaWrapper<SysUser> wrapper = lambdaJoinQuery()
                .selectAll(SysUser.class)
                .innerJoin(SysUserStation.class, i -> i.eq(SysUserStation::getUserId, SysUser::getUserId)
                    .eq(SysUserStation::getPowerStationId, dto.getPowerStationId())
                    .eq(SysUser::getUserType, SysUser.PROPRIETOR_TYPE)
                    .eq(StrUtil.isNotBlank(dto.getStatus()), SysUser::getStatus, dto.getStatus()));
        if (StrUtil.isNotBlank(dto.getNickName())) {
            if (Tools.isNum(dto.getNickName())) {
                wrapper.and(i -> i.eq(SysUser::getUserId, dto.getNickName()).or().like(SysUser::getNickName, dto.getNickName()));
            } else {
                wrapper.like(SysUser::getNickName, dto.getNickName());
            }
        }
        List<SysUser> list = wrapper.list();
        if (!list.isEmpty()) {
            List<Long> tenantIds = list.stream().map(SysUser::getTenantId).distinct().toList();
            Map<Long, String> map = deptService.getName(tenantIds);
            list.forEach(user -> {
                user.setTenantName(map.get(user.getTenantId()));
                user.setPassword(StrUtil.EMPTY);
                user.setPhonenumber(DesensitizedUtil.mobilePhone(user.getPhonenumber()));
                user.setEmail(DesensitizedUtil.email(user.getEmail()));
            });
        }
        return list;
    }

    @Override
    public Page<SysUser> proprietorPage(SysUserProprietorPageReqDTO dto) {
        SysDept dept = nullThrow(baseMapper.getTenantId(dto.getPowerStationId()));
        MPJLambdaWrapper<SysUser> wrapper = lambdaJoinQuery()
                .selectAll(SysUser.class)
                .leftJoin(SysUserStation.class, i -> i.eq(SysUserStation::getUserId, SysUser::getUserId)
                        .eq(SysUserStation::getPowerStationId, dto.getPowerStationId()))
                .eq(SysUser::getTenantId, dept.getTenantId())
                .eq(dept.getTenantId() == DIY_TENANT_ID, SysUser::getCreateId, getUserId())
                .eq(SysUser::getUserType, SysUser.PROPRIETOR_TYPE)
                .isNull(SysUserStation::getPowerStationId)
                .orderByDesc(SysUser::getUserId);
        if (StrUtil.isNotBlank(dto.getName())) {
            if (Tools.isNum(dto.getName())) {
                wrapper.and(i -> i.eq(SysUser::getUserId, dto.getName()).or().like(SysUser::getNickName, dto.getName()));
            } else {
                wrapper.like(SysUser::getNickName, dto.getName());
            }
        }
        Page<SysUser> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        wrapper.list();
        if (!page.getResult().isEmpty()) {
            page.getResult().forEach(user -> {
                user.setPassword(StrUtil.EMPTY);
                user.setPhonenumber(DesensitizedUtil.mobilePhone(user.getPhonenumber()));
                user.setEmail(DesensitizedUtil.email(user.getEmail()));
                user.setTenantName(dept.getDeptName());
            });
        }
        return page;
    }

    @Override
    public void page(SysUserPageReqDTO dto) {
        MPJLambdaWrapper<SysUser> wrapper = lambdaQueryTenant(SysUser::getTenantId, SysUser::getDeptId, SysUser::getCreateId)
                .eq(SysUser::getUserType, dto.getUserType())
                .eq(StrUtil.isNotBlank(dto.getStatus()), SysUser::getStatus, dto.getStatus())
                .like(StrUtil.isNotBlank(dto.getUserName()), SysUser::getUserName, dto.getUserName())
                .orderByDesc(SysUser::getUserId);
        if (StrUtil.isNotBlank(dto.getNickName())) {
            if (Tools.isNum(dto.getNickName())) {
                wrapper.and(i -> i.eq(SysUser::getUserId, dto.getNickName()).or().like(SysUser::getNickName, dto.getNickName()));
            } else {
                wrapper.like(SysUser::getNickName, dto.getNickName());
            }
        }
        if (SysUser.isStaff(dto.getUserType())) {
            if (dto.getDeptId().equals(dto.getTenantId())) {
                wrapper.eq(SysUser::getTenantId, dto.getTenantId());
            } else {
                wrapper.eq(SysUser::getDeptId, dto.getDeptId());
            }
        } else {
            wrapper.eq(SysUser::getTenantId, dto.getDeptId());
        }
        List<SysUser> list = wrapper.list();
        if (!list.isEmpty()) {
            if (SysUser.isStaff(dto.getUserType())) {
                List<Long> userIds = list.stream().map(SysUser::getUserId).toList();
                List<SysUserRole> sysUserRoles = userRoleMapper.selectList(Wrappers.<SysUserRole>lambdaQuery().in(SysUserRole::getUserId, userIds));
                Map<Long, List<Long>> userRoleMap = sysUserRoles.stream()
                        .collect(Collectors.groupingBy(SysUserRole::getUserId, Collectors.mapping(SysUserRole::getRoleId, Collectors.toList())));
                list.forEach(user -> user.setRoleIds(userRoleMap.getOrDefault(user.getUserId(), Collections.emptyList())));
                List<Long> roleIds = sysUserRoles.stream().map(SysUserRole::getRoleId).distinct().toList();
                Map<Long, String> roleMap = roleMapper.selectList(Wrappers.<SysRole>lambdaQuery().in(SysRole::getRoleId, roleIds))
                        .stream().collect(Collectors.toMap(SysRole::getRoleId, SysRole::getRoleName));
                list.forEach(user -> {
                    user.setPassword(StrUtil.EMPTY);
                    user.setRoleNames(user.getRoleIds().stream().map(roleMap::get).toList());
                    user.setPhonenumber(DesensitizedUtil.mobilePhone(user.getPhonenumber()));
                    user.setEmail(DesensitizedUtil.email(user.getEmail()));
                });
            } else {
                list.forEach(user -> {
                    user.setPassword(StrUtil.EMPTY);
                    user.setPhonenumber(DesensitizedUtil.mobilePhone(user.getPhonenumber()));
                    user.setEmail(DesensitizedUtil.email(user.getEmail()));
                });
            }
        }
    }

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByUserName(String userName, String clientType) {
        String userType = "";
        if (clientType != null) {
            switch (clientType) {
                case "2" -> userType = " and u.user_type = '00'";
                case "3" -> userType = " and u.user_type = '01'";
            }
        }
        SysUser sysUser = baseMapper.selectUserByUserName(userName, userType);
        if (sysUser != null && sysUser.getRoles() != null) {
            sysUser.setRoleIds(sysUser.getRoles().stream().map(SysRole::getRoleId).toList());
        }
        return sysUser;
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserById(Long userId) {
        SysUser sysUser = baseMapper.selectUserById(userId);
        if (sysUser != null) {
            if (SysUser.isStaff(sysUser.getUserType())) {
                sysUser.setRoleIds(sysUser.getRoles().stream().map(SysRole::getRoleId).toList());
            }
        }
        return sysUser;
    }

    /**
     * 查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserRoleGroup(String userName) {
        List<SysRole> list = roleMapper.selectRolesByUserName(userName);
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return list.stream().map(SysRole::getRoleName).collect(Collectors.joining(","));
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     */
    @Override
    public void checkUserNameUnique(SysUser user) {
        if (lambdaQuery().eq(SysUser::getUserName, user.getUserName())
                .ne(user.getUserId() != null, SysUser::getUserId, user.getUserId())
                .exists()) {
            throw new ServiceException("用户名已存在");
        }
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     */
    @Override
    public void checkPhoneUnique(SysUser user) {
        if (StrUtil.isBlank(user.getAreaCode()) || StringUtils.isBlank(user.getPhonenumber())) {
            return;
        }
        if (lambdaQuery().eq(SysUser::getAreaCode, user.getAreaCode())
                .eq(SysUser::getPhonenumber, user.getPhonenumber())
                .eq(SysUser::getUserType, user.getUserType())
                .ne(user.getUserId() != null, SysUser::getUserId, user.getUserId())
                .exists()) {
            throw new ServiceException("手机号已存在");
        }
    }

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     */
    @Override
    public void checkEmailUnique(SysUser user) {
        if (StrUtil.isBlank(user.getEmail())) {
            return;
        }
        if (lambdaQuery().eq(SysUser::getEmail, user.getEmail())
                .eq(SysUser::getUserType, user.getUserType())
                .ne(user.getUserId() != null, SysUser::getUserId, user.getUserId())
                .exists()) {
            throw new ServiceException("邮箱已存在");
        }
    }

    /**
     * 校验sn是否存在
     *
     * @param user 用户信息
     */
    @Override
    public void checkSn(SysUser user) {
        String c = user.getRemark();
        if (StrUtil.isBlank(c)) {
            return;
        }
        Tools.snCheck(c);
        if (baseMapper.getProductIdByCode(Tools.snModel(c)) == null) {
            throw new ServiceException("设备型号错误");
        }
    }

    /**
     * 新增保存用户信息
     *
     * @param dto 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertUser(SysUserDTO dto) {
        SysUser user = BeanUtil.copyProperties(dto, SysUser.class);
        checkUserNameUnique(user);
        checkPhoneUnique(user);
        checkEmailUnique(user);
        //电站处新增业主
        if (dto.getPowerStationId() != null) {
            Long tenantId = nullThrow(baseMapper.getTenantId(dto.getPowerStationId())).getTenantId();
            user.setTenantId(tenantId);
            user.setDeptId(null);
        } else {
            nullThrow(dto.getDeptId());
            SysDept dept = deptService.getByIdThrow(dto.getDeptId());
            user.setTenantId(dept.getTenantId());
            user.setDeptId(dto.getDeptId());
        }
        user.setPassword(SecurityUtils.encryptPassword(dto.getPassword()));
        user.setCreateId(getUserId());
        // 新增用户信息
        boolean save = save(user);
        // 新增用户与角色管理
        insertUserRole(user.getUserId(), user.getUserType(), dto.getRoleIds());
        // 新增电站
        if (dto.getPowerStationId() != null && SysUser.isProprietor(dto.getUserType())) {
            SysUserStation station = new SysUserStation();
            station.setPowerStationId(dto.getPowerStationId());
            station.setUserId(user.getUserId());
            userStationMapper.insert(station);
        }
        if (SysUser.isStaff(user.getUserType())) {
            // 新增用户与地区管理
            SysUserDistrict ud = new SysUserDistrict(user.getUserId());
            sysUserDistrictMapper.insert(ud);
            RedisUtil.set(CacheConstants.USER_DISTRICT_KEY + user.getUserId(), SysUserDistrict.toJson(ud));
        }
        return save;
    }

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public SysUser registerUser(SysUser user) {
        user.setTenantId(DIY_TENANT_ID);
        user.setDeptId(DIY_TENANT_ID);
        // 新增用户信息
        save(user);
        // 新增用户与角色管理
        insertUserRole(user.getUserId(), user.getUserType(), List.of(DIY_ROLE_ID));
        if (SysUser.isStaff(user.getUserType())) {
            // 新增用户与地区管理
            SysUserDistrict ud = new SysUserDistrict(user.getUserId());
            sysUserDistrictMapper.insert(ud);
            RedisUtil.set(CacheConstants.USER_DISTRICT_KEY + user.getUserId(), SysUserDistrict.toJson(ud));
        }
        return user;
    }

    /**
     * 修改保存用户信息
     *
     * @param dto 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUser(SysUserUpdateDTO dto) {
        SysUser user = getByIdThrow(dto.getUserId());
        BeanUtil.copyProperties(dto, user, "password", "email", "phonenumber");
        if (dto.getPhonenumber().contains("*")) {
            String phone = DesensitizedUtil.mobilePhone(user.getPhonenumber());
            if (!phone.equals(dto.getPhonenumber())) {
                throw new ServiceException("手机号码格式错误，请重新输入");
            }
        } else {
            user.setPhonenumber(dto.getPhonenumber());
        }
        if (dto.getEmail().contains("*")) {
            String email = DesensitizedUtil.email(user.getEmail());
            if (!email.equals(dto.getEmail())) {
                throw new ServiceException("邮箱格式错误，请重新输入");
            }
        } else {
            user.setEmail(dto.getEmail());
        }
        if (StrUtil.isNotBlank(dto.getPassword())) {
            user.setPassword(SecurityUtils.encryptPassword(dto.getPassword()));
        }
        checkUserNameUnique(user);
        checkPhoneUnique(user);
        checkEmailUnique(user);
        if (SysUser.isStaff(user.getUserType())) {
            // 删除用户与角色关联
            userRoleMapper.deleteUserRoleByUserId(user.getUserId());
            // 新增用户与角色管理
            insertUserRole(user.getUserId(), user.getUserType(), dto.getRoleIds());
        }
        return updateById(user);
    }

    /**
     * 修改用户状态
     *
     * @return 结果
     */
    @Override
    public boolean updateUserStatus(SysUserUpdateStatusDTO dto) {
        SysUser user = getByIdThrow(dto.getUserId());
        return lambdaUpdate().eq(SysUser::getUserId, user.getUserId())
                .set(SysUser::getStatus, user.getStatus().equals("0") ? "1" : "0")
                .update();
    }

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean updateUserProfile(SysUser user) {
        return baseMapper.updateUser(user) > 0;
    }

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar   头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(String userName, String avatar) {
        return baseMapper.updateUserAvatar(userName, avatar) > 0;
    }

    /**
     * 重置用户密码
     *
     * @param dto 用户信息
     * @return 结果
     */
    @Override
    public boolean resetPwd(SysUserResetPwdDTO dto) {
        String password = configService.selectConfigByKey("sys.user.initPassword");
        return lambdaUpdate().eq(SysUser::getUserId, dto.getUserId())
                .set(SysUser::getPassword, SecurityUtils.encryptPassword(password))
                .update();
    }

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPwd(String userName, String password) {
        return baseMapper.resetUserPwd(userName, password);
    }

    /**
     * 新增用户角色信息
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    public void insertUserRole(Long userId, String userType, List<Long> roleIds) {
        if (SysUser.isStaff(userType)) {
            if (CollUtil.isNotEmpty(roleIds)) {
                // 新增用户与角色管理
                List<SysUserRole> list = roleIds.stream().map(roleId -> new SysUserRole(userId, roleId)).toList();
                userRoleMapper.batchUserRole(list);
            }
        } else {
            userRoleMapper.batchUserRole(List.of(new SysUserRole(userId, OWNER_ROLE_ID)));
        }
    }

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserById(Long userId) {
        if (baseMapper.existStation(userId)) {
            throw new ServiceException("该用户已绑定电站，请先解除电站绑定");
        }
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        baseMapper.deleteStationCollectById(userId);
        return baseMapper.deleteUserById(userId);
    }

    @Override
    public boolean boundStation(SysUserStationDTO dto) {
        List<Long> oldUserIds = userStationMapper.selectUserIdByPowerStationId(dto.getPowerStationId());
        List<SysUserStation> list = dto.getUserIds().stream().filter(userId -> !oldUserIds.contains(userId))
                .map(userId -> new SysUserStation(dto.getPowerStationId(), userId)).toList();
        return SqlHelper.retBool(userStationMapper.insert(list));
    }

    @Override
    public int removeStation(SysUserStation dto) {
        return userStationMapper.delete(Wrappers.<SysUserStation>lambdaQuery()
                .eq(SysUserStation::getUserId, dto.getUserId())
                .eq(SysUserStation::getPowerStationId, dto.getPowerStationId()));
    }

    @Override
    public List<String> proprietor(Integer powerStationId) {
        return baseMapper.proprietor(powerStationId);
    }

    @Override
    public SysUserDistrict selectUserDistrict(Long userId) {
        return sysUserDistrictMapper.selectById(userId);
    }

    @Override
    public boolean updateDistrict(SysUserDistrict ud) {
        RedisUtil.set(CacheConstants.USER_DISTRICT_KEY + ud.getUserId(), SysUserDistrict.toJson(ud));
        return SqlHelper.retBool(sysUserDistrictMapper.updateById(ud));
    }

    @Override
    public List<KV> all(Long tenantId) {
        return baseMapper.all(tenantId);
    }

    @Override
    public boolean lastOperateStation(Integer stationId) {
        lambdaUpdate().eq(SysUser::getUserId, getUserId()).set(SysUser::getLastOperateId, stationId).update();
        return baseMapper.isAllowOwnerViewLayout(stationId);
    }

    @Override
    public String logout(Long id) {
        SysUser user = getByIdThrow(id);
        lambdaUpdate().eq(SysUser::getUserId, id).remove();
        return user.getUserName();
    }

    @Override
    public String logout() {
        Long userId = getUserId();
        SysUser user = getByIdThrow(userId);
        lambdaUpdate().eq(SysUser::getUserId, userId).remove();
        return user.getUserName();
    }

    @Override
    public void export(HttpServletResponse response) throws IOException {
        response.setContentType("text/csv");
        response.setCharacterEncoding(CharsetUtil.GBK);
        response.setHeader("Content-Disposition", "attachment; filename=\"file.csv\"");
        SysUser user = getSysUser();
        String deptName = baseMapper.getDeptName(user.getTenantId());
        try (PrintWriter writer = response.getWriter()) {
            writer.append(I18nUtil.get("用户名,昵称,邮箱,手机号,所属机构")).append("\n");
            writer.append("%s,%s,%s,%s,%s".formatted(
                    user.getUserName(),
                    user.getNickName(),
                    user.getEmail(),
                    user.getAreaCode() + user.getPhonenumber(),
                    deptName
            )).append("\n");
            writer.flush();
        }
    }

}
