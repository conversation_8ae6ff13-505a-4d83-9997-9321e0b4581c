package com.ruoyi.system.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024-10-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(name = "SysDistrictUpdateDTO", description = "行政区域")
public class SysDistrictUpdateDTO extends SysDistrictDTO {

    @Schema(description = "区域编码")
    @NotNull
    @Positive
    private Integer id;

}
