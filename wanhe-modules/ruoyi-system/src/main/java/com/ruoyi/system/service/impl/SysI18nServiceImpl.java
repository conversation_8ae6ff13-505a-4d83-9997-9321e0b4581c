package com.ruoyi.system.service.impl;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.enums.I18nTypeEnum;
import com.ruoyi.common.core.enums.LanguageEnum;
import com.ruoyi.common.redis.RedisUtil;
import com.ruoyi.common.security.service.impl.BaseServiceImpl;
import com.ruoyi.common.security.utils.DictUtils;
import com.ruoyi.system.api.domain.SysDictData;
import com.ruoyi.system.api.domain.SysI18n;
import com.ruoyi.system.mapper.SysDictDataMapper;
import com.ruoyi.system.mapper.SysI18nMapper;
import com.ruoyi.system.pojo.SysI18nPageReqDTO;
import com.ruoyi.system.pojo.SysI18nUpdateDTO;
import com.ruoyi.system.service.ISysI18nService;
import com.wanhe.common.i18n.util.TransApi;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/19
 */
@Service
public class SysI18nServiceImpl extends BaseServiceImpl<SysI18nMapper, SysI18n> implements ISysI18nService {

    @Resource
    private SysDictDataMapper dictDataMapper;

    @Override
    public List<SysI18n> page(SysI18nPageReqDTO dto) {
        List<SysI18n> list = lambdaQuery()
                .eq(dto.getType() != null, SysI18n::getType, dto.getType())
                .like(StrUtil.isNotBlank(dto.getNameZh()), SysI18n::getNameZh, dto.getNameZh())
                .orderByDesc(SysI18n::getId)
                .list();
        if (!list.isEmpty()) {
            Map<String, String> map = DictUtils.getDictCacheMap("sys_i18n_source");
            list.forEach(i -> i.setTypeName(map.get(i.getType().toString())));
        }
        return list;
    }

    @Override
    public void add(I18nTypeEnum type, Long dataId, String name) {
        SysI18n i18n = new SysI18n();
        i18n.setType(type.getType());
        i18n.setDataId(dataId);
        i18n.setNameZh(name);
        i18n.setNameDe(TransApi.get(name, LanguageEnum.DE.getBaidu()));
        i18n.setNameEn(TransApi.get(name, LanguageEnum.EN.getBaidu()));
        i18n.setNameFra(TransApi.get(name, LanguageEnum.FR.getBaidu()));
        save(i18n);
    }

    @Override
    public void update(I18nTypeEnum type, Long dataId, String name) {
        SysI18n i18n = lambdaQuery().eq(SysI18n::getType, type.getType())
                .eq(SysI18n::getDataId, dataId).one();
        if (i18n == null) {
            add(type, dataId, name);
        } else {
            if (!i18n.getNameZh().equals(name)) {
                i18n.setNameZh(name);
                i18n.setNameDe(TransApi.get(name, LanguageEnum.DE.getBaidu()));
                i18n.setNameEn(TransApi.get(name, LanguageEnum.EN.getBaidu()));
                i18n.setNameFra(TransApi.get(name, LanguageEnum.FR.getBaidu()));
                updateById(i18n);
            }
        }
    }

    @Override
    public void delete(I18nTypeEnum type, Long dataId) {
        lambdaUpdate().eq(SysI18n::getType, type.getType())
                .eq(SysI18n::getDataId, dataId)
                .remove();
    }

    @Override
    public boolean update(SysI18nUpdateDTO dto) {
        SysI18n i18n = getByIdThrow(dto.getId());
        lambdaUpdate().eq(SysI18n::getId, dto.getId())
                .set(SysI18n::getNameDe, dto.getNameDe())
                .set(SysI18n::getNameEn, dto.getNameEn())
                .set(SysI18n::getNameFra, dto.getNameFra())
                .update();
        switch (i18n.getType()) {
            case 2 -> {
                SysDictData data = dictDataMapper.selectById(i18n.getDataId());
                if (data != null) {
                    RedisUtil.delete("sys_dict:"+data.getDictType()+":*");
                }
            }
            case 4, 5 -> RedisUtil.delete(CacheConstants.FAULT_DATA_KEY+"*");
        }
        return true;
    }

    @Override
    @Transactional
    public boolean importData(List<SysI18n> list) {
        Map<Integer, SysI18n> map = lambdaQuery()
                .select(SysI18n::getId, SysI18n::getNameDe, SysI18n::getNameEn, SysI18n::getNameFra)
                .list().stream().collect(Collectors.toMap(SysI18n::getId, Function.identity()));
        if (!map.isEmpty()) {
            List<SysI18n> update = new ArrayList<>();
            for (SysI18n i18n : list) {
                SysI18n i = map.get(i18n.getId());
                if (i != null) {
                    if (!Objects.equals(i18n.getNameDe(), i.getNameDe()) || !Objects.equals(i18n.getNameEn(), i.getNameEn()) || !Objects.equals(i18n.getNameFra(), i.getNameFra())) {
                        i.setNameDe(i18n.getNameDe());
                        i.setNameEn(i18n.getNameEn());
                        i.setNameFra(i18n.getNameFra());
                        update.add(i);
                    }
                }
            }
            if (!update.isEmpty()) {
                updateBatchById(update);
            }
        }
        RedisUtil.delete("sys_dict:*");
        RedisUtil.delete(CacheConstants.FAULT_DATA_KEY+"*");
        return true;
    }

}
