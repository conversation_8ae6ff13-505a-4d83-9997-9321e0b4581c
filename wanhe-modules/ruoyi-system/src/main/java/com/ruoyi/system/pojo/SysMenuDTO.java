package com.ruoyi.system.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/11/7
 */
@Data
public class SysMenuDTO {

    /** 菜单名称 */
    @NotBlank
    private String menuName;

    /** 父菜单ID */
    @NotNull
    private Long parentId;

    /** 显示顺序 */
    @NotNull
    private Integer orderNum;

    /** 路由地址 */
    @Size(max = 200)
    private String path;

    /** 组件路径 */
    @Size(max = 200)
    private String component;

    /** 路由参数 */
    private String query;

    /** 路由名称，默认和路由地址相同的驼峰格式（注意：因为vue3版本的router会删除名称相同路由，为避免名字的冲突，特殊情况可以自定义） */
    private String routeName;

    /** 是否为外链（0是 1否） */
    @NotBlank
    @Pattern(regexp = "^0|1$")
    private String isFrame;

    /** 是否缓存（0缓存 1不缓存） */
    @NotBlank
    @Pattern(regexp = "^0|1$")
    private String isCache;

    /** 菜单位置（0安装端 1后台管理端） */
    @NotBlank
    @Pattern(regexp = "^0|1$")
    private String menuLocation;

    /** 类型（M目录 C菜单 F按钮） */
    @NotBlank
    @Pattern(regexp = "^M|C|F$")
    private String menuType;

    /** 显示状态（0显示 1隐藏） */
    @NotBlank
    @Pattern(regexp = "^0|1$")
    private String visible;

    /** 菜单状态（0正常 1停用） */
    @NotBlank
    @Pattern(regexp = "^0|1$")
    private String status;

    /** 权限字符串 */
    @Size(max = 100)
    @JsonProperty
    private String perms;

    /** 菜单图标 */
    private String icon;

    /** 备注 */
    private String remark;

}
