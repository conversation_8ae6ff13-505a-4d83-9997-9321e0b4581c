package com.ruoyi.system.service;

import com.ruoyi.common.security.service.IBaseService;
import com.ruoyi.system.domain.SysHelp;
import com.ruoyi.system.pojo.SysHelpDTO;
import com.ruoyi.system.pojo.SysHelpPageReqDTO;
import com.ruoyi.system.pojo.SysHelpUpdateDTO;
import com.ruoyi.system.pojo.UpgradeDTO;

/**
 * 公告 服务层
 *
 * <AUTHOR>
 */
public interface ISysHelpService extends IBaseService<SysHelp> {

    void page(SysHelpPageReqDTO dto);

    SysHelp upgrade(UpgradeDTO dto);

    Boolean mustUpgrade(UpgradeDTO dto);

    boolean add(SysHelpDTO dto);

    boolean update(SysHelpUpdateDTO dto);

}
