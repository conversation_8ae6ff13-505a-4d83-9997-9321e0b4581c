package com.ruoyi.system.service;

import com.ruoyi.common.security.service.IMPJBaseService;
import com.ruoyi.system.api.domain.SysDept;
import com.ruoyi.system.domain.SysDeptDistrict;
import com.ruoyi.system.domain.vo.TreeSelect;
import com.ruoyi.system.pojo.SysDeptDTO;
import com.ruoyi.system.pojo.SysDeptSearchReq;
import com.ruoyi.system.pojo.SysDeptUpdateDTO;

import java.util.List;
import java.util.Map;

/**
 * 部门管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysDeptService extends IMPJBaseService<SysDept> {

    Map<Long, String> getName(List<Long> ids);

    List<SysDept> list(SysDeptSearchReq req);

    /**
     * 构建前端所需要树结构
     *
     * @param depts 部门列表
     * @return 树结构列表
     */
    List<SysDept> buildDeptTree(List<SysDept> depts);

    /**
     * 构建前端所需要下拉树结构
     *
     * @param depts 部门列表
     * @return 下拉树结构列表
     */
    List<TreeSelect> buildDeptTreeSelect(List<SysDept> depts);

    /**
     * 根据ID查询所有子部门（正常状态）
     *
     * @param deptId 部门ID
     * @return 子部门数
     */
    int selectNormalChildrenDeptById(Long deptId);

    /**
     * 是否存在部门子节点
     *
     * @param deptId 部门ID
     * @return 结果
     */
    boolean hasChildByDeptId(Long deptId);

    /**
     * 查询部门是否存在用户
     *
     * @param deptId 部门ID
     * @return 结果 true 存在 false 不存在
     */
    boolean checkDeptExistUser(Long deptId);

    /**
     * 新增保存部门信息
     *
     * @param dept 部门信息
     * @return 结果
     */
    boolean insertDept(SysDeptDTO dept);

    /**
     * 修改保存部门信息
     *
     * @param dto 部门信息
     * @return 结果
     */
    boolean updateDept(SysDeptUpdateDTO dto);

    boolean updateDistrict(SysDeptDistrict dd);

    SysDeptDistrict selectDeptDistrict(Long tenantId);

    boolean delete(Long deptId);
}
