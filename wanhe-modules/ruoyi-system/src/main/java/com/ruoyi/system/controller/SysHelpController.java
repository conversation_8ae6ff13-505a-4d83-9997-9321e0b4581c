package com.ruoyi.system.controller;

import com.github.pagehelper.Page;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.SysHelp;
import com.ruoyi.system.pojo.SysHelpPageReqDTO;
import com.ruoyi.system.pojo.SysHelpDTO;
import com.ruoyi.system.pojo.SysHelpUpdateDTO;
import com.ruoyi.system.service.ISysHelpService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 帮助中心
 * <AUTHOR>
 * @date 2024/11/12
 */
@RestController
@RequestMapping("/help/centre")
public class SysHelpController extends BaseController {

    @Resource
    private ISysHelpService helpService;

    /**
     * 分页 system:help:page
     */
    @RequiresPermissions("system:help:page")
    @GetMapping("/page")
    public TableDataInfo<SysHelp> page(SysHelpPageReqDTO dto) {
        Page<SysHelp> page = startPage(dto);
        dto.setType(1);
        helpService.page(dto);
        return getDataTable(page);
    }

    /**
     * 详细
     */
    @GetMapping(value = "/{id}")
    public R<SysHelp> getInfo(@PathVariable Integer id) {
        return R.ok(helpService.getById(id));
    }

    /**
     * 新增 system:help:add
     */
    @RequiresPermissions("system:help:add")
    @Log(title = "帮助中心", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@Validated @RequestBody SysHelpDTO dto) {
        dto.setType(1);
        return R.ok(helpService.add(dto));
    }

    /**
     * 修改 system:help:edit
     */
    @RequiresPermissions("system:help:edit")
    @Log(title = "帮助中心", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@Validated @RequestBody SysHelpUpdateDTO dto) {
        return R.ok(helpService.update(dto));
    }

    /**
     * 删除 system:help:remove
     */
    @RequiresPermissions("system:help:remove")
    @Log(title = "帮助中心", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public R<Boolean> remove(@PathVariable Integer id) {
        return R.ok(helpService.removeById(id));
    }

}
