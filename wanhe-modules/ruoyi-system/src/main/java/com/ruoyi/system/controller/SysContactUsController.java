package com.ruoyi.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.Page;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.SysContactUs;
import com.ruoyi.system.pojo.SysContactUsPageReqDTO;
import com.ruoyi.system.service.ISysContactUsService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 联系我们
 *
 * <AUTHOR>
 * @date 2025/05/13
 */
@RestController
@RequestMapping("/contactUs")
public class SysContactUsController extends BaseController {

    @Resource
    private ISysContactUsService contactUsService;

    /**
     * 分页
     */
    @RequiresPermissions("system:contactUs:page")
    @GetMapping("/page")
    public TableDataInfo<SysContactUs> page(SysContactUsPageReqDTO dto) {
        Page<SysContactUs> page = startPage(dto);
        contactUsService.page(dto);
        return getDataTable(page);
    }

    /**
     * 查询所有
     */
    @GetMapping("/list")
    public R<List<SysContactUs>> list(@RequestParam String language) {
        List<SysContactUs> list = contactUsService.list(new LambdaQueryWrapper<SysContactUs>().eq(SysContactUs::getLanguage, language).orderByAsc(SysContactUs::getSort));
        return R.ok(list);
    }

    /**
     * 详细
     */
    @GetMapping(value = "/{id}")
    public R<SysContactUs> getInfo(@PathVariable Integer id) {
        return R.ok(contactUsService.getById(id));
    }

    /**
     * 新增
     */
    @RequiresPermissions("system:contactUs:add")
    @Log(title = "联系我们", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@Validated @RequestBody SysContactUs contactUs) {
        return R.ok(contactUsService.save(contactUs));
    }

    /**
     * 修改
     */
    @RequiresPermissions("system:contactUs:edit")
    @Log(title = "联系我们", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@Validated @RequestBody SysContactUs contactUs) {
        return R.ok(contactUsService.updateById(contactUs));
    }

    /**
     * 删除
     */
    @RequiresPermissions("system:contactUs:del")
    @Log(title = "联系我们", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public R<Boolean> remove(@PathVariable Integer id) {
        return R.ok(contactUsService.removeById(id));
    }

}
