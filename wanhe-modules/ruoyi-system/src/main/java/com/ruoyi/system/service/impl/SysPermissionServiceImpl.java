package com.ruoyi.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.ruoyi.common.security.service.BaseService;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.service.ISysMenuService;
import com.ruoyi.system.service.ISysPermissionService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Set;

/**
 * 用户权限处理
 *
 * <AUTHOR>
 */
@Service
public class SysPermissionServiceImpl implements ISysPermissionService, BaseService {

    @Resource
    private ISysMenuService menuService;

    /**
     * 获取菜单数据权限
     *
     * @param user 用户Id
     * @return 菜单权限信息
     */
    @Override
    public Set<String> getMenuPermission(SysUser user) {
        // 管理员拥有所有权限
        if (isAdminRole(user.getRoleIds())) {
            Set<String> perms = new HashSet<>(1);
            perms.add("*:*:*");
            return perms;
        } else {
            if (CollUtil.isNotEmpty(user.getRoles())) {
                return menuService.selectMenuPermsByRoleIds(user.getRoleIds());
            } else {
                return menuService.selectMenuPermsByUserId(user.getUserId());
            }
        }
    }
}
