package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/1/12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("sys_api_token_station")
public class SysApiTokenStation {

    @Schema(description = "token编号")
    @TableId
    private Long tokenId;

    @Schema(description = "电站编号")
    private Integer stationId;

}
