package com.ruoyi.system.pojo;

import jakarta.validation.constraints.*;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/6
 */
@Data
public class SysUserDTO {

    /** 部门ID */
    private Long deptId;

    /** 姓名 */
    @NotBlank
    private String nickName;

    /** 角色组 */
    private List<Long> roleIds;

    /** 区号 */
    private String areaCode;

    /** 手机号码 */
    private String phonenumber;

    /** 用户账号 */
    @NotBlank
    private String userName;

    /** 密码 */
    @NotBlank
    private String password;

    /** 用户邮箱 */
    @Email
    @Size(max = 50)
    private String email;

    /** 帐号状态（0正常 1停用） */
    @NotBlank
    @Pattern(regexp = "^0|1$")
    private String status;

    /** 用户类型（00 员工 01 业主） */
    @NotBlank
    @Pattern(regexp = "^00|01$")
    private String userType;

    /** 电站ID 和 租户id一起传 */
    private Integer powerStationId;

    /** 备注 */
    private String remark;

}
