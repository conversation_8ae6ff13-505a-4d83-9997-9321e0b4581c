package com.ruoyi.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.ruoyi.common.core.constant.UserConstants;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.service.impl.MPJBaseServiceImpl;
import com.ruoyi.system.api.domain.SysDept;
import com.ruoyi.system.domain.SysDeptDistrict;
import com.ruoyi.system.domain.vo.TreeSelect;
import com.ruoyi.system.mapper.SysDeptDistrictMapper;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.pojo.SysDeptDTO;
import com.ruoyi.system.pojo.SysDeptSearchReq;
import com.ruoyi.system.pojo.SysDeptUpdateDTO;
import com.ruoyi.system.service.ISysDeptService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 部门管理 服务实现
 *
 * <AUTHOR>
 */
@Service
public class SysDeptServiceImpl extends MPJBaseServiceImpl<SysDeptMapper, SysDept> implements ISysDeptService {

    @Resource
    private SysDeptDistrictMapper sysDeptDistrictMapper;

    @Override
    public Map<Long, String> getName(List<Long> ids) {
        return baseMapper.getName(join(ids)).stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
    }

    @Override
    public List<SysDept> list(SysDeptSearchReq req) {
        return lambdaQueryTenant(SysDept::getTenantId, SysDept::getDeptId, SysDept::getCreateId)
                .selectAll(SysDept.class)
                .select("p.dept_name as parentName")
                .leftJoin("sys_dept p on t.parent_id = p.dept_id")
                .in(Boolean.FALSE.equals(req.getDept()), SysDept::getType, List.of(1,2,3))
                .like(StrUtil.isNotBlank(req.getDeptName()), SysDept::getDeptName, req.getDeptName())
                .eq(StrUtil.isNotBlank(req.getStatus()), SysDept::getStatus, req.getStatus())
                .orderByAsc(SysDept::getType)
                .orderByAsc(SysDept::getOrderNum)
                .list();
    }

    /**
     * 构建前端所需要树结构
     *
     * @param depts 部门列表
     * @return 树结构列表
     */
    @Override
    public List<SysDept> buildDeptTree(List<SysDept> depts) {
        List<SysDept> returnList = new ArrayList<>();
        List<Long> tempList = depts.stream().map(SysDept::getDeptId).toList();
        for (SysDept dept : depts) {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(dept.getParentId())) {
                recursionFn(depts, dept);
                returnList.add(dept);
            }
        }
        if (returnList.isEmpty()) {
            returnList = depts;
        }
        return returnList;
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param depts 部门列表
     * @return 下拉树结构列表
     */
    @Override
    public List<TreeSelect> buildDeptTreeSelect(List<SysDept> depts) {
        List<SysDept> deptTrees = buildDeptTree(depts);
        return deptTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    /**
     * 根据ID查询所有子部门（正常状态）
     *
     * @param deptId 部门ID
     * @return 子部门数
     */
    @Override
    public int selectNormalChildrenDeptById(Long deptId) {
        return baseMapper.selectNormalChildrenDeptById(deptId);
    }

    /**
     * 是否存在子节点
     *
     * @param deptId 部门ID
     * @return 结果
     */
    @Override
    public boolean hasChildByDeptId(Long deptId) {
        int result = baseMapper.hasChildByDeptId(deptId);
        return result > 0;
    }

    /**
     * 查询部门是否存在用户
     *
     * @param deptId 部门ID
     * @return 结果 true 存在 false 不存在
     */
    @Override
    public boolean checkDeptExistUser(Long deptId) {
        int result = baseMapper.checkDeptExistUser(deptId);
        return result > 0;
    }

    /**
     * 新增保存部门信息
     *
     * @param dto 部门信息
     * @return 结果
     */
    @Override
    @Transactional
    public boolean insertDept(SysDeptDTO dto) {
        SysDept p = getByIdThrow(dto.getParentId());
        if (!UserConstants.DEPT_NORMAL.equals(p.getStatus())) {
            throw new ServiceException("部门停用，不允许新增");
        }
        SysDept sysDept = baseMapper.checkDeptNameUnique(dto.getDeptName(), dto.getParentId());
        if (sysDept != null) {
            throw new ServiceException("数据重复");
        }
        SysDept dept = BeanUtil.copyProperties(dto, SysDept.class);
        dept.setAncestors(p.getAncestors() + "," + dept.getParentId());
        save(dept);
        if (dto.getType() == 4) {
            dept.setTenantId(p.getTenantId());
        } else {
            dept.setTenantId(dept.getDeptId());
            sysDeptDistrictMapper.insert(new SysDeptDistrict(dept.getTenantId(), Collections.emptyList()));
        }
        return lambdaUpdate().eq(SysDept::getDeptId, dept.getDeptId())
                .set(SysDept::getFullPath, p.getFullPath() + "," + dept.getDeptId())
                .set(SysDept::getTenantId, dept.getTenantId())
                .update();
    }

    /**
     * 修改保存部门信息
     *
     * @param dto 部门信息
     * @return 结果
     */
    @Override
    @Transactional
    public boolean updateDept(SysDeptUpdateDTO dto) {
        if (StringUtils.equals(UserConstants.DEPT_DISABLE, dto.getStatus()) && selectNormalChildrenDeptById(dto.getDeptId()) > 0) {
            throw new ServiceException("该部门包含未停用的子部门");
        }
        SysDept dept = getByIdThrow(dto.getDeptId());
        if (!dept.getParentId().equals(dto.getParentId())) {
            if (dto.getParentId() == 0 && dto.getDeptId() != 100L) {
                throw new ServiceException("不能修改到主目录");
            }
            SysDept newParentDept = getByIdThrow(dto.getParentId());
            if (dto.getDeptId().equals(dto.getParentId()) || Arrays.asList(newParentDept.getFullPath().split(",")).contains(dto.getDeptId().toString())) {
                throw new ServiceException("上级部门不能是自己或下级部门", dto.getDeptName());
            }
            if (newParentDept.getType() >= dept.getType()) {
                throw new ServiceException("上级机构不能更改到同级或下级机构", dto.getDeptName());
            }
            boolean exist = baseMapper.exist(dto.getDeptId());
            if (exist) {
                throw new ServiceException("存在用户或电站不能修改父级机构");
            }
            String newAncestors = newParentDept.getAncestors() + "," + newParentDept.getDeptId();
            String newFullPath = newParentDept.getFullPath() + "," + dto.getDeptId();
            dept.setAncestors(newAncestors);
            dept.setFullPath(newFullPath);
            if (dto.getType() == 4) {
                dept.setTenantId(newParentDept.getTenantId());
            } else {
                dept.setTenantId(dept.getDeptId());
            }
        }
        SysDept sysDept = baseMapper.checkDeptNameUnique(dto.getDeptName(), dept.getParentId());
        if (sysDept != null && !sysDept.getDeptId().equals(dto.getDeptId())) {
            throw new ServiceException("数据重复");
        }
        BeanUtil.copyProperties(dto, dept);
        if (UserConstants.DEPT_NORMAL.equals(dto.getStatus()) && StringUtils.isNotEmpty(dept.getAncestors())
                && !StringUtils.equals("0", dept.getAncestors())) {
            // 如果该部门是启用状态，则启用该部门的所有上级部门
            updateParentDeptStatusNormal(dept);
        }
        return updateById(dept);
    }

    @Override
    public boolean updateDistrict(SysDeptDistrict dd) {
        return SqlHelper.retBool(sysDeptDistrictMapper.updateById(dd));
    }

    @Override
    public SysDeptDistrict selectDeptDistrict(Long tenantId) {
        return sysDeptDistrictMapper.selectById(tenantId);
    }

    @Override
    public boolean delete(Long deptId) {
        if (baseMapper.exist(deptId)) {
            throw new ServiceException("存在用户或电站，不允许删除");
        }
        return removeById(deptId);
    }

    /**
     * 修改该部门的父级部门状态
     *
     * @param dept 当前部门
     */
    private void updateParentDeptStatusNormal(SysDept dept) {
        String ancestors = dept.getAncestors();
        Long[] deptIds = Convert.toLongArray(ancestors);
        baseMapper.updateDeptStatusNormal(deptIds);
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<SysDept> list, SysDept t) {
        // 得到子节点列表
        List<SysDept> childList = getChildList(list, t);
        t.setChildren(childList);
        for (SysDept tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<SysDept> getChildList(List<SysDept> list, SysDept t) {
        List<SysDept> tlist = new ArrayList<>();
        for (SysDept n : list) {
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getDeptId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<SysDept> list, SysDept t) {
        return !getChildList(list, t).isEmpty();
    }
}
