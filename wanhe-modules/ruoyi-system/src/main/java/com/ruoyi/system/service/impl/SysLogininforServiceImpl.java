package com.ruoyi.system.service.impl;

import cn.hutool.core.util.ObjUtil;
import com.ruoyi.common.security.service.impl.BaseServiceImpl;
import com.ruoyi.system.api.domain.SysLogininfor;
import com.ruoyi.system.mapper.SysLogininforMapper;
import com.ruoyi.system.pojo.SysLogininforPageReqDTO;
import com.ruoyi.system.service.ISysLogininforService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * 系统访问日志情况信息 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysLogininforServiceImpl extends BaseServiceImpl<SysLogininforMapper, SysLogininfor> implements ISysLogininforService {

    @Resource
    private RestTemplate restTemplate;

    /**
     * 新增系统登录日志
     *
     * @param logininfor 访问日志对象
     */
    @Override
    public boolean insertLogininfor(SysLogininfor logininfor) {
        String ip = logininfor.getIpaddr();
        if (ObjUtil.isNotEmpty(ip)) {
            String ipAddr = restTemplate.getForObject("http://ip.plyz.net/ip.ashx?ip=" + ip, String.class);
            if (!ObjectUtils.isEmpty(ipAddr)) {
                String addr = ipAddr.substring(ip.length() + 1);
                if (addr.startsWith("内网")) {
                    logininfor.setOperLocation("内网IP");
                } else {
                    logininfor.setOperLocation(addr);
                }
            }
        }
        return save(logininfor);
    }

    @Override
    public List<SysLogininfor> page(SysLogininforPageReqDTO dto) {
        return lambdaQueryTenant(SysLogininfor::getTenantId, SysLogininfor::getDeptId, SysLogininfor::getCreateId)
                .eq(ObjUtil.isNotEmpty(dto.getIpaddr()), SysLogininfor::getIpaddr, dto.getIpaddr())
                .eq(ObjUtil.isNotEmpty(dto.getStatus()), SysLogininfor::getStatus, dto.getStatus())
                .eq(ObjUtil.isNotEmpty(dto.getUserName()), SysLogininfor::getUserName, dto.getUserName())
                .between(dto.getBeginTime() != null && dto.getEndTime() != null, SysLogininfor::getAccessTime, dto.getBeginTime(), dto.getEndTime())
                .orderByDesc(SysLogininfor::getInfoId)
                .list();
    }

    /**
     * 查询系统登录日志集合
     *
     * @param logininfor 访问日志对象
     * @return 登录记录集合
     */
    @Override
    public List<SysLogininfor> selectLogininforList(SysLogininfor logininfor) {
        return baseMapper.selectLogininforList(logininfor);
    }

    /**
     * 批量删除系统登录日志
     *
     * @param infoIds 需要删除的登录日志ID
     * @return 结果
     */
    @Override
    public int deleteLogininforByIds(Long[] infoIds) {
        return baseMapper.deleteLogininforByIds(infoIds);
    }

    /**
     * 清空系统登录日志
     */
    @Override
    public void cleanLogininfor() {
        baseMapper.cleanLogininfor();
    }
}
