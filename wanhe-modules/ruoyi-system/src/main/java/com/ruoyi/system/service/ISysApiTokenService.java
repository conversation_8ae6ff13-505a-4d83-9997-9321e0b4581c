package com.ruoyi.system.service;

import com.ruoyi.common.security.service.IMPJBaseService;
import com.ruoyi.system.api.domain.SysApiToken;
import com.ruoyi.system.pojo.ApiTokenDTO;
import com.ruoyi.system.pojo.ApiTokenUpdateDTO;
import com.ruoyi.system.pojo.SysApiTokenPageReqDTO;

/**
 * <AUTHOR>
 * @date 2024/11/11
 */
public interface ISysApiTokenService extends IMPJBaseService<SysApiToken> {

    void page(SysApiTokenPageReqDTO dto);

    boolean add(ApiTokenDTO dto);

    boolean update(ApiTokenUpdateDTO dto);

    SysApiToken get(Long apiTokenId);

    boolean delete(Long apiTokenId);
}
