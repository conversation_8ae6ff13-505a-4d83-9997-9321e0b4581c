package com.ruoyi.system.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Future;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/8
 */
@Setter
@Getter
public class ApiTokenUpdateDTO extends ApiTokenDTO {

    @Schema(description = "主键")
    @NotNull
    private Long id;

}
