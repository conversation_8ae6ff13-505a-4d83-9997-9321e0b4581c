package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.domain.SysStates;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/2
 */
public interface SysStatesMapper extends BaseMapper<SysStates> {

    @Select("select province from sys_states where country = #{country} order by id")
    List<String> getAllByCountry(String country);

}
