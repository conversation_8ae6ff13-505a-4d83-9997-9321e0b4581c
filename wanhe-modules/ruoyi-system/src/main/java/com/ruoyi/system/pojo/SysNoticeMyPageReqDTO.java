package com.ruoyi.system.pojo;

import com.ruoyi.common.core.domain.PageReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/11/6
 */
@Setter
@Getter
public class SysNoticeMyPageReqDTO extends PageReqDTO {

    @Schema(description = "类型（1通知 2公告）", hidden = true)
    private Integer type;

    /** 标题 */
    private String title;

    /** 标题英文 */
    private String titleEn;

    //开始时间
    private LocalDateTime beginTime;

    //结束时间
    private LocalDateTime endTime;

    //是否已读
    private Boolean isRead;

}
