package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.web.domain.base.CUEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 帮助中心表 sys_help
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("sys_help")
public class SysHelp extends CUEntity {

    /** ID */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /** 标题 */
    private String title;

    /** 语言 */
    private String language;

    @JsonIgnore
    @Schema(description = "类型（1帮助中心 2帮助文件 3版本升级）", hidden = true)
    private Integer type;

    /** 内容 */
    private String content;

    /** 排序 */
    private Integer sort;

    /** 文件大小 */
    private String fileSize;

    /** 文件类型 */
    private String fileType;

    /** 版本 */
    private String versions;

    /** 客户端 2安装端 3客户端 */
    private Integer clientType;

    /** 版本号 */
    private Integer versionNumber;

    /** 系统类型 1安卓 2ios */
    private Integer sysType;

    /** 强制升级 0：非强制  1：强制 */
    private Integer must;

}
