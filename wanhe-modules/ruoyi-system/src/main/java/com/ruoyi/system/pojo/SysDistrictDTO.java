package com.ruoyi.system.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-10-30
 */
@Data
@Schema(name = "SysDistrictDTO", description = "行政区域")
public class SysDistrictDTO {

    @Schema(description = "父级(不存在父级的情况传0)")
    @NotNull
    @PositiveOrZero
    private Integer pid;

    @Schema(description = "行政区名称")
    @NotBlank
    private String name;

}
