package com.ruoyi.system.controller;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.ClientTypeEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.SysMenu;
import com.ruoyi.system.domain.vo.RouterVo;
import com.ruoyi.system.pojo.SysMenuDTO;
import com.ruoyi.system.pojo.SysMenuSearchReq;
import com.ruoyi.system.pojo.SysMenuUpdateDTO;
import com.ruoyi.system.service.ISysMenuService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 菜单信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/menu")
public class SysMenuController extends BaseController {

    @Resource
    private ISysMenuService menuService;

    /**
     * 新增菜单 system:menu:add
     */
    @RequiresPermissions("system:menu:add")
    @Log(title = "菜单管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@Validated @RequestBody SysMenuDTO dto) {
        if (StrUtil.isBlank(dto.getPerms())) {
            dto.setPerms(StrUtil.EMPTY);
        } else {
            dto.setPerms(dto.getPerms().trim());
        }
        return R.ok(menuService.insertMenu(dto));
    }

    /**
     * 修改菜单 system:menu:edit
     */
    @RequiresPermissions("system:menu:edit")
    @Log(title = "菜单管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@Validated @RequestBody SysMenuUpdateDTO dto) {
        if (StrUtil.isBlank(dto.getPerms())) {
            dto.setPerms(StrUtil.EMPTY);
        } else {
            dto.setPerms(dto.getPerms().trim());
        }
        return R.ok(menuService.updateMenu(dto));
    }

    /**
     * 获取菜单列表 system:menu:list
     */
    @RequiresPermissions("system:menu:list")
    @GetMapping("/list")
    public R<List<SysMenu>> list(@Validated SysMenuSearchReq req) {
        return R.ok(menuService.selectMenuList(req));
    }

    /**
     * 根据菜单编号获取详细信息 system:menu:query
     */
    @RequiresPermissions("system:menu:query")
    @GetMapping(value = "/{menuId}")
    public R<SysMenu> getInfo(@PathVariable Long menuId) {
        return R.ok(menuService.selectMenuById(menuId));
    }

    /**
     * 获取菜单下拉树列表
     */
    @GetMapping("/treeselect")
    public R<List<SysMenu>> treeselect(SysMenuSearchReq req) {
        return R.ok(menuService.selectMenuList(req));
    }

    /**
     * 加载对应角色菜单列表树
     */
    @GetMapping(value = "/roleMenuTreeselect/{roleId}")
    public AjaxResult roleMenuTreeselect(@PathVariable("roleId") Long roleId) {
        List<SysMenu> menus = menuService.selectMenuList();
        AjaxResult ajax = AjaxResult.success();
        ajax.put("checkedKeys", menuService.selectMenuListByRoleId(roleId));
        ajax.put("menus", menuService.buildMenuTreeSelect(menus));
        return ajax;
    }

    /**
     * 删除菜单 system:menu:remove
     */
    @RequiresPermissions("system:menu:remove")
    @Log(title = "菜单管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{menuId}")
    public AjaxResult remove(@PathVariable("menuId") Long menuId) {
        if (menuService.hasChildByMenuId(menuId)) {
            throw new ServiceException("存在子菜单,不允许删除");
        }
        if (menuService.checkMenuExistRole(menuId)) {
            throw new ServiceException("菜单已分配,不允许删除");
        }
        return toAjax(menuService.deleteMenuById(menuId));
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("/getRouters")
    public R<List<RouterVo>> getRouters(@RequestHeader(SecurityConstants.CLIENT_TYPE) String clientType) {
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(ClientTypeEnum.of(clientType));
        return R.ok(menuService.buildMenus(menus));
    }

}