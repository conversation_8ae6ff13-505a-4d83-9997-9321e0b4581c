package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/8
 */
@Data
@NoArgsConstructor
@TableName(value = "sys_dept_district", autoResultMap = true)
public class SysDeptDistrict {

    /** 租户ID */
    @TableId(type = IdType.INPUT)
    @NotNull
    private Long tenantId;

    /** 是否全部国家 */
    @TableField(value = "is_all_country")
    @NotNull
    private Boolean allCountry;

    /** 国家区域Id */
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private List<Integer> countryIds;

    public SysDeptDistrict(Long tenantId, List<Integer> countryIds) {
        this.tenantId = tenantId;
        this.countryIds = countryIds;
    }
}
