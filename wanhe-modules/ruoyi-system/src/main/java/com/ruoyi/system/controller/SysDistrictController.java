package com.ruoyi.system.controller;

import com.github.pagehelper.Page;
import com.ruoyi.common.core.domain.IntStr;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.SysDistrict;
import com.ruoyi.system.pojo.*;
import com.ruoyi.system.service.ISysDistrictService;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 行政区域管理
 * <AUTHOR>
 * @date 2024/10/30
 */
@RestController
@RequestMapping("/district")
public class SysDistrictController extends BaseController {

    @Resource
    private ISysDistrictService districtService;

    /**
     * 新增 system:sysDistrict:add
    @RequiresPermissions("system:sysDistrict:add")
    @Log(title = "地区", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@Validated @RequestBody SysDistrictDTO dto) {
        return R.ok(districtService.add(dto));
    }
     */

    /**
     * 修改 system:sysDistrict:edit
    @RequiresPermissions("system:sysDistrict:edit")
    @Log(title = "地区", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@Validated @RequestBody SysDistrictUpdateDTO dto) {
        return R.ok(districtService.update(dto));
    }
     */

    /**
     * 详情
     */
    @GetMapping(value = "/{id}")
    public R<SysDistrict> getInfo(@PathVariable Integer id) {
        return R.ok(districtService.getById(id));
    }

    /**
     * 分页 system:sysDistrict:page
     */
    @RequiresPermissions("system:sysDistrict:page")
    @GetMapping("/page")
    public TableDataInfo<SysDistrict> page(@Validated SysDistrictPageReqDTO dto) {
        Page<SysDistrict> page = districtService.page(dto);
        return getDataTable(page);
    }

    /**
     * 删除 system:sysDistrict:remove
     */
    @RequiresPermissions("system:sysDistrict:remove")
    @Log(title = "地区", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public R<Boolean> remove(@PathVariable Integer id) {
        return R.ok(districtService.delete(id));
    }

    /**
     * 通过父级查询子集列表
     */
    @GetMapping("/list/{pid}")
    public R<List<IntStr>> list(@PathVariable Integer pid) {
        return R.ok(districtService.listByPid(pid));
    }

    /**
     * 通过多个父级查询子集列表
     */
    @GetMapping("/multi/list")
    public R<List<SysDistrictVO>> multiList(@RequestParam String pid) {
        return R.ok(districtService.listByPid(pid));
    }

    /**
     * 通过id查询名称
     */
    @PostMapping("/name")
    public R<List<IntStr>> nameById(@Validated @NotEmpty @RequestBody List<Integer> ids) {
        return R.ok(districtService.listByIds(ids));
    }

    /**
     * 查询全部国家
     */
    @GetMapping("/country")
    public R<List<CountryDTO>> getAllCountry() {
        return R.ok(districtService.getAllCountry());
    }

    /**
     * 更新全部国家
     */
    @PutMapping("/country")
    public R<Boolean> updateAllCountry(@Validated @NotNull @RequestBody List<CountryDTO> list) {
        return R.ok(districtService.updateAllCountry(list));
    }

}