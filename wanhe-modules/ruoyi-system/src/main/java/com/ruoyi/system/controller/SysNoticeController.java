package com.ruoyi.system.controller;

import com.github.pagehelper.Page;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.SysConfig;
import com.ruoyi.system.domain.SysNotice;
import com.ruoyi.system.pojo.SysNoticeDTO;
import com.ruoyi.system.pojo.SysNoticeMyPageReqDTO;
import com.ruoyi.system.pojo.SysNoticePageReqDTO;
import com.ruoyi.system.pojo.SysNoticeUpdateDTO;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysNoticeService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 公告
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/notice")
public class SysNoticeController extends BaseController {

    @Resource
    private ISysNoticeService noticeService;
    @Resource
    private ISysConfigService sysConfigService;

    /**
     * 分页 system:notice:list
     */
    @RequiresPermissions("system:notice:list")
    @GetMapping("/list")
    public TableDataInfo<SysNotice> list(SysNoticePageReqDTO dto) {
        Page<SysNotice> page = startPage(dto);
        dto.setType(1);
        noticeService.page(dto);
        return getDataTable(page);
    }

    /**
     * 个人中心公告分页
     */
    @GetMapping("/page")
    public TableDataInfo<SysNotice> page(SysNoticeMyPageReqDTO dto) {
        Page<SysNotice> page = startPage(dto);
        dto.setType(1);
        noticeService.page(dto);
        return getDataTable(page);
    }

    /**
     * 个人中心详细
     */
    @GetMapping(value = "/info")
    public R<SysNotice> get(@RequestParam Integer id) {
        return R.ok(noticeService.get(id));
    }

    /**
     * 详细
     */
    @GetMapping(value = "/{id}")
    public R<SysNotice> getInfo(@PathVariable Integer id) {
        return R.ok(noticeService.getById(id));
    }


    /**
     * 一键已读所有通知
     *
     * @return {@link R }<{@link Void }>
     */
    @GetMapping(value = "/readAll")
    public R<Void> readAllNotice() {
        noticeService.readAllNotice();
        return R.ok();
    }

    /**
     * 新增 system:notice:add
     */
    @RequiresPermissions("system:notice:add")
    @Log(title = "公告", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@Validated @RequestBody SysNoticeDTO dto) {
        dto.setType(1);
        dto.setEnable(false);
        return R.ok(noticeService.add(dto));
    }

    /**
     * 修改 system:notice:edit
     */
    @RequiresPermissions("system:notice:edit")
    @Log(title = "公告", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@Validated @RequestBody SysNoticeUpdateDTO dto) {
        if (dto.getId() < 0) {
            dto.setId(Math.abs(dto.getId()));
            if (SysConfig.IDS.contains(dto.getId())) {
                sysConfigService.editApp(dto.getId(), dto.getContent());
                return R.ok();
            }
        }
        return R.ok(noticeService.update(dto));
    }

    /**
     * 删除 system:notice:remove
     */
    @RequiresPermissions("system:notice:remove")
    @Log(title = "公告", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public R<Boolean> remove(@PathVariable Integer id) {
        return R.ok(noticeService.removeById(id));
    }

    /**
     * 是否发送公告 system:notice:enable
     */
    @RequiresPermissions("system:notice:enable")
    @Log(title = "公告", businessType = BusinessType.UPDATE)
    @PutMapping("/enable/{id}")
    public R<Boolean> enable(@PathVariable Integer id) {
        return R.ok(noticeService.enable(id));
    }

}
