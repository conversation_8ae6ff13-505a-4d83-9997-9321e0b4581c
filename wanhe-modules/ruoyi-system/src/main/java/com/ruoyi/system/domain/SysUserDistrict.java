package com.ruoyi.system.domain;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.ruoyi.system.api.dto.UserDistrict;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/8
 */
@Data
@NoArgsConstructor
@TableName(value = "sys_user_district", autoResultMap = true)
public class SysUserDistrict {

    /** 用户ID */
    @TableId
    @NotNull
    private Long userId;

    /** 是否全部国家 */
    @TableField(value = "is_all_country")
    @NotNull
    private Boolean allCountry;

    /** 国家区域id（全选省的国家） */
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    @NotNull
    private List<Integer> countryIds;

    /** 省级区域Id */
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    @NotNull
    private List<Integer> provinceIds;

    public SysUserDistrict(Long userId) {
        this.userId = userId;
        this.allCountry = Boolean.TRUE;
        this.countryIds = Collections.emptyList();
        this.provinceIds = Collections.emptyList();
    }

    public static String toJson(SysUserDistrict ud) {
        UserDistrict userDistrict = new UserDistrict();
        userDistrict.setAllCountry(ud.getAllCountry());
        if (ud.getCountryIds().isEmpty()) {
            userDistrict.setCountryIdStr(StrUtil.EMPTY);
            userDistrict.setCountryIds(Collections.emptyList());
        } else {
            userDistrict.setCountryIdStr(CollUtil.join(ud.getCountryIds(), ","));
            userDistrict.setCountryIds(ud.getCountryIds());
        }
        if (ud.getProvinceIds().isEmpty()) {
            userDistrict.setProvinceIdStr(StrUtil.EMPTY);
            userDistrict.setProvinceIds(Collections.emptyList());
        } else {
            userDistrict.setProvinceIdStr(CollUtil.join(ud.getProvinceIds(), ","));
            userDistrict.setProvinceIds(ud.getProvinceIds());
        }
        return JSON.toJSONString(userDistrict);
    }

}
