package com.ruoyi.system.service;

import com.github.pagehelper.Page;
import com.ruoyi.common.core.domain.KV;
import com.ruoyi.common.security.service.IMPJBaseService;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.domain.SysUserDistrict;
import com.ruoyi.system.api.domain.SysUserStation;
import com.ruoyi.system.pojo.*;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.util.List;

/**
 * 用户 业务层
 *
 * <AUTHOR>
 */
public interface ISysUserService extends IMPJBaseService<SysUser> {

    void page(SysUserPageReqDTO dto);

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    SysUser selectUserByUserName(String userName, String clientType);

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    SysUser selectUserById(Long userId);

    /**
     * 根据用户ID查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    String selectUserRoleGroup(String userName);

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     */
    void checkUserNameUnique(SysUser user);

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     */
    void checkPhoneUnique(SysUser user);

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     */
    void checkEmailUnique(SysUser user);

    void checkSn(SysUser user);

    /**
     * 新增用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    boolean insertUser(SysUserDTO user);

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    SysUser registerUser(SysUser user);

    /**
     * 修改用户信息
     *
     * @param dto 用户信息
     * @return 结果
     */
    boolean updateUser(SysUserUpdateDTO dto);

    /**
     * 修改用户状态
     *
     * @return 结果
     */
    boolean updateUserStatus(SysUserUpdateStatusDTO dto);

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    boolean updateUserProfile(SysUser user);

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar   头像地址
     * @return 结果
     */
    boolean updateUserAvatar(String userName, String avatar);

    /**
     * 重置用户密码
     *
     * @param dto 用户信息
     * @return 结果
     */
    boolean resetPwd(SysUserResetPwdDTO dto);

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    int resetUserPwd(String userName, String password);

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    int deleteUserById(Long userId);

    /**
     * 绑定电站
     */
    boolean boundStation(SysUserStationDTO dto);

    /**
     * 移除电站
     */
    int removeStation(SysUserStation dto);

    /**
     * 通过电站查询业主
     */
    List<String> proprietor(Integer powerStationId);

    SysUserDistrict selectUserDistrict(Long userId);

    boolean updateDistrict(SysUserDistrict ud);

    Page<SysUser> proprietorPage(SysUserProprietorPageReqDTO dto);

    List<SysUser> stationList(ProprietorSearchReqDTO dto);

    boolean updatePassword(SysUser sysUser);

    SysUser getByUsername(String username);

    List<KV> all(Long tenantId);

    boolean lastOperateStation(Integer stationId);

    String logout();

    String logout(Long id);

    void export(HttpServletResponse response) throws IOException;
}
