package com.ruoyi.system.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/7
 */
@Data
public class SysRoleDTO {

    @Schema(description = "租户id")
    @NotNull
    protected Long tenantId;

    /** 角色名称 */
    @NotBlank
    private String roleName;

    /** 角色排序 */
    @NotNull
    private Integer roleSort;

    /** 角色状态（0正常 1停用） */
    @NotBlank
    @Pattern(regexp = "^0|1$")
    private String status;

    /** 备注 */
    private String remark;

    /** 后台菜单树选择项是否关联显示（ 0：父子不互相关联显示 1：父子互相关联显示） */
    @NotNull
    private Boolean menuCheckStrictly;

    /** 安装端菜单树选择项是否关联显示（0：父子不互相关联显示 1：父子互相关联显示 ） */
    @NotNull
    private Boolean deptCheckStrictly;

    /** 菜单ID */
    @NotNull
    private List<Long> menuIds;

    /** 安装端菜单ID */
    @NotNull
    private List<Long> appMenuIds;

    /** 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限 5：仅本人数据权限） */
    @NotNull
    private Integer dataScope;

    /** 自定义部门id */
    private List<Long> customDeptIds;

    /** 自定义部门是否关联显示（ 0：父子不互相关联显示 1：父子互相关联显示） */
    private Boolean customDeptCheckStrictly;

}
