package com.ruoyi.system.controller;

import com.github.pagehelper.Page;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.LanguageEnum;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.SysI18n;
import com.ruoyi.system.pojo.SysI18nPageReqDTO;
import com.ruoyi.system.pojo.SysI18nUpdateDTO;
import com.ruoyi.system.service.ISysI18nService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 国际化配置
 *
 * <AUTHOR>
 * @date 2023-08-24
 */
@RestController
@RequestMapping("/i18n")
public class SysI18nController extends BaseController {

    @Resource
    private ISysI18nService sysI18nService;

    /**
     * 分页 system:i18n:page
     */
    @RequiresPermissions("system:i18n:page")
    @GetMapping("/page")
    public TableDataInfo<SysI18n> page(SysI18nPageReqDTO dto) {
        Page<SysI18n> page = startPage(dto);
        sysI18nService.page(dto);
        return getDataTable(page);
    }

    /**
     * 修改 system:i18n:edit
     */
    @RequiresPermissions("system:i18n:edit")
    @Log(title = "国际化配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@Validated @RequestBody SysI18nUpdateDTO dto) {
        return R.ok(sysI18nService.update(dto));
    }

    /**
     * 导出 system:i18n:export
     */
    @Log(title = "国际化配置", businessType = BusinessType.EXPORT)
    @RequiresPermissions("system:i18n:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, HttpServletRequest request, SysI18nPageReqDTO dto) {
        List<SysI18n> list = sysI18nService.page(dto);
        ExcelUtil<SysI18n> util = new ExcelUtil<>(SysI18n.class);
        util.setLocal(LanguageEnum.getLocal(request));
        util.exportExcel(response, list, "国际化配置");
    }

    /**
     * 导入 system:i18n:import
     */
    @Log(title = "国际化配置", businessType = BusinessType.IMPORT)
    @RequiresPermissions("system:i18n:import")
    @PostMapping("/import")
    public R<Boolean> importData(HttpServletRequest request, MultipartFile file) throws Exception {
        ExcelUtil<SysI18n> util = new ExcelUtil<>(SysI18n.class);
        util.setLocal(LanguageEnum.getLocal(request));
        List<SysI18n> list = util.importExcel(file.getInputStream());
        return R.ok(sysI18nService.importData(list));
    }

}
