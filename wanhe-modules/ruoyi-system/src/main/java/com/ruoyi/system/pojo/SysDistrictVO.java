package com.ruoyi.system.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/9
 */
@Data
public class SysDistrictVO {

    @Schema(description = "父级")
    @JsonIgnore
    private Integer pid;

    @Schema(description = "id")
    private Integer id;

    @Schema(description = "行政区名称")
    private String name;

    @Schema(description = "子集")
    private List<SysDistrictVO> children;

}
