package com.ruoyi.system.controller;

import com.github.pagehelper.Page;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.KV;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.ClientTypeEnum;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.SysRole;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.domain.SysUserStation;
import com.ruoyi.system.api.model.LoginUser;
import com.ruoyi.system.domain.SysUserDistrict;
import com.ruoyi.system.pojo.*;
import com.ruoyi.system.service.ISysPermissionService;
import com.ruoyi.system.service.ISysRoleService;
import com.ruoyi.system.service.ISysUserService;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 用户信息（业主）
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user")
public class SysUserController extends BaseController {

    @Resource
    private ISysUserService userService;
    @Resource
    private ISysRoleService roleService;
    @Resource
    private ISysPermissionService permissionService;
    @Resource
    private TokenService tokenService;

    /**
     * 用户注销
     */
    @Log(title = "用户管理", businessType = BusinessType.LOGOUT)
    @PostMapping("/logout")
    public R<String> logout() {
        return R.ok(userService.logout());
    }

    /**
     * 注销他人账号
     */
    @Log(title = "用户管理", businessType = BusinessType.LOGOUT)
    @PostMapping("/logout/admin")
    public R<String> logout(@Parameter(description = "用户ID") @RequestParam Long id) {
        return R.ok(userService.logout(id));
    }

    /**
     * 新增员工或业主(传电站Id就会绑定电站)
     */
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@Validated @RequestBody SysUserDTO dto) {
        return R.ok(userService.insertUser(dto));
    }

    /**
     * 修改员工或业主
     */
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@Validated @RequestBody SysUserUpdateDTO dto) {
        return R.ok(userService.updateUser(dto));
    }

    /**
     * 员工分页
     */
    @GetMapping("/page")
    public TableDataInfo<SysUser> page(@Validated SysUserPageReqDTO dto) {
        Page<SysUser> page = startPage(dto);
        userService.page(dto);
        return getDataTable(page);
    }

    /**
     * 电站业主列表查询
     */
    @GetMapping("/station/list")
    public R<List<SysUser>> stationList(@Validated ProprietorSearchReqDTO dto) {
        return R.ok(userService.stationList(dto));
    }

    /**
     * 选择业主分页
     */
    @GetMapping("/proprietor/page")
    public TableDataInfo<SysUser> proprietorPage(@Validated SysUserProprietorPageReqDTO dto) {
        return getDataTable(userService.proprietorPage(dto));
    }

    /**
     * 电站绑定业主(选择业主）
     */
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping("/bound/station")
    public R<Boolean> boundStation(@Validated @RequestBody SysUserStationDTO dto) {
        return R.ok(userService.boundStation(dto));
    }

    /**
     * 移除业主
     */
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove/station")
    public R<Boolean> removeStation(@Validated @RequestBody SysUserStation dto) {
        return R.ok(userService.removeStation(dto));
    }

    /**
     * 获取当前用户信息
     */
    @InnerAuth
    @GetMapping("/info/{username}")
    public R<LoginUser> info(@PathVariable("username") String username, @RequestHeader(SecurityConstants.CLIENT_TYPE) String clientType) {
        SysUser sysUser = userService.selectUserByUserName(username, clientType);
        if (StringUtils.isNull(sysUser)) {
            return R.fail("用户名或密码错误");
        }
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(sysUser);
        LoginUser sysUserVo = new LoginUser();
        sysUserVo.setSysUser(sysUser);
        sysUserVo.setRoles(Collections.emptySet());
        sysUserVo.setPermissions(permissions);
        return R.ok(sysUserVo);
    }

    @Schema(description = "注册用户信息", hidden = true)
    @InnerAuth
    @PostMapping("/register")
    public R<SysUser> register(@RequestBody SysUser sysUser) {
        userService.checkSn(sysUser);
        userService.checkUserNameUnique(sysUser);
        userService.checkEmailUnique(sysUser);
        return R.ok(userService.registerUser(sysUser));
    }

    @Schema(description = "修改密码", hidden = true)
    @InnerAuth
    @PostMapping("/update/password")
    public R<Boolean> updatePassword(@RequestBody SysUser sysUser) {
        return R.ok(userService.updatePassword(sysUser));
    }

    @Schema(description = "校验是否发送邮件", hidden = true)
    @InnerAuth
    @GetMapping("/get/by/username")
    public R<SysUser> getByUsername(@RequestParam String username) {
        return R.ok(userService.getByUsername(username));
    }

    /**
     * 记录用户登录IP地址和登录时间
     */
    @InnerAuth
    @PutMapping("/recordlogin")
    public R<Boolean> recordlogin(@RequestBody SysUser sysUser) {
        return R.ok(userService.updateUserProfile(sysUser));
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/getInfo")
    public AjaxResult getInfo(@RequestHeader(SecurityConstants.CLIENT_TYPE) String clientType,
                              @RequestParam(defaultValue = "false") Boolean isNew) {
        ClientTypeEnum clientTypeEnum = ClientTypeEnum.of(clientType);
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getSysUser();

        if (isNew) {
            SysUser newUser = userService.getByIdThrow(user.getUserId());
            if (!Objects.equals(user.getAvatar(), newUser.getAvatar()) ||
                    !Objects.equals(user.getNickName(), newUser.getNickName()) ||
                    !Objects.equals(user.getEmail(), newUser.getEmail()) ||
                    !Objects.equals(user.getPhonenumber(), newUser.getPhonenumber())) {
                user.setAvatar(newUser.getAvatar());
                user.setNickName(newUser.getNickName());
                user.setEmail(newUser.getEmail());
                user.setPhonenumber(newUser.getPhonenumber());
                tokenService.refreshToken(loginUser, clientTypeEnum);
            }
        }


        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        if (!loginUser.getPermissions().equals(permissions)) {
            loginUser.setPermissions(permissions);
            tokenService.refreshToken(loginUser, clientTypeEnum);
        }
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", Collections.emptyList());
        ajax.put("permissions", permissions);
        return ajax;
    }

    /**
     * 通过电站查询业主姓名
     */
    @GetMapping("/proprietor")
    public R<List<String>> proprietor(@Schema(description = "电站id") @RequestParam Integer powerStationId) {
        return R.ok(userService.proprietor(powerStationId));
    }

    /**
     * 删除用户
     */
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userId}")
    public R<Boolean> remove(@PathVariable Long userId) {
        return R.ok(userService.deleteUserById(userId));
    }

    /**
     * 重置密码
     */
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    public R<Boolean> resetPwd(@Validated @RequestBody SysUserResetPwdDTO dto) {
        return R.ok(userService.resetPwd(dto));
    }

    /**
     * 状态修改
     */
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public R<Boolean> changeStatus(@Validated @RequestBody SysUserUpdateStatusDTO dto) {
        return R.ok(userService.updateUserStatus(dto));
    }

    /**
     * 根据用户编号获取授权角色
     */
    @GetMapping("/authRole/{userId}")
    public AjaxResult authRole(@PathVariable("userId") Long userId) {
        AjaxResult ajax = AjaxResult.success();
        SysUser user = userService.selectUserById(userId);
        List<SysRole> roles = roleService.selectRolesByUserId(userId);
        ajax.put("user", user);
        ajax.put("roles", roles);
        return ajax;
    }

    /**
     * 查询用户区域权限
     */
    @GetMapping("/district")
    public R<SysUserDistrict> info(@RequestParam Long userId) {
        return R.ok(userService.selectUserDistrict(userId));
    }

    /**
     * 用户区域权限更新
     */
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/district")
    public R<Boolean> updateDistrict(@Validated @RequestBody SysUserDistrict ud) {
        return R.ok(userService.updateDistrict(ud));
    }

    /**
     * 查询用户下拉选项
     */
    @GetMapping("/all/list")
    public R<List<KV>> all(@RequestParam Long tenantId) {
        return R.ok(userService.all(tenantId));
    }

    /**
     * 更新最后操作的电站
     */
    @PutMapping("/last/operate/station")
    public R<Boolean> lastOperateStation(@RequestParam Integer stationId, @RequestHeader(SecurityConstants.CLIENT_TYPE) String clientType) {
        LoginUser loginUser = tokenService.getLoginUser();
        loginUser.getSysUser().setLastOperateId(stationId);
        tokenService.setLoginUser(loginUser, ClientTypeEnum.of(clientType));
        boolean b = userService.lastOperateStation(stationId);
        R<Boolean> apiResult = new R<>();
        apiResult.setCode(200);
        apiResult.setData(b);
        return apiResult;
    }

    /**
     * 导出用户数据
     */
    @PostMapping(value = "/export")
    public void export(HttpServletResponse response) throws IOException {
        userService.export(response);
    }

}
