package com.ruoyi.system.service;

import com.ruoyi.common.security.service.IBaseService;
import com.ruoyi.system.api.domain.SysDictType;
import com.ruoyi.system.api.dto.DictDataVO;

import java.util.List;

/**
 * 字典 业务层
 * 
 * <AUTHOR>
 */
public interface ISysDictTypeService extends IBaseService<SysDictType> {
    /**
     * 根据条件分页查询字典类型
     * 
     * @param dictType 字典类型信息
     * @return 字典类型集合信息
     */
    List<SysDictType> selectDictTypeList(SysDictType dictType);

    /**
     * 根据所有字典类型
     * 
     * @return 字典类型集合信息
     */
    List<SysDictType> selectDictTypeAll();

    /**
     * 根据字典类型查询字典数据
     * 
     * @param dictType 字典类型
     * @return 字典数据集合信息
     */
    List<DictDataVO> selectDictDataByType(String dictType);

    /**
     * 按类型远程选择字典数据
     *
     * @param dictType dict 类型
     * @return {@link List }<{@link DictDataVO }>
     */
    List<DictDataVO> remoteSelectDictDataByType(String dictType);

    /**
     * 根据字典类型ID查询信息
     * 
     * @param dictId 字典类型ID
     * @return 字典类型
     */
    SysDictType selectDictTypeById(Long dictId);

    /**
     * 根据字典类型查询信息
     * 
     * @param dictType 字典类型
     * @return 字典类型
     */
    SysDictType selectDictTypeByType(String dictType);

    /**
     * 批量删除字典信息
     * 
     * @param dictId 需要删除的字典ID
     */
    boolean deleteDictTypeById(Long dictId);

    /**
     * 新增保存字典类型信息
     * 
     * @param dictType 字典类型信息
     * @return 结果
     */
    boolean insertDictType(SysDictType dictType);

    /**
     * 修改保存字典类型信息
     * 
     * @param dictType 字典类型信息
     * @return 结果
     */
    boolean updateDictType(SysDictType dictType);
}
