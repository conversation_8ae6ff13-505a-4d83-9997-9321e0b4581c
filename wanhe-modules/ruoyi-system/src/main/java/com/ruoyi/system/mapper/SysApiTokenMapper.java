package com.ruoyi.system.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.ruoyi.common.core.domain.IntStr;
import com.ruoyi.system.api.domain.SysApiToken;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @date 2024-11-11
*/
public interface SysApiTokenMapper extends MPJBaseMapper<SysApiToken> {

    @Select("select id as k,station_name as v from wh_power_station where id in(${ids})")
    List<IntStr> getStation(String ids);

}
