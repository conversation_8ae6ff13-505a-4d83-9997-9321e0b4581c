package com.ruoyi.system.service.impl;

import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.service.impl.BaseServiceImpl;
import com.ruoyi.system.domain.SysContactUs;
import com.ruoyi.system.mapper.SysContactUsMapper;
import com.ruoyi.system.pojo.SysContactUsPageReqDTO;
import com.ruoyi.system.service.ISysContactUsService;
import org.springframework.stereotype.Service;

/**
 * 联系我们
 *
 * <AUTHOR>
 * @date 2025/05/13
 */
@Service
public class ISysContactUsServiceImpl extends BaseServiceImpl<SysContactUsMapper, SysContactUs> implements ISysContactUsService {

    @Override
    public void page(SysContactUsPageReqDTO dto) {
        lambdaQuery().eq(StringUtils.isNotBlank(dto.getLanguage()), SysContactUs::getLanguage, dto.getLanguage())
                .orderByAsc(SysContactUs::getSort)
                .list();
    }
}
