package com.ruoyi.system.service.impl;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.UserConstants;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.redis.RedisUtil;
import com.ruoyi.common.security.service.impl.BaseServiceImpl;
import com.ruoyi.system.domain.SysConfig;
import com.ruoyi.system.mapper.SysConfigMapper;
import com.ruoyi.system.service.ISysConfigService;
import jakarta.annotation.PostConstruct;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 参数配置 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class SysConfigServiceImpl extends BaseServiceImpl<SysConfigMapper, SysConfig> implements ISysConfigService {

    /**
     * 项目启动时，初始化参数到缓存
     */
    @PostConstruct
    public void init() {
        loadingConfigCache();
    }

    /**
     * 查询参数配置信息
     *
     * @param configId 参数配置ID
     * @return 参数配置信息
     */
    @Override
    public SysConfig selectConfigById(Long configId) {
        SysConfig config = new SysConfig();
        config.setConfigId(configId);
        return baseMapper.selectConfig(config);
    }

    /**
     * 根据键名查询参数配置信息
     *
     * @param configKey 参数key
     * @return 参数键值
     */
    @Override
    public String selectConfigByKey(String configKey) {
        String key = CacheConstants.SYS_CONFIG_KEY + configKey;
        String configValue = RedisUtil.get(key);
        if (StrUtil.isNotEmpty(configValue)) {
            return configValue;
        }
        SysConfig retConfig = lambdaQuery().eq(SysConfig::getConfigKey, configKey).one();
        if (retConfig != null) {
            RedisUtil.set(key, retConfig.getConfigValue());
            return retConfig.getConfigValue();
        }
        return StrUtil.EMPTY;
    }

    /**
     * 查询参数配置列表
     *
     * @param config 参数配置信息
     * @return 参数配置集合
     */
    @Override
    public List<SysConfig> selectConfigList(SysConfig config) {
        return baseMapper.selectConfigList(config);
    }

    /**
     * 新增参数配置
     *
     * @param config 参数配置信息
     * @return 结果
     */
    @Override
    public boolean insertConfig(SysConfig config) {
        boolean b = save(config);
        if (b) {
            RedisUtil.set(CacheConstants.SYS_CONFIG_KEY + config.getConfigKey(), config.getConfigValue());
        }
        return b;
    }

    /**
     * 修改参数配置
     *
     * @param config 参数配置信息
     * @return 结果
     */
    @Override
    public boolean updateConfig(SysConfig config) {
        SysConfig temp = getByIdThrow(config.getConfigId());
        if (!StrUtil.equals(temp.getConfigKey(), config.getConfigKey())) {
            RedisUtil.delete(CacheConstants.SYS_CONFIG_KEY + temp.getConfigKey());
        }
        temp.setConfigName(config.getConfigName());
        temp.setConfigKey(config.getConfigKey());
        temp.setConfigValue(config.getConfigValue());
        temp.setConfigType(config.getConfigType());
        temp.setRemark(config.getRemark());
        boolean b = updateById(temp);
        if (b) {
            RedisUtil.set(CacheConstants.SYS_CONFIG_KEY + temp.getConfigKey(), temp.getConfigValue());
        }
        return b;
    }

    @Override
    public void editApp(Long id, String text) {
        SysConfig config = getByIdThrow(id);
        lambdaUpdate().eq(SysConfig::getConfigId, config.getConfigId()).set(SysConfig::getConfigValue, text).update();
        RedisUtil.set(CacheConstants.SYS_CONFIG_KEY + config.getConfigKey(), text);
    }

    /**
     * 批量删除参数信息
     *
     * @param configIds 需要删除的参数ID
     */
    @Override
    public void deleteConfigByIds(Long[] configIds) {
        for (Long configId : configIds) {
            SysConfig config = selectConfigById(configId);
            if (StrUtil.equals(UserConstants.YES, config.getConfigType())) {
                throw new ServiceException("内置参数不能删除");
            }
            baseMapper.deleteConfigById(configId);
            RedisUtil.delete(CacheConstants.SYS_CONFIG_KEY + config.getConfigKey());
        }
    }

    /**
     * 加载参数缓存数据
     */
    @Override
    public void loadingConfigCache() {
        RedisUtil.set(list().stream().collect(Collectors.toMap(c -> CacheConstants.SYS_CONFIG_KEY + c.getConfigKey(), SysConfig::getConfigValue)));
    }

    /**
     * 清空参数缓存数据
     */
    @Override
    public void clearConfigCache() {
        RedisUtil.delete(RedisUtil.keys(CacheConstants.SYS_CONFIG_KEY + "*"));
    }

    /**
     * 重置参数缓存数据
     */
    @Override
    public void resetConfigCache() {
        clearConfigCache();
        loadingConfigCache();
    }

}
