package com.ruoyi.system.service.impl;

import com.ruoyi.common.core.enums.I18nTypeEnum;
import com.ruoyi.common.security.service.impl.BaseServiceImpl;
import com.ruoyi.common.security.utils.DictUtils;
import com.ruoyi.system.api.domain.SysDictData;
import com.ruoyi.system.api.dto.DictDataVO;
import com.ruoyi.system.mapper.SysDictDataMapper;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.system.service.ISysI18nService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 字典 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysDictDataServiceImpl extends BaseServiceImpl<SysDictDataMapper, SysDictData> implements ISysDictDataService {
    @Resource
    private SysDictDataMapper dictDataMapper;
    @Resource
    private ISysI18nService sysI18nService;

    /**
     * 根据条件分页查询字典数据
     *
     * @param dictData 字典数据信息
     * @return 字典数据集合信息
     */
    @Override
    public List<SysDictData> selectDictDataList(SysDictData dictData) {
        return dictDataMapper.selectDictDataList(dictData);
    }

    /**
     * 根据字典类型和字典键值查询字典数据信息
     *
     * @param dictType  字典类型
     * @param dictValue 字典键值
     * @return 字典标签
     */
    @Override
    public String selectDictLabel(String dictType, String dictValue) {
        return dictDataMapper.selectDictLabel(dictType, dictValue);
    }

    /**
     * 根据字典数据ID查询信息
     *
     * @param dictCode 字典数据ID
     * @return 字典数据
     */
    @Override
    public SysDictData selectDictDataById(Long dictCode) {
        return dictDataMapper.selectDictDataById(dictCode);
    }

    /**
     * 批量删除字典数据信息
     *
     * @param dictCode 需要删除的字典数据ID
     */
    @Override
    @Transactional
    public void deleteDictDataById(Long dictCode) {
        SysDictData data = getByIdThrow(dictCode);
        removeById(dictCode);
        sysI18nService.delete(I18nTypeEnum.DICT_DATA, data.getDictCode());
        List<DictDataVO> dictDatas = dictDataMapper.selectDictDataVO(data.getDictType());
        DictUtils.setDictCache(data.getDictType(), dictDatas);
    }

    /**
     * 新增保存字典数据信息
     *
     * @param data 字典数据信息
     */
    @Override
    @Transactional
    public void insertDictData(SysDictData data) {
        save(data);
        sysI18nService.add(I18nTypeEnum.DICT_DATA, data.getDictCode(), data.getDictLabel());
        List<DictDataVO> dictDatas = dictDataMapper.selectDictDataVO(data.getDictType());
        DictUtils.setDictCache(data.getDictType(), dictDatas);
    }

    /**
     * 修改保存字典数据信息
     *
     * @param data 字典数据信息
     */
    @Override
    @Transactional
    public void updateDictData(SysDictData data) {
        updateById(data);
        sysI18nService.update(I18nTypeEnum.DICT_DATA, data.getDictCode(), data.getDictLabel());
        List<DictDataVO> dictDatas = dictDataMapper.selectDictDataVO(data.getDictType());
        DictUtils.setDictCache(data.getDictType(), dictDatas);
    }
}
