package com.ruoyi.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.ruoyi.common.security.service.impl.MPJBaseServiceImpl;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.domain.SysNotice;
import com.ruoyi.system.domain.SysUserNotice;
import com.ruoyi.system.mapper.SysNoticeMapper;
import com.ruoyi.system.mapper.SysUserNoticeMapper;
import com.ruoyi.system.pojo.SysNoticeDTO;
import com.ruoyi.system.pojo.SysNoticeMyPageReqDTO;
import com.ruoyi.system.pojo.SysNoticePageReqDTO;
import com.ruoyi.system.pojo.SysNoticeUpdateDTO;
import com.ruoyi.system.service.ISysNoticeService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 公告 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class SysNoticeServiceImpl extends MPJBaseServiceImpl<SysNoticeMapper, SysNotice> implements ISysNoticeService {

    @Resource
    private SysUserNoticeMapper userNoticeMapper;

    @Override
    public void page(SysNoticePageReqDTO dto) {
        LambdaQueryChainWrapper<SysNotice> lambdaQuery = lambdaQuery();
        lambdaQuery.eq(SysNotice::getType, dto.getType());
        if (StrUtil.isNotBlank(dto.getTitle()) || StrUtil.isNotBlank(dto.getTitleEn())) {
            lambdaQuery.and(i -> i.like(ObjUtil.isNotEmpty(dto.getTitle()), SysNotice::getTitle, dto.getTitle())
                    .or()
                    .like(ObjUtil.isNotEmpty(dto.getTitleEn()), SysNotice::getTitleEn, dto.getTitleEn()));
        }
        lambdaQuery.eq(ObjUtil.isNotNull(dto.getEnable()), SysNotice::getEnable, dto.getEnable())
                .orderByDesc(SysNotice::getId)
                .list();
    }

    @Override
    public void page(SysNoticeMyPageReqDTO dto) {
        MPJLambdaWrapper<SysNotice> wrapper = lambdaJoinQuery()
                .selectAll(SysNotice.class)
                .select("if(un.user_id is null, 0, 1) as isRead")
                .leftJoin(SysUserNotice.class, "un", i -> i.eq(SysUserNotice::getNoticeId, SysNotice::getId).eq(SysUserNotice::getUserId, getUserId()))
                .eq(SysNotice::getType, dto.getType())
                .eq(SysNotice::getEnable, Boolean.TRUE)
                .and(ObjUtil.isNotEmpty(dto.getTitle()), i -> i.like(SysNotice::getTitle, dto.getTitle()).or().like(SysNotice::getContent, dto.getTitle()))
                .and(ObjUtil.isNotEmpty(dto.getTitleEn()), i -> i.like(SysNotice::getTitleEn, dto.getTitleEn()).or().like(SysNotice::getContentEn, dto.getTitleEn()))
                .isNotNull(Boolean.TRUE.equals(dto.getIsRead()), SysUserNotice::getUserId)
                .isNull(Boolean.FALSE.equals(dto.getIsRead()), SysUserNotice::getUserId)
                .orderByDesc(SysNotice::getCreateTime);
        LocalDateTime createTime = getSysUser().getCreateTime();
        if (ObjUtil.isNotNull(dto.getBeginTime()) && ObjUtil.isNotNull(dto.getEndTime())) {
            if (createTime.isAfter(dto.getBeginTime())) {
                dto.setBeginTime(createTime);
            }
            wrapper.between(SysNotice::getCreateTime, dto.getBeginTime(), dto.getEndTime());
        } else {
            wrapper.gt(SysNotice::getCreateTime, createTime);
        }
        wrapper.list();
    }

    @Override
    public void readAllNotice() {
        SysUser sysUser = getSysUser();
        Long userId = sysUser.getUserId();
        LocalDateTime createTime = sysUser.getCreateTime();

        List<Integer> unreadNoticeList = userNoticeMapper.getUnreadNotice(userId, createTime);
        if (CollUtil.isNotEmpty(unreadNoticeList)) {
            List<SysUserNotice> userNotices = unreadNoticeList.parallelStream().map(noticeId -> new SysUserNotice(userId, noticeId))
                            .toList();
            userNoticeMapper.insert(userNotices);
        }
    }


    @Override
    public boolean add(SysNoticeDTO dto) {
        SysNotice notice = BeanUtil.copyProperties(dto, SysNotice.class);
        return save(notice);
    }

    @Override
    public boolean update(SysNoticeUpdateDTO dto) {
        SysNotice notice = getByIdThrow(dto.getId());
        BeanUtil.copyProperties(dto, notice);
        return updateById(notice);
    }

    @Override
    public boolean enable(Serializable id) {
        SysNotice notice = getByIdThrow(id);
        return lambdaUpdate().eq(SysNotice::getId, id)
                .set(SysNotice::getEnable, !notice.getEnable())
                .update();
    }

    @Override
    public SysNotice get(Integer id) {
        SysNotice notice = getByIdThrow(id);
        Long userId = getUserId();
        SysUserNotice userNotice = userNoticeMapper.get(userId, id);
        if (userNotice == null) {
            userNoticeMapper.insert(new SysUserNotice(userId, id));
        }
        return notice;
    }

}
