package com.ruoyi.system.pojo;

import com.ruoyi.common.core.domain.PageReqDTO;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/11/6
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysUserPageReqDTO extends PageReqDTO {

    /** 部门ID */
    @NotNull
    private Long deptId;

    /** 姓名 */
    private String nickName;

    /** 用户账号 */
    private String userName;

    /** 帐号状态（0正常 1停用） */
    private String status;

    /** 用户类型（00 员工 01 业主） */
    @NotBlank
    @Pattern(regexp = "^00|01$")
    private String userType;

    /** 租户id */
    private Long tenantId;

}
