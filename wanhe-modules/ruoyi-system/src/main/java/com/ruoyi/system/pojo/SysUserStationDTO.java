package com.ruoyi.system.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/1
 */
@Data
public class SysUserStationDTO {

    @Schema(description = "电站编号")
    @NotNull
    private Integer powerStationId;

    @Schema(description = "用户编号")
    @NotEmpty
    private List<Long> userIds;

}
