package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.web.domain.base.CUEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 通知公告表 sys_notice
 * 
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("sys_notice")
public class SysNotice extends CUEntity {

    /** ID */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /** 标题 */
    private String title;

    /** 标题英文 */
    private String titleEn;

    @JsonIgnore
    @Schema(description = "类型（1通知 2公告）", hidden = true)
    private Integer type;

    /** 内容 */
    private String content;

    /** 内容 */
    private String contentEn;

    /** 是否启用 */
    @TableField("is_enable")
    private Boolean enable;

    /** 是否启用 */
    @TableField(exist = false)
    private Boolean isRead;

}
