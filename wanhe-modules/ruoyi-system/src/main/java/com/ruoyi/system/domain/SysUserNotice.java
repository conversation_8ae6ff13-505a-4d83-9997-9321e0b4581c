package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/11/1
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("sys_user_notice")
public class SysUserNotice {

    @Schema(description = "用户编号")
    @TableId
    private Long userId;

    @Schema(description = "公告编号")
    private Integer noticeId;

}
