package com.ruoyi.system.service;

import com.ruoyi.common.security.service.IBaseService;
import com.ruoyi.system.api.domain.SysOperLog;
import com.ruoyi.system.pojo.SysOperlogPageReqDTO;

import java.util.List;

/**
 * 操作日志 服务层
 *
 * <AUTHOR>
 */
public interface ISysOperLogService extends IBaseService<SysOperLog> {
    /**
     * 新增操作日志
     *
     * @param operLog 操作日志对象
     * @return 结果
     */
    boolean insertOperlog(SysOperLog operLog);

    List<SysOperLog> page(SysOperlogPageReqDTO dto);

    /**
     * 清空操作日志
     */
    void cleanOperLog();
}
