package com.ruoyi.system.service.impl;

import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.security.service.impl.BaseServiceImpl;
import com.ruoyi.common.security.utils.DictUtils;
import com.ruoyi.system.api.domain.SysDictType;
import com.ruoyi.system.api.dto.DictDataVO;
import com.ruoyi.system.mapper.SysDictDataMapper;
import com.ruoyi.system.mapper.SysDictTypeMapper;
import com.ruoyi.system.service.ISysDictTypeService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 字典 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysDictTypeServiceImpl extends BaseServiceImpl<SysDictTypeMapper, SysDictType> implements ISysDictTypeService {

    @Resource
    private SysDictTypeMapper dictTypeMapper;
    @Resource
    private SysDictDataMapper dictDataMapper;

    /**
     * 根据条件分页查询字典类型
     *
     * @param dictType 字典类型信息
     * @return 字典类型集合信息
     */
    @Override
    public List<SysDictType> selectDictTypeList(SysDictType dictType) {
        return dictTypeMapper.selectDictTypeList(dictType);
    }

    /**
     * 根据所有字典类型
     *
     * @return 字典类型集合信息
     */
    @Override
    public List<SysDictType> selectDictTypeAll() {
        return dictTypeMapper.selectDictTypeAll();
    }

    /**
     * 根据字典类型查询字典数据
     *
     * @param dictType 字典类型
     * @return 字典数据集合信息
     */
    @Override
    public List<DictDataVO> selectDictDataByType(String dictType) {
        List<DictDataVO> dictDatas = DictUtils.getDictCacheByDict(dictType);
        if (dictDatas != null) {
            return dictDatas;
        }
        dictDatas = dictDataMapper.selectDictDataVO(dictType);
        DictUtils.setDictCache(dictType, dictDatas);
        DictUtils.languageHandle(dictDatas);
        return dictDatas;
    }

    @Override
    public List<DictDataVO> remoteSelectDictDataByType(String dictType) {
        List<DictDataVO> dictDatas = DictUtils.getDictCacheByDict(dictType);
        if (dictDatas != null) {
            return dictDatas;
        }
        dictDatas = dictDataMapper.selectDictDataVO(dictType);
        DictUtils.setDictCache(dictType, dictDatas);
        return dictDatas;
    }


    /**
     * 根据字典类型ID查询信息
     *
     * @param dictId 字典类型ID
     * @return 字典类型
     */
    @Override
    public SysDictType selectDictTypeById(Long dictId) {
        return dictTypeMapper.selectDictTypeById(dictId);
    }

    /**
     * 根据字典类型查询信息
     *
     * @param dictType 字典类型
     * @return 字典类型
     */
    @Override
    public SysDictType selectDictTypeByType(String dictType) {
        return dictTypeMapper.selectDictTypeByType(dictType);
    }

    /**
     * 批量删除字典类型信息
     *
     * @param dictId 需要删除的字典ID
     */
    @Override
    public boolean deleteDictTypeById(Long dictId) {
        SysDictType dictType = selectDictTypeById(dictId);
        if (dictDataMapper.countDictDataByType(dictType.getDictType()) > 0) {
            throw new ServiceException("已分配,不能删除");
        }
        boolean b = removeById(dictId);
        if (b) {
            DictUtils.removeDictCache(dictType.getDictType());
        }
        return b;
    }

    /**
     * 新增保存字典类型信息
     *
     * @param dict 字典类型信息
     * @return 结果
     */
    @Override
    public boolean insertDictType(SysDictType dict) {
        boolean save = save(dict);
        if (save) {
            DictUtils.setDictCache(dict.getDictType(), null);
        }
        return save;
    }

    /**
     * 修改保存字典类型信息
     *
     * @param dict 字典类型信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDictType(SysDictType dict) {
        SysDictType oldDict = getByIdThrow(dict.getDictId());
        boolean update = updateById(dict);
        if (update && !oldDict.getDictType().equals(dict.getDictType())) {
            dictDataMapper.updateDictDataType(oldDict.getDictType(), dict.getDictType());
            List<DictDataVO> dictDatas = dictDataMapper.selectDictDataVO(dict.getDictType());
            DictUtils.removeDictCache(dict.getDictType());
            DictUtils.setDictCache(dict.getDictType(), dictDatas);
        }
        return update;
    }
}
