package com.ruoyi.system.service;

import com.ruoyi.common.core.enums.I18nTypeEnum;
import com.ruoyi.common.security.service.IBaseService;
import com.ruoyi.system.api.domain.SysI18n;
import com.ruoyi.system.pojo.SysI18nPageReqDTO;
import com.ruoyi.system.pojo.SysI18nUpdateDTO;

import java.util.List;

/**
 * 国际化配置Service接口
 *
 * <AUTHOR>
 * @date 2023-08-24
 */
public interface ISysI18nService extends IBaseService<SysI18n> {

    List<SysI18n> page(SysI18nPageReqDTO dto);

    void add(I18nTypeEnum type, Long dataId, String name);

    void update(I18nTypeEnum type, Long dataId, String name);

    void delete(I18nTypeEnum type, Long dataId);

    boolean update(SysI18nUpdateDTO dto);

    boolean importData(List<SysI18n> list);
}
