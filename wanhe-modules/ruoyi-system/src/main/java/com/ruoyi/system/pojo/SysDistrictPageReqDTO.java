package com.ruoyi.system.pojo;

import com.ruoyi.common.core.domain.PageReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024-10-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(name = "SysDistrictDTO", description = "行政区域")
public class SysDistrictPageReqDTO extends PageReqDTO {

    @Schema(description = "国家")
    private String country;

    @Schema(description = "省份")
    private String province;

}
