package com.ruoyi.system.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.SysDept;
import com.ruoyi.system.domain.SysDeptDistrict;
import com.ruoyi.system.pojo.SysDeptDTO;
import com.ruoyi.system.pojo.SysDeptSearchReq;
import com.ruoyi.system.pojo.SysDeptUpdateDTO;
import com.ruoyi.system.service.ISysDeptService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 部门信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dept")
public class SysDeptController extends BaseController {

    @Resource
    private ISysDeptService deptService;

    /**
     * 获取部门列表 system:dept:list
     */
    @RequiresPermissions("system:dept:list")
    @GetMapping("/list")
    public R<List<SysDept>> list(SysDeptSearchReq req) {
        return R.ok(deptService.list(req));
    }

    /**
     * 新增部门（机构类型只能显示小于父级部门类型,最后一级类型不能添加） system:dept:add
     */
    @RequiresPermissions("system:dept:add")
    @Log(title = "部门管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@Validated @RequestBody SysDeptDTO dto) {
        return R.ok(deptService.insertDept(dto));
    }

    /**
     * 修改部门（机构类型只能显示小于父级部门类型,最后一级类型不能添加） system:dept:edit
     */
    @RequiresPermissions("system:dept:edit")
    @Log(title = "部门管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@Validated @RequestBody SysDeptUpdateDTO dto) {
        return R.ok(deptService.updateDept(dto));
    }

    /**
     * 根据部门编号获取详细信息
     */
    @GetMapping(value = "/{deptId}")
    public R<SysDept> getInfo(@PathVariable Long deptId) {
        return R.ok(deptService.getByIdThrow(deptId));
    }

    /**
     * 删除部门 system:dept:remove
     */
    @RequiresPermissions("system:dept:remove")
    @Log(title = "部门管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deptId}")
    public R<Boolean> remove(@PathVariable Long deptId) {
        if (deptService.hasChildByDeptId(deptId)) {
            throw new ServiceException("存在下级部门,不允许删除");
        }
        if (deptService.checkDeptExistUser(deptId)) {
            throw new ServiceException("部门存在用户,不允许删除");
        }
        return R.ok(deptService.delete(deptId));
    }

    /**
     * 查询租户国家
     */
    @GetMapping("/district")
    public R<SysDeptDistrict> info(@RequestParam Long tenantId) {
        return R.ok(deptService.selectDeptDistrict(tenantId));
    }

    /**
     * 租户更新国家区域 system:dept:district
     */
    @RequiresPermissions("system:dept:district")
    @Log(title = "部门区域权限", businessType = BusinessType.UPDATE)
    @PutMapping("/district")
    public R<Boolean> updateDistrict(@Validated @RequestBody SysDeptDistrict dd) {
        return R.ok(deptService.updateDistrict(dd));
    }

}

