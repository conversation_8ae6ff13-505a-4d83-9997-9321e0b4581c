package com.ruoyi.system.service;

import com.ruoyi.common.security.service.IBaseService;
import com.ruoyi.system.api.domain.SysRole;
import com.ruoyi.system.pojo.SysRoleDTO;
import com.ruoyi.system.pojo.SysRolePageReqDTO;
import com.ruoyi.system.pojo.SysRoleStatusDTO;
import com.ruoyi.system.pojo.SysRoleUpdateDTO;

import java.util.List;
import java.util.Set;

/**
 * 角色业务层
 *
 * <AUTHOR>
 */
public interface ISysRoleService extends IBaseService<SysRole> {

    void page(SysRolePageReqDTO dto);

    /**
     * 根据用户ID查询角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<SysRole> selectRolesByUserId(Long userId);

    /**
     * 根据用户ID查询角色权限
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    Set<String> selectRolePermissionByUserId(Long userId);

    /**
     * 查询所有角色
     *
     * @return 角色列表
     */
    List<SysRole> selectRoleAll(Long tenantId);
    List<SysRole> selectRoleAll2(Long tenantId);

    /**
     * 通过角色ID查询角色
     *
     * @param roleId 角色ID
     * @return 角色对象信息
     */
    SysRole selectRoleById(Long roleId);

    /**
     * 通过角色ID查询角色使用数量
     *
     * @param roleId 角色ID
     * @return 结果
     */
    int countUserRoleByRoleId(Long roleId);

    /**
     * 新增保存角色信息
     *
     * @param dto 角色信息
     * @return 结果
     */
    boolean insertRole(SysRoleDTO dto);

    /**
     * 修改保存角色信息
     *
     * @param dto 角色信息
     * @return 结果
     */
    boolean updateRole(SysRoleUpdateDTO dto);

    /**
     * 修改角色状态
     *
     * @return 结果
     */
    boolean updateRoleStatus(SysRoleStatusDTO dto);

    /**
     * 通过角色ID删除角色
     *
     * @param roleId 角色ID
     * @return 结果
     */
    boolean deleteRoleById(Long roleId);

}
