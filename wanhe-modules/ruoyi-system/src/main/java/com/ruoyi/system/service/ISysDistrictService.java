package com.ruoyi.system.service;

import com.github.pagehelper.Page;
import com.ruoyi.common.core.domain.IntStr;
import com.ruoyi.common.security.service.IBaseService;
import com.ruoyi.system.domain.SysDistrict;
import com.ruoyi.system.pojo.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/30
 */
public interface ISysDistrictService extends IBaseService<SysDistrict> {

    boolean add(SysDistrictDTO dto);

    boolean update(SysDistrictUpdateDTO dto);

    Page<SysDistrict> page(SysDistrictPageReqDTO dto);

    List<IntStr> listByPid(Integer pid);

    List<SysDistrictVO> listByPid(String pid);

    List<IntStr> listByIds(List<Integer> ids);

    boolean delete(Integer id);

    List<CountryDTO> getAllCountry();

    boolean updateAllCountry(List<CountryDTO> list);
}
