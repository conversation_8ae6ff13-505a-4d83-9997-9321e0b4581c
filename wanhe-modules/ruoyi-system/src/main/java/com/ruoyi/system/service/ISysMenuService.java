package com.ruoyi.system.service;

import com.ruoyi.common.core.enums.ClientTypeEnum;
import com.ruoyi.common.security.service.IBaseService;
import com.ruoyi.system.domain.SysMenu;
import com.ruoyi.system.domain.vo.RouterVo;
import com.ruoyi.system.domain.vo.TreeSelect;
import com.ruoyi.system.pojo.SysMenuDTO;
import com.ruoyi.system.pojo.SysMenuSearchReq;
import com.ruoyi.system.pojo.SysMenuUpdateDTO;

import java.util.List;
import java.util.Set;

/**
 * 菜单 业务层
 *
 * <AUTHOR>
 */
public interface ISysMenuService extends IBaseService<SysMenu> {
    /**
     * 根据用户查询系统菜单列表
     *
     * @return 菜单列表
     */
    List<SysMenu> selectMenuList();

    /**
     * 根据用户查询系统菜单列表
     *
     * @return 菜单列表
     */
    List<SysMenu> selectMenuList(SysMenuSearchReq req);

    /**
     * 根据用户ID查询权限
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    Set<String> selectMenuPermsByUserId(Long userId);

    /**
     * 根据角色ID查询权限
     *
     * @param roleIds 角色ID
     * @return 权限列表
     */
    Set<String> selectMenuPermsByRoleIds(List<Long> roleIds);

    /**
     * 根据用户ID查询菜单树信息
     *
     * @return 菜单列表
     */
    List<SysMenu> selectMenuTreeByUserId(ClientTypeEnum clientType);

    /**
     * 根据角色ID查询菜单树信息
     *
     * @param roleId 角色ID
     * @return 选中菜单列表
     */
    List<Long> selectMenuListByRoleId(Long roleId);

    /**
     * 构建前端路由所需要的菜单
     *
     * @param menus 菜单列表
     * @return 路由列表
     */
    List<RouterVo> buildMenus(List<SysMenu> menus);

    /**
     * 构建前端所需要树结构
     *
     * @param menus 菜单列表
     * @return 树结构列表
     */
    List<SysMenu> buildMenuTree(List<SysMenu> menus);

    /**
     * 构建前端所需要下拉树结构
     *
     * @param menus 菜单列表
     * @return 下拉树结构列表
     */
    List<TreeSelect> buildMenuTreeSelect(List<SysMenu> menus);

    /**
     * 根据菜单ID查询信息
     *
     * @param menuId 菜单ID
     * @return 菜单信息
     */
    SysMenu selectMenuById(Long menuId);

    /**
     * 是否存在菜单子节点
     *
     * @param menuId 菜单ID
     * @return 结果 true 存在 false 不存在
     */
    boolean hasChildByMenuId(Long menuId);

    /**
     * 查询菜单是否存在角色
     *
     * @param menuId 菜单ID
     * @return 结果 true 存在 false 不存在
     */
    boolean checkMenuExistRole(Long menuId);

    /**
     * 新增保存菜单信息
     *
     * @param dto 菜单信息
     * @return 结果
     */
    boolean insertMenu(SysMenuDTO dto);

    /**
     * 修改保存菜单信息
     *
     * @param dto 菜单信息
     * @return 结果
     */
    boolean updateMenu(SysMenuUpdateDTO dto);

    /**
     * 删除菜单管理信息
     *
     * @param menuId 菜单ID
     * @return 结果
     */
    int deleteMenuById(Long menuId);

}
