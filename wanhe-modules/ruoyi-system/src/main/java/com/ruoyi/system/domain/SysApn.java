package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.domain.base.CUEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Apn
 *
 * <AUTHOR>
 * @date 2025/05/13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("sys_apn")
public class SysApn extends CUEntity {

    /** ID */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /** apn */
    private String apn;

    /** 排序 */
    private Integer sort;

}
