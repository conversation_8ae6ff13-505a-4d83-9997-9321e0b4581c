package com.ruoyi.system.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.ruoyi.common.core.domain.KV;
import com.ruoyi.system.api.domain.SysDept;
import com.ruoyi.system.api.domain.SysUser;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户表 数据层
 *
 * <AUTHOR>
 */
public interface SysUserMapper extends MPJBaseMapper<SysUser> {

    @Select("select dept_name from sys_dept where dept_id = #{tenantId} and del_flag = '0'")
    String getDeptName(Long tenantId);

    @Select("select is_allow_owner_view_layout from wh_power_station where id = #{stationId}")
    boolean isAllowOwnerViewLayout(Integer stationId);

    @Delete("select count(*) from sys_user_station where user_id = #{id}")
    boolean existStation(Long id);

    @Delete("delete from wh_power_station_collect where user_id = #{id}")
    void deleteStationCollectById(Long id);

    @Select("select user_id as k, nick_name as v from sys_user where tenant_id = #{tenantId} and user_type = '00' and status = '0' and del_flag = '0'")
    List<KV> all(Long tenantId);

    @Select("select id from wh_product where product_code = #{code} and deleted = 0")
    Integer getProductIdByCode(String code);

    @Select("select s.tenant_id,d.dept_name from wh_power_station s inner join sys_dept d on s.tenant_id = d.dept_id and s.id = #{stationId}")
    SysDept getTenantId(Integer stationId);

    @Select("""
    select nick_name from sys_user where del_flag = '0' and
    user_id in(select user_id from sys_user_station where power_station_id = #{powerStationId})
    """)
    List<String> proprietor(Integer powerStationId);

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    SysUser selectUserByUserName(String userName, String userType);

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    SysUser selectUserById(Long userId);

    /**
     * 修改用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    int updateUser(SysUser user);

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar   头像地址
     * @return 结果
     */
    int updateUserAvatar(@Param("userName") String userName, @Param("avatar") String avatar);

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    int resetUserPwd(@Param("userName") String userName, @Param("password") String password);

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    int deleteUserById(Long userId);

    /**
     * 校验用户名称是否唯一
     *
     * @param userName 用户名称
     * @return 结果
     */
    SysUser checkUserNameUnique(String userName);

    /**
     * 校验手机号码是否唯一
     *
     * @param phonenumber 手机号码
     * @return 结果
     */
    SysUser checkPhoneUnique(String phonenumber);

    /**
     * 校验email是否唯一
     *
     * @param email 用户邮箱
     * @return 结果
     */
    SysUser checkEmailUnique(String email);
}
