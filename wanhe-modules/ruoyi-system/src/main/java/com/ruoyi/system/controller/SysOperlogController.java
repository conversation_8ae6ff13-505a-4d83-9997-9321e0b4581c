package com.ruoyi.system.controller;

import com.github.pagehelper.Page;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.SysOperLog;
import com.ruoyi.system.pojo.SysOperlogPageReqDTO;
import com.ruoyi.system.service.ISysOperLogService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 操作日志记录
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/operlog")
public class SysOperlogController extends BaseController {
    @Resource
    private ISysOperLogService operLogService;

    /**
     * 分页 system:operlog:list
     */
    @RequiresPermissions("system:operlog:list")
    @GetMapping("/list")
    public TableDataInfo<SysOperLog> list(SysOperlogPageReqDTO dto) {
        Page<SysOperLog> page = startPage(dto);
        operLogService.page(dto);
        return getDataTable(page);
    }

    /**
     * 导出 system:operlog:export
     */
    @Log(title = "操作日志", businessType = BusinessType.EXPORT)
    @RequiresPermissions("system:operlog:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysOperlogPageReqDTO dto) {
        List<SysOperLog> list = operLogService.page(dto);
        ExcelUtil<SysOperLog> util = new ExcelUtil<>(SysOperLog.class);
        util.exportExcel(response, list, "操作日志");
    }

    /**
     * 删除 system:operlog:remove
     */
    @Log(title = "操作日志", businessType = BusinessType.DELETE)
    @RequiresPermissions("system:operlog:remove")
    @DeleteMapping("/{operId}")
    public R<Boolean> remove(@PathVariable Long operId) {
        return R.ok(operLogService.removeById(operId));
    }

    /**
     * 清空 system:operlog:clean
     */
    @RequiresPermissions("system:operlog:clean")
    @Log(title = "操作日志", businessType = BusinessType.CLEAN)
    @DeleteMapping("/clean")
    public R<Void> clean() {
        operLogService.cleanOperLog();
        return R.ok();
    }

    /**
     * 新增
     */
    @InnerAuth
    @PostMapping
    public R<Boolean> add(@RequestBody SysOperLog operLog) {
        return R.ok(operLogService.insertOperlog(operLog));
    }
}
