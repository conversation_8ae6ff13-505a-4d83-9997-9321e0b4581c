package com.ruoyi.system.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/1/19
 */
@Setter
@Getter
public class SysI18nUpdateDTO {

    @Schema(description = "主键id")
    @NotNull
    private Integer id;

    @Schema(description = "中文")
    @NotBlank
    private String nameZh;

    @Schema(description = "英文")
    @NotBlank
    private String nameEn;

    @Schema(description = "德文")
    @NotBlank
    private String nameDe;

    @Schema(description = "法文")
    @NotBlank
    private String nameFra;

}
