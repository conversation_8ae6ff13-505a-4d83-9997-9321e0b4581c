package com.ruoyi.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.http.HttpUtil;
import com.ruoyi.common.core.utils.NumUtil;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.service.impl.BaseServiceImpl;
import com.ruoyi.system.domain.SysHelp;
import com.ruoyi.system.mapper.SysHelpMapper;
import com.ruoyi.system.pojo.SysHelpDTO;
import com.ruoyi.system.pojo.SysHelpPageReqDTO;
import com.ruoyi.system.pojo.SysHelpUpdateDTO;
import com.ruoyi.system.pojo.UpgradeDTO;
import com.ruoyi.system.service.ISysHelpService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 公告 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class SysHelpServiceImpl extends BaseServiceImpl<SysHelpMapper, SysHelp> implements ISysHelpService {

    @Override
    public void page(SysHelpPageReqDTO dto) {
        lambdaQuery().eq(SysHelp::getType, dto.getType())
                .eq(StringUtils.isNotNull(dto.getClientType()), SysHelp::getClientType, dto.getClientType())
                .eq(StringUtils.isNotNull(dto.getLanguage()), SysHelp::getLanguage, dto.getLanguage())
                .like(ObjUtil.isNotEmpty(dto.getTitle()), SysHelp::getTitle, dto.getTitle())
                .orderByAsc(SysHelp::getSort)
                .list();
    }

    @Override
    public SysHelp upgrade(UpgradeDTO dto) {
        return baseMapper.upgrade(dto);
    }

    @Override
    public Boolean mustUpgrade(UpgradeDTO dto) {
        return baseMapper.mustUpgrade(dto) > 0;
    }

    @Override
    public boolean add(SysHelpDTO dto) {
        SysHelp help = BeanUtil.copyProperties(dto, SysHelp.class);
        fileSize(help);
        return save(help);
    }

    @Override
    public boolean update(SysHelpUpdateDTO dto) {
        SysHelp help = getByIdThrow(dto.getId());
        BeanUtil.copyProperties(dto, help, "type");
        fileSize(help);
        return updateById(help);
    }

    private void fileSize(SysHelp help) {
        if (help.getType() == 2) {
            try {
                help.setFileType(FileUtil.getSuffix(help.getContent()));
                byte[] bytes = HttpUtil.downloadBytes(help.getContent());
                BigDecimal len = new BigDecimal(bytes.length);
                if (len.compareTo(NumUtil.MB) > 0) {
                    help.setFileSize(len.divide(NumUtil.MB, 2, RoundingMode.UP).stripTrailingZeros() + "MB");
                } else if (len.compareTo(NumUtil.B) > 0) {
                    help.setFileSize(len.divide(NumUtil.B, 2, RoundingMode.UP).stripTrailingZeros() + "KB");
                } else {
                    help.setFileSize(len + "B");
                }
            } catch (Exception e) {
                help.setFileSize("文件错误");
            }
        }
    }

}
