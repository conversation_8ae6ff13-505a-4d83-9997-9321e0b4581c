package com.ruoyi.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.Page;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.SysApn;
import com.ruoyi.system.pojo.SysApnPageReqDTO;
import com.ruoyi.system.service.ISysApnService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Apn
 *
 * <AUTHOR>
 * @date 2025/05/13
 */
@RestController
@RequestMapping("/apn")
public class SysApnController extends BaseController {

    @Resource
    private ISysApnService apnService;

    /**
     * 分页
     */
    @RequiresPermissions("system:apn:page")
    @GetMapping("/page")
    public TableDataInfo<SysApn> page(SysApnPageReqDTO dto) {
        Page<SysApn> page = startPage(dto);
        apnService.page(dto);
        return getDataTable(page);
    }

    /**
     * 查询所有
     */
    @GetMapping("/list")
    public R<List<SysApn>> list() {
        List<SysApn> list = apnService.list(new LambdaQueryWrapper<SysApn>().orderByAsc(SysApn::getSort));
        return R.ok(list);
    }

    /**
     * 详细
     */
    @GetMapping(value = "/{id}")
    public R<SysApn> getInfo(@PathVariable Integer id) {
        return R.ok(apnService.getById(id));
    }

    /**
     * 新增
     */
    @RequiresPermissions("system:apn:add")
    @Log(title = "apn", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@Validated @RequestBody SysApn sysApn) {
        return R.ok(apnService.save(sysApn));
    }

    /**
     * 修改
     */
    @RequiresPermissions("system:apn:edit")
    @Log(title = "apn", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@Validated @RequestBody SysApn sysApn) {
        return R.ok(apnService.updateById(sysApn));
    }

    /**
     * 删除
     */
    @RequiresPermissions("system:apn:del")
    @Log(title = "apn", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public R<Boolean> remove(@PathVariable Integer id) {
        return R.ok(apnService.removeById(id));
    }

}
