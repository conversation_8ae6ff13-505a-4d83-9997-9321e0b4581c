package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.core.domain.IntStr;
import com.ruoyi.system.domain.SysDistrict;
import com.ruoyi.system.pojo.CountryDTO;
import com.ruoyi.system.pojo.SysDistrictVO;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
* <AUTHOR>
* @date 2024-10-30
*/
public interface SysDistrictMapper extends BaseMapper<SysDistrict> {

    @Update("update sys_district set is_delete = #{delete} where id in(${ids})")
    void updateDelete(Integer delete, String ids);

    @Select("select id,`name`,if(is_delete = 0, 1, 0) as `start` from sys_district where pid = 0")
    List<CountryDTO> getAllCountry();

    @Select("select id from sys_district where pid = 0 and is_delete = 0")
    List<Integer> getAllId();

    @Select("""
    select (select count(*) from wh_power_station where deleted = 0 and country_id = ${id}) +
    (select count(*) from sys_dept_district where json_contains(country_ids, '${id}')) +
    (select count(*) from sys_user_district where json_contains(country_ids, '${id}')) count
    """)
    int countByCountryId(Integer id);

    @Select("""
    select (select count(*) from wh_power_station where deleted = 0 and province_id = ${id}) +
    (select count(*) from sys_user_district where json_contains(province_ids, '${id}')) count
    """)
    int countByProvinceId(Integer id);

    @Select("select id as k, name as v from sys_district where pid = #{pid} and is_delete = 0")
    List<IntStr> listByPid(Integer pid);

    @Select("select id, pid, name from sys_district where (pid in(${pid}) or id in(${pid})) and is_delete = 0")
    List<SysDistrictVO> listByPids(String pid);

    @Select("select id as k, name as v from sys_district where id in(${pid})")
    List<IntStr> listByIds(String ids);
}
