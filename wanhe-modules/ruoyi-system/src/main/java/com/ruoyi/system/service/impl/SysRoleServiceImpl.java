package com.ruoyi.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.service.impl.BaseServiceImpl;
import com.ruoyi.system.api.domain.SysDept;
import com.ruoyi.system.api.domain.SysRole;
import com.ruoyi.system.domain.SysMenu;
import com.ruoyi.system.domain.SysRoleMenu;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.mapper.SysRoleMapper;
import com.ruoyi.system.mapper.SysRoleMenuMapper;
import com.ruoyi.system.mapper.SysUserRoleMapper;
import com.ruoyi.system.pojo.SysRoleDTO;
import com.ruoyi.system.pojo.SysRolePageReqDTO;
import com.ruoyi.system.pojo.SysRoleStatusDTO;
import com.ruoyi.system.pojo.SysRoleUpdateDTO;
import com.ruoyi.system.service.ISysRoleService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 角色 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysRoleServiceImpl extends BaseServiceImpl<SysRoleMapper, SysRole> implements ISysRoleService {

    @Resource
    private SysRoleMenuMapper roleMenuMapper;
    @Resource
    private SysUserRoleMapper userRoleMapper;
    @Resource
    private SysDeptMapper sysDeptMapper;

    @Override
    public void page(SysRolePageReqDTO dto) {
        List<SysRole> list = lambdaQueryTenant(SysRole::getTenantId, SysRole::getDeptId, SysRole::getCreateId)
                .like(StrUtil.isNotBlank(dto.getRoleName()), SysRole::getRoleName, dto.getRoleName())
                .eq(StrUtil.isNotBlank(dto.getStatus()), SysRole::getStatus, dto.getStatus())
                .between(dto.getBeginTime() != null && dto.getEndTime() != null, SysRole::getCreateTime, dto.getBeginTime(), dto.getEndTime())
                .orderByAsc(SysRole::getRoleSort)
                .orderByDesc(SysRole::getRoleId)
                .list();
        if (ObjUtil.isNotEmpty(list)) {
            List<Long> tenantIds = list.stream().map(SysRole::getTenantId).distinct().toList();
            Map<Long, String> map = sysDeptMapper.selectList(Wrappers.<SysDept>lambdaQuery()
                            .select(SysDept::getTenantId, SysDept::getDeptName)
                            .in(SysDept::getDeptId, tenantIds)).stream()
                    .collect(Collectors.toMap(SysDept::getTenantId, SysDept::getDeptName));
            list.forEach(p -> p.setTenantName(map.get(p.getTenantId())));
        }
    }

    /**
     * 根据用户ID查询角色
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    @Override
    public List<SysRole> selectRolesByUserId(Long userId) {
        List<SysRole> userRoles = baseMapper.selectRolePermissionByUserId(userId);
        List<SysRole> roles = selectRoleAll(null);
        for (SysRole role : roles) {
            for (SysRole userRole : userRoles) {
                if (role.getRoleId().longValue() == userRole.getRoleId().longValue()) {
                    role.setFlag(true);
                    break;
                }
            }
        }
        return roles;
    }

    /**
     * 根据用户ID查询权限
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    @Override
    public Set<String> selectRolePermissionByUserId(Long userId) {
        List<SysRole> perms = baseMapper.selectRolePermissionByUserId(userId);
        Set<String> permsSet = new HashSet<>();
        for (SysRole perm : perms) {
            if (StringUtils.isNotNull(perm)) {
                permsSet.addAll(Arrays.asList(perm.getRoleKey().trim().split(",")));
            }
        }
        return permsSet;
    }

    /**
     * 查询所有角色
     *
     * @return 角色列表
     */
    @Override
    public List<SysRole> selectRoleAll(Long tenantId) {
        return lambdaQuery().eq(SysRole::getTenantId, tenantId == null ? getTenantId() : tenantId).list();
    }

    /**
     * 查询所有角色
     *
     * @return 角色列表
     */
    @Override
    public List<SysRole> selectRoleAll2(Long tenantId) {
        Set<Long> tenantIds = new HashSet<>(2);
        tenantIds.add(tenantId);
        tenantIds.add(getTenantId());
        return lambdaQuery().select(SysRole::getRoleId, SysRole::getRoleName)
                .eq(SysRole::getStatus, "0")
                .in(SysRole::getTenantId, tenantIds)
                .list();
    }

    /**
     * 通过角色ID查询角色
     *
     * @param roleId 角色ID
     * @return 角色对象信息
     */
    @Override
    public SysRole selectRoleById(Long roleId) {
        SysRole role = getById(roleId);
        if (role != null) {
            if (roleId == ADMIN_ROLE_ID) {
                List<SysMenu> menuList = roleMenuMapper.getMenuList();
                if (CollUtil.isNotEmpty(menuList)) {
                    role.setMenuIds(menuList.stream().filter(rm -> rm.getMenuLocation().equals("1")).map(SysMenu::getMenuId).toList());
                    role.setAppMenuIds(menuList.stream().filter(rm -> rm.getMenuLocation().equals("0")).map(SysMenu::getMenuId).toList());
                }
            } else {
                List<SysRoleMenu> roleMenuList = roleMenuMapper.getRoleMenuList(roleId);
                if (CollUtil.isNotEmpty(roleMenuList)) {
                    role.setMenuIds(roleMenuList.stream().filter(rm -> rm.getMenuLocation() == 1).map(SysRoleMenu::getMenuId).toList());
                    role.setAppMenuIds(roleMenuList.stream().filter(rm -> rm.getMenuLocation() == 0).map(SysRoleMenu::getMenuId).toList());
                }
            }
        }
        return role;
    }

    /**
     * 校验角色名称是否唯一
     */
    private void checkRoleNameUnique(String roleName, Long id) {
        if (lambdaQuery().eq(SysRole::getRoleName, roleName)
                .eq(SysRole::getTenantId, getTenantId())
                .ne(id != null, SysRole::getRoleId, id)
                .exists()) {
            throw new ServiceException("角色名称已存在");
        }
    }

    /**
     * 通过角色ID查询角色使用数量
     *
     * @param roleId 角色ID
     * @return 结果
     */
    @Override
    public int countUserRoleByRoleId(Long roleId) {
        return userRoleMapper.countUserRoleByRoleId(roleId);
    }

    /**
     * 新增保存角色信息
     *
     * @param dto 角色信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertRole(SysRoleDTO dto) {
        checkRoleNameUnique(dto.getRoleName(), null);
        SysRole role = BeanUtil.copyProperties(dto, SysRole.class);
        boolean save = save(role);
        insertRoleMenu(dto.getMenuIds(), dto.getAppMenuIds(), role.getRoleId());
        return save;
    }

    /**
     * 新增角色菜单信息
     */
    public void insertRoleMenu(List<Long> menuIds, List<Long> appMenuIds, Long roleId) {
        // 新增用户与角色管理
        List<SysRoleMenu> list = new ArrayList<>(menuIds.size() + appMenuIds.size());
        for (Long menuId : menuIds) {
            SysRoleMenu rm = new SysRoleMenu();
            rm.setRoleId(roleId);
            rm.setMenuId(menuId);
            rm.setMenuLocation(1);
            list.add(rm);
        }
        for (Long menuId : appMenuIds) {
            SysRoleMenu rm = new SysRoleMenu();
            rm.setRoleId(roleId);
            rm.setMenuId(menuId);
            rm.setMenuLocation(0);
            list.add(rm);
        }
        if (!list.isEmpty()) {
            roleMenuMapper.batchRoleMenu(list);
        }
    }

    /**
     * 修改保存角色信息
     *
     * @param dto 角色信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRole(SysRoleUpdateDTO dto) {
        if (dto.getRoleId() == ADMIN_ROLE_ID) {
            throw new ServiceException("不允许修改超级管理员角色信息");
        }
        checkRoleNameUnique(dto.getRoleName(), dto.getRoleId());
        SysRole role = getByIdThrow(dto.getRoleId());
        BeanUtil.copyProperties(dto, role);
        // 删除角色与菜单关联
        roleMenuMapper.deleteRoleMenuByRoleId(role.getRoleId());
        insertRoleMenu(dto.getMenuIds(), dto.getAppMenuIds(), role.getRoleId());
        return updateById(role);
    }

    /**
     * 修改角色状态
     *
     * @param dto 角色信息
     * @return 结果
     */
    @Override
    public boolean updateRoleStatus(SysRoleStatusDTO dto) {
        SysRole role = getByIdThrow(dto.getRoleId());
        return lambdaUpdate().eq(SysRole::getRoleId, role.getRoleId())
                .set(SysRole::getStatus, role.getStatus().equals("0") ? "1" : "0")
                .update();
    }

    /**
     * 通过角色ID删除角色
     *
     * @param roleId 角色ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRoleById(Long roleId) {
        if (roleId == DIY_ROLE_ID || roleId == ADMIN_ROLE_ID || roleId == OWNER_ROLE_ID) {
            return false;
        }
        if (countUserRoleByRoleId(roleId) > 0) {
            throw new ServiceException("该角色已分配,不能删除");
        }
        // 删除角色与菜单关联
        roleMenuMapper.deleteRoleMenuByRoleId(roleId);
        return removeById(roleId);
    }

}
