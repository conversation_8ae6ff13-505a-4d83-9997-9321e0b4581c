package com.ruoyi.system.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

/**
 * <AUTHOR>
 * @date 2024/11/5
 */
@Data
public class SysDeptUpdateDTO {

    /** 父部门ID 一级位0*/
    @NotNull
    @PositiveOrZero
    private Long parentId;

    @Schema(description = "部门id")
    @NotNull
    @Positive
    private Long deptId;

    /** 部门名称 */
    @NotBlank
    private String deptName;

    /** 图标 */
    private String logo;

    /** 区号 */
    private String areaCode;

    /** 联系电话 */
    private String phone;

    /** 机构类型 1 供应商 2 经销商 3 安装商 4部门 */
    @NotNull
    @Range(min = 1, max = 4)
    private Integer type;

    /** 邮箱 */
    @Email
    @Size(max = 50)
    private String email;

    /** 显示顺序 */
    private Integer orderNum;

    /** 备注 */
    private String remark;

    /** 部门状态:0正常,1停用 */
    @NotBlank
    @Pattern(regexp = "^0|1$")
    private String status;

}
