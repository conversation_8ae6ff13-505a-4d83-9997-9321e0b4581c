package com.ruoyi.system.controller;

import com.github.pagehelper.Page;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.SysLogininfor;
import com.ruoyi.system.pojo.SysLogininforPageReqDTO;
import com.ruoyi.system.service.ISysLogininforService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

/**
 * 系统访问记录
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/logininfor")
public class SysLogininforController extends BaseController {

    @Resource
    private ISysLogininforService logininforService;
    @Resource
    private RedisService redisService;

    /**
     * 分页
     */
    @RequiresPermissions("system:logininfor:list")
    @GetMapping("/list")
    public TableDataInfo<SysLogininfor> list(SysLogininforPageReqDTO dto) {
        Page<SysLogininfor> page = startPage(dto);
        logininforService.page(dto);
        return getDataTable(page);
    }

    /*
     * 导出 system:logininfor:export
    @Log(title = "登录日志", businessType = BusinessType.EXPORT)
    @RequiresPermissions("system:logininfor:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysLogininforPageReqDTO dto) {
        List<SysLogininfor> list = logininforService.page(dto);
        ExcelUtil<SysLogininfor> util = new ExcelUtil<>(SysLogininfor.class);
        util.exportExcel(response, list, "登录日志");
    }
     */

    /**
     * 删除 system:logininfor:remove
     */
    @RequiresPermissions("system:logininfor:remove")
    @Log(title = "登录日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{infoId}")
    public R<Boolean> remove(@PathVariable Long infoId) {
        return R.ok(logininforService.removeById(infoId));
    }

    /**
     * 清空 system:logininfor:remove
     */
    @RequiresPermissions("system:logininfor:remove")
    @Log(title = "登录日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/clean")
    public R<Void> clean() {
        logininforService.cleanLogininfor();
        return R.ok();
    }

    /**
     * 账户解锁 system:logininfor:unlock
     */
    @RequiresPermissions("system:logininfor:unlock")
    @Log(title = "账户解锁", businessType = BusinessType.OTHER)
    @GetMapping("/unlock/{userName}")
    public R<Boolean> unlock(@PathVariable("userName") String userName) {
        return R.ok(redisService.deleteObject(CacheConstants.PWD_ERR_CNT_KEY + userName));
    }

    /**
     * 新增
     */
    @InnerAuth
    @PostMapping
    public R<Boolean> add(@RequestBody SysLogininfor logininfor) {
        return R.ok(logininforService.insertLogininfor(logininfor));
    }
}
