package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.domain.SysHelp;
import com.ruoyi.system.pojo.UpgradeDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 通知公告表 数据层
 *
 * <AUTHOR>
 */
public interface SysHelpMapper extends BaseMapper<SysHelp> {

    @Select("select title,content,language,type,versions,version_number,client_type,sys_type,must from sys_help where type = 3 and client_type = #{dto.clientType} and version_number > #{dto.versionNumber} and sys_type = #{dto.sysType} and language = #{dto.language} order by version_number desc limit 1")
    SysHelp upgrade(@Param("dto") UpgradeDTO dto);

    @Select("select count(1) from sys_help where type = 3 and client_type = #{dto.clientType} and version_number > #{dto.versionNumber} and sys_type = #{dto.sysType} and must = 1")
    int mustUpgrade(@Param("dto") UpgradeDTO dto);

}
