package com.ruoyi.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.ruoyi.common.core.domain.IntStr;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.security.service.impl.BaseServiceImpl;
import com.ruoyi.system.domain.SysDistrict;
import com.ruoyi.system.mapper.SysDistrictMapper;
import com.ruoyi.system.mapper.SysStatesMapper;
import com.ruoyi.system.pojo.*;
import com.ruoyi.system.service.ISysDistrictService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/30
 */
@Service
public class SysDistrictServiceImpl extends BaseServiceImpl<SysDistrictMapper, SysDistrict> implements ISysDistrictService {

    @Resource
    private SysStatesMapper statesMapper;

    @Override
    @Transactional
    public boolean add(SysDistrictDTO dto) {
        SysDistrict entity = BeanUtil.copyProperties(dto, SysDistrict.class);
        if (dto.getPid() == 0) {
            entity.setGrade(0);
        } else {
            SysDistrict p = getByIdThrow(dto.getPid());
            entity.setGrade(p.getGrade() + 1);
        }
        save(entity);
        if (entity.getGrade() == 0) {
            List<String> list = statesMapper.getAllByCountry(dto.getName());
            if (!list.isEmpty()) {
                List<SysDistrict> states = list.stream().map(p -> new SysDistrict(entity.getId(), p, 1)).toList();
                saveBatch(states);
            }
        }
        return true;
    }

    @Override
    public boolean update(SysDistrictUpdateDTO dto) {
        SysDistrict entity = getByIdThrow(dto.getId());
        if (dto.getPid() == 0) {
            entity.setGrade(0);
        } else {
            SysDistrict p = getByIdThrow(dto.getPid());
            entity.setGrade(p.getGrade() + 1);
        }
        BeanUtil.copyProperties(dto, entity);
        return updateById(entity);
    }

    @Override
    public Page<SysDistrict> page(SysDistrictPageReqDTO dto) {
        if (ObjUtil.isEmpty(dto.getProvince())) {
            Page<SysDistrict> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
            lambdaQuery().eq(SysDistrict::getPid, 0)
                    .like(StrUtil.isNotBlank(dto.getCountry()), SysDistrict::getName, dto.getCountry())
                    .list();
            if (ObjUtil.isNotEmpty(page.getResult())) {
                List<Integer> ids = page.getResult().stream().map(SysDistrict::getId).toList();
                Map<Integer, List<SysDistrict>> map = lambdaQuery().in(SysDistrict::getPid, ids).list()
                        .stream().collect(Collectors.groupingBy(SysDistrict::getPid));
                page.getResult().forEach(p -> p.setChildren(map.get(p.getId())));
            }
            return page;
        } else {
            if (ObjUtil.isEmpty(dto.getCountry())) {
                Page<SysDistrict> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
                lambdaQuery().eq(SysDistrict::getGrade, 1).like(SysDistrict::getName, dto.getProvince()).list();
                return page;
            } else {
                List<Integer> pid = lambdaQuery().eq(SysDistrict::getPid, 0).like(SysDistrict::getName, dto.getCountry()).list()
                        .stream().map(SysDistrict::getId).toList();
                if (ObjUtil.isNotEmpty(pid)) {
                    Page<SysDistrict> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
                    lambdaQuery().eq(SysDistrict::getGrade, 1).in(SysDistrict::getPid, pid)
                            .like(SysDistrict::getName, dto.getProvince())
                            .list();
                    return page;
                }
                return null;
            }
        }
    }

    @Override
    public List<IntStr> listByPid(Integer pid) {
        return baseMapper.listByPid(pid);
    }

    @Override
    public List<SysDistrictVO> listByPid(String pid) {
        List<SysDistrictVO> all = baseMapper.listByPids(pid);
        List<SysDistrictVO> list = all.stream().filter(item -> item.getPid() == 0)
                .peek(item -> item.setChildren(new ArrayList<>())).toList();
        Map<Integer, List<SysDistrictVO>> map = all.stream().filter(item -> item.getPid() != 0)
                .collect(Collectors.groupingBy(SysDistrictVO::getPid));
        list.forEach(item -> item.setChildren(map.get(item.getId())));
        return list;
    }

    @Override
    public List<IntStr> listByIds(List<Integer> ids) {
        return baseMapper.listByIds(CollUtil.join(ids, ","));
    }

    @Override
    public boolean delete(Integer id) {
        SysDistrict district = getByIdThrow(id);
        int count;
        if (district.getGrade() == 0) {
            count = baseMapper.countByCountryId(id);
        } else {
            count = baseMapper.countByProvinceId(id);
        }
        if (count != 0) {
            throw new ServiceException("已经被使用，无法删除", district.getName());
        }
        return removeById(id);
    }

    @Override
    public List<CountryDTO> getAllCountry() {
        return baseMapper.getAllCountry();
    }

    @Override
    @Transactional
    public boolean updateAllCountry(List<CountryDTO> list) {
        List<Integer> oldIds = baseMapper.getAllId();
        List<Integer> newIds = new ArrayList<>();
        List<Integer> delIds = new ArrayList<>();
        for (CountryDTO dto : list) {
            //新的启用
            if (dto.getStart()) {
                if (!oldIds.contains(dto.getId())) {
                    newIds.add(dto.getId());
                }
            } else {
                if (oldIds.contains(dto.getId())) {
                    if (baseMapper.countByCountryId(dto.getId()) > 0) {
                        throw new ServiceException("不能删除已经被使用的国家{0}", dto.getName());
                    }
                    delIds.add(dto.getId());
                }
            }
        }
        if (CollUtil.isNotEmpty(newIds)) {
            baseMapper.updateDelete(0, join(newIds));
        }
        if (CollUtil.isNotEmpty(delIds)) {
            baseMapper.updateDelete(1, join(delIds));
        }
        return true;
    }

}
