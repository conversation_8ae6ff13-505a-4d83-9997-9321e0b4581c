package com.ruoyi.system.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/11/12
 */
@Data
public class SysHelpDTO {

    @Schema(description = "类型（1帮助中心 2帮助文件）", hidden = true)
    private Integer type;

    /** 标题 */
    private String title;

    /** 语言 */
    @NotBlank(message = "语言不能为空")
    private String language;

    /** 内容 */
    private String content;

    /** 排序 */
    private Integer sort;

    /** 版本名称 */
    private String versions;

    /** 客户端 2安装端 3客户端 */
    private Integer clientType;

    /** 版本号 */
    private Integer versionNumber;

    /** 系统类型 1安卓 2ios */
    private Integer sysType;

    /** 强制升级 0：非强制  1：强制 */
    private Integer must;

}
