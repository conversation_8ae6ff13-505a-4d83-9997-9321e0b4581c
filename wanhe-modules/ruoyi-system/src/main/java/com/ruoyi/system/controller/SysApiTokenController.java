package com.ruoyi.system.controller;

import com.github.pagehelper.Page;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.SysApiToken;
import com.ruoyi.system.pojo.ApiTokenDTO;
import com.ruoyi.system.pojo.ApiTokenUpdateDTO;
import com.ruoyi.system.pojo.SysApiTokenPageReqDTO;
import com.ruoyi.system.service.ISysApiTokenService;
import jakarta.annotation.Resource;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * apiToken管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/token")
public class SysApiTokenController extends BaseController {

    @Resource
    private ISysApiTokenService apiTokenService;

    /**
     * 分页 system:apiToken:page
     */
    @RequiresPermissions("system:apiToken:page")
    @GetMapping("/page")
    public TableDataInfo<SysApiToken> page(@ParameterObject SysApiTokenPageReqDTO dto) {
        Page<SysApiToken> page = startPage(dto);
        apiTokenService.page(dto);
        return getDataTable(page);
    }

    /**
     * 查询详细
     */
    @GetMapping(value = "/{apiTokenId}")
    public R<SysApiToken> getInfo(@PathVariable Long apiTokenId) {
        return R.ok(apiTokenService.get(apiTokenId));
    }

    /**
     * 新增 system:apiToken:add
     */
    @RequiresPermissions("system:apiToken:add")
    @Log(title = "apiToken", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@Validated @RequestBody ApiTokenDTO dto) {
        return R.ok(apiTokenService.add(dto));
    }

    /**
     * 修改 system:apiToken:edit
     */
    @RequiresPermissions("system:apiToken:edit")
    @Log(title = "apiToken", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@Validated @RequestBody ApiTokenUpdateDTO dto) {
        return R.ok(apiTokenService.update(dto));
    }

    /**
     * 删除 system:apiToken:remove
     */
    @RequiresPermissions("system:apiToken:remove")
    @Log(title = "apiToken", businessType = BusinessType.DELETE)
    @DeleteMapping("/{apiTokenId}")
    public R<Boolean> remove(@PathVariable Long apiTokenId) {
        return R.ok(apiTokenService.delete(apiTokenId));
    }

}
