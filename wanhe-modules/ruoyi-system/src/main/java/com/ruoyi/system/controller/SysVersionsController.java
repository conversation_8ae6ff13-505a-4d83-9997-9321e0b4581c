package com.ruoyi.system.controller;

import com.github.pagehelper.Page;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.SysHelp;
import com.ruoyi.system.pojo.SysHelpDTO;
import com.ruoyi.system.pojo.SysHelpPageReqDTO;
import com.ruoyi.system.pojo.SysHelpUpdateDTO;
import com.ruoyi.system.pojo.UpgradeDTO;
import com.ruoyi.system.service.ISysHelpService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 版本升级
 * <AUTHOR>
 * @date 2024/11/12
 */
@RestController
@RequestMapping("/versions")
public class SysVersionsController extends BaseController {

    @Resource
    private ISysHelpService helpService;

    /**
     * 分页 system:versions:page
     */
    @RequiresPermissions("system:versions:page")
    @GetMapping("/page")
    public TableDataInfo<SysHelp> page(SysHelpPageReqDTO dto) {
        Page<SysHelp> page = startPage(dto);
        dto.setType(3);
        helpService.page(dto);
        return getDataTable(page);
    }

    /**
     * 详细
     */
    @GetMapping(value = "/{id}")
    public R<SysHelp> getInfo(@PathVariable Integer id) {
        return R.ok(helpService.getById(id));
    }

    /**
     * 获取最新版本的升级内容
     */
    @GetMapping(value = "/upgrade")
    public R<SysHelp> upgrade(@Validated UpgradeDTO dto) {
        return R.ok(helpService.upgrade(dto));
    }

    /**
     * 判断是否需要强制升级
     */
    @GetMapping(value = "/mustUpgrade")
    public R<Boolean> mustUpgrade(@Validated UpgradeDTO dto) {
        return R.ok(helpService.mustUpgrade(dto));
    }

    /**
     * 新增 system:versions:add
     */
    @RequiresPermissions("system:versions:add")
    @Log(title = "版本升级", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@Validated @RequestBody SysHelpDTO dto) {
        dto.setType(3);
        return R.ok(helpService.add(dto));
    }

    /**
     * 修改 system:versions:edit
     */
    @RequiresPermissions("system:versions:edit")
    @Log(title = "版本升级", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@Validated @RequestBody SysHelpUpdateDTO dto) {
        return R.ok(helpService.update(dto));
    }

    /**
     * 删除 system:versions:remove
     */
    @RequiresPermissions("system:versions:remove")
    @Log(title = "版本升级", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public R<Boolean> remove(@PathVariable Integer id) {
        return R.ok(helpService.removeById(id));
    }

}
