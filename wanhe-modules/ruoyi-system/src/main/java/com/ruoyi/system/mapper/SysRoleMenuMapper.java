package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.SysMenu;
import com.ruoyi.system.domain.SysRoleMenu;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 角色与菜单关联表 数据层
 *
 * <AUTHOR>
 */
public interface SysRoleMenuMapper {

    @Select("select menu_id,menu_location from sys_menu where del_flag = 0")
    List<SysMenu> getMenuList();

    @Select("select menu_id,menu_location from sys_role_menu where role_id = #{roleId}")
    List<SysRoleMenu> getRoleMenuList(Long roleId);
    /**
     * 查询菜单使用数量
     *
     * @param menuId 菜单ID
     * @return 结果
     */
    int checkMenuExistRole(Long menuId);

    /**
     * 通过角色ID删除角色和菜单关联
     *
     * @param roleId 角色ID
     * @return 结果
     */
    int deleteRoleMenuByRoleId(Long roleId);

    /**
     * 批量删除角色菜单关联信息
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteRoleMenu(Long[] ids);

    /**
     * 批量新增角色菜单信息
     *
     * @param roleMenuList 角色菜单列表
     * @return 结果
     */
    int batchRoleMenu(List<SysRoleMenu> roleMenuList);
}
