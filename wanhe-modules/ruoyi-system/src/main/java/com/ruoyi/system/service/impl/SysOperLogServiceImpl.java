package com.ruoyi.system.service.impl;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.service.impl.BaseServiceImpl;
import com.ruoyi.system.api.domain.SysOperLog;
import com.ruoyi.system.mapper.SysOperLogMapper;
import com.ruoyi.system.pojo.SysOperlogPageReqDTO;
import com.ruoyi.system.service.ISysOperLogService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * 操作日志 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysOperLogServiceImpl extends BaseServiceImpl<SysOperLogMapper, SysOperLog> implements ISysOperLogService {

    @Resource
    private RestTemplate restTemplate;

    /**
     * 新增操作日志
     *
     * @param operLog 操作日志对象
     * @return 结果
     */
    @Override
    public boolean insertOperlog(SysOperLog operLog) {
        String ip = operLog.getOperIp();
        if (ObjUtil.isNotEmpty(ip)) {
            String ipAddr = restTemplate.getForObject("http://ip.plyz.net/ip.ashx?ip=" + ip, String.class);
            if (!ObjectUtils.isEmpty(ipAddr)) {
                String addr = ipAddr.substring(ip.length() + 1);
                if (addr.startsWith("内网")) {
                    operLog.setOperLocation("内网IP");
                } else {
                    operLog.setOperLocation(addr);
                }
            }
        }
        return save(operLog);
    }

    @Override
    public List<SysOperLog> page(SysOperlogPageReqDTO dto) {
        LambdaQueryChainWrapper<SysOperLog> wrapper = lambdaQueryTenant(SysOperLog::getTenantId, SysOperLog::getDeptId, SysOperLog::getCreateId)
                .eq(ObjUtil.isNotEmpty(dto.getOperIp()), SysOperLog::getOperIp, dto.getOperIp())
                .eq(ObjUtil.isNotEmpty(dto.getTitle()), SysOperLog::getTitle, dto.getTitle())
                .eq(ObjUtil.isNotEmpty(dto.getOperName()), SysOperLog::getOperName, dto.getOperName())
                .in(ObjUtil.isNotEmpty(dto.getBusinessType()), SysOperLog::getBusinessType, dto.getBusinessType())
                .eq(ObjUtil.isNotEmpty(dto.getStatus()), SysOperLog::getStatus, dto.getStatus())
                .between(dto.getBeginTime() != null && dto.getEndTime() != null, SysOperLog::getOperTime, dto.getBeginTime(), dto.getEndTime())
                .orderByDesc(SysOperLog::getOperId);
        if (dto.getIsLogout() != null && dto.getIsLogout()) {
            wrapper.eq(SysOperLog::getBusinessType, BusinessType.LOGOUT.ordinal());
        } else {
            wrapper.ne(SysOperLog::getBusinessType, BusinessType.OTHER.ordinal());
        }
        return wrapper.list();
    }

    /**
     * 清空操作日志
     */
    @Override
    public void cleanOperLog() {
        baseMapper.cleanOperLog();
    }
}
