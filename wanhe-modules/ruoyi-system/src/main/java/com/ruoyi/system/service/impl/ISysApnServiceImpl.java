package com.ruoyi.system.service.impl;

import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.service.impl.BaseServiceImpl;
import com.ruoyi.system.domain.SysApn;
import com.ruoyi.system.mapper.SysApnMapper;
import com.ruoyi.system.pojo.SysApnPageReqDTO;
import com.ruoyi.system.service.ISysApnService;
import org.springframework.stereotype.Service;

/**
 * Apn
 *
 * <AUTHOR>
 * @date 2025/05/13
 */
@Service
public class ISysApnServiceImpl extends BaseServiceImpl<SysApnMapper, SysApn> implements ISysApnService {

    @Override
    public void page(SysApnPageReqDTO dto) {
        lambdaQuery().like(StringUtils.isNotBlank(dto.getApn()), SysApn::getApn, dto.getApn())
                .orderByAsc(SysApn::getSort)
                .list();
    }
}
