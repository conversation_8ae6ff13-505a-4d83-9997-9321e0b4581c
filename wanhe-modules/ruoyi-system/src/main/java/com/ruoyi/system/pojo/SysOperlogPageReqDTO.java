package com.ruoyi.system.pojo;

import com.ruoyi.common.core.domain.PageReqDTO;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/6
 */
@Setter
@Getter
public class SysOperlogPageReqDTO extends PageReqDTO {

    /** 操作ip */
    private String operIp;

    /** 操作模块 */
    private String title;

    /** 操作人员 */
    private String operName;

    /** 业务类型（0其它 1新增 2修改 3删除） */
    private List<Integer> businessType;

    /** 操作状态（0正常 1异常） */
    private Integer status;

    /** 登录开始日期 */
    private LocalDate beginTime;

    /** 登录结束日期 */
    private LocalDate endTime;

    /** 查询是否注销数据 */
    private Boolean isLogout;

}
