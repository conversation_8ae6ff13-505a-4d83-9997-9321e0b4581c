<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysMenuMapper">

	<resultMap type="SysMenu" id="SysMenuResult">
		<id     property="menuId"         column="menu_id"        />
		<result property="menuName"       column="menu_name"      />
		<result property="parentName"     column="parent_name"    />
		<result property="parentId"       column="parent_id"      />
		<result property="orderNum"       column="order_num"      />
		<result property="path"           column="path"           />
		<result property="component"      column="component"      />
		<result property="query"          column="query"          />
		<result property="routeName"      column="route_name"     />
		<result property="isFrame"        column="is_frame"       />
		<result property="isCache"        column="is_cache"       />
		<result property="menuType"       column="menu_type"      />
		<result property="visible"        column="visible"        />
		<result property="status"         column="status"         />
		<result property="perms"          column="perms"          />
		<result property="icon"           column="icon"           />
		<result property="createBy"       column="create_by"      />
		<result property="createTime"     column="create_time"    />
		<result property="updateTime"     column="update_time"    />
		<result property="updateBy"       column="update_by"      />
		<result property="remark"         column="remark"         />
		<result property="menuLocation"         column="menu_location"         />
	</resultMap>
    
    <select id="selectMenuList" parameterType="SysMenu" resultMap="SysMenuResult">
		select m.menu_id, i.name_${language} as menu_name, m.parent_id, m.order_num, m.path, m.component, m.`query`, m.route_name, m.is_frame,
		m.is_cache, m.menu_type, m.visible, m.status, ifnull(m.perms,'') as perms, m.icon, m.create_time
		from sys_menu m
		left join sys_i18n i on i.type = 1 and i.data_id = m.menu_id
		where m.menu_location = #{menuLocation} and m.del_flag = '0'
		<if test="menuName != null and menuName != ''">
			AND m.menu_name like concat('%', #{menuName}, '%')
		</if>
		<if test="status != null and status != ''">
			AND m.status = #{status}
		</if>
		order by m.parent_id, m.order_num
	</select>
	
	<select id="selectMenuTreeAll" resultMap="SysMenuResult">
		<if test="language == 'zh'">
			select m.menu_id, m.parent_id, m.menu_name, m.path, m.component, m.`query`, m.route_name, m.visible, m.status, ifnull(m.perms,'') as perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time
			from sys_menu m
			where m.menu_type in ('M', 'C') and m.status = 0 and m.menu_location = #{menuLocation} and m.del_flag = '0'
			order by m.parent_id, m.order_num
		</if>
		<if test="language != 'zh'">
			select m.menu_id, m.parent_id, i.name_${language} as menu_name, m.path, m.component, m.`query`, m.route_name, m.visible, m.status, ifnull(m.perms,'') as perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time
			from sys_menu m
			left join sys_i18n i on i.type = 1 and i.data_id = m.menu_id
			where m.menu_type in ('M', 'C') and m.status = 0 and m.menu_location = #{menuLocation} and m.del_flag = '0'
			order by m.parent_id, m.order_num
		</if>
	</select>
	
	<select id="selectMenuListByUserId" parameterType="SysMenu" resultMap="SysMenuResult">
		select distinct m.menu_id, m.parent_id, i.name_${language} as menu_name, m.path, m.component, m.`query`, m.route_name, m.visible, m.status, ifnull(m.perms,'') as perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time
		from sys_menu m
		left join sys_i18n i on i.type = 1 and i.data_id = m.menu_id
		left join sys_role_menu rm on m.menu_id = rm.menu_id
		left join sys_user_role ur on rm.role_id = ur.role_id
		left join sys_role ro on ur.role_id = ro.role_id
		where ur.user_id = #{userId} and m.menu_location = #{menuLocation} and m.del_flag = '0'
		<if test="menuName != null and menuName != ''">
            AND m.menu_name like concat('%', #{menuName}, '%')
		</if>
		<if test="status != null and status != ''">
            AND m.status = #{status}
		</if>
		order by m.parent_id, m.order_num
	</select>
    
    <select id="selectMenuTreeByUserId" parameterType="Long" resultMap="SysMenuResult">
		<if test="language == 'zh'">
			select distinct m.menu_id, m.parent_id, m.menu_name, m.path, m.component, m.`query`, m.route_name, m.visible, m.status, ifnull(m.perms,'') as perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time
			from sys_menu m
			left join sys_role_menu rm on m.menu_id = rm.menu_id
			left join sys_user_role ur on rm.role_id = ur.role_id
			left join sys_role ro on ur.role_id = ro.role_id
			left join sys_user u on ur.user_id = u.user_id
			where u.user_id = #{userId} and m.menu_type in ('M', 'C') and m.status = 0 and ro.status = 0 and m.menu_location = '${menuLocation}' and m.del_flag = '0'
			order by m.parent_id, m.order_num
		</if>
		<if test="language != 'zh'">
			select distinct m.menu_id, m.parent_id, i.name_${language} as menu_name, m.path, m.component, m.`query`, m.route_name, m.visible, m.status, ifnull(m.perms,'') as perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time
			from sys_menu m
			left join sys_i18n i on i.type = 1 and i.data_id = m.menu_id
			left join sys_role_menu rm on m.menu_id = rm.menu_id
			left join sys_user_role ur on rm.role_id = ur.role_id
			left join sys_role ro on ur.role_id = ro.role_id
			left join sys_user u on ur.user_id = u.user_id
			where u.user_id = #{userId} and m.menu_type in ('M', 'C') and m.status = 0 and ro.status = 0 and m.menu_location = '${menuLocation}' and m.del_flag = '0'
			order by m.parent_id, m.order_num
		</if>
	</select>
	
	<select id="selectMenuListByRoleId" resultType="Long">
		select m.menu_id
		from sys_menu m
            left join sys_role_menu rm on m.menu_id = rm.menu_id
        where rm.role_id = #{roleId} and m.del_flag = '0'
            <if test="menuCheckStrictly">
              and m.menu_id not in (select m.parent_id from sys_menu m inner join sys_role_menu rm on m.menu_id = rm.menu_id and rm.role_id = #{roleId})
            </if>
		order by m.parent_id, m.order_num
	</select>

	<select id="selectMenuPermsByUserId" parameterType="Long" resultType="String">
		select distinct m.perms
		from sys_menu m
			 left join sys_role_menu rm on m.menu_id = rm.menu_id
			 left join sys_user_role ur on rm.role_id = ur.role_id
			 left join sys_role r on r.role_id = ur.role_id
		where m.status = '0' and r.status = '0' and ur.user_id = #{userId} and m.perms != '' and m.del_flag = '0'
	</select>
	
	<select id="selectMenuPermsByRoleId" parameterType="String" resultType="String">
		select distinct m.perms
		from sys_menu m left join sys_role_menu rm on m.menu_id = rm.menu_id
		where m.status = '0' and rm.role_id in(#{roleId}) and m.perms != '' and m.del_flag = '0'
	</select>
	
	<select id="selectMenuById" parameterType="Long" resultMap="SysMenuResult">
		select m.menu_id, m.parent_id, i.name_${language} as menu_name, m.path, m.component, m.`query`, m.route_name, m.visible, m.status, ifnull(m.perms,'') as perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time
		from sys_menu m
		left join sys_i18n i on i.type = 1 and i.data_id = m.menu_id
		where menu_id = #{menuId} and del_flag = '0'
	</select>
	
	<select id="hasChildByMenuId" resultType="Integer">
	    select count(1) from sys_menu where parent_id = #{menuId} and del_flag = '0'
	</select>
	
	<delete id="deleteMenuById" parameterType="Long">
	    update sys_menu set del_flag = '2' where menu_id = #{menuId} and del_flag = '0'
	</delete>

</mapper> 