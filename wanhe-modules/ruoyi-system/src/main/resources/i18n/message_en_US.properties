#system.common_en_US.properties
#Sat Feb 08 11:09:55 CST 2025
修改个人信息异常，请联系管理员=Personal info change error. Contact admin
新增菜单'{0}'失败，地址必须以http(s)开头=Failed to add menu '{0}'. Address must start with http(s)
存在用户或电站，不允许删除=Users or power stations exist and deletion is not allowed
存在用户或电站不能修改父级机构=There are users or power stations that cannot modify the parent mechanism
不允许修改超级管理员角色信息=Not allowed to change role info of super admin
用户名已存在=Login account already exists
修改菜单'{0}'失败，菜单名称已存在=Failed to change menu '{0}'. Menu name already exists
已分配,不能删除=It has been assigned and cannot be deleted
用户名不存在=Username does not exist
菜单已分配,不允许删除=Menu has been assigned and cannot be deleted
该角色已分配,不能删除=Role has been assigned and cannot be deleted
手机号码格式错误，请重新输入=Tel format error. Enter again
上传图片异常，请联系管理员=Error uploading picture. Contact admin
修改菜单'{0}'失败，上级菜单不能选择自己=Failed to change menu '{0}'. Parent menu cannot be itself
部门存在用户,不允许删除=Users exist under this department. Deletion not allowed
角色名称已存在=Role name already exists
手机号已存在=Tel already exists
存在下级部门,不允许删除=Sub-departments exist. Deletion not allowed
新密码不能与旧密码相同=New password cannot be the same as old one
内置参数不能删除=Built-in parameters cannot be deleted
部门停用，不允许新增=Department disabled. Addition not allowed
邮箱已存在=Email already exists
文件服务异常，请联系管理员=File service error. Contact admin
修改密码异常，请联系管理员=Password change error. Contact admin
用户名或密码错误=Wrong username or password
不能删除已经被使用的国家{0}=Used country {0} cannot be deleted
设备型号错误=Device model error
新增菜单'{0}'失败，菜单名称已存在=Failed to add menu '{0}'. Menu name already exists
修改菜单'{0}'失败，地址必须以http(s)开头=Failed to change menu '{0}'. Address must start with http(s)
文件格式不正确，请上传{0}格式=File format error. Upload file in {0} format
该部门包含未停用的子部门=Non-disabled sub-departments exist under this department
邮箱格式错误，请重新输入=Email format error. Enter again
存在子菜单,不允许删除=Sub-menus exist. Deletion not allowed
邮箱输入错误=Email input error
修改密码失败，旧密码错误=Failed to change password. Wrong old password
该用户已绑定电站，请先解除电站绑定=The user has been bound to a power station. Unbind the power station first
用户名,昵称,邮箱,手机号,所属机构=Username,nickname,email address,phone number,affiliation
