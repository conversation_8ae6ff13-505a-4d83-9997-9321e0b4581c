# Spring
spring:
  cloud:
    nacos:
      username: nacos
      password: nacos
      discovery:
        # 服务注册地址
        server-addr: ${NACOS_SERVER_ADDR:47.105.115.215:8848}
        # 命名空间
        namespace: test
      config:
        # 配置中心地址
        server-addr: ${NACOS_SERVER_ADDR:47.105.115.215:8848}
        # 命名空间
        namespace: test
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
