<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wh.data.mapper.DeviceAlarmMapper">

    <insert id="batchSave">
        insert into wh_device_alarm(device_id, power_station_id, sn_number, alarm_code, alarm_key, alarm_data, occurred_at, device_type, tenant_id, create_time) values
        <foreach collection="list" item="t" separator=",">
            (#{t.deviceId}, #{t.powerStationId}, #{t.snNumber}, #{t.alarmCode}, #{t.alarmKey}, #{t.alarmData}, #{t.occurredAt}, #{t.deviceType}, #{t.tenantId}, now())
        </foreach>
    </insert>
</mapper>