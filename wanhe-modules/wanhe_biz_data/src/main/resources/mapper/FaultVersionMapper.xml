<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wh.data.mapper.FaultVersionMapper">


    <select id="page" resultType="com.wh.data.domain.FaultVersion">
        SELECT *
        FROM wh_fault_version
        <where>
            <if test="versionName != null and versionName != '' ">
                version_name LIKE CONCAT('%', #{versionName}, '%')
            </if>
            <if test="versionNo != null and versionNo != '' ">
                AND version_no LIKE CONCAT('%', #{versionNo}, '%')
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>