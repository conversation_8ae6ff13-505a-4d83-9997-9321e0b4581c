package com.wh.data.pojo.station;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ruoyi.common.core.json.BigDecimalSerializer;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/1/12
 */
@Data
public class BatteryDataVO {

    // 电池序列号
    private String sn = "";
    // 电池连接状态，0：未连接，1：已连接
    private Integer commStatus = 0;
    // SOH,%
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal soh;
    // 电池电压SOC,%
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal soc;
    // 电池电压,V
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal vol;
    // 电池电流,A
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal curr;
    // 功率,W
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal power;

}
