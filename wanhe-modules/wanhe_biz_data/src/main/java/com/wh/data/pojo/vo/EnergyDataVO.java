package com.wh.data.pojo.vo;

import com.ruoyi.common.core.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/12/16
 */
@Data
public class EnergyDataVO {

    @Excel(name = "电站名称", width = 20)
    @Schema(description = "电站名称")
    private String stationName;

    @Excel(name = "日期", width = 18)
    @Schema(description = "日期")
    private String date;

    @Excel(name = "发电量", width = 22, decimalToStr = true)
    @Schema(description = "发电量")
    private BigDecimal stageDailyEnergy;

    @Excel(name = "消耗量", decimalToStr = true)
    @Schema(description = "消耗量")
    private BigDecimal consumption = BigDecimal.ZERO;

}
