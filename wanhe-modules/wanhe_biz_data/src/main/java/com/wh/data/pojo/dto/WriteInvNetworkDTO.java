package com.wh.data.pojo.dto;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/4
 */
@Setter
@Getter
public class WriteInvNetworkDTO extends DeviceDataDTO {

    // 微逆链接的组件数
    private int pcCount;
    // 光伏板位置
    private List<Pv> pv;

    public WriteInvNetworkDTO(String sn) {
        super(sn);
    }

    @Data
    public static class Pv {

        // 组件编号
        private int num;
        // 位置行列
        private String location;
        // 方向，0：水平，1：垂直
        private int direction;
        // 倾角
        private BigDecimal angle;
        // 仰角
        private BigDecimal elevation;

        public Pv(int direction, BigDecimal angle, BigDecimal elevation) {
            this.direction = direction;
            if (angle != null) {
                this.angle = angle.stripTrailingZeros();
            }
            if (elevation != null) {
                this.elevation = elevation.stripTrailingZeros();
            }
        }
    }
}
