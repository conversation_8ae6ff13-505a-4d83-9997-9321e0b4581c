package com.wh.data.init;

import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.InfluxDBClientFactory;
import okhttp3.OkHttpClient;
import org.influxdb.InfluxDB;
import org.influxdb.impl.InfluxDBImpl;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

/**
 * <AUTHOR>
 * @date 2024/12/8
 */
@Configuration
@ConditionalOnProperty("spring.influx.url")
public class InfluxDbConfiguration {

    @Bean
    public InfluxDBClient influxDBClient(InfluxDbProperties properties) {
        return InfluxDBClientFactory.create(properties.getUrl(), properties.getPassword().toCharArray(), properties.getUser(), properties.getDatabase());
    }

    @Bean
    public InfluxDB influxDb(InfluxDbProperties properties) {
        OkHttpClient.Builder builder = new OkHttpClient.Builder();
        builder.readTimeout(Duration.ofMinutes(10));
        return new InfluxDBImpl(properties.getUrl(), properties.getUser(), properties.getPassword(), builder);
    }

}
