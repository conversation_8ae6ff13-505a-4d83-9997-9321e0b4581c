package com.wh.data.init;

import com.wh.data.protocol.TopicService;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

@Component
public class TopicServiceLocator implements ApplicationContextAware {

    private Map<String, TopicService> serviceMap;
    private final Map<String, Method> methodMap = new HashMap<>();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        serviceMap = applicationContext.getBeansOfType(TopicService.class);
        for (Map.Entry<String, TopicService> entry : serviceMap.entrySet()) {
            for (Method method : entry.getValue().getClass().getDeclaredMethods()) {
                String name = method.getName();
                String key = entry.getKey() + Character.toUpperCase(name.charAt(0)) + name.substring(1);
                methodMap.put(key, method);
            }
        }
    }

    public TopicService getService(String name) {
        return serviceMap.get(name);
    }

    public Method getMethod(String name, String topic) {
        int index = topic.lastIndexOf('/');
        String key = name + topic.substring(index + 1);
        return methodMap.get(key);
    }

}
