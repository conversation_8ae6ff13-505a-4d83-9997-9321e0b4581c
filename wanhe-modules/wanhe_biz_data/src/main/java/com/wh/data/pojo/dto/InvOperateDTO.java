package com.wh.data.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class InvOperateDTO {

    @Schema(description = "设备sn")
    @NotBlank
    private String sn;

    @Schema(description = "操作类型 1 开机，2 关机，3 重启，4 并网文件升级, 5查看并网文件")
    @NotNull
    @Range(min = 1, max = 5)
    private Integer operateType;

    @Schema(description = "文件id  operateType是4 时必填")
    private Integer fileId;

}
