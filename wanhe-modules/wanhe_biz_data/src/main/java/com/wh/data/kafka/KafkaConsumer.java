package com.wh.data.kafka;

import com.alibaba.fastjson2.JSON;
import com.wh.data.init.TopicServiceLocator;
import com.wh.data.protocol.TopicService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.context.annotation.Profile;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.List;

/**
 * <AUTHOR> 2024-09-14 15:11
 */
@Slf4j
@Component
@Profile({"prod", "dev","test", "local"})
public class KafkaConsumer {

    @Resource
    private TopicServiceLocator topicServiceLocator;

    /**
     * 消费kafka消息
     */
    @KafkaListener(topics = "${kafka.topic}", groupId = "${kafka.group.id}", containerFactory = "kafkaListenerFactory")
    public void topicListener(List<ConsumerRecord<String, String>> records, Acknowledgment ack) {
        for (ConsumerRecord<String, String> record : records) {
            log.info("kafka接收到消息: topic:{},off:{},data:{}", record.key(), record.offset(), record.value());
            try {
                handleData(record.key(), record.value());
            } catch (Exception e) {
                log.error("kafka消费异常", e);
            }
            ack.acknowledge();
        }
    }

    public void handleData(String topic, String data) throws Exception {
        if (topic == null || data == null) {
            log.info("主题或数据异常{},{}", topic, data);
            return;
        }
        //设备数据
        //判断上报消息体是什么指令 和 是什么方法分布调用不同方法
        int index = topic.indexOf('/');
        String name = topic.substring(index + 1, index + 4);
        TopicService service = topicServiceLocator.getService(name);
        if (service == null) {
            log.error("主题名称错误{}", topic);
            return;
        }
        Method method= topicServiceLocator.getMethod(name, topic);
        if (method == null) {
            log.error("该主题方法还不存在{}", topic);
            return;
        }
        Object msg = method.invoke(service, JSON.parseObject(data));
        if (msg != null) {
            log.error("{}处理失败,{}", topic, msg);
        }
    }

}
