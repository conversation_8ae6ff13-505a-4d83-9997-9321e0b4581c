package com.wh.data.pojo.vo;

import com.fasterxml.jackson.annotation.JsonRawValue;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/2/8
 */
@Data
@NoArgsConstructor
public class CommandResultVO {

    //响应时间
    private LocalDateTime time;

    //响应topic
    private String topic;

    //响应qos
    private Integer qos;

    //响应内容
    @JsonRawValue
    private String message;

    public CommandResultVO(LocalDateTime time, String message) {
        this.time = time;
        this.message = message;
        this.qos = 1;
    }

}
