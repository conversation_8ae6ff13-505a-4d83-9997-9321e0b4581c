package com.wh.data.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/2/8
 */
@Data
@TableName("wh_fault_param")
public class FaultParam {

    @Schema(description = "主键id")
    @EqualsAndHashCode.Include
    @TableId(type = IdType.AUTO)
    protected Integer id;

    @Schema(description = "版本ID")
    private Long versionId;

    @Schema(description = "参数ID")
    @NotNull
    private Integer paramId;

    @Schema(description = "参数分类")
    @NotBlank
    private String paramGroup;

    @Schema(description = "中文名称")
    @NotBlank
    private String paramName;

    @Schema(description = "英文名称")
    private String paramNameEn;

    @Schema(description = "值名称")
    private String paramVarName;

    @Schema(description = "宏名称")
    private String paramMacroName;

    @Schema(description = "描述")
    private String paramRemark;

    @Schema(description = "单位")
    private String paramUnit;

    @Schema(description = "读写属性")
    @NotBlank
    private String rwAttr;

    @Schema(description = "运行读")
    private String rwOfRunAttr;

    @Schema(description = "最大值")
    @NotBlank
    private String maxValue;

    @Schema(description = "最小值")
    @NotBlank
    private String minValue;

    @Schema(description = "默认值")
    private String defaultValue;

    @Schema(description = "数据类型")
    @NotBlank
    private String paramType;

    @Schema(description = "数组保存")
    private String saveAttr;

}
