package com.wh.data.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmuUpgradeDTO {

    @Schema(description = "设备sn")
    @NotBlank
    private String sn;

    @Schema(description = "选择的文件ID")
    @NotNull
    private Integer fileId;

}
