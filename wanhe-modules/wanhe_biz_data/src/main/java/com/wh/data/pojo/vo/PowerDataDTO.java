package com.wh.data.pojo.vo;

import com.ruoyi.common.core.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/12/16
 */
@Data
public class PowerDataDTO {

    @Excel(name = "电站名称", width = 20)
    @Schema(description = "电站名称")
    private String stationName;

    @Excel(name = "设备号", width = 22)
    @Schema(description = "设备号")
    private String sn;

    @Excel(name = "日期", width = 18)
    @Schema(description = "日期")
    private String date;

    @Excel(name = "功率")
    @Schema(description = "功率")
    private BigDecimal power;

}
