package com.wh.data.pojo.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class InvInfoCollectDTO extends DeviceDataDTO {

    private InvData invData;

    public InvInfoCollectDTO(String sn) {
        super(sn);
    }

    @Data
    @AllArgsConstructor
    public static class InvData {
        @JSONField(name = "Inv")
        private List<String> inv;
    }
}
