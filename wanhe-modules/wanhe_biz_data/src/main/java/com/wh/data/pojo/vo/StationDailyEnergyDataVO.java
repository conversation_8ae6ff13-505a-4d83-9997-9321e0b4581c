package com.wh.data.pojo.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 电站数据统计
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StationDailyEnergyDataVO {

    //Y轴
    private List<String> yaxis;

    //发电量
    private List<List<Object>> data;

    // 最大值电量
    private BigDecimal max;
}
