package com.wh.data.pojo.dto;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.domain.PageReqDTO;
import com.ruoyi.common.core.utils.TimeZoneUtil;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/12/19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PowerDataPageReqDTO extends PageReqDTO {

    //电站id
    @NotNull
    private Integer stationId;

    //日期
    @NotNull
    private String date;

    //查询类型 1日 3月 4年
    private Integer type;

    private String endDate;

    public Integer getType() {
        return type == null ? 1 : type;
    }

    public LocalDateTime getBeginTime() {
        return switch (getType()) {
            case 1 -> LocalDateTime.parse(date + " 00:00", TimeZoneUtil.yyyyMMddHHmm);
            case 3 -> LocalDateTime.parse(date + "-01 00:00", TimeZoneUtil.yyyyMMddHHmm);
            case 4 -> LocalDateTime.parse(date + "-01-01 00:00", TimeZoneUtil.yyyyMMddHHmm);
            default -> null;
        };
    }

    public LocalDateTime getEndTime() {
        return switch (getType()) {
            case 1 -> LocalDateTime.parse((StrUtil.isBlank(endDate) ? date : endDate) + " 00:00", TimeZoneUtil.yyyyMMddHHmm).plusDays(1);
            case 3 -> LocalDateTime.parse((StrUtil.isBlank(endDate) ? date : endDate) + "-01 00:00", TimeZoneUtil.yyyyMMddHHmm).plusMonths(1);
            case 4 -> {
                LocalDateTime endTime = LocalDateTime.parse(date + "-01-01 00:00", TimeZoneUtil.yyyyMMddHHmm).plusYears(1);
                LocalDateTime now = LocalDateTime.now();
                if (endTime.isAfter(now)) {
                    yield LocalDateTime.of(now.getYear(), now.getMonth(), 1, 0, 0).plusMonths(1);
                }
                yield endTime;
            }
            default -> null;
        };
    }
}
