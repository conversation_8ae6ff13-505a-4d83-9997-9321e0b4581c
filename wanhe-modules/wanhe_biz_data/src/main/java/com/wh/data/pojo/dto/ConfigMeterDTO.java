package com.wh.data.pojo.dto;

import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/12/6
 */
@Setter
@Getter
public class ConfigMeterDTO extends DeviceDataDTO {

    //并网测电表
    private Meter gridMeter = new Meter(0, BigDecimal.ZERO);
    //负载测电表
    private Meter loadMeter = new Meter();
    //光伏测电表
    private Meter photovoltaicMeter = new Meter();

    public ConfigMeterDTO(String sn) {
        super(sn);
    }

    @Data
    @NoArgsConstructor
    public static class Meter {

        // 是否启用电表，0：不启用，1：启用
        private int enable = 0;
        //电表sn号
        private String sn = "";
        // 是否启用防逆流功能
        private Integer enableReflux;
        // 防逆流功率
        private BigDecimal power;

        public void setSn(String sn) {
            this.enable = 1;
            this.sn = sn;
        }

        public Meter(Integer enableReflux, BigDecimal power) {
            this.enableReflux = enableReflux;
            this.power = power;
        }
    }

}
