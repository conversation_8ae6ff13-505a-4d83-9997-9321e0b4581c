package com.wh.data.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wh.data.domain.FaultNotice;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/30
 */
public interface FaultNoticeMapper extends BaseMapper<FaultNotice> {

    @Select("select count(*) from sys_notice n left join sys_user_notice un on n.id = un.notice_id and un.user_id = #{userId} where n.create_time > #{createTime} and n.is_enable = 1 and un.user_id is null union all select count(*) from wh_fault_notice where user_id = #{userId} and is_read = 0")
    List<Integer> unreadCount(LocalDateTime createTime, Long userId);

    /**
     * 阅读所有故障通知
     *
     * @param userId 用户id
     */
    @Update("update wh_fault_notice set is_read = 1 where user_id = #{userId} and is_read = 0")
    void readAllFaultNotice(Long userId);
}
