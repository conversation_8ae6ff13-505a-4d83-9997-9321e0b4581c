package com.wh.data.pojo.vo;

import com.fasterxml.jackson.annotation.JsonRawValue;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/2/8
 */
@Data
public class CommandVO {

    //请求时间
    private LocalDateTime time = LocalDateTime.now();

    //请求id
    private Integer id;

    //请求topic
    private String topic;

    //请求qos
    private Integer qos;

    //请求内容
    @JsonRawValue
    private String message;

    public CommandVO(Integer id, String topic, Integer qos, String message) {
        this.id = id;
        this.topic = topic;
        this.qos = qos;
        this.message = message;
    }
}
