package com.wh.data.controller;

import com.github.pagehelper.Page;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.wh.data.domain.FaultVersion;
import com.wh.data.pojo.dto.FaultVersionQueryDTO;
import com.wh.data.service.IFaultVersionService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2025/6/23
 */
@Tag(name = "故障版本模块")
@RestController
@AllArgsConstructor
@RequestMapping("/fault/version")
public class FaultVersionController extends BaseController {

    private final IFaultVersionService faultVersionService;

    /**
     * 分页查询
     *
     * @param dto 到
     * @return {@link TableDataInfo }<{@link FaultVersion }>
     */
    @GetMapping("/page")
    public TableDataInfo<FaultVersion> page(FaultVersionQueryDTO dto) {
        Page<FaultVersion> page = startPage(dto);
        faultVersionService.page(dto);
        return getDataTable(page);
    }

    /**
     * 按id获取详细信息
     *
     * @return {@link FaultVersion }
     */
    @GetMapping("/{id}")
    public FaultVersion getDetailById(@PathVariable("id") Long id) {
        return faultVersionService.getById(id);
    }

    /**
     * 添加故障版本
     *
     * @param faultVersion 故障版本
     * @return {@link R }<{@link Void }>
     */
    @PostMapping
    @Log(title = "故障版本", businessType = BusinessType.INSERT)
    public R<Void> addFaultVersion(@RequestBody FaultVersion faultVersion) {
        faultVersionService.addFaultVersion(faultVersion);
        return R.ok();
    }

    /**
     * 更新故障版本
     *
     * @param faultVersion 故障版本
     * @return {@link R }<{@link Void }>
     */
    @PutMapping
    @Log(title = "故障版本", businessType = BusinessType.UPDATE)
    public R<Void> updateFaultVersion(@RequestBody FaultVersion faultVersion) {
        faultVersionService.updateFaultVersion(faultVersion);
        return R.ok();
    }

    /**
     * 删除故障版本
     *
     * @param id id
     * @return {@link R }<{@link Void }>
     */
    @DeleteMapping("/{id}")
    @Log(title = "故障版本", businessType = BusinessType.DELETE)
    public R<Void> deleteFaultVersion(@PathVariable("id") Long id) {
        faultVersionService.removeById(id);
        return R.ok();
    }
}
