package com.wh.data.controller;

import cn.hutool.core.util.CharsetUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.wh.data.domain.FaultParam;
import com.wh.data.pojo.dto.FaultParamPageReqDTO;
import com.wh.data.service.FaultParamService;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;

/**
 * 故障参数
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@RestController
@RequestMapping("/fault/param")
public class FaultParamController extends BaseController {

    @Resource
    private FaultParamService faultParamService;

    /**
     * 新增
     */
    @Log(title = "故障参数", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@Validated @RequestBody FaultParam dto) {
        return R.ok(faultParamService.add(dto));
    }

    /**
     * 修改
     */
    @Log(title = "故障参数", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@Validated @RequestBody FaultParam dto) {
        return R.ok(faultParamService.update(dto));
    }

    /**
     * 导出
     */
    @Log(title = "故障参数", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response) throws IOException {
        response.setCharacterEncoding(CharsetUtil.UTF_8);
        response.setContentType("text/plain");
        response.setHeader("Content-Disposition", "attachment; filename=\"file.json\"");
        try (PrintWriter writer = response.getWriter()) {
            writer.write(faultParamService.exportData().toJSONString(JSONWriter.Feature.WriteNulls, JSONWriter.Feature.PrettyFormat));
            writer.flush();
        }
    }

    /**
     * 导入
     */
    @Log(title = "故障参数", businessType = BusinessType.IMPORT)
    @PostMapping("/import")
    public R<Boolean> importData(MultipartFile file, @RequestParam("versionId") Long versionId) {
        JSONObject jsonObject;
        try {
            jsonObject = new ObjectMapper().readValue(file.getInputStream(), JSONObject.class);
        } catch (Exception e) {
            throw new ServiceException("json数据解析失败");
        }
        return R.ok(faultParamService.importData(jsonObject, versionId));
    }

    /**
     * 列表
     */
    @GetMapping("/list")
    public R<List<FaultParam>> list(@Validated @NotEmpty @RequestParam @Schema(description = "参数编号") List<Integer> paramIds,
                                    @RequestParam(value = "versionNo", required = false) String versionNo) {
        return R.ok(faultParamService.listByParamIds(paramIds, versionNo));
    }

    /**
     * 分类下拉项
     */
    @GetMapping("/type/list")
    public R<List<String>> typeList() {
        return R.ok(faultParamService.typeList());
    }

    /**
     * 分页
     */
    @GetMapping(value = "/page")
    public TableDataInfo<FaultParam> page(@Validated FaultParamPageReqDTO dto) {
        Page<FaultParam> page = startPage(dto);
        faultParamService.page(dto);
        return getDataTable(page);
    }

    /**
     * 删除
     */
    @Log(title = "故障参数", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public R<Boolean> remove(@PathVariable Integer id) {
        return R.ok(faultParamService.removeById(id));
    }

}
