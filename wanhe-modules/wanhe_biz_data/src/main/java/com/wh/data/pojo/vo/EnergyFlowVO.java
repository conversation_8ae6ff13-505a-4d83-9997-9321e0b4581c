package com.wh.data.pojo.vo;

import com.ruoyi.common.core.domain.DataUnit;
import com.ruoyi.common.core.utils.Tools;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/11/1
 */
@Data
public class EnergyFlowVO {

    @Schema(description = "橙色 并网点电表功率")
    private BigDecimal meterPower;
    private String meterPowerUnit;

    @Schema(description = "蓝色 电站下所有微逆ac功率的和")
    private BigDecimal acTempPower;
    private String acTempPowerUnit;

    @Schema(description = "绿色 电池")
    private BigDecimal green = BigDecimal.ZERO;
    private String greenUnit = "W";

    @Schema(description = "紫色 并网点功率+所有微逆功率")
    private BigDecimal power;
    private String powerUnit;

    //电表功率W 设备功率W
    public EnergyFlowVO(BigDecimal meterPower, BigDecimal acTempPower) {
        DataUnit mp = Tools.covertPowerUnit(meterPower);
        this.meterPower = mp.getData();
        this.meterPowerUnit = mp.getUnit();
        DataUnit acp = Tools.covertPowerUnit(acTempPower);
        this.acTempPower = acp.getData();
        this.acTempPowerUnit = acp.getUnit();
        DataUnit p = Tools.covertPowerUnit(meterPower.add(acTempPower));
        this.power = p.getData();
        this.powerUnit = p.getUnit();
    }

}
