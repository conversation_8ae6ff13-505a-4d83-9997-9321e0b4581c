package com.wh.data.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wh.data.domain.DeviceAlarm;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
* <AUTHOR>
* @date 2024-10-26
*/
public interface DeviceAlarmMapper extends BaseMapper<DeviceAlarm> {

    void batchSave(List<DeviceAlarm> deviceAlarmList);

    @Select("select count(*) from wh_device_alarm where device_id = #{deviceId} and is_resolved = 0")
    int getAlarmCount(Integer deviceId);

    @Update("update wh_device_alarm set is_resolved = 1, resolved_at = #{resolvedAt}, update_time = now() where device_id = #{deviceId} and is_resolved = 0 and is_delete = 0")
    void updateAlarmAll(String resolvedAt, Integer deviceId);

    @Update("update wh_device_alarm set is_resolved = 1, resolved_at = #{resolvedAt}, update_time = now() where id in(${ids}) and is_resolved = 0")
    void updateAlarms(String resolvedAt, String ids);

    @Select("select id, alarm_code, alarm_data from wh_device_alarm where device_id = #{deviceId} and is_resolved = 0 and is_delete = 0")
    List<DeviceAlarm> getAlarm(Integer deviceId);

}
