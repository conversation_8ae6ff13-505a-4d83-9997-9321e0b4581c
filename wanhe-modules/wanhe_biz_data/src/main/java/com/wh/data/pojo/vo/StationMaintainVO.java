package com.wh.data.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/20
 */
@Data
public class StationMaintainVO {

    @Schema(description = "电站id")
    private Integer id;

    @Schema(description = "电站名称")
    private String stationName;

    @Schema(description = "租户名称|机构名称")
    private String tenantName;

    @Schema(description = "电站类型（0:屋顶光伏 1:阳台光伏 2:商业光伏）")
    private String stationType;

    @Schema(description = "装机容量kW")
    private BigDecimal installedCapacity;

    @Schema(description = "业主名称")
    private String proprietor;

    @Schema(description = "安装时间")
    private LocalDateTime installTime;

    @Schema(description = "国家ID")
    private String country;

    @Schema(description = "省份ID")
    private String province;

    @Schema(description = "详细信息")
    private String stationDetails;

    @Schema(description = "最后升级时间")
    private LocalDateTime lastUpgradeTime;

    @Schema(description = "当前功率折线图")
    private List<List<Object>> powerList = new ArrayList<>();

    @Schema(description = "当前功率折线图单位")
    private String powerListUnit = "kW";

    @Schema(description = "当前功率")
    private BigDecimal power;

    @Schema(description = "比例")
    private BigDecimal rate;

}
