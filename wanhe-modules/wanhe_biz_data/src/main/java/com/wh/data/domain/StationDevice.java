package com.wh.data.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonRawValue;
import com.ruoyi.common.core.utils.TimeZoneUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-10-28
 */
@Data
@Accessors(chain = true)
@TableName("wh_station_device")
@Schema(name = "StationDevice", description = "设备表")
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = false)
public class StationDevice {

    @Schema(description = "主键id")
    @EqualsAndHashCode.Include
    @TableId(type = IdType.AUTO)
    protected Integer id;

    @Schema(description = "租户id")
    protected Long tenantId;

    @Schema(description = "逻辑删除（0正常 非0删除) 唯一索引使用", hidden = true)
    @TableField(select = false)
    @TableLogic
    @JsonIgnore
    protected Integer deleted;

    @Schema(description = "电站id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Integer powerStationId;

    @Schema(description = "序列号")
    private String number;

    @Schema(description = "EMU设备id")
    private Integer pid;

    @Schema(description = "emu设备序列号")
    private String emuNumber;

    @Schema(description = "设备类型 （0：EMU 1:微逆 2：电表）有pid是emu微逆 没有就是wifi微逆")
    private Integer deviceType;

    @Schema(description = "设备状态 0离线 1在线 2告警 3故障 4停机 5自检 6预启动")
    private Integer status;

    @Schema(description = "故障码")
    @JsonRawValue
    private String faultCode;

    @Schema(description = "设备型号")
    private String deviceModel;

    @Schema(description = "硬件版本")
    private String hardwareVersion;

    @Schema(description = "软件版本")
    private String softwareVersion;

    @Schema(description = "硬件wifi版本")
    private String hardwareWifiVersion;

    @Schema(description = "软件wifi版本")
    private String softwareWifiVersion;

    @Schema(description = "并网文件ID")
    private Integer gridFileId;

    @Schema(description = "电表位置 0负载侧电表 1光伏测电表 2并网点电表")
    private Integer powerGridSeat;

    @Schema(description = "连接微逆数量")
    private Integer wnNum;

    @Schema(description = "设备替换次数")
    private Integer replaceNum;

    @Schema(description = "通道数量")
    private Integer connectNum;

    @Schema(description = "质保期（月）")
    private Integer guaranteePeriod;

    @Schema(description = "激活时间")
    private LocalDateTime activateTime;

    @Schema(description = "最后报告时间")
    private LocalDateTime lastReportTime;

    @Schema(description = "最后升级时间")
    private LocalDateTime lastUpgradeTime;

    @Schema(description = "联网模式 0:WIFI 1:有线网 2:4G")
    private Integer commType;

    @Schema(description = "通信状态  1-代表通信正常   0-代表通信断开    2-代表待升级(可能由于升级过程中掉电或升级失败，微逆处于Boot中，未起来需要再次升级)")
    private Integer commStatus;

    @Schema(description = "组网方式 0:手动组网 1:自动组网")
    private Integer networkType;

    @Schema(description = "创建人")
    protected String createBy;

    @Schema(description = "创建时间")
    protected LocalDateTime createTime;

    @Schema(description = "更新人")
    protected String updateBy;

    @Schema(description = "更新时间")
    protected LocalDateTime updateTime;

    @Schema(description = "微逆集合")
    @TableField(exist = false)
    private List<StationDevice> wnList;

    @Schema(description = "电站名称")
    @TableField(exist = false)
    private String stationName;

    @Schema(description = "并网文件版本")
    @TableField(exist = false)
    private String gridFileVersions;

    @Schema(description = "设备类型名称")
    @TableField(exist = false)
    private String deviceTypeName;

    @Schema(description = "租户名称|机构名称")
    @TableField(exist = false)
    protected String tenantName;

    public String getFinishPeriod() {
        if (guaranteePeriod == null) {
            return null;
        }
        //生产时间
        LocalDate producedDate;
        try {
            producedDate = LocalDate.parse(number.substring(9, 15), TimeZoneUtil.yyMMdd);
        } catch (Exception e) {
            producedDate = null;
        }
        if (producedDate != null && activateTime != null) {
            //1.激活时间-生产时间>12个月（具体时间再讨论），按生产时间计，否则按激活时间计
            int months = Period.between(producedDate, activateTime.toLocalDate()).getMonths();
            if (months <= 12) {
                return TimeZoneUtil.yyyyMMdd.format(activateTime.toLocalDate().plusMonths(guaranteePeriod));
            }
        }
        if (createTime != null) {
            return TimeZoneUtil.yyyyMMdd.format(createTime.toLocalDate().plusMonths(guaranteePeriod));
        }
        return null;
    }

}
