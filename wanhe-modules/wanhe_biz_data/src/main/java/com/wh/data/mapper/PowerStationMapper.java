package com.wh.data.mapper;

import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.github.yulichang.base.MPJBaseMapper;
import com.ruoyi.common.core.domain.IntStr;
import com.ruoyi.system.api.domain.SysUser;
import com.wh.data.domain.PowerStation;
import com.wh.data.domain.PowerStationPrice;
import com.wh.data.pojo.station.InfoVO;
import com.wh.data.pojo.vo.StationMaintainVO;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

public interface PowerStationMapper extends MPJBaseMapper<PowerStation> {

    @Select("select tenant_id from wh_power_station where id = #{id}")
    Long getTenantId(Integer id);

    @Select("SELECT months,begin_time,end_time,price FROM wh_power_station_price where power_station_id = #{id}")
    @Result(column = "months", property = "months", typeHandler = Fastjson2TypeHandler.class)
    List<PowerStationPrice> getPrice(Integer id);

    @Select("select s.id, s.station_status from wh_power_station s inner join wh_station_device d on s.id = d.power_station_id and d.number = #{sn} and d.deleted = 0 and s.deleted = 0")
    PowerStation getBySn(String sn);

    @Update("update wh_power_station set station_status = #{status}, last_report_time = #{updateTime} where id = #{id}")
    void updateStatus(Integer id, int status, String updateTime);

    @Select("select id as k,station_name as v from wh_power_station where id in(${ids})")
    List<IntStr> getIdName(String ids);

    @Select("""
    select u.user_id,u.nick_name,u.email,u.area_code,u.phonenumber,s.station_name as remark
    from sys_user u inner join sys_user_station us on us.user_id = u.user_id and us.power_station_id = #{id} and u.del_flag = '0'
    inner join wh_power_station s on s.id = us.power_station_id
    """)
    List<SysUser> getStationUser(Integer id);

    @Select("select fault_notification_method from wh_power_station where id = #{id}")
    String getFaultNotice(Integer id);

    @Select("""
    select s.id,s.station_name,s.station_type,s.installed_capacity,s.install_time,s.station_details,
    (select group_concat(nick_name) from sys_user where del_flag = '0' and user_id in(select user_id from sys_user_station where power_station_id = s.id)) as proprietor,
    (select name from sys_district where id = s.country_id) as country,
    (select name from sys_district where id = s.province_id) as province,
    (select last_upgrade_time from wh_station_device where deleted = 0 and power_station_id = s.id order by last_upgrade_time desc limit 1) as lastUpgradeTime,
    (select dept_name from sys_dept where dept_id = s.tenant_id) as tenantName,
    (select kw from wh_power_station_extend where id = s.id) as power
    from wh_power_station s where s.id = #{id} and s.deleted = 0
    """)
    StationMaintainVO getStationMaintainVO(Integer id);

    @Select("select station_name, station_type from wh_power_station where id = #{id}")
    PowerStation get(Integer id);

    @Select("select id as k, station_name as v from wh_power_station where deleted = 0${dataScopeSql} limit ${size}")
    List<IntStr> getIdNameListBySql(String dataScopeSql, int size);

    @Select("select create_time from wh_power_station where id = #{id}")
    LocalDateTime getCreateTime(Integer id);

    @Select("select station_id from sys_api_token_station where token_id = #{tokenId}")
    List<Integer> getStationIds(Long tokenId);

    @Select("select id as stationId, station_type from wh_power_station where id = (select station_id from sys_api_token_station where token_id = #{tokenId} and station_id = #{id})")
    InfoVO getInfo(Long tokenId, Integer id);

    @Select("SELECT p.id,p.station_name,p.time_zone FROM `wh_power_station` p LEFT JOIN wh_station_device s on s.power_station_id = p.id WHERE s.number = #{emuNumber} AND s.is_delete=0 and p.is_delete=0 LIMIT 1")
    PowerStation getStationByEmu(String emuNumber);

}
