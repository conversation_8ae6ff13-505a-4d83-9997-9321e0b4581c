package com.wh.data.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.ruoyi.common.core.domain.IntStr;
import com.wh.data.domain.StationDevice;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-10-28
 */
public interface StationDeviceMapper extends MPJBaseMapper<StationDevice> {

    @Select("select distinct `status` from wh_station_device where power_station_id = #{stationId} and deleted = 0 and device_type in(0,1)")
    List<Integer> getStatus(Integer stationId);

    @Select("select id as k, number as v from wh_station_device where id in(${ids})")
    List<IntStr> getIdNumber(String ids);

    @Select("select id, device_model, device_type from wh_station_device where power_station_id = #{stationId} and device_type in(1,2)")
    List<StationDevice> getDeviceModel(Integer stationId);

    @Select("select id,emu_number,number,status,device_type,grid_file_id,power_station_id,tenant_id,create_time,software_version from wh_station_device where number = #{sn} and deleted = 0")
    StationDevice getBySn(String sn);

    @Select("select id,number,power_grid_seat from wh_station_device where power_station_id = #{stationId} and device_type = #{deviceType} and deleted = 0")
    List<StationDevice> getByStationIdDeviceType(Integer stationId, int deviceType);

    @Select("select device_model from wh_station_device where number = #{number} and deleted = 0")
    String getModel(String number);

}
