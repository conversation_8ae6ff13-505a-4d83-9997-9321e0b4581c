package com.wh.data.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class QueryEmuEnergyDataDTO {

    @Schema(description = "电站id")
    @NotNull
    private Integer stationId;

    @Schema(description = "emu编号")
    @NotEmpty
    private List<Integer> ids;

    @Schema(description = "数据类型 1发电量 2发电功率")
    @NotNull
    private Integer dataType;

    @Schema(description = "查询类型 1日 3月 4年 5总")
    @NotNull
    private Integer type;

    @Schema(description = "查询时间")
    private String date;

}
