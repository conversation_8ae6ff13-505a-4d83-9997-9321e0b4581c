package com.wh.data.pojo.vo;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.enums.ClientTypeEnum;
import com.ruoyi.common.core.utils.NumUtil;
import com.ruoyi.common.core.utils.TimeZoneUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/1
 */
@Setter
@Getter
@NoArgsConstructor
public class PowerEarningsVO {

    @Schema(description = "更新时间")
    private String updateTime;

    @Schema(description = "日收益")
    private BigDecimal dayProfit = BigDecimal.ZERO;
    private String dayProfitUnit = StrUtil.EMPTY;

    @Schema(description = "月收益")
    private BigDecimal monthProfit = BigDecimal.ZERO;
    private String monthProfitUnit = StrUtil.EMPTY;

    @Schema(description = "年收益")
    private BigDecimal yearProfit = BigDecimal.ZERO;
    private String yearProfitUnit = StrUtil.EMPTY;

    @Schema(description = "使用期收益")
    private BigDecimal allProfit = BigDecimal.ZERO;
    private String allProfitUnit = StrUtil.EMPTY;

    public PowerEarningsVO(ZonedDateTime updateTime) {
        this.updateTime = updateTime.toLocalDateTime().format(TimeZoneUtil.yyyyMMddHHmmss);
    }

    public PowerEarningsVO(List<BigDecimal> list, BigDecimal powerPrice, ZonedDateTime updateTime, ClientTypeEnum clientType) {
        this.updateTime = updateTime.toLocalDateTime().format(TimeZoneUtil.yyyyMMddHHmmss);
        dayProfit = list.get(0).multiply(powerPrice);
        monthProfit = list.get(1).multiply(powerPrice);
        yearProfit = list.get(2).multiply(powerPrice);
        allProfit = list.get(3).multiply(powerPrice);
        if (clientType == ClientTypeEnum.PC) {
            if (dayProfit.compareTo(NumUtil.THOUSAND) > 0) {
                dayProfit = dayProfit.divide(NumUtil.THOUSAND, 2, RoundingMode.HALF_UP);
                dayProfitUnit = "K";
            }
            if (monthProfit.compareTo(NumUtil.THOUSAND) > 0) {
                monthProfit = monthProfit.divide(NumUtil.THOUSAND, 2, RoundingMode.HALF_UP);
                monthProfitUnit = "K";
            }
            if (yearProfit.compareTo(NumUtil.THOUSAND) > 0) {
                yearProfit = yearProfit.divide(NumUtil.THOUSAND, 2, RoundingMode.HALF_UP);
                yearProfitUnit = "K";
            }
            if (allProfit.compareTo(NumUtil.THOUSAND) > 0) {
                allProfit = allProfit.divide(NumUtil.THOUSAND, 2, RoundingMode.HALF_UP);
                allProfitUnit = "K";
            }
        }
    }
}
