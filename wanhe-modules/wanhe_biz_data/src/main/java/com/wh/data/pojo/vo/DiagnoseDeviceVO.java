package com.wh.data.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/23
 */
@Data
public class DiagnoseDeviceVO {

    @Schema(description = "系统状态 0离线 1在线 2告警 3故障 4停机 5自检")
    private Integer sysStatus;

    @Schema(description = "序列号")
    private String snNumber;

    @Schema(description = "设备类型 （0：EMU 1:微逆 2：电表）")
    private Integer deviceType;

    @Schema(description = "设备型号")
    private String deviceModel;

    @Schema(description = "告警名称")
    private List<String> alarmName = new ArrayList<>();

    @Schema(description = "处理方法")
    private List<String> handlingMethod = new ArrayList<>();

}
