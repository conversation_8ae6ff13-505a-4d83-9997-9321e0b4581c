package com.wh.data.influxdb.dto;

import cn.hutool.core.collection.CollUtil;
import com.ruoyi.common.core.domain.PageReqDTO;
import com.ruoyi.common.core.exception.ServiceException;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/7
 */
@Data
@Accessors(chain = true, fluent = true)
public class Param {

    //表名
    private String measurement;
    //influxdb
    private String iql;
    //查询站点id
    private Integer stationId;
    //开始时间
    private ZonedDateTime begin;
    //结束时间
    private ZonedDateTime end;
    //设备id
    private Integer deviceId;
    //设备sn
    private String deviceSn;
    //返回数据格式化
    private DateTimeFormatter format;
    //数据是否保留2位小数四舍五入
    private boolean dataFormat = true;
    //数据是空的不要
    private boolean dataNull = false;
    //分页
    private Integer pageNum;
    private Integer pageSize;
    //时区
    private ZoneId zoneId;

    public Param begin(ZonedDateTime begin) {
        this.begin = begin;
        return this;
    }

    public Param end(ZonedDateTime end) {
        this.end = end;
        return this;
    }

    public Param dataNotFormat() {
        this.dataFormat = false;
        return this;
    }

    public Param dataNotNull() {
        this.dataNull = true;
        return this;
    }

    private Param() {
    }

    private Param(String measurement) {
        this.measurement = measurement;
    }

    public static Param of() {
        return new Param();
    }

    public static Param of(ZoneId zoneId) {
        return new Param().zoneId(zoneId);
    }

    public static Param of(String measurement, ZoneId zoneId) {
        return new Param(measurement).zoneId(zoneId);
    }

    public static Param of(ZoneId zoneId, Integer stationId) {
        return new Param().zoneId(zoneId).stationId(stationId);
    }

    public static Param of(String measurement, String iql) {
        return new Param(measurement).iql(iql);
    }

    public static Param of(String measurement, ZoneId zoneId, Integer stationId) {
        return new Param(measurement).zoneId(zoneId).stationId(stationId);
    }

    public static Param of(String measurement, ZoneId zoneId, Integer stationId, String iql) {
        return new Param(measurement).zoneId(zoneId).stationId(stationId).iql(iql);
    }

    public static Param of(String measurement, ZoneId zoneId, Integer stationId, ZonedDateTime begin, ZonedDateTime end, String iql) {
        return new Param(measurement).zoneId(zoneId).stationId(stationId).begin(begin).end(end).iql(iql);
    }

    public static Param of(String measurement, ZoneId zoneId, Integer stationId, ZonedDateTime begin, ZonedDateTime end) {
        return new Param(measurement).zoneId(zoneId).stationId(stationId).begin(begin).end(end);
    }

    public static Param of(String measurement, ZoneId zoneId, Integer stationId, ZonedDateTime begin, ZonedDateTime end, String iql, DateTimeFormatter format) {
        return new Param(measurement).zoneId(zoneId).stationId(stationId).begin(begin).end(end).iql(iql).format(format);
    }

    public Param iql(String iql) {
        this.iql = iql;
        return this;
    }

    public Param iql(List<String> list) {
        if (CollUtil.isEmpty(list)) {
            throw new ServiceException("参数错误");
        }
        StringBuilder sb = new StringBuilder();
        list.forEach(iql -> {
            if (iql != null) {
                sb.append(";").append(iql);
            }
        });
        this.iql = sb.substring(1);
        return this;
    }

    public Param page(PageReqDTO dto) {
        pageSize = dto.getPageSize();
        pageNum = dto.getPageNum();
        return this;
    }

    public String getBeginSecond() {
        return begin.toEpochSecond() + "s";
    }

    public String getEndSecond() {
        return end.toEpochSecond() + "s";
    }

    public String getSecond(ZonedDateTime time) {
        return time.toEpochSecond() + "s";
    }

}
