package com.wh.data.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpgradeInvFileDTO {

    @Schema(description = "微逆设备号")
    @NotBlank
    private String sn;

    @Schema(description = "文件id")
    @NotNull
    private Integer fileId;

}
