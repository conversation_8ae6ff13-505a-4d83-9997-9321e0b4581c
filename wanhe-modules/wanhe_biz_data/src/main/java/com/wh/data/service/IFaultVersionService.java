package com.wh.data.service;

import com.ruoyi.common.security.service.IBaseService;
import com.wh.data.domain.FaultVersion;
import com.wh.data.pojo.dto.FaultVersionQueryDTO;

/**
 * <AUTHOR>
 * @date 2025/6/24
 */
public interface IFaultVersionService extends IBaseService<FaultVersion> {

    /**
     * 页
     *
     * @param dto 到
     */
    void page(FaultVersionQueryDTO dto);

    /**
     * 添加故障版本
     *
     * @param faultVersion 故障版本
     */
    void addFaultVersion(FaultVersion faultVersion);

    /**
     * 更新故障版本
     *
     * @param faultVersion 故障版本
     */
    void updateFaultVersion(FaultVersion faultVersion);
}
