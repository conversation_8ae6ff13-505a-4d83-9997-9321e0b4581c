package com.wh.data.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/11/1
 */
@Data
public class ElectricityVO {

    @Schema(description = "电网依赖度")
    private BigDecimal degree;



    @Schema(description = "总发电量 大圈 所有微逆发电量")
    private BigDecimal fdAll = BigDecimal.ZERO;

    //总发电量-馈入电网
    @Schema(description = "发电量 负载消耗 并网点正向电量+微逆发电量-并网点逆向电量")
    private BigDecimal fdConsume = BigDecimal.ZERO;
    private BigDecimal fdConsumeRate = BigDecimal.ZERO;

    @Schema(description = "发电量 馈入电网 并网电表逆向电量")
    private BigDecimal faFeed = BigDecimal.ZERO;
    private BigDecimal faFeedRate = BigDecimal.ZERO;

    @Schema(description = "发电量 电池充电")
    private BigDecimal faCharge = BigDecimal.ZERO;
    private BigDecimal faChargeRate = BigDecimal.ZERO;



    @Schema(description = "总用电量 大圈 并网电表正向电量-并网电表逆向电量+微逆发电量")
    private BigDecimal ydAll = BigDecimal.ZERO;

    //总用电量-电网供电
    @Schema(description = "用电量 光伏供电 所有微逆发电")
    private BigDecimal ydWn = BigDecimal.ZERO;
    private BigDecimal ydWnRate = BigDecimal.ZERO;

    @Schema(description = "用电量 电网供电 并网电表正向电量")
    private BigDecimal ydMeter = BigDecimal.ZERO;
    private BigDecimal ydMeterRate = BigDecimal.ZERO;

    @Schema(description = "用电量 电池供电")
    private BigDecimal ydCharge = BigDecimal.ZERO;
    private BigDecimal ydChargeRate = BigDecimal.ZERO;

}
