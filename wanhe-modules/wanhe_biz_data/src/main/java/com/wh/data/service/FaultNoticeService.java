package com.wh.data.service;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.security.service.impl.BaseServiceImpl;
import com.ruoyi.system.api.domain.SysUser;
import com.wh.data.domain.FaultNotice;
import com.wh.data.mapper.FaultNoticeMapper;
import com.wh.data.pojo.dto.FaultNoticePageReqDTO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/30
 */
@Service
public class FaultNoticeService extends BaseServiceImpl<FaultNoticeMapper, FaultNotice> {

    public void page(FaultNoticePageReqDTO dto) {
        lambdaQuery()
                .eq(FaultNotice::getUserId, getUserId())
                .like(StrUtil.isNotBlank(dto.getKeyword()), FaultNotice::getContent, dto.getKeyword())
                .between(dto.getBeginTime() != null && dto.getEndTime() != null, FaultNotice::getCreateTime, dto.getBeginTime(), dto.getEndTime())
                .eq(dto.getIsRead() != null, FaultNotice::getIsRead, dto.getIsRead())
                .orderByDesc(FaultNotice::getId)
                .list();
    }

    public FaultNotice get(Integer id) {
        FaultNotice notice = getByIdThrow(id);
        lambdaUpdate().eq(FaultNotice::getId, id).set(FaultNotice::getIsRead, Boolean.TRUE).update();
        return notice;
    }

    /**
     * 一键已读所有故障通知
     */
    public void readAllFaultNotice() {
        Long userId = getUserId();
        baseMapper.readAllFaultNotice(userId);
    }

    public List<Integer> unreadCount() {
        SysUser user = getSysUser();
        return baseMapper.unreadCount(user.getCreateTime(), user.getUserId());
    }

}
