package com.wh.data.service;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.utils.TimeZoneUtil;
import com.ruoyi.system.api.domain.PowerStationExtend;
import com.wh.data.domain.PowerStation;
import com.wh.data.influxdb.dto.Param;
import com.wh.data.influxdb.entity.DeviceData;
import com.wh.data.influxdb.mapper.InfluxMapper;
import com.wh.data.mapper.PowerStationExtendMapper;
import com.wh.data.pojo.vo.StationAttrDataVO;
import jakarta.annotation.Resource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/12/13
 */
@Service
public class PowerStationExtendService extends DataScopeServiceImpl<PowerStationExtendMapper, PowerStationExtend> {

    @Resource
    private InfluxMapper influxMapper;

    /**
     * 拼接查询条件 name = '1' or name = '2'
     */
    public String orTag(String name, String[] ids) {
        StringBuilder sb = new StringBuilder();
        for (String id : ids) {
            sb.append(" or ").append(name).append(" = '").append(id).append("'");
        }
        return sb.substring(4);
    }

    /**
     * 15分钟统计一次当日发电量和实时功率
     */
    public void syncData(Map<String, String> tzMap) {
        ZoneId zoneId = TimeZoneUtil.getZoneId(tzMap.get("tz"));
        ZonedDateTime begin = TimeZoneUtil.getZonedEarlyTime(zoneId);
        ZonedDateTime end = begin.plusDays(1);
        if (begin.getHour() == 0 && begin.getMinute() < 30) {
            baseMapper.resetDayKwh(tzMap.get("ids"));
        }
        String[] idsStr = tzMap.get("ids").split(",");
        String idStr = orTag("powerStationId", idsStr);
        String iql1 = "select sum(data) as data from (select last(dailyEnergy) as data from {table} where (%s) and deviceType = 1 and time >= {begin} and time < {end} group by powerStationId,deviceId) group by powerStationId";
        String iql2 = "select sum(data)/1000 as data from (select last(acTempPower) as data from {table} where (%s) and deviceType = 1 and time >= now() - 15m and time <= now() group by powerStationId, deviceId) where time >= now() - 15m and time <= now() group by powerStationId";
        Param param = Param.of(DeviceData.MN, zoneId).iql(List.of(iql1.formatted(idStr), iql2.formatted(idStr))).begin(begin).end(end);
        List<List<JSONObject>> list = influxMapper.queryMultiList(param);
        List<JSONObject> energyList = list.get(0);
        List<JSONObject> powerList = list.get(1);
        Map<Integer, PowerStationExtend> map = new HashMap<>(Math.max(energyList.size(), powerList.size()));
        for (JSONObject json : energyList) {
            Integer powerStationId = json.getInteger("powerStationId");
            BigDecimal data = json.getBigDecimal("data");
            map.computeIfAbsent(powerStationId, k -> new PowerStationExtend(data, BigDecimal.ZERO));
        }
        for (JSONObject json : powerList) {
            Integer powerStationId = json.getInteger("powerStationId");
            BigDecimal data = json.getBigDecimal("data");
            map.computeIfAbsent(powerStationId, k -> new PowerStationExtend(data)).setKw(data);
        }
        String time = TimeZoneUtil.yyyyMMddHHmmss.format(LocalDateTime.now());
        for (Map.Entry<Integer, PowerStationExtend> m : map.entrySet()) {
            baseMapper.updateData(m.getKey(), m.getValue().getKw(), m.getValue().getDayKwh(), time);
        }
    }

    /**
     * 大于一天小于15分钟没有上报数据电站和设备进行修复
     */
    public void syncData1() {
        LocalDateTime now = LocalDateTime.now();
        String time = TimeZoneUtil.yyyyMMddHHmmss.format(now);
        String beginTime = TimeZoneUtil.yyyyMMddHHmmss.format(now.plusDays(-1));
        String endTime = TimeZoneUtil.yyyyMMddHHmmss.format(now.plusMinutes(-15));
        //没有更新到的数据功率重置
        baseMapper.resetPower(beginTime, time);
        //电站离线
        baseMapper.stationOffLine(beginTime, endTime);
        baseMapper.stationOffLine2();
        //设备离线
        baseMapper.deviceOffLine(beginTime, endTime);
        //电站上线
        baseMapper.stationOnLine(endTime);
    }

    /**
     * 更新电站所有发电量
     */
    @Async
    public void syncData2(Map<String, String> tzMap) {
        ZoneId zoneId = TimeZoneUtil.getZoneId(tzMap.get("tz"));
        String[] idsStr = tzMap.get("ids").split(",");
        String idStr = orTag("powerStationId", idsStr);
        String iql = "select sum(data) as data from (select last(dailyEnergy) as data from {table} where (%s) and deviceType = 1 group by time(1d),powerStationId,deviceId {tz}) group by powerStationId";
        Param param = Param.of(DeviceData.MN, zoneId).iql(iql.formatted(idStr));
        Map<JSONObject, List<JSONObject>> list = influxMapper.queryGroupList(param);
        for (Map.Entry<JSONObject, List<JSONObject>> map : list.entrySet()) {
            BigDecimal dailyEnergy = map.getValue().get(0).getBigDecimal("data");
            if (dailyEnergy != null && dailyEnergy.compareTo(BigDecimal.ZERO) > 0) {
                PowerStationExtend extend = new PowerStationExtend();
                extend.setId(map.getKey().getInteger("powerStationId"));
                extend.setAllKwh(dailyEnergy);
                updateById(extend);
            }
        }
    }

    public List<StationAttrDataVO> queryPowerStationTop5() {
        return lambdaQueryAll(PowerStationExtend::getId, null)
                .selectAs(PowerStationExtend::getId, StationAttrDataVO::getPowerStationId)
                .selectAs(PowerStation::getStationName, StationAttrDataVO::getPowerStationName)
                .selectAs(PowerStationExtend::getAllKwh, StationAttrDataVO::getDailyEnergy)
                .orderByDesc(PowerStationExtend::getAllKwh)
                .last("limit 10")
                .list(StationAttrDataVO.class);
    }

}
