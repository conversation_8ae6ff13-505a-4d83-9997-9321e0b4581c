package com.wh.data.pojo.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/10
 */
@Data
public class FaultWaveSettingDTO {

    //微逆序列号
    @NotBlank
    private String sn;

    //数据
    @Valid
    @NotEmpty
    private List<GridParamDTO> data;

}
