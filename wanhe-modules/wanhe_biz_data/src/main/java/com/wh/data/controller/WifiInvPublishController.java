package com.wh.data.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.wh.data.biz.WifiInvPublishBiz;
import com.wh.data.pojo.dto.*;
import com.wh.data.service.StationDeviceService;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * wifi微逆设备相关操作
 *
 * <AUTHOR>
 * @date 2024/11/8 0008
 */
@RestController
@RequestMapping("/wifi/inv/publish")
public class WifiInvPublishController {

    @Resource
    private WifiInvPublishBiz wifiInvPublishBiz;
    @Resource
    private StationDeviceService stationDeviceService;

    /**
     * wifi微逆批量操作 开机，关机，重启，并网文件升级，WIFI版固件升级，固件升级
     */
    @Log(title = "WIFI微逆批量操作", businessType = BusinessType.COMMAND)
    @PostMapping(value = "/batch/operate")
    public R<List<Integer>> batchOperate(@Validated @RequestBody InvBatchOperateDTO dto) {
        if (dto.getOperateType() == 4) {
            stationDeviceService.noAuthExists(dto.getSns());
        } else {
            stationDeviceService.exists(dto.getSns());
        }
        return R.ok(wifiInvPublishBiz.batchOperate(dto));
    }

    /**
     * wifi微逆 1开机，2关机，3重启，4并网文件升级, 5查看并网文件
     */
    @Log(title = "WIFI微逆操作", businessType = BusinessType.COMMAND)
    @PostMapping(value = "/invOperate")
    public R<Integer> invOperate(@Validated @RequestBody InvOperateDTO dto) {
        if (dto.getOperateType() == 4) {
            stationDeviceService.noAuthExists(dto.getSn());
        } else {
            stationDeviceService.exists(dto.getSn());
        }
        return R.ok(wifiInvPublishBiz.invOperate(dto));
    }

    /**
     * WIFI版固件升级
     */
    @Log(title = "微逆WIFI固件升级", businessType = BusinessType.COMMAND)
    @PostMapping(value = "/invUpgradeWifiFile")
    public R<Integer> invUpgradeWifiFile(@Validated @RequestBody UpgradeInvFileDTO dto) {
        stationDeviceService.exists(dto.getSn());
        return R.ok(wifiInvPublishBiz.invUpgradeFile(dto, 1));
    }

    /**
     * 固件升级
     */
    @Log(title = "WIFI微逆固件升级", businessType = BusinessType.COMMAND)
    @PostMapping(value = "/invUpgradeFile")
    public R<Integer> invUpgradeFile(@Validated @RequestBody UpgradeInvFileDTO dto) {
        stationDeviceService.exists(dto.getSn());
        return R.ok(wifiInvPublishBiz.invUpgradeFile(dto, 0));
    }

    /**
     * 时区下发
     */
    @Log(title = "WIFI微逆时区下发", businessType = BusinessType.COMMAND)
    @PostMapping(value = "/setInvTimeZone")
    public R<Integer> setInvTimeZone(@Validated @RequestBody SetTimeZoneDTO dto) {
        stationDeviceService.exists(dto.getSn());
        return R.ok(wifiInvPublishBiz.setInvTimeZone(dto));
    }

    /**
     * 收集微逆版本信息
     *
     * @param dto
     * @return {@link R }<{@link Integer }>
     */
    @Log(title = "收集微逆版本信息", businessType = BusinessType.COMMAND)
    @PostMapping(value = "/readInvAllParam")
    public R<Integer> readInvAllParam(@Validated @RequestBody EmuParamDTO dto) {
        stationDeviceService.exists(dto.getSn());
        return R.ok(wifiInvPublishBiz.readInvAllParam(dto));
    }

    /**
     * 读取故障录播默认参数
     */
    @Log(title = "读取故障录播默认参数", businessType = BusinessType.COMMAND)
    @GetMapping(value = "/read/fault/wave/setting")
    public R<Integer> readFaultWaveSetting(@RequestParam @Schema(description = "微逆序列号") String sn) {
        stationDeviceService.noAuthExists(sn);
        return R.ok(wifiInvPublishBiz.readFaultWaveSetting(sn));
    }

    /**
     * 写故障录播参数
     */
    @Log(title = "写故障录播参数", businessType = BusinessType.COMMAND)
    @PostMapping(value = "/write/fault/wave/setting")
    public R<Integer> faultWaveSetting(@Validated @RequestBody FaultWaveSettingDTO dto) {
        stationDeviceService.noAuthExists(dto.getSn());
        return R.ok(wifiInvPublishBiz.faultWaveSetting(dto));
    }

    /**
     * 读取故障录播
     */
    @Log(title = "读取故障录播", businessType = BusinessType.COMMAND)
    @GetMapping(value = "/read/fault/wave")
    public R<Integer> readFaultWave(@RequestParam @Schema(description = "微逆序列号") String sn) {
        stationDeviceService.noAuthExists(sn);
        return R.ok(wifiInvPublishBiz.readFaultWave(sn));
    }

    /**
     * 读故障记录文件
     */
    @Log(title = "读故障记录文件", businessType = BusinessType.COMMAND)
    @GetMapping(value = "/read/fault/log")
    public R<Integer> readFaultLog(@RequestParam @Schema(description = "微逆序列号") String sn,
                                   @RequestParam(required = false, defaultValue = "1") @Schema(description = "页码") Integer pageNum) {
        stationDeviceService.noAuthExists(sn);
        return R.ok(wifiInvPublishBiz.readFaultLog(sn, pageNum));
    }
}
