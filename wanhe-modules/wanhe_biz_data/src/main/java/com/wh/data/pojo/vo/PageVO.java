package com.wh.data.pojo.vo;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.constant.HttpStatus;
import com.ruoyi.common.core.utils.Tools;
import com.ruoyi.common.core.web.page.TableDataInfo;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/21
 */
@Setter
@Getter
@NoArgsConstructor
public class PageVO<T> extends TableDataInfo<T> {

    //发电量
    private BigDecimal energy = BigDecimal.ZERO;

    //消耗量
    private BigDecimal consume = BigDecimal.ZERO;

    public PageVO(List<T> rows) {
        setRows(rows);
        setCode(HttpStatus.SUCCESS);
    }

    /**
     * @param total 统计总数
     * @param data 电表数据
     * @param list 列表数据
     */
    public static <T> PageVO<T> of(List<JSONObject> total, List<List<JSONObject>> data, List<T> list) {
        PageVO<T> rspData = new PageVO<>();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setRows(list);
        rspData.setMsg("查询成功");
        if (!total.isEmpty()) {
            rspData.setTotal(total.get(0).getLong("total"));
            rspData.setEnergy(Tools.halfUp(total.get(0).getBigDecimal("data")));
        }
        if (CollUtil.isNotEmpty(data) && CollUtil.isNotEmpty(data.get(0))) {
            rspData.setConsume(Tools.halfUp(data.get(0).get(0).getBigDecimal("data").add(rspData.getEnergy())));
        }
        return rspData;
    }

    /**
     * @param total 统计总数
     * @param list 列表数据
     */
    public static <T> PageVO<T> of(List<T> list, long total) {
        PageVO<T> rspData = new PageVO<>();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setRows(list);
        rspData.setMsg("查询成功");
        rspData.setTotal(total);
        return rspData;
    }
}
