package com.wh.data.pojo.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 电站数据统计
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StationAttrDataVO {

    //站点id
    private Integer powerStationId;

    //站点name
    private String powerStationName;

    // 发电量
    private BigDecimal dailyEnergy;

    public StationAttrDataVO(Integer powerStationId, BigDecimal dailyEnergy) {
        this.powerStationId = powerStationId;
        this.dailyEnergy = dailyEnergy;
    }
}
