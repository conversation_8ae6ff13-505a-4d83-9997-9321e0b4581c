package com.wh.data.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/3
 */
@Data
public class EmuBatchOperateDTO {

    @Schema(description = "设备号")
    @NotEmpty
    @Valid
    private List<Device> devices;

    @Schema(description = "操作类型 1开机 2关机 3重启 4并网文件升级 5微逆固件升级")
    @NotNull
    @Range(min = 1, max = 5)
    private Integer operateType;

    @Schema(description = "文件id  operateType是4，5，6 时必填")
    private Integer fileId;

    @Data
    public static class Device {

        @Schema(description = "emu序列号")
        @NotBlank
        private String emuNumber;

        @Schema(description = "微逆序列号")
        @NotEmpty
        private List<String> numbers;

    }

}
