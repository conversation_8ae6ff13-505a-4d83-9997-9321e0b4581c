package com.wh.data.pojo.station;

import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/12
 */
@Data
public class InfoVO {

    // 电站ID
    private Integer stationId;
    // 电站类型,0:屋顶光伏 1:阳台光伏 2:工商业光伏
    private Integer stationType;
    // 时间戳
    private long timeStamp = System.currentTimeMillis();
    // 微逆数据
    private List<InvDataVO> invData = Collections.emptyList();
    // 电池数据
    private BatteryDataVO batteryData = new BatteryDataVO();
    // 电表数据
    private List<MeterDataVO> meterData = Collections.emptyList();

}
