package com.wh.data.enums;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.domain.IntStr;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.redis.RedisUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/21 0021
 */
@Getter
@AllArgsConstructor
public enum CommandEnum {

    EMU_RESTART(0, "重启", "", Duration.ZERO),
    EMU_COLLECT_WN_VERSION(1, "收集微逆版本信息", "", Duration.ZERO),
    EMU_UPGRADE_FILE(2, "固件升级", "emugj", Duration.ofMinutes(4)),
    EMU_INV_UPGRADE_FILE(3, "微逆固件升级", "wngj", Duration.ofMinutes(1)),
    EMU_TIME_ZONE(4, "时区下发", "", Duration.ZERO),
    EMU_INV_ON(5, "微逆开机", "", Duration.ZERO),
    EMU_INV_OFF(6, "微逆关机", "", Duration.ZERO),
    EMU_INV_RESTART(7, "微逆重启", "", Duration.ZERO),
//    EMU_INF_GRID_FILE_UPGRADE(8, "微逆并网文件升级", "", Duration.ZERO),
    EMU_READ_NETWORK(9, "自动组网", "", Duration.ZERO),
    EMU_WRITE_NETWORK(10, "下发组网信息", "", Duration.ZERO),
    EMU_CONFIG_METER(11, "配置电表", "", Duration.ZERO),
    EMU_WRITE_GRID(12, "下发并网文件", "", Duration.ZERO),
    EMU_READ_GRID(13, "并网文件查看", "", Duration.ZERO),
    EMU_READ_FAULT_WAVE(14, "读取故障录播", "gzlb", Duration.ofSeconds(40)),
    EMU_READ_FAULT_LOG(15, "读故障记录文件", "gzjl", Duration.ofSeconds(40)),
    EMU_WRITE_FAULT(16, "写故障录播参数配置", "", Duration.ZERO),
    EMU_WRITE(17, "写EMU参数", "", Duration.ZERO),
    EMU_INV_WRITE(18, "写微逆参数", "", Duration.ZERO),
    EMU_READ_FAULT_WAVE_PARAM(19, "读取故障录播参数配置", "", Duration.ZERO),

    INV_ON(20, "开机", "", Duration.ZERO),
    INV_OFF(21, "关机", "", Duration.ZERO),
    INV_RESTART(22, "重启", "", Duration.ZERO),
//    INV_GRID_FILE_UPGRADE(23, "并网文件升级", "", Duration.ZERO),
    INV_UPGRADE_FILE(24, "固件升级", "invgj", Duration.ofMinutes(1)),
    INV_WIFI_UPGRADE_FILE(25, "WIFI固件升级", "invwifigj", Duration.ofMinutes(4)),
    INV_TIME_ZONE(26, "时区下发", "", Duration.ZERO),
    INV_WRITE_NETWORK(27, "下发组网信息", "", Duration.ZERO),
    INV_WRITE_GRID(28, "下发并网文件", "", Duration.ZERO),
    INV_READ_GRID(29, "并网文件查看", "", Duration.ZERO),
    INV_WRITE(30, "写微逆参数", "", Duration.ZERO),
    METER_DATA(31, "获取电表数据", "", Duration.ZERO),
    INV_READ_PARAM(32, "读取微逆参数", "", Duration.ZERO),
    INV_READ_FAULT_WAVE_PARAM(33, "读取故障录播参数配置", "", Duration.ZERO),
    INV_WRITE_FAULT(34, "写故障录播参数配置", "", Duration.ZERO),
    INV_READ_FAULT_WAVE(35, "读取故障录播", "gzlb", Duration.ofSeconds(40)),
    INV_READ_FAULT_LOG(36, "读故障记录文件", "gzjl", Duration.ofSeconds(40)),
    ;

    private final Integer val;
    private final String desc;
    private final String key;
    private final Duration timeout;

    public static final List<IntStr> EMU = Arrays.stream(CommandEnum.values()).filter(item -> item.getVal() < 20)
            .map(item -> new IntStr(item.getVal(), item.getDesc())).toList();

    public static final List<IntStr> INV = Arrays.stream(CommandEnum.values()).filter(item -> item.getVal() >= 20)
            .map(item -> new IntStr(item.getVal(), item.getDesc())).toList();

    public static final Map<Integer, String> MAP = Arrays.stream(CommandEnum.values()).collect(Collectors.toMap(CommandEnum::getVal, CommandEnum::getDesc));

    public void noRepeatRequest(String sn) {
        String keyStr = "repeat:" + key + sn;
        Boolean b = RedisUtil.setIfAbsent(keyStr, StrUtil.EMPTY, timeout);
        if (b != null && !b) {
            Long expire = RedisUtil.getExpire(keyStr);
            throw new ServiceException("请勿重复请求{0}，请于{1}秒后重试", sn, expire);
        }
    }

    public void noRepeatRequest(String sn, int size) {
        String keyStr = "repeat:" + key + sn;
        Boolean b = RedisUtil.setIfAbsent(keyStr, StrUtil.EMPTY, Duration.ofMinutes(size));
        if (b != null && !b) {
            Long expire = RedisUtil.getExpire(keyStr);
            throw new ServiceException("请勿重复请求{0}，请于{1}秒后重试", sn, expire);
        }
    }

    public void delRepeatRequest(String sn) {
        RedisUtil.delete("repeat:" + key + sn);
    }

}
