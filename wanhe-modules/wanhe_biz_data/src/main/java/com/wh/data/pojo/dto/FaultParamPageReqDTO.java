package com.wh.data.pojo.dto;

import com.ruoyi.common.core.domain.PageReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/1/10
 */
@Setter
@Getter
public class FaultParamPageReqDTO extends PageReqDTO {

    //参数分类
    @Schema(description = "参数分类")
    private String paramGroup;

    //参数ID
    @Schema(description = "参数ID")
    private Integer paramId;

    //版本ID
    @Schema(description = "版本ID")
    private Long versionId;

}
