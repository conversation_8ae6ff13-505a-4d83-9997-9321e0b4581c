package com.wh.data.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024-11-04
 */
@Data
@TableName("wh_device_alarm")
@Schema(name = "DeviceAlarm", description = "设备告警故障表")
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = false)
public class DeviceAlarm {

    @Schema(description = "主键id")
    @EqualsAndHashCode.Include
    @TableId(type = IdType.AUTO)
    private Integer id;

    @Schema(description = "设备id")
    private Integer deviceId;

    @Schema(description = "电站id")
    private Integer powerStationId;

    @Schema(description = "序列号")
    private String snNumber;

    @Schema(description = "告警code")
    private String alarmCode;

    @Schema(description = "告警key")
    private String alarmKey;

    @Schema(description = "告警数据")
    private Integer alarmData;

    @Schema(description = "告警发生时间")
    private String occurredAt;

    @Schema(description = "告警解决时间")
    private String resolvedAt;

    @Schema(description = "告警是否已解决 （0：未解决，1：已解决）")
    @TableField("is_resolved")
    private Integer resolved;

    @Schema(description = "设备类型 （0：EMU 1:微逆 2：电表）")
    private Integer deviceType;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "告警名称")
    @TableField(exist = false)
    private String alarmName;

    @Schema(description = "处理建议")
    @TableField(exist = false)
    private String alarmSuggest;
}
