package com.wh.data.service;

import cn.hutool.core.util.CharsetUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.enums.DeviceTypeEnum;
import com.ruoyi.common.core.enums.LanguageEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.I18nUtil;
import com.ruoyi.common.core.utils.TimeZoneUtil;
import com.ruoyi.common.core.utils.Tools;
import com.ruoyi.system.api.RemotePubMqttService;
import com.ruoyi.system.api.domain.GridData;
import com.ruoyi.system.api.domain.PublishDTO;
import com.wh.data.domain.PowerStation;
import com.wh.data.domain.StationDevice;
import com.wh.data.domain.StationDeviceRemote;
import com.wh.data.enums.CommandEnum;
import com.wh.data.enums.FaultWaveEnum;
import com.wh.data.mapper.GridDataMapper;
import com.wh.data.mapper.StationDeviceRemoteMapper;
import com.wh.data.pojo.dto.StationDeviceRemotePageDTO;
import com.wh.data.pojo.vo.CommandResultVO;
import com.wh.data.pojo.vo.FaultWaveSettingVO;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.PrintWriter;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/2
 */
@Service
public class StationDeviceRemoteService extends DataScopeServiceImpl<StationDeviceRemoteMapper, StationDeviceRemote> {

    @Resource
    private StationDeviceService stationDeviceService;
    @Resource
    private RemotePubMqttService remotePubMqttService;
    @Resource
    private GridDataMapper gridDataMapper;

    /**
     * 发送mqtt并且保存命令下发记录
     */
    public Integer sendCommand(String sn, String mid, String topic, String message, CommandEnum command, DeviceTypeEnum deviceType) {
        return sendCommand(sn, null, mid, topic, message, command, deviceType, null, 0);
    }

    /**
     * 发送mqtt并且保存命令下发记录
     */
    public Integer sendCommand(String sn, List<String> invSn, String mid, String topic, String message, CommandEnum command, DeviceTypeEnum deviceType) {
        return sendCommand(sn, invSn, mid, topic, message, command, deviceType, null, 0);
    }

    public Integer sendCommand(String sn, List<String> invSn, String mid, String topic, String message, CommandEnum command, DeviceTypeEnum deviceType, Integer powerStationId, Integer qos) {
        StationDevice device = stationDeviceService.getBySn(sn);
        if (device == null) {
            Long tenantId = stationDeviceService.getTenantId(powerStationId);
            if (tenantId == null) {
                throw new ServiceException("电站不存在");
            }
            device = new StationDevice();
            device.setPowerStationId(powerStationId);
            device.setTenantId(tenantId);
        }
        PublishDTO publishDto = new PublishDTO(qos, topic, message);
        Boolean publish = remotePubMqttService.publish(publishDto, SecurityConstants.INNER);
        if (!Boolean.TRUE.equals(publish)) throw new ServiceException("mqtt下发命令失败");
        StationDeviceRemote remote = new StationDeviceRemote();
        remote.setPowerStationId(device.getPowerStationId());
        remote.setStationDeviceId(device.getId());
        remote.setTenantId(device.getTenantId());
        remote.setMid(mid);
        remote.setNumber(sn);
        if (invSn != null) {
            remote.setInvSn(JSON.toJSONString(invSn));
        }
        remote.setDeviceType(deviceType.getVal());
        remote.setOperate(command.getVal());
        remote.setIssueContent(message);
        save(remote);
        return remote.getId();
    }

    public CommandResultVO commandResult(Integer id, String topic) {
        StationDeviceRemote result = baseMapper.getResponseResult(id);
        if (result == null || result.getOperate() == null || result.getResponseResult() == null) {
            return null;
        }
        CommandResultVO vo = new CommandResultVO(result.getResponseTime(), result.getResponseResult());
        switch (result.getOperate()) {
            case 17 -> vo.setTopic(topic.replace("WriteEmuParam", "ResponseWriteEmuParam"));
            case 31 -> vo.setTopic(topic.replace("WriteInvParam", "ResponseWriteInvParam"));
            case 18 -> vo.setTopic(topic.replace("WriteInvOfEmuParam", "ResponseWriteInvOfEmuParam"));
        }
        return vo;
    }

    public void commandResultExport(Integer id, HttpServletResponse response) throws IOException {
        StationDeviceRemote result = baseMapper.getResponseResult(id);
        response.setCharacterEncoding(CharsetUtil.UTF_8);
        response.setContentType("text/plain");
        response.setHeader("Content-Disposition", "attachment; filename=\"file.json\"");
        try (PrintWriter writer = response.getWriter()) {
            if (result != null) {
                writer.write(JSON.parseObject(result.getResponseResult()).toString(JSONWriter.Feature.WriteNulls, JSONWriter.Feature.PrettyFormat));
            }
            writer.flush();
        }
    }

    public Object queryCallBackResult(Integer id) {
        StationDeviceRemote result = baseMapper.getResponseResult(id);
        if (result == null) {
            return null;
        }
        return formatData(result.getOperate(), result.getResponseResult());
    }

    private Object formatData(Integer command, String result) {
        if (command == null || result == null || result.isEmpty()) {
            return null;
        }
        JSONObject json = JSON.parseObject(result);
        switch (command) {
            //自动组网排序
            case 9 -> {
                JSONArray array = json.getJSONArray("inv");
                if (array != null && array.size() > 1) {
                    json.put("inv", array.stream().sorted().toList());
                }
            }
            //并网文件查看
            case 13, 29 -> {
                Integer gridFileId = json.getInteger("gridFileId");
                if (gridFileId != null) {
                    json.put("gridFileName", gridDataMapper.getGridFileName(gridFileId));
                    JSONArray params = json.getJSONArray("params");
                    Map<Integer, GridData> map = gridDataMapper.all().stream().collect(Collectors.toMap(GridData::getAddr, Function.identity()));

                    int index = 0;
                    for (int i = 0; i < params.size(); i++) {
                        JSONObject param = params.getJSONObject(i);
                        int addr = param.getIntValue("addr");
                        // 删除掉 addr 为 9 的并网文件版本数据
                        if (addr == 9) {
                            index = i;
                        }
                        GridData data = map.get(param.getIntValue("addr"));
                        if (data != null) {
                            param.put("name", data.getNameEn());
                            param.put("unit", data.getUnit());
                            if (data.getType().equals("float")) {
                                param.put("value", Tools.intBitsToFloat(param.getIntValue("value")));
                            }
                        }
                    }
                    params.remove(index);
                }
            }
            //读取故障录播参数配置
            case 19 -> {
                JSONArray channel = json.getJSONArray("channelId");
                FaultWaveEnum[] values = FaultWaveEnum.values();
                List<FaultWaveSettingVO> list = new ArrayList<>(values.length);
                for (FaultWaveEnum value : values) {
                    FaultWaveSettingVO vo = new FaultWaveSettingVO();
                    vo.setAddr(value.getAddr());
                    vo.setName(value.getName());
                    vo.setRw(value.getRw());
                    switch (value.getAddr()) {
                        case 219 -> vo.setValue(json.getBigDecimal("sampleFreq"));
                        case 220 -> vo.setValue(json.getBigDecimal("sampleRate"));
                        case 221 -> vo.setValue(json.getBigDecimal("trigPercent"));
                        case 223 -> vo.setValue(json.getBigDecimal("trigType"));
                        case 225 -> vo.setValue(json.getBigDecimal("trigId"));
                        case 226 -> vo.setValue(json.getBigDecimal("trigBit"));
                        case 227 -> vo.setValue(json.getBigDecimal("trigLimit"));
                        case 228 -> vo.setValue(!channel.isEmpty() ? channel.getBigDecimal(0) : null);
                        case 229 -> vo.setValue(channel.size() > 1 ? channel.getBigDecimal(1) : null);
                        case 230 -> vo.setValue(channel.size() > 2 ? channel.getBigDecimal(2) : null);
                        case 231 -> vo.setValue(channel.size() > 3 ? channel.getBigDecimal(3) : null);
                        case 232 -> vo.setValue(channel.size() > 4 ? channel.getBigDecimal(4) : null);
                        case 233 -> vo.setValue(channel.size() > 5 ? channel.getBigDecimal(5) : null);
                    }
                    vo.setValue(value.getValue());
                    vo.setMax(value.getMax());
                    vo.setMin(value.getMin());
                    vo.setUnit(value.getUnit());
                    vo.setType(value.getType());
                    vo.setType2(value.getType2());
                    list.add(vo);
                }
                return list;
            }
        }
        return json;
    }

    public List<Object> queryCallBackResults(List<Integer> ids) {
        Map<Integer, StationDeviceRemote> map = baseMapper.getResponseResults(join(ids)).stream().collect(Collectors.toMap(StationDeviceRemote::getId, Function.identity()));
        return ids.stream().map(id -> {
            StationDeviceRemote result = map.get(id);
            if (result == null) {
                return null;
            }
            return formatData(result.getOperate(), result.getResponseResult());
        }).toList();
    }

    public void page(StationDeviceRemotePageDTO dto) {
        List<StationDeviceRemote> list = lambdaQueryAll(StationDeviceRemote::getPowerStationId, dto.getStationId())
                .selectAll(StationDeviceRemote.class)
                .select(PowerStation::getStationName, PowerStation::getTimeZone)
                .eq(StationDeviceRemote::getDeviceType, dto.getDeviceType())
                .eq(dto.getOperate() != null, StationDeviceRemote::getOperate, dto.getOperate())
                .between(dto.getStartTime() != null && dto.getEndTime() != null, StationDeviceRemote::getCreateTime, dto.getStartTime(), dto.getEndTime())
                .between(dto.getResponseStartTime() != null && dto.getResponseEndTime() != null, StationDeviceRemote::getResponseTime, dto.getResponseStartTime(), dto.getResponseEndTime())
                .orderByDesc(StationDeviceRemote::getId)
                .list();
        if (!list.isEmpty()) {
            Locale local = LanguageEnum.getLocal();
            list.forEach(r -> {
                r.setCommand(CommandEnum.MAP.get(r.getOperate()));
                r.setCommand(I18nUtil.get(r.getCommand(), local));
                ZoneId zoneId = TimeZoneUtil.getZoneId(r.getTimeZone());
                r.setCreateTime(TimeZoneUtil.getZonedTime(r.getCreateTime(), zoneId));
                if (r.getResponseTime() != null) {
                    r.setResponseTime(TimeZoneUtil.getZonedTime(r.getResponseTime(), zoneId));
                }
            });
        }
    }

}
