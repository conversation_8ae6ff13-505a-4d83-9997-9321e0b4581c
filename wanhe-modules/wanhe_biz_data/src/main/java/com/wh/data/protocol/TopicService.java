package com.wh.data.protocol;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.enums.DeviceTypeEnum;
import com.ruoyi.common.security.service.BaseService;
import com.wh.data.domain.StationDeviceRemote;
import com.wh.data.influxdb.entity.DeviceData;
import com.wh.data.influxdb.mapper.InfluxMapper;
import com.wh.data.service.PowerStationService;
import com.wh.data.service.StationDeviceRemoteService;
import com.wh.data.service.StationDeviceService;
import jakarta.annotation.Resource;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;

/**
 * 默认数据处理方法
 */
@Slf4j
@Getter
public abstract class TopicService implements BaseService {

    public static final String DATA = "data";
    public static final String TIME = "time";
    public static final String INV_DATA = "invData";
    public static final String WIFI = "wifi";
    public static final String MID = "mid";
    public static final String TIMESTAMP = "timeStamp";
    public static final String SN = "sn";
    public static final String EMU_SN = "emuSn";
    public static final String GATE_WAY_DATA = "gateWayData";
    public static final String PID = "pid";
    public static final String DEVICE_ID = "deviceId";
    public static final String RESPONSE_ID = "responseId";
    public static final String STATUS = "status";

    @Resource
    protected InfluxMapper influxMapper;
    @Resource
    protected ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Resource
    protected PowerStationService powerStationService;
    @Resource
    protected StationDeviceService stationDeviceService;
    @Resource
    protected StationDeviceRemoteService stationDeviceRemoteService;

    public void copyWifiToDeviceData(DeviceData data, Integer stationId, Integer deviceId, String sn,
                                     JSONObject json, Instant instant, DeviceTypeEnum deviceType, JSONObject wifi) {
        data.setPowerStationId(stationId);
        data.setDeviceId(deviceId);
        data.setSn(sn);
        data.setMid(json.getString(MID));
        data.setCreateTime(instant);
        data.setDeviceType(deviceType.getVal());
        data.setRunningStatus(wifi.getInteger("runningStatus"));
        data.setCommType(wifi.getInteger("commType"));
        data.setMonitorNum(wifi.getInteger("monitorNum"));
        data.setUploadInterval(wifi.getInteger("uploadInterval"));
        data.setWifiSoftVer(wifi.getString("softVer"));
        data.setWifiHardVer(wifi.getString("hardVer"));
        data.setTimeCode(wifi.getString("timeCode"));
        if (StrUtil.isEmpty(data.getTimeCode())) {
            data.setTimeCode(json.getString("timeCode"));
        }
    }

    /**
     * 更新回调记录
     */
    public void updateCommand(JSONObject data) {
        String responseId = data.getString(RESPONSE_ID);
        if (responseId == null) {
            responseId = data.getString("ResponseId");
            if (responseId == null) {
                return;
            }
        }
        stationDeviceRemoteService.lambdaUpdate()
                .eq(StationDeviceRemote::getMid, responseId)
                .set(StationDeviceRemote::getResponseResult, data.toString())
                .set(StationDeviceRemote::getResponseTime, LocalDateTime.now())
                .update();
    }

    /**
     * 构建光伏总功率数据
     *
     * @param deviceData 设备数据
     * @param dataJson       数据
     */
    public void buildPvData(DeviceData deviceData, JSONObject dataJson) {
        BigDecimal pvTempPower = dataJson.getBigDecimal("pvTempPower") == null ? BigDecimal.ZERO : dataJson.getBigDecimal("pvTempPower");
        BigDecimal pv2TempPower = dataJson.getBigDecimal("pv2TempPower") == null ? BigDecimal.ZERO : dataJson.getBigDecimal("pv2TempPower");
        BigDecimal pv3TempPower = dataJson.getBigDecimal("pv3TempPower") == null ? BigDecimal.ZERO : dataJson.getBigDecimal("pv3TempPower");
        BigDecimal pv4TempPower = dataJson.getBigDecimal("pv4TempPower") == null ? BigDecimal.ZERO : dataJson.getBigDecimal("pv4TempPower");
        BigDecimal pv5TempPower = dataJson.getBigDecimal("pv5TempPower") == null ? BigDecimal.ZERO : dataJson.getBigDecimal("pv5TempPower");
        BigDecimal pv6TempPower = dataJson.getBigDecimal("pv6TempPower") == null ? BigDecimal.ZERO : dataJson.getBigDecimal("pv6TempPower");
        BigDecimal pv7TempPower = dataJson.getBigDecimal("pv7TempPower") == null ? BigDecimal.ZERO : dataJson.getBigDecimal("pv7TempPower");
        BigDecimal pv8TempPower = dataJson.getBigDecimal("pv8TempPower") == null ? BigDecimal.ZERO : dataJson.getBigDecimal("pv8TempPower");

        deviceData.setPvTotalPower(pvTempPower.add(pv2TempPower).add(pv3TempPower).add(pv4TempPower).add(pv5TempPower).add(pv6TempPower).add(pv7TempPower).add(pv8TempPower));
    }

}
