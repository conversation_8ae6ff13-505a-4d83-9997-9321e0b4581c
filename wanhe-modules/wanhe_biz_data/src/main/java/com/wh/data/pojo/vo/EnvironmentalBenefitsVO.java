package com.wh.data.pojo.vo;

import com.ruoyi.common.core.domain.DataUnit;
import com.ruoyi.common.core.utils.NumUtil;
import com.ruoyi.common.core.utils.Tools;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Setter
@Getter
@NoArgsConstructor
public class EnvironmentalBenefitsVO {

    //时间
    private String year;
    //发电量数据
    private BigDecimal data = BigDecimal.ZERO;
    private String dataUnit = "kWh";
    // 树
    private BigDecimal treeNum = BigDecimal.ZERO;
    //co2
    private BigDecimal co2 = BigDecimal.ZERO;
    //汽油
    private BigDecimal gasoline = BigDecimal.ZERO;

    @Schema(description = "节约标准煤,单位：kg")
    private BigDecimal coal = BigDecimal.ZERO;

    public EnvironmentalBenefitsVO(String year, BigDecimal data) {
        this.year = year;
        if (data.compareTo(BigDecimal.ZERO) > 0) {
            DataUnit du = Tools.covertKwhUnit(data);
            this.data = du.getData();
            this.dataUnit = du.getUnit();
            BigDecimal thData = data.divide(NumUtil.THOUSAND, 4, RoundingMode.HALF_UP);
            this.treeNum = thData.multiply(NumUtil.TREE).setScale(2, RoundingMode.HALF_UP);
            this.co2 = thData.multiply(NumUtil.COZ).setScale(2, RoundingMode.HALF_UP);
            this.gasoline = thData.multiply(NumUtil.GASOLINE).setScale(2, RoundingMode.HALF_UP);
            this.coal = thData.multiply(NumUtil.COAL).setScale(2, RoundingMode.HALF_UP);
        }
    }

}
