package com.wh.data.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wh.data.domain.FaultVersion;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/24
 */
public interface FaultVersionMapper extends BaseMapper<FaultVersion> {

    /**
     * 页
     *
     * @param versionName 版本名称
     * @param versionNo   版本号
     * @return {@link List }<{@link FaultVersion }>
     */
    List<FaultVersion> page(@Param("versionName") String versionName, @Param("versionNo") String versionNo);
}
