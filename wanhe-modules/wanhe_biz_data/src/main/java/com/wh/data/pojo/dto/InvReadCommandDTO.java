package com.wh.data.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025/5/13 15:08
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InvReadCommandDTO {

    @Schema(description = "设备sn")
    @NotBlank
    private String sn;

    @Schema(description = "qos")
    @NotNull
    private Integer qos;

    @Schema(description = "起始地址")
    @NotNull
    private Integer startAddr;

    @Schema(description = "参数个数")
    @NotNull
    private Integer paramNum;

}
