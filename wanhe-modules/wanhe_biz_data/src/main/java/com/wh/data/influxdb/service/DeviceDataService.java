package com.wh.data.influxdb.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.ArrTotal;
import com.ruoyi.common.core.domain.DataUnit;
import com.ruoyi.common.core.enums.ClientTypeEnum;
import com.ruoyi.common.core.enums.DeviceTypeEnum;
import com.ruoyi.common.core.enums.LanguageEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.I18nUtil;
import com.ruoyi.common.core.utils.NumUtil;
import com.ruoyi.common.core.utils.TimeZoneUtil;
import com.ruoyi.common.core.utils.Tools;
import com.ruoyi.common.core.utils.bean.BeanUtils;
import com.ruoyi.common.redis.RedisUtil;
import com.ruoyi.common.security.utils.Sql;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.dto.PowerStationListDataVO;
import com.wh.data.domain.Fault;
import com.wh.data.domain.PowerStation;
import com.wh.data.domain.PowerStationPrice;
import com.wh.data.domain.StationDevice;
import com.wh.data.influxdb.dto.Param;
import com.wh.data.influxdb.entity.DeviceData;
import com.wh.data.influxdb.entity.MeterData;
import com.wh.data.pojo.PriceSite;
import com.wh.data.pojo.dto.*;
import com.wh.data.pojo.station.InfoVO;
import com.wh.data.pojo.station.InvDataVO;
import com.wh.data.pojo.station.MeterDataVO;
import com.wh.data.pojo.vo.*;
import com.wh.data.protocol.TopicService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoField;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/6
 */
@Slf4j
@Service
public class DeviceDataService extends TopicService {

    public StationMaintainVO maintain(Integer stationId) {
        ZoneId zoneId = Sql.getZoneId(stationId);
        StationMaintainVO vo = powerStationService.getBaseMapper().getStationMaintainVO(stationId);
        if (vo.getInstalledCapacity().compareTo(BigDecimal.ZERO) == 0) {
            vo.setRate(BigDecimal.ZERO);
        } else {
            vo.setRate(vo.getPower().multiply(NumUtil.HUNDRED).divide(vo.getInstalledCapacity(), 2, RoundingMode.HALF_UP));
        }
        ZonedDateTime beginTime = LocalDate.now().atStartOfDay().atZone(zoneId);
        ZonedDateTime endTime = beginTime.plusDays(1);
        String iql = "select sum(data) as data from (select last(acTempPower) as data from {table} where powerStationId = '{stationId}' and deviceType = 1 and time >= {begin} and time < {end} group by time(15m),deviceId {tz}) where time >= {begin} and time < {end} group by time(15m) {tz}";
        Param param = Param.of(DeviceData.MN, zoneId, stationId, beginTime, endTime, iql, TimeZoneUtil.HHmm);
        Map<String, BigDecimal> map = Tools.getMap(influxMapper.queryList(param));
        while (beginTime.isBefore(endTime)) {
            String time = TimeZoneUtil.HHmm.format(beginTime.toLocalDateTime());
            vo.getPowerList().add(Tools.getTimeData(time, map.get(time)));
            beginTime = beginTime.plusMinutes(15);
        }
        vo.setPowerListUnit(Tools.covertPowerWUnit(vo.getPowerList()));
        return vo;
    }

    public PowerStationHistoryDataVO queryPowerStationHistoryData(StationQueryDataDTO dto) {
        // todo 发电功率 acTempPower 负载功率用的电表的power
        ZoneId zoneId = Sql.getZoneId(dto.getStationId());
        ZonedDateTime beginTime, endTime;
        switch (dto.getType()) {
            case 1 -> {
                beginTime = LocalDateTime.parse(dto.getDate() + " 00:00", TimeZoneUtil.yyyyMMddHHmm).atZone(zoneId);
                endTime = beginTime.plusDays(1);
                String iql1 = "select sum(data) as data from (select last(acTempPower) as data from %s where powerStationId = '{stationId}' and deviceType = 1 and time >= {begin} and time < {end} group by time(15m),deviceId {tz}) where time >= {begin} and time < {end} group by time(15m) {tz}";
                String iql2 = "select sum(data) * 1000 as data from (select last(power) as data from %s where powerStationId = '{stationId}' and powerGridSeat = 2 and time >= {begin} and time < {end} group by time(15m),deviceId {tz}) where time >= {begin} and time < {end} group by time(15m) {tz}";
                Param param = Param.of(zoneId).begin(beginTime).end(endTime).stationId(dto.getStationId()).format(TimeZoneUtil.HHmm);
                List<List<JSONObject>> list = influxMapper.queryMultiList(param.iql(List.of(iql1.formatted(DeviceData.MN), iql2.formatted(MeterData.MN))));
                return new PowerStationHistoryDataVO(list, dto.getType(), beginTime, endTime);
            }
            case 2 -> {
                List<Integer> time = Arrays.stream(dto.getDate().split("-")).map(Integer::parseInt).toList();
                beginTime = LocalDate.ofYearDay(time.get(0), 1).with(WeekFields.ISO.weekOfYear(), time.get(1)).with(ChronoField.DAY_OF_WEEK, 1).atStartOfDay().atZone(zoneId);
                endTime = beginTime.plusDays(7);
                String iql1 = "select sum(data) as data from (select last(acTempPower) as data from %s where powerStationId = '{stationId}' and deviceType = 1 and time >= {begin} and time < {end} group by time(15m),deviceId {tz}) where time >= {begin} and time < {end} group by time(15m) {tz}";
                String iql2 = "select sum(data) * 1000 as data from (select last(power) as data from %s where powerStationId = '{stationId}' and powerGridSeat = 2 and time >= {begin} and time < {end} group by time(15m),deviceId {tz}) where time >= {begin} and time < {end} group by time(15m) {tz}";
                Param param = Param.of(zoneId).begin(beginTime).end(endTime).stationId(dto.getStationId()).format(TimeZoneUtil.MMddHHmm);
                List<List<JSONObject>> list = influxMapper.queryMultiList(param.iql(List.of(iql1.formatted(DeviceData.MN), iql2.formatted(MeterData.MN))));
                return new PowerStationHistoryDataVO(list, dto.getType(), beginTime, endTime);
            }
            case 3 -> {
                beginTime = LocalDateTime.parse(dto.getDate() + "-01 00:00", TimeZoneUtil.yyyyMMddHHmm).atZone(zoneId);
                endTime = beginTime.plusMonths(1);
                ZonedDateTime now = ZonedDateTime.now(zoneId);
                if (endTime.isAfter(now)) {
                    endTime = now;
                }
                String iql1 = "select sum(data) as data from (select last(dailyEnergy) as data from %s where powerStationId = '{stationId}' and deviceType = 1 and time >= {begin} and time < {end} group by time(1d),deviceId {tz}) group by time(1d) fill(0) {tz}";
                String iql2 = "select sum(stageEnergy) as data from %s where powerStationId = '{stationId}' and time >= {begin} and time < {end} group by time(1d) fill(0) {tz}";
                Param param = Param.of(zoneId).stationId(dto.getStationId()).begin(beginTime).end(endTime).format(TimeZoneUtil.MMdd);
                List<List<JSONObject>> list = influxMapper.queryMultiList(param.iql(List.of(iql1.formatted(DeviceData.MN), iql2.formatted(MeterData.MN))));
                return new PowerStationHistoryDataVO(list, dto.getType(), beginTime, endTime);
            }
            case 4 -> {
                beginTime = LocalDateTime.parse(dto.getDate() + "-01-01 00:00", TimeZoneUtil.yyyyMMddHHmm).atZone(zoneId);
                LocalDate beginMonth = beginTime.toLocalDate();
                List<String> iqlList = new ArrayList<>();
                Param param = Param.of(zoneId).stationId(dto.getStationId());
                ZonedDateTime now = ZonedDateTime.now(zoneId);
                for (int i = 0; i < 12 && beginTime.isBefore(now); i++) {
                    endTime = beginTime.plusMonths(1);
                    String iql1 = "select sum(data) as data from (select last(dailyEnergy) as data from %s where powerStationId = '{stationId}' and deviceType = 1 and time >= %s and time < %s group by time(1d),deviceId {tz})";
                    String iql2 = "select sum(stageEnergy) as data from %s where powerStationId = '{stationId}' and time >= %s and time < %s {tz}";
                    String begin = param.getSecond(beginTime), end = param.getSecond(endTime);
                    iqlList.add(iql1.formatted(DeviceData.MN, begin, end));
                    iqlList.add(iql2.formatted(MeterData.MN, begin, end));
                    beginTime = endTime;
                }
                List<BigDecimal> list = influxMapper.queryMultiObjData(param.iql(iqlList));
                List<List<Object>> consumer = new ArrayList<>();
                List<List<Object>> producer = new ArrayList<>();
                String time = null;
                for (int i = 0; i < list.size(); i++) {
                    int j = i / 2;
                    if (i % 2 == 0) {
                        time = TimeZoneUtil.yyyyMM.format(beginMonth.plusMonths(j));
                        consumer.add(Tools.getTimeData(time, list.get(i)));
                    } else {
                        producer.add(Tools.getTimeData(time, list.get(i).add((BigDecimal) consumer.get(j).get(1))));
                    }
                }
                for (int i = now.getMonthValue(); i < 12; i++) {
                    time = TimeZoneUtil.yyyyMM.format(beginMonth.plusMonths(i));
                    consumer.add(List.of(time, 0));
                    producer.add(List.of(time, 0));
                }
                return new PowerStationHistoryDataVO(consumer, producer);
            }
            case 5 -> {
                int beginYear = powerStationService.getYear(dto.getStationId());
                beginTime = ZonedDateTime.of(beginYear, 1, 1, 0, 0, 0, 0, zoneId);
                List<String> iqlList = new ArrayList<>();
                Param param = Param.of(zoneId).stationId(dto.getStationId());
                int nowYear = Year.now().getValue();
                for (int i = beginYear; i <= nowYear; i++) {
                    endTime = beginTime.plusYears(1);
                    String iql1 = "select sum(data) as data from (select last(dailyEnergy) as data from %s where powerStationId = '{stationId}' and deviceType = 1 and time >= %s and time < %s group by time(1d),deviceId {tz})";
                    String iql2 = "select sum(stageEnergy) as data from %s where powerStationId = '{stationId}' and time >= %s and time < %s {tz}";
                    String begin = param.getSecond(beginTime), end = param.getSecond(endTime);
                    iqlList.add(iql1.formatted(DeviceData.MN, begin, end));
                    iqlList.add(iql2.formatted(MeterData.MN, begin, end));
                    beginTime = endTime;
                }
                List<BigDecimal> list = influxMapper.queryMultiObjData(param.iql(iqlList));
                List<List<Object>> consumer = new ArrayList<>();
                List<List<Object>> producer = new ArrayList<>();
                String time = null;
                for (int i = 0; i < list.size(); i++) {
                    if (i % 2 == 0) {
                        time = String.valueOf(beginYear++);
                        consumer.add(Tools.getTimeData(time, list.get(i)));
                    } else {
                        producer.add(Tools.getTimeData(time, list.get(i).add((BigDecimal) consumer.get(i / 2).get(1))));
                    }
                }
                return new PowerStationHistoryDataVO(consumer, producer);
            }
        }
        return null;
    }

    /**
     * 每日发电量对比
     */
    public StationDailyEnergyDataVO queryPowerStationDailyEnergyData(Integer stationId) {
        ZoneId zoneId = Sql.getZoneId(stationId);
        ZonedDateTime now = ZonedDateTime.now(zoneId);
        ZonedDateTime lastMonth = now.plusMonths(-11);
        ZonedDateTime beginTime = ZonedDateTime.of(lastMonth.getYear(), lastMonth.getMonthValue(), 1, 0, 0, 0, 0, zoneId);
        ZonedDateTime endTime = beginTime.plusYears(1);
        String iql = "select sum(data) as data from (select last(dailyEnergy) as data from {table} where powerStationId = '{stationId}' and deviceType = 1 and time >= {begin} and time < {end} group by time(1d),deviceId {tz}) group by time(1d) {tz}";
        Param param = Param.of(DeviceData.MN, zoneId, stationId, beginTime, endTime, iql).format(TimeZoneUtil.yyyyMMdd).dataNotNull();
        Map<String, BigDecimal> dataMap = Tools.getMap(influxMapper.queryList(param));
        List<String> yaxis = new ArrayList<>(12);
        List<List<Object>> listList = new ArrayList<>(372);
        BigDecimal max = BigDecimal.ZERO;
        LocalDate today = now.toLocalDate();
        for (int m = 11, x = 0; m >= 0; m--, x++) {
            LocalDate month = beginTime.toLocalDate().plusMonths(m);
            int lastDayOfMonth = month.with(TemporalAdjusters.lastDayOfMonth()).getDayOfMonth();
            for (int y = 0; y < 31; y++) {
                if (y < lastDayOfMonth) {
                    LocalDate day = month.plusDays(y);
                    if (day.isAfter(today)) {
                        listList.add(List.of(x, y, "-"));
                    } else {
                        String time = TimeZoneUtil.yyyyMMdd.format(day);
                        BigDecimal data = dataMap.getOrDefault(time, BigDecimal.ZERO);
                        if (data.compareTo(max) > 0) max = data;
                        listList.add(List.of(x, y, data));
                    }
                } else {
                    listList.add(List.of(x, y, "-"));
                }
            }
            yaxis.add(month.format(TimeZoneUtil.yyyyMM));
        }
        if (max.compareTo(BigDecimal.ZERO) > 0) {
            max = max.setScale(0, RoundingMode.UP);
        } else {
            max = BigDecimal.ONE;
        }
        return new StationDailyEnergyDataVO(yaxis, listList, max);
    }

    public void queryPowerStationDailyEnergyDataDownload(HttpServletResponse response, Integer stationId) throws IOException {
        ZoneId zoneId = Sql.getZoneId(stationId);
        ZonedDateTime now = ZonedDateTime.now(zoneId);
        ZonedDateTime lastMonth = now.plusMonths(-11);

        PowerStation powerStation = powerStationService.getById(stationId);
        ZonedDateTime zonedDateTime = powerStation.getCreateTime().atZone(zoneId);
        if (zonedDateTime.isAfter(lastMonth)) {
            lastMonth = zonedDateTime;
        }

        ZonedDateTime beginTime = ZonedDateTime.of(lastMonth.getYear(), lastMonth.getMonthValue(), lastMonth.getDayOfMonth(), 0, 0, 0, 0, zoneId);
        ZonedDateTime endTime = beginTime.plusYears(1);
        String iql = "select sum(data) as data from (select last(dailyEnergy) as data from {table} where powerStationId = '{stationId}' and deviceType = 1 and time >= {begin} and time < {end} group by time(1d),deviceId {tz}) group by time(1d) {tz}";
        Param param = Param.of(DeviceData.MN, zoneId, stationId, beginTime, endTime, iql).format(TimeZoneUtil.yyyyMMdd).dataNotNull();
        Map<String, BigDecimal> dataMap = Tools.getMap(influxMapper.queryList(param));
        response.setContentType("text/csv");
        response.setCharacterEncoding(CharsetUtil.GBK);
        response.setHeader("Content-Disposition", "attachment; filename=\"file.csv\"");
        try (PrintWriter writer = response.getWriter()) {
            writer.append(I18nUtil.get("日期,发电量")).append("\n");
            LocalDate month = beginTime.toLocalDate();
            for (int x = 0; x < 12 && !month.isAfter(now.toLocalDate()); x++) {
                month = beginTime.toLocalDate().plusMonths(x);
                int lastDayOfMonth = month.with(TemporalAdjusters.lastDayOfMonth()).getDayOfMonth();
                for (int y = 0; y < lastDayOfMonth && !month.plusDays(y).isAfter(now.toLocalDate()); y++) {
                    String time = TimeZoneUtil.yyyyMMdd.format(month.plusDays(y));
                    BigDecimal data = dataMap.getOrDefault(time, BigDecimal.ZERO);
                    writer.write(time + "," + data + "\n");
                }
            }
            writer.flush();
        }
    }

    public DeviceDataVO queryNowInvAttrData(Integer invId) {
        StationDevice device = stationDeviceService.getById(invId);
        String iql = "select * from {table} where sn = '{deviceSn}' and time <= now() order by time desc limit 1";
        Param param = Param.of(DeviceData.MN, iql).deviceSn(device.getNumber());
        return influxMapper.queryObj(param, DeviceDataVO.class);
    }

    /**
     * 按上报时间查询历史数据详情
     */
    public Object queryHistoryInvAttrData(QueryDataDTO dto) {
        ZoneId zoneId = Sql.getZoneId(dto.getPowerStationId());
        String invStr = dto.getInvId() == null ? "" : " and pid = '" + dto.getInvId() + "'";
        ZonedDateTime beginTime, endTime;
        if (dto.getQueryType() == 1) {
            beginTime = LocalDateTime.parse(dto.getQueryTime() + " 00:00", TimeZoneUtil.yyyyMMddHHmm).atZone(zoneId);
            endTime = beginTime.plusDays(1);
        } else {
            if (dto.getQueryTime().length() == 10) {
                endTime = LocalDateTime.parse(dto.getQueryTime() + " 23:59:59", TimeZoneUtil.yyyyMMddHHmmss).atZone(zoneId);
                beginTime = endTime.toLocalDate().plusDays(-6).atStartOfDay().atZone(zoneId);
            } else {
                List<Integer> time = Arrays.stream(dto.getQueryTime().split("-")).map(Integer::parseInt).toList();
                beginTime = LocalDate.ofYearDay(time.get(0), 1).with(WeekFields.ISO.weekOfYear(), time.get(1)).with(ChronoField.DAY_OF_WEEK, 1).atStartOfDay().atZone(zoneId);
                endTime = beginTime.plusDays(7);
            }
        }
        ZonedDateTime now = ZonedDateTime.now(zoneId);
        if (endTime.isAfter(now)) {
            endTime = now;
        }
        List<JSONObject> result = new ArrayList<>();
        if (dto.getDataType() == 1) {
            String iql = "select last(pvTotalPower) as pvTotalPower, last(pvTempPower) as pvTempPower, last(pv2TempPower) as pv2TempPower, last(pv3TempPower) as pv3TempPower, last(pv4TempPower) as pv4TempPower" +
                    ", last(pv5TempPower) as pv5TempPower, last(pv6TempPower) as pv6TempPower, last(pv7TempPower) as pv7TempPower, last(pv8TempPower) as pv8TempPower " +
                    "from {table} where powerStationId = '{stationId}'%s and deviceType = 1 and time >= {begin} and time < {end} group by time(15m),deviceId";
            Param param = Param.of(DeviceData.MN, zoneId, dto.getPowerStationId(), beginTime, endTime, iql.formatted(invStr));
            Map<JSONObject, List<JSONObject>> data = influxMapper.queryGroupList(param.format(TimeZoneUtil.yyyyMMddHHmmss));
            if (data.isEmpty()) {
                do {
                    String time = TimeZoneUtil.yyyyMMddHHmmss.format(beginTime.toLocalDateTime());
                    JSONObject obj = new JSONObject(2);
                    obj.put(TIME, time);
                    obj.put(DATA, "-");
                    result.add(obj);
                    beginTime = beginTime.plusMinutes(15);
                } while (beginTime.isBefore(now) && beginTime.isBefore(endTime));
            } else {
                for (Map.Entry<JSONObject, List<JSONObject>> map : data.entrySet()) {
                    String deviceId = map.getKey().getString(DEVICE_ID);
                    Map<String, ArrTotal<BigDecimal>> values = Tools.getListMap(map.getValue());
                    if (result.isEmpty()) {
                        do {
                            String time = TimeZoneUtil.yyyyMMddHHmmss.format(beginTime.toLocalDateTime());
                            JSONObject obj = new JSONObject(3);
                            obj.put(TIME, time);
                            ArrTotal<BigDecimal> val = values.get(time);
                            if (val == null) {
                                obj.put(deviceId, "-");
                                obj.put(DATA, "-");
                            } else {
                                obj.put(deviceId, val.getArr());
                                obj.put(DATA, val.getTotal());
                            }
                            result.add(obj);
                            beginTime = beginTime.plusMinutes(15);
                        } while (beginTime.isBefore(now) && beginTime.isBefore(endTime));
                    } else {
                        for (JSONObject obj : result) {
                            String time = obj.getString(TIME);
                            ArrTotal<BigDecimal> val = values.get(time);
                            if (val == null || val.getTotal()== null) {
                                obj.put(deviceId, "-");
                            } else {
                                obj.put(deviceId, val.getArr());
                                Object o = obj.get(DATA);
                                if (o instanceof BigDecimal d) {
                                    obj.put(DATA, d.add(val.getTotal()));
                                } else {
                                    obj.put(DATA, val.getTotal());
                                }
                            }
                        }
                    }
                }
                String lastIql = "select last(pvTempPower) as data from {table} where powerStationId = '{stationId}'%s and deviceType = 1 ";
                Param param1 = Param.of(DeviceData.MN, zoneId, dto.getPowerStationId(), lastIql.formatted(invStr));
                JSONObject lastData = influxMapper.queryObj(param1.format(TimeZoneUtil.yyyyMMddHHmmss));
                if(lastData != null) {
                    // 删除最后一个
                    JSONObject jsonObject = result.get(result.size() - 1);
                    LocalDateTime lastTime = LocalDateTime.parse(jsonObject.get("time").toString(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    LocalDateTime lastDataTime = LocalDateTime.parse(lastData.get("time").toString(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    if (lastDataTime.plusMinutes(15L).isAfter(lastTime) && (null == jsonObject.get("data") || "-".equals(jsonObject.get("data")))) {
                        result.remove(result.size() - 1);
                    }
                }
            }
        } else {
            String iql = "select sum(pvEnergy) as pvEnergy, sum(pv2Energy) as pv2Energy, sum(pv3Energy) as pv3Energy, sum(pv4Energy) as pv4Energy, " +
                    "sum(pv5Energy) as pv5Energy, sum(pv6Energy) as pv6Energy, sum(pv7Energy) as pv7Energy, sum(pv8Energy) as pv8Energy from " +
                    "(select last(pvEnergy) as pvEnergy, last(pv2Energy) as pv2Energy, last(pv3Energy) as pv3Energy, last(pv4Energy) as pv4Energy, " +
                    "last(pv5Energy) as pv5Energy, last(pv6Energy) as pv6Energy, last(pv7Energy) as pv7Energy, last(pv8Energy) as pv8Energy " +
                    "from {table} where powerStationId = '{stationId}'%s and deviceType = 1 and time >= {begin} and time < {end} group by time(1d),deviceId {tz}) group by deviceId fill(0) {tz}";
            Param param = Param.of(DeviceData.MN, zoneId, dto.getPowerStationId(), beginTime, endTime, iql.formatted(invStr));
            Map<JSONObject, List<JSONObject>> data = influxMapper.queryGroupList(param);
            while (beginTime.isBefore(now) && beginTime.isBefore(endTime)) {
                ZonedDateTime lastTime = beginTime.plusMinutes(15);
                if (lastTime.isBefore(now)) {
                    beginTime = lastTime;
                } else {
                    break;
                }
            }
            int i = 0;
            JSONObject obj = new JSONObject(5);
            obj.put(TIME, TimeZoneUtil.yyyyMMddHHmmss.format(beginTime.toLocalDateTime()));
            if (data.isEmpty()) {
                obj.put(DATA, BigDecimal.ZERO);
                result.add(obj);
            } else {
                for (Map.Entry<JSONObject, List<JSONObject>> map : data.entrySet()) {
                    String deviceId = map.getKey().getString(DEVICE_ID);
                    if (i++ == 0) {
                        ArrTotal<BigDecimal> arrTotal = Tools.getArrTotal(map.getValue().get(0));
                        obj.put(DATA, arrTotal.getTotal());
                        obj.put(deviceId, arrTotal.getArr());
                    } else {
                        ArrTotal<BigDecimal> arrTotal = Tools.getArrTotal(map.getValue().get(0));
                        obj.put(DATA, obj.getBigDecimal(DATA).add(arrTotal.getTotal()));
                        obj.put(deviceId, arrTotal.getArr());
                    }
                }
                result.add(obj);
            }
        }
        return result;
    }

    /**
     * 每年发电量
     */
    public List<EnvironmentalBenefitsVO> queryYearOfEnergy(Integer stationId) {
        ZoneId zoneId = Sql.getZoneId(stationId);
        int nowYear = ZonedDateTime.now(zoneId).getYear();
        int createYear = powerStationService.getYear(stationId);
        ZonedDateTime beginTime = ZonedDateTime.of(createYear, 1, 1, 0, 0, 0, 0, zoneId);
        List<String> iqlList = new ArrayList<>();
        Param param = Param.of(DeviceData.MN, zoneId, stationId);
        for (int i = 0; i <= nowYear - createYear; i++) {
            ZonedDateTime endTime = beginTime.plusYears(1);
            String iql = "select sum(data) as data from (select last(dailyEnergy) as data from {table} where powerStationId = '{stationId}' and deviceType = 1 and time >= %s and time < %s group by time(1d),deviceId {tz})";
            iqlList.add(iql.formatted(param.getSecond(beginTime), param.getSecond(endTime)));
            beginTime = endTime;
        }
        List<BigDecimal> multiObj = influxMapper.queryMultiObjData(param.iql(iqlList));
        List<EnvironmentalBenefitsVO> result = new ArrayList<>();
        BigDecimal all = BigDecimal.ZERO;
        for (int i = 0; i < multiObj.size(); i++) {
            result.add(new EnvironmentalBenefitsVO(String.valueOf(createYear + i), multiObj.get(i)));
            all = all.add(multiObj.get(i));
        }
        result.add(new EnvironmentalBenefitsVO(null, all));
        return result;
    }

    /**
     * 每月发电量
     */
    public MonthOfEnergyVO queryMonthOfEnergy(MonthOfEnergyDTO dto) {
        ZoneId zoneId = Sql.getZoneId(dto.getStationId());
        Param param = Param.of(DeviceData.MN, zoneId, dto.getStationId());
        List<String> iqlList = new ArrayList<>();
        if (dto.getYear() == null) {
            int createYear = powerStationService.getYear(dto.getStationId());
            int endYear = Year.now().getValue();
            ZonedDateTime beginTime = ZonedDateTime.of(createYear, 1, 1, 0, 0, 0, 0, zoneId);
            for (int i = createYear; i <= endYear; i++) {
                ZonedDateTime endTime = beginTime.plusYears(1);
                String iql = "select sum(data) as data from (select last(dailyEnergy) as data from {table} where powerStationId = '{stationId}' and deviceType = 1 and time >= %s and time < %s group by time(1d),deviceId {tz})";
                iqlList.add(iql.formatted(param.getSecond(beginTime), param.getSecond(endTime)));
                beginTime = endTime;
            }
            List<BigDecimal> multiObj = influxMapper.queryMultiObjData(param.iql(iqlList));
            List<List<Object>> result = new ArrayList<>();
            for (int i = 0; i < multiObj.size(); i++) {
                result.add(Tools.getTimeData(String.valueOf(createYear + i), multiObj.get(i)));
            }
            return new MonthOfEnergyVO(result, Tools.covertUnit(result));
        } else {
            ZonedDateTime beginTime = ZonedDateTime.of(dto.getYear(), 1, 1, 0, 0, 0, 0, zoneId);
            ZonedDateTime now = ZonedDateTime.now(zoneId);
            for (int i = 0; i < 12 && beginTime.isBefore(now); i++) {
                ZonedDateTime endTime = beginTime.plusMonths(1);
                String iql = "select sum(data) as data from (select last(dailyEnergy) as data from {table} where powerStationId = '{stationId}' and deviceType = 1 and time >= %s and time < %s group by time(1d),deviceId {tz})";
                iqlList.add(iql.formatted(param.getSecond(beginTime), param.getSecond(endTime)));
                beginTime = endTime;
            }
            List<BigDecimal> multiObj = influxMapper.queryMultiObjData(param.iql(iqlList));
            List<List<Object>> result = new ArrayList<>();
            for (int i = 0; i < multiObj.size(); i++) {
                result.add(Tools.getTimeData(String.valueOf(i + 1), multiObj.get(i)));
            }
            return new MonthOfEnergyVO(result, Tools.covertUnit(result));
        }
    }

    public List<EnergyByStationVO> queryEnergyByStation(QueryEnergyByStationDTO dto) {
        ZoneId zoneId = Sql.getZoneId(dto.getStationId());
        ZonedDateTime now = ZonedDateTime.now(zoneId);
        if (dto.getYear() == null) {
            int createYear = powerStationService.getYear(dto.getStationId());
            int endYear = now.getYear();
            Param param = Param.of(DeviceData.MN, zoneId, dto.getStationId());
            List<String> iqlList = new ArrayList<>();
            for (int i = createYear; i <= endYear; i++) {
                ZonedDateTime beginTime = ZonedDateTime.of(createYear, 1, 1, 0, 0, 0, 0, zoneId);
                for (int m = 1; m <= 12 && (i != endYear || m <= now.getMonthValue()); m++) {
                    ZonedDateTime endTime = beginTime.plusMonths(1);
                    String iql = "select sum(data) as data from (select last(dailyEnergy) as data from {table} where powerStationId = '{stationId}' and deviceType = 1 and time >= %s and time < %s group by time(1d),deviceId {tz})";
                    iqlList.add(iql.formatted(param.getSecond(beginTime), param.getSecond(endTime)));
                    beginTime = endTime;
                }
            }
            List<JSONObject> list = influxMapper.queryMultiObj(param.iql(iqlList));
            List<EnergyByStationVO> result = new ArrayList<>();
            for (int i = createYear; i <= endYear; i++) {
                EnergyByStationVO vo = new EnergyByStationVO();
                vo.setSn(i);
                List<List<Object>> ms = new ArrayList<>(12);
                for (int m = 1; m <= 12; m++) {
                    BigDecimal val;
                    if (list.size() < m) {
                        val = BigDecimal.ZERO;
                    } else {
                        val = Optional.ofNullable(list.get(m - 1).getBigDecimal(TopicService.DATA)).orElse(BigDecimal.ZERO);
                    }
                    if (createYear == endYear && m > now.getMonthValue()) {
                        ms.add(List.of(m, "-"));
                    } else {
                        ms.add(List.of(m, val));
                    }
                }
                vo.setUnit(Tools.covertUnit(ms));
                vo.setData(ms);
                result.add(vo);
            }
            return result;
        }
        switch (dto.getType()) {
            case 1 -> {
                ZonedDateTime beginTime = ZonedDateTime.of(dto.getYear(), now.getMonthValue(), 1, 0, 0, 0, 0, zoneId);
                ZonedDateTime endTime = beginTime.plusMonths(1);
                if (endTime.isAfter(now)) {
                    endTime = now;
                }
                String iql = "select sum(data) as data from (select last(dailyEnergy) as data,last(pid) as pid from {table} where powerStationId = '{stationId}' and deviceType = 1 and time >= {begin} and time < {end} group by time(1d),deviceId {tz}) where time >= {begin} and time < {end} group by time(1d),pid fill(0) {tz}";
                Param param = Param.of(DeviceData.MN, zoneId, dto.getStationId(), beginTime, endTime, iql);
                Map<JSONObject, List<JSONObject>> map = influxMapper.queryGroupList(param.format(TimeZoneUtil.yyyyMMdd));
                List<Integer> snIds = map.keySet().stream().map(tags -> tags.getInteger(PID)).toList();
                if (snIds.isEmpty()) {
                    return Collections.emptyList();
                }
                Map<Integer, String> idSnMap = stationDeviceService.getIdSnMap(snIds);
                List<EnergyByStationVO> result = new ArrayList<>(map.size());
                for (Map.Entry<JSONObject, List<JSONObject>> m : map.entrySet()) {
                    EnergyByStationVO vo = new EnergyByStationVO();
                    vo.setId(m.getKey().getInteger(PID));
                    vo.setSn(idSnMap.get(vo.getId()));
                    vo.setData(m.getValue().stream().map(d -> Tools.getTimeData(d.getString(TIME), d.getBigDecimal(DATA))).toList());
                    vo.setUnit(Tools.covertUnit(vo.getData()));
                    result.add(vo);
                }
                return result;
            }
            case 3 -> {
                ZonedDateTime beginTime = ZonedDateTime.of(dto.getYear(), 1, 1, 0, 0, 0, 0, zoneId);
                Param param = Param.of(DeviceData.MN, zoneId, dto.getStationId());
                List<String> iqlList = new ArrayList<>(12);
                List<String> months = new ArrayList<>(12);
                for (int i = 0; i < 12 && beginTime.isBefore(now); i++) {
                    ZonedDateTime endTime = beginTime.plusMonths(1);
                    String iql = "select sum(data) as data from (select sum(data) as data from (select sum(data) as data from (select last(dailyEnergy) as data,last(pid) as pid from {table} where powerStationId = '{stationId}' and deviceType = 1 and time >= %s and time < %s group by time(1d),deviceId {tz}) group by time(1d),pid {tz})) group by pid";
                    iqlList.add(iql.formatted(param.getSecond(beginTime), param.getSecond(endTime)));
                    months.add(TimeZoneUtil.M.format(beginTime.toLocalDateTime()));
                    beginTime = endTime;
                }
                List<Map<Integer, List<JSONObject>>> list = influxMapper.queryMultiGroupList(param.iql(iqlList), "pid", Integer.class);
                Set<Integer> snIds = list.stream().flatMap(m -> m.keySet().stream()).collect(Collectors.toSet());
                if (snIds.isEmpty()) {
                    return Collections.emptyList();
                }
                Map<Integer, String> idSnMap = stationDeviceService.getIdSnMap(snIds);
                List<EnergyByStationVO> result = new ArrayList<>(list.size());
                for (Integer snId : snIds) {
                    EnergyByStationVO vo = new EnergyByStationVO();
                    vo.setId(snId);
                    vo.setSn(idSnMap.get(vo.getId()));
                    List<List<Object>> dataList = new ArrayList<>();
                    for (int i = 0; i < months.size(); i++) {
                        List<JSONObject> arr = list.get(i).get(snId);
                        if (arr == null) {
                            dataList.add(List.of(months.get(i), BigDecimal.ZERO));
                        } else {
                            JSONObject json = arr.get(0);
                            dataList.add(Tools.getTimeData(months.get(i), json.getBigDecimal(DATA), true));
                        }
                    }
                    vo.setData(dataList);
                    vo.setUnit(Tools.covertUnit(vo.getData()));
                    result.add(vo);
                }
                return result;
            }
        }
        return null;
    }

    public List<EmuEnergyDataVO> queryEmuEnergyData(QueryEmuEnergyDataDTO dto) {
        ZoneId zoneId = Sql.getZoneId(dto.getStationId());
        String pidTerm = influxMapper.orTag(PID, dto.getIds());
        ZonedDateTime now = ZonedDateTime.now(zoneId);
        if (dto.getDataType() == 1) {
            switch (dto.getType()) {
                case 1 -> {
                    ZonedDateTime beginTime = LocalDateTime.parse(dto.getDate() + " 00:00", TimeZoneUtil.yyyyMMddHHmm).atZone(zoneId);
                    ZonedDateTime endTime = beginTime.plusDays(1);
                    String iql = "select sum(data) as data from (select last(dailyEnergy) as data,last(pid) as pid from {table} where powerStationId = '{stationId}' and (%s) and deviceType = 1 and time >= {begin} and time < {end} group by time(15m),deviceId) group by time(15m),pid";
                    Param param = Param.of(DeviceData.MN, zoneId, dto.getStationId(), beginTime, endTime, iql.formatted(pidTerm)).format(TimeZoneUtil.HHmm).dataNotNull();
                    Map<JSONObject, List<JSONObject>> map = influxMapper.queryGroupList(param);
                    return EmuEnergyDataVO.of(dto, map, beginTime, endTime, now);
                }
                case 3 -> {
                    ZonedDateTime beginTime = LocalDateTime.parse(dto.getDate() + "-01 00:00", TimeZoneUtil.yyyyMMddHHmm).atZone(zoneId);
                    ZonedDateTime endTime = beginTime.plusMonths(1);
                    String iql = "select sum(data) as data from (select last(dailyEnergy) as data,last(pid) as pid from {table} where powerStationId = '{stationId}' and (%s) and deviceType = 1 and time >= {begin} and time < {end} group by time(1d),deviceId {tz}) group by time(1d),pid fill(0) {tz}";
                    Param param = Param.of(DeviceData.MN, zoneId, dto.getStationId(), beginTime, endTime, iql.formatted(pidTerm)).format(TimeZoneUtil.yyyyMMdd).dataNotNull();
                    Map<JSONObject, List<JSONObject>> map = influxMapper.queryGroupList(param);
                    return EmuEnergyDataVO.of(dto, map, beginTime, endTime, now);
                }
                case 4 -> {
                    ZonedDateTime beginTime = LocalDateTime.parse(dto.getDate() + "-01-01 00:00", TimeZoneUtil.yyyyMMddHHmm).atZone(zoneId);
                    Param param = Param.of(DeviceData.MN, zoneId, dto.getStationId());
                    List<String> iqlList = new ArrayList<>(12);
                    List<String> months = new ArrayList<>(12);
                    for (int i = 0; i < 12 && beginTime.isBefore(now); i++) {
                        ZonedDateTime endTime = beginTime.plusMonths(1);
                        String iql = "select sum(data) as data from (select last(dailyEnergy) as data,last(pid) as pid from {table} where powerStationId = '{stationId}' and (%s) and deviceType = 1 and time >= %s and time < %s group by time(1d),deviceId {tz}) group by pid fill(0) {tz}";
                        iqlList.add(iql.formatted(pidTerm, param.getSecond(beginTime), param.getSecond(endTime)));
                        months.add(TimeZoneUtil.M.format(beginTime.toLocalDateTime()));
                        beginTime = endTime;
                    }
                    List<Map<Integer, List<JSONObject>>> list = influxMapper.queryMultiGroupList(param.iql(iqlList), "pid", Integer.class);
                    return EmuEnergyDataVO.of(dto, months, list);
                }
                case 5 -> {
                    Integer beginYear = powerStationService.getYear(dto.getStationId());
                    ZonedDateTime beginTime = ZonedDateTime.of(beginYear, 1, 1, 0, 0, 0, 0, zoneId);
                    Param param = Param.of(DeviceData.MN, zoneId, dto.getStationId());
                    int nowYear = Year.now().getValue();
                    List<String> iqlList = new ArrayList<>();
                    List<String> years = new ArrayList<>();
                    for (int i = beginYear; i <= nowYear; i++) {
                        ZonedDateTime endTime = beginTime.plusYears(1);
                        String iql = "select sum(data) as data from (select last(dailyEnergy) as data,last(pid) as pid from {table} where powerStationId = '{stationId}' and (%s) and deviceType = 1 and time >= %s and time < %s group by time(1d),deviceId {tz}) group by pid fill(0) {tz}";
                        iqlList.add(iql.formatted(pidTerm, param.getSecond(beginTime), param.getSecond(endTime)));
                        years.add(TimeZoneUtil.yyyy.format(beginTime.toLocalDateTime()));
                        beginTime = endTime;
                    }
                    List<Map<Integer, List<JSONObject>>> list = influxMapper.queryMultiGroupList(param.iql(iqlList), "pid", Integer.class);
                    return EmuEnergyDataVO.of(dto, years, list);
                }
            }
        } else {
            switch (dto.getType()) {
                case 1 -> {
                    ZonedDateTime beginTime = LocalDateTime.parse(dto.getDate() + " 00:00", TimeZoneUtil.yyyyMMddHHmm).atZone(zoneId);
                    ZonedDateTime endTime = beginTime.plusDays(1);
                    String iql = "select last(acTempPower) as data from {table} where powerStationId = '{stationId}' and (%s) and deviceType = 1 and time >= {begin} and time < {end} group by time(15m),pid";
                    Param param = Param.of(DeviceData.MN, zoneId, dto.getStationId(), beginTime, endTime, iql.formatted(pidTerm)).format(TimeZoneUtil.HHmm).dataNotNull();
                    Map<JSONObject, List<JSONObject>> map = influxMapper.queryGroupList(param);
                    return EmuEnergyDataVO.of(dto, map, beginTime, endTime, now);
                }
                case 3 -> {
                    ZonedDateTime beginTime = LocalDateTime.parse(dto.getDate() + "-01 00:00", TimeZoneUtil.yyyyMMddHHmm).atZone(zoneId);
                    ZonedDateTime endTime = beginTime.plusMonths(1);
                    String iql = "select last(acTempPower) as data from {table} where powerStationId = '{stationId}' and (%s) and deviceType = 1 and time >= {begin} and time < {end} group by time(1d),pid {tz}";
                    Param param = Param.of(DeviceData.MN, zoneId, dto.getStationId(), beginTime, endTime, iql.formatted(pidTerm)).format(TimeZoneUtil.yyyyMMdd).dataNotNull();
                    Map<JSONObject, List<JSONObject>> map = influxMapper.queryGroupList(param);
                    return EmuEnergyDataVO.of(dto, map, beginTime, endTime, now);
                }
                case 4 -> {
                    ZonedDateTime beginTime = LocalDateTime.parse(dto.getDate() + "-01-01 00:00", TimeZoneUtil.yyyyMMddHHmm).atZone(zoneId);
                    Param param = Param.of(DeviceData.MN, zoneId, dto.getStationId()).format(TimeZoneUtil.M);
                    List<String> iqlList = new ArrayList<>(12);
                    List<String> months = new ArrayList<>(12);
                    for (int i = 0; i < 12 && beginTime.isBefore(now); i++) {
                        ZonedDateTime endTime = beginTime.plusMonths(1);
                        String iql = "select last(acTempPower) as data from {table} where powerStationId = '{stationId}' and (%s) and deviceType = 1 and time >= %s and time < %s group by pid";
                        iqlList.add(iql.formatted(pidTerm, param.getSecond(beginTime), param.getSecond(endTime)));
                        months.add(TimeZoneUtil.M.format(beginTime.toLocalDateTime()));
                        beginTime = endTime;
                    }
                    Map<JSONObject, Map<String, JSONObject>> map = influxMapper.queryMultiGroupObj(param.iql(iqlList));
                    return EmuEnergyDataVO.of(dto, months, map);
                }
                case 5 -> {
                    Integer beginYear = powerStationService.getYear(dto.getStationId());
                    ZonedDateTime beginTime = ZonedDateTime.of(beginYear, 1, 1, 0, 0, 0, 0, zoneId);
                    Param param = Param.of(DeviceData.MN, zoneId, dto.getStationId()).format(TimeZoneUtil.yyyy);
                    int nowYear = Year.now().getValue();
                    List<String> iqlList = new ArrayList<>();
                    List<String> years = new ArrayList<>();
                    for (int i = beginYear; i <= nowYear; i++) {
                        ZonedDateTime endTime = beginTime.plusYears(1);
                        String iql = "select last(acTempPower) as data from {table} where powerStationId = '{stationId}' and (%s) and deviceType = 1 and time >= %s and time < %s group by pid";
                        iqlList.add(iql.formatted(pidTerm, param.getSecond(beginTime), param.getSecond(endTime)));
                        years.add(TimeZoneUtil.yyyy.format(beginTime.toLocalDateTime()));
                        beginTime = endTime;
                    }
                    Map<JSONObject, Map<String, JSONObject>> map = influxMapper.queryMultiGroupObj(param.iql(iqlList));
                    return EmuEnergyDataVO.of(dto, years, map);
                }
            }
        }
        return null;
    }

    public List<QueryEnergyDataVO> queryMicroInverterData(QueryMicroInverterDataDTO dto) {
        ZoneId zoneId = Sql.getZoneId(dto.getStationId());
        String term = influxMapper.orTag(DEVICE_ID, dto.getIds());
        ZonedDateTime beginTime, endTime;
        if (dto.getType() == 1) {
            beginTime = LocalDateTime.parse(dto.getDate() + " 00:00", TimeZoneUtil.yyyyMMddHHmm).atZone(zoneId);
            endTime = beginTime.plusDays(1);
        } else {
            List<Integer> time = Arrays.stream(dto.getDate().split("-")).map(Integer::parseInt).toList();
            beginTime = LocalDate.ofYearDay(time.get(0), 1).with(WeekFields.ISO.weekOfYear(), time.get(1)).with(ChronoField.DAY_OF_WEEK, 1).atStartOfDay().atZone(zoneId);
            endTime = beginTime.plusDays(7);
            LocalDate now = ZonedDateTime.now(zoneId).toLocalDate();
            if (endTime.toLocalDate().isAfter(now)) {
                endTime = now.plusDays(1).atStartOfDay().atZone(zoneId);
            }
        }
        String iql = "select (last(pvTempPower)+last(pv2TempPower)+last(pv3TempPower)+last(pv4TempPower)+last(pv5TempPower)+last(pv6TempPower)+last(pv7TempPower)+last(pv8TempPower)) as pvTempPower, \n" +
                "(last(Pv1CurrReal)+last(Pv2CurrReal)+last(Pv3CurrReal)+last(Pv4CurrReal)+last(Pv5CurrReal)+last(Pv6CurrReal)+last(Pv7CurrReal)+last(Pv8CurrReal)) as pvCurrReal,\n" +
                "last(acTempPower) as acTempPower, last(acVolReal) as acVolReal, last(acCurrReal) as acCurrReal, last(gridFreq) as gridFreq, last(enviroMosTempReal) as enviroMosTempReal,\n " +
                "last(boostMosTempReal) as boostMosTempReal,  last(dabMosTempReal) as dabMosTempReal from {table} where powerStationId = '{stationId}' and (%s) and deviceType = 1 and time >= {begin} and time < {end} group by time(15m),deviceId {tz}";
        Param param = Param.of(DeviceData.MN, zoneId, dto.getStationId(), beginTime, endTime, iql.formatted(term))
                .format(dto.getType() == 1 ? TimeZoneUtil.HHmm : TimeZoneUtil.MMddHHmm);
        Map<Integer, List<JSONObject>> map = influxMapper.queryGroupList(param, DEVICE_ID, Integer.class);
        for (Integer id : dto.getIds()) {
            map.putIfAbsent(id, Collections.emptyList());
        }
        List<QueryEnergyDataVO> result = new ArrayList<>(dto.getIds().size() * 9);
        //获取总时间
        List<String> times = new ArrayList<>();
        do {
            times.add((dto.getType() == 1 ? TimeZoneUtil.HHmm : TimeZoneUtil.MMddHHmm).format(beginTime.toLocalDateTime()));
        } while ((beginTime = beginTime.plusMinutes(15)).isBefore(endTime));
        for (Map.Entry<Integer, List<JSONObject>> m : map.entrySet()) {
            List<List<Object>> acVolRealList = new ArrayList<>(times.size());
            List<List<Object>> acCurrRealList = new ArrayList<>(times.size());
            List<List<Object>> boostMosTempReal = new ArrayList<>(times.size());
            List<List<Object>> dabMosTempReal = new ArrayList<>(times.size());
            List<List<Object>> enviroMosTempReal = new ArrayList<>(times.size());
            List<List<Object>> acTempPowerList = new ArrayList<>(times.size());
            List<List<Object>> gridFreqList = new ArrayList<>(times.size());
            List<List<Object>> pvTempPowerList = new ArrayList<>(times.size());
            List<List<Object>> pvCurrRealList = new ArrayList<>(times.size());
            Map<String, JSONObject> dataMap = m.getValue().stream().collect(Collectors.toMap(json -> json.getString(TIME), json -> json));
            for (String timeStr : times) {
                JSONObject json = dataMap.get(timeStr);
                if (json != null) {
                    acTempPowerList.add(Tools.getTimeData(timeStr, json.getBigDecimal("acTempPower")));
                    acVolRealList.add(Tools.getTimeData(timeStr, json.getBigDecimal("acVolReal")));
                    acCurrRealList.add(Tools.getTimeData(timeStr, json.getBigDecimal("acCurrReal")));
                    gridFreqList.add(Tools.getTimeData(timeStr, json.getBigDecimal("gridFreq")));
                    enviroMosTempReal.add(Tools.getTimeData(timeStr, json.getBigDecimal("enviroMosTempReal")));
                    boostMosTempReal.add(Tools.getTimeData(timeStr, json.getBigDecimal("boostMosTempReal")));
                    dabMosTempReal.add(Tools.getTimeData(timeStr, json.getBigDecimal("dabMosTempReal")));
                    pvTempPowerList.add(Tools.getTimeData(timeStr, json.getBigDecimal("pvTempPower")));
                    pvCurrRealList.add(Tools.getTimeData(timeStr, json.getBigDecimal("pvCurrReal")));
                } else {
                    acTempPowerList.add(List.of(timeStr, "-"));
                    acVolRealList.add(List.of(timeStr, "-"));
                    acCurrRealList.add(List.of(timeStr, "-"));
                    gridFreqList.add(List.of(timeStr, "-"));
                    enviroMosTempReal.add(List.of(timeStr, "-"));
                    boostMosTempReal.add(List.of(timeStr, "-"));
                    dabMosTempReal.add(List.of(timeStr, "-"));
                    pvTempPowerList.add(List.of(timeStr, "-"));
                    pvCurrRealList.add(List.of(timeStr, "-"));
                }
            }
            result.add(new QueryEnergyDataVO(m.getKey(), 1, acTempPowerList));
            result.add(new QueryEnergyDataVO(m.getKey(), 2, acVolRealList));
            result.add(new QueryEnergyDataVO(m.getKey(), 3, acCurrRealList));
            result.add(new QueryEnergyDataVO(m.getKey(), 4, gridFreqList));
            result.add(new QueryEnergyDataVO(m.getKey(), 5, enviroMosTempReal));
            result.add(new QueryEnergyDataVO(m.getKey(), 6, dabMosTempReal));
            result.add(new QueryEnergyDataVO(m.getKey(), 7, boostMosTempReal));
            result.add(new QueryEnergyDataVO(m.getKey(), 8, pvTempPowerList));
            result.add(new QueryEnergyDataVO(m.getKey(), 9, pvCurrRealList));
        }
        return result;
    }

    public List<QueryEnergyDataExportVO> queryMicroInverterDataExport(QueryMicroInverterDataDTO dto) {
        ZoneId zoneId = Sql.getZoneId(dto.getStationId());
        String term = influxMapper.orTag(DEVICE_ID, dto.getIds());
        ZonedDateTime beginTime, endTime;
        ZonedDateTime now = ZonedDateTime.now(zoneId);
        if (dto.getType() == 1) {
            beginTime = LocalDateTime.parse(dto.getDate() + " 00:00", TimeZoneUtil.yyyyMMddHHmm).atZone(zoneId);
            endTime = beginTime.plusDays(1);
        } else {
            List<Integer> time = Arrays.stream(dto.getDate().split("-")).map(Integer::parseInt).toList();
            beginTime = LocalDate.ofYearDay(time.get(0), 1).with(WeekFields.ISO.weekOfYear(), time.get(1)).with(ChronoField.DAY_OF_WEEK, 1).atStartOfDay().atZone(zoneId);
            endTime = beginTime.plusDays(7);
            LocalDate today = ZonedDateTime.now(zoneId).toLocalDate();
            if (endTime.toLocalDate().isAfter(today)) {
                endTime = today.plusDays(1).atStartOfDay().atZone(zoneId);
            }
        }
        String iql = "select last(acTempPower) as power, last(acVolReal) as volReal, last(gridFreq) as gridFreq, last(acMosTempReal) as mosTempReal from {table} where powerStationId = '{stationId}' and (%s) and deviceType = 1 and time >= {begin} and time < {end} group by time(15m),deviceId {tz}";
        Param param = Param.of(DeviceData.MN, zoneId, dto.getStationId(), beginTime, endTime, iql.formatted(term));
        Map<JSONObject, List<JSONObject>> map = influxMapper.queryGroupList(param.format(TimeZoneUtil.yyyyMMddHHmm));
        List<QueryEnergyDataExportVO> result = new ArrayList<>();
        for (Map.Entry<JSONObject, List<JSONObject>> m : map.entrySet()) {
            Map<String, JSONObject> dataMap = m.getValue().stream().collect(Collectors.toMap(json -> json.getString(TIME), json -> json));
            do {
                String timeStr = TimeZoneUtil.yyyyMMddHHmm.format(beginTime.toLocalDateTime());
                QueryEnergyDataExportVO vo = new QueryEnergyDataExportVO();
                vo.setTime(timeStr);
                JSONObject json = dataMap.get(timeStr);
                vo.setPower(Objects.requireNonNullElse(json.getBigDecimal("power"), "-"));
                vo.setVolReal(Objects.requireNonNullElse(json.getBigDecimal("volReal"), "-"));
                vo.setGridFreq(Objects.requireNonNullElse(json.getBigDecimal("gridFreq"), "-"));
                vo.setMosTempReal(Objects.requireNonNullElse(json.getBigDecimal("mosTempReal"), "-"));
                result.add(vo);
            } while ((beginTime = beginTime.plusMinutes(15)).isBefore(endTime) && beginTime.isBefore(now));
        }
        return result;
    }

    public HomeHistoricalDataVO queryPowerStationEnergyData(StationDataDTO dto) {
        ZoneId zoneId = ZoneId.systemDefault();
        String termIds = powerStationService.getTermIds();
        Param param = Param.of(DeviceData.MN, zoneId).dataNotNull();
        switch (dto.getQueryType()) {
            case 3 -> {
                ZonedDateTime beginTime = LocalDateTime.parse(dto.getQueryTime() + "-01 00:00", TimeZoneUtil.yyyyMMddHHmm).atZone(zoneId);
                ZonedDateTime endTime = beginTime.plusMonths(1);
                String iql = "select sum(data) as data from (select last(dailyEnergy) as data from {table} where deviceType = 1%s and time >= {begin} and time < {end} group by time(1d),deviceId {tz}) group by time(1d) fill(0) {tz}";
                param.begin(beginTime).end(endTime).iql(iql.formatted(termIds)).format(TimeZoneUtil.d);
                List<JSONObject> list = influxMapper.queryList(param);
                return new HomeHistoricalDataVO(list, beginTime, endTime);
            }
            case 4 -> {
                ZonedDateTime beginTime = LocalDateTime.parse(dto.getQueryTime() + "-01-01 00:00", TimeZoneUtil.yyyyMMddHHmm).atZone(zoneId);
                List<String> iqlList = new ArrayList<>();
                List<String> months = new ArrayList<>();
                ZonedDateTime now = ZonedDateTime.now(zoneId);
                for (int i = 0; i < 12; i++) {
                    ZonedDateTime endTime = beginTime.plusMonths(1);
                    if (beginTime.isBefore(now)) {
                        String iql = "select sum(data) as data from (select last(dailyEnergy) as data from {table} where deviceType = 1%s and time >= %s and time < %s group by time(1d),deviceId {tz})";
                        iqlList.add(iql.formatted(termIds, param.getSecond(beginTime), param.getSecond(endTime)));
                    }
                    months.add(TimeZoneUtil.yyyyMM.format(beginTime.toLocalDateTime()));
                    beginTime = endTime;
                }
                param.iql(iqlList);
                List<JSONObject> list = influxMapper.queryMultiObj(param);
                return new HomeHistoricalDataVO(months, list);
            }
            case 5 -> {
                int minYear = 2025;
                int nowYear = Year.now().getValue();
                ZonedDateTime beginTime = ZonedDateTime.of(minYear, 1, 1, 0, 0, 0, 0, zoneId);
                List<String> iqlList = new ArrayList<>();
                List<String> years = new ArrayList<>();
                for (int i = 0; i <= nowYear - minYear; i++) {
                    ZonedDateTime endTime = beginTime.plusYears(1);
                    String iql = "select sum(data) as data from (select last(dailyEnergy) as data from {table} where deviceType = 1%s and time >= %s and time < %s group by time(1d),deviceId {tz})";
                    iqlList.add(iql.formatted(termIds, param.getSecond(beginTime), param.getSecond(endTime)));
                    years.add(TimeZoneUtil.yyyy.format(beginTime.toLocalDateTime()));
                    beginTime = endTime;
                }
                param.iql(iqlList);
                List<JSONObject> list = influxMapper.queryMultiObj(param);
                return new HomeHistoricalDataVO(years, list);
            }
        }
        return null;
    }

    /**
     * 获取电站数据-今日发电量，本月发电量，本年发电量，累计发电量
     */
    public PowerStationListDataVO energy() {
        String termIds;
        int count;
        List<PowerStation> powerStationList = null;
        List<Integer> ids = null;
        String dataScopeSql = dataScopeSqlId();
        switch (dataScopeSql) {
            case "" -> {
                termIds = "";
                powerStationList = powerStationService.lambdaQuery().list();
                count = powerStationList.size();
            }
            case FALSE -> {
                termIds = " and false";
                count = 0;
            }
            default -> {
                ids = Sql.getStationId(dataScopeSql);
                if (ids.isEmpty()) {
                    termIds = " and false";
                    count = 0;
                } else {
                    StringBuilder sb = new StringBuilder();
                    ids.forEach(id -> sb.append(" or powerStationId = '").append(id).append("'"));
                    termIds = " and (" + sb.substring(4) + ")";
                    count = ids.size();
                }
            }
        }

        if (CollUtil.isEmpty(powerStationList) && CollUtil.isNotEmpty(ids)) {
            powerStationList = powerStationService.listByIds(ids);
        }
        PowerStationListDataVO extend = new PowerStationListDataVO();
        if (CollUtil.isNotEmpty(powerStationList)) {
            Map<String, List<Integer>> collect = powerStationList.parallelStream().collect(Collectors.groupingBy(PowerStation::getTimeZone,
                    Collectors.mapping(PowerStation::getId, Collectors.toList())));
            for (String timeZone : collect.keySet()) {

                List<Integer> powerStationIds = collect.get(timeZone);
                if (CollUtil.isEmpty(powerStationIds)) {
                    continue;
                }
                String powerStationIdString = CollUtil.join(powerStationIds, "|");
                ZoneId zoneId = ZoneId.of(timeZone);
                ZonedDateTime now = ZonedDateTime.now(zoneId);
                List<String> iqlList = new ArrayList<>(4);
                Param param = Param.of(DeviceData.MN, TimeZoneUtil.getZoneId(timeZone));

                ZonedDateTime beginTime = LocalDateTime.of(now.toLocalDate(), LocalTime.MIN).atZone(zoneId);
                ZonedDateTime endTime = beginTime.plusDays(1);
                iqlList.add("select sum(data) as data from (select last(dailyEnergy) as data from {table} where powerStationId =~ /^(%s)$/ and deviceType = 1%s and time >= %s and time < %s group by time(1d),deviceId {tz})"
                        .formatted(powerStationIdString, termIds, param.getSecond(beginTime), param.getSecond(endTime)));

                beginTime = LocalDateTime.of(now.toLocalDate().with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN).atZone(zoneId);
                endTime = beginTime.plusMonths(1);
                iqlList.add("select sum(data) as data from (select last(dailyEnergy) as data from {table} where powerStationId =~ /^(%s)$/ and deviceType = 1%s and time >= %s and time < %s group by time(1d),deviceId {tz})"
                        .formatted(powerStationIdString, termIds, param.getSecond(beginTime), param.getSecond(endTime)));

                beginTime = LocalDateTime.of(now.toLocalDate().with(TemporalAdjusters.firstDayOfYear()), LocalTime.MIN).atZone(zoneId);
                endTime = beginTime.plusYears(1);
                iqlList.add("select sum(data) as data from (select last(dailyEnergy) as data from {table} where powerStationId =~ /^(%s)$/ and deviceType = 1%s and time >= %s and time < %s group by time(1d),deviceId {tz})"
                        .formatted(powerStationIdString, termIds, param.getSecond(beginTime), param.getSecond(endTime)));

                iqlList.add("select sum(data) as data from (select last(dailyEnergy) as data from {table} where powerStationId =~ /^(%s)$/ and deviceType = 1%s group by time(1d),deviceId {tz})".formatted(powerStationIdString, termIds));
                List<BigDecimal> list = influxMapper.queryMultiList(param.iql(iqlList)).stream().map(arr -> {
                    if (arr.isEmpty()) {
                        return BigDecimal.ZERO;
                    }
                    return arr.get(0).getBigDecimal(DATA);
                }).toList();

                extend.setDayKwh(extend.getDayKwh().add(list.get(0)));
                extend.setMonthKwh(extend.getMonthKwh().add(list.get(1)));
                extend.setYearKwh(extend.getYearKwh().add(list.get(2)));
                extend.setAllKwh(extend.getAllKwh().add(list.get(3)));
            }
        }

        extend.setCount(count);
        DataUnit day = Tools.covertKwhUnit(extend.getDayKwh());
        extend.setDayKwh(day.getData());
        extend.setDayKwhUnit(day.getUnit());
        DataUnit month = Tools.covertKwhUnit(extend.getMonthKwh());
        extend.setMonthKwh(month.getData());
        extend.setMonthKwhUnit(month.getUnit());
        DataUnit year = Tools.covertKwhUnit(extend.getYearKwh());
        extend.setYearKwh(year.getData());
        extend.setYearKwhUnit(year.getUnit());
        DataUnit all = Tools.covertKwhUnit(extend.getAllKwh());
        extend.setAllKwh(all.getData());
        extend.setAllKwhUnit(all.getUnit());
        return extend;
    }

    public HomeEnvironmentalBenefitsVO queryYearOfEnergy(ClientTypeEnum clientType) {
        ZoneId zoneId = ZoneId.systemDefault();
        String termIds = powerStationService.getTermIds();
        YearMonth ym = YearMonth.now();
        ZonedDateTime beginTime = ym.atDay(1).atStartOfDay().atZone(zoneId);
        ZonedDateTime endTime = beginTime.plusMonths(1);
        List<String> iql = new ArrayList<>(2);
        Param param = Param.of(DeviceData.MN, zoneId);
        iql.add("select sum(data) as data from (select last(dailyEnergy) as data from {table} where deviceType = 1%s and time >= %s and time < %s group by time(1d),deviceId {tz})".formatted(termIds, param.getSecond(beginTime), param.getSecond(endTime)));
        iql.add("select sum(data) as data from (select last(dailyEnergy) as data from {table} where deviceType = 1%s group by time(1d),deviceId {tz})".formatted(termIds));
        if (clientType == ClientTypeEnum.PC) {
            iql.add("select sum(data) as data from (select last(dailyEnergy) as data from {table} where deviceType = 1%s and time >= %s and time < %s group by time(1d),deviceId {tz})".formatted(termIds, param.getSecond(beginTime.plusYears(-1)), param.getSecond(endTime.plusYears(-1))));
        }
        List<BigDecimal> list = influxMapper.queryMultiObjData(param.iql(iql));
        return new HomeEnvironmentalBenefitsVO(list);
    }

    public PowerConsumptionVO powerConsumption(Integer id) {
        PowerStation station = powerStationService.getByIdThrow(id);
        ZoneId zoneId = Sql.getZoneId(id);
        ZonedDateTime now = ZonedDateTime.now(zoneId);
        List<String> iqlList = new ArrayList<>();
        Param param = Param.of(zoneId).stationId(id);
        // 日发电和用电量
        ZonedDateTime beginTime = now.toLocalDate().atStartOfDay().atZone(zoneId);
        ZonedDateTime endTime = beginTime.plusDays(1);
        iqlList.add("select sum(data) as data from (select last(dailyEnergy) as data from %s where powerStationId = '{stationId}' and deviceType = 1 and time >= %s and time < %s group by time(1d),deviceId {tz})"
                .formatted(DeviceData.MN, param.getSecond(beginTime), param.getSecond(endTime)));
        iqlList.add("select sum(stageEnergy) as data from %s where powerStationId = '{stationId}' and powerGridSeat = 2 and time >= %s and time < %s"
                .formatted(MeterData.MN, param.getSecond(beginTime), param.getSecond(endTime)));
        // 月发电和用电量
        beginTime = now.toLocalDate().with(TemporalAdjusters.firstDayOfMonth()).atStartOfDay().atZone(zoneId);
        endTime = beginTime.plusMonths(1);
        iqlList.add("select sum(data) as data from (select last(dailyEnergy) as data from %s where powerStationId = '{stationId}' and deviceType = 1 and time >= %s and time < %s group by time(1d),deviceId {tz})"
                .formatted(DeviceData.MN, param.getSecond(beginTime), param.getSecond(endTime)));
        iqlList.add("select sum(stageEnergy) as data from %s where powerStationId = '{stationId}' and powerGridSeat = 2 and time >= %s and time < %s"
                .formatted(MeterData.MN, param.getSecond(beginTime), param.getSecond(endTime)));
        // 年发电和用电量
        beginTime = now.toLocalDate().with(TemporalAdjusters.firstDayOfYear()).atStartOfDay().atZone(zoneId);
        endTime = beginTime.plusYears(1);
        iqlList.add("select sum(data) as data from (select last(dailyEnergy) as data from %s where powerStationId = '{stationId}' and deviceType = 1 and time >= %s and time < %s group by time(1d),deviceId {tz})"
                .formatted(DeviceData.MN, param.getSecond(beginTime), param.getSecond(endTime)));
        iqlList.add("select sum(stageEnergy) as data from %s where powerStationId = '{stationId}' and powerGridSeat = 2 and time >= %s and time < %s"
                .formatted(MeterData.MN, param.getSecond(beginTime), param.getSecond(endTime)));
        // 总发电和用电量
        ZonedDateTime createTime = station.getCreateTime().atZone(zoneId);
        iqlList.add("select sum(data) as data from (select last(dailyEnergy) as data from %s where powerStationId = '{stationId}' and deviceType = 1 and time >= %s and time <= %s group by time(1d),deviceId {tz})"
                .formatted(DeviceData.MN, param.getSecond(createTime), param.getSecond(now)));
        iqlList.add("select sum(stageEnergy) as data from %s where powerStationId = '{stationId}' and powerGridSeat = 2 and time >= %s and time <= %s"
                .formatted(MeterData.MN, param.getSecond(createTime), param.getSecond(now)));
        List<BigDecimal> list = influxMapper.queryMultiObjData(param.iql(iqlList));
        LocalDateTime lastReportTime = TimeZoneUtil.getZonedTime(station.getLastReportTime(), zoneId);
        return new PowerConsumptionVO(list, lastReportTime);
    }

    public PowerEarningsVO earnings(Integer id, ClientTypeEnum clientType) {
        String key = "cache:earnings_" + id + "_" + clientType.getVal();
        PowerEarningsVO vo = RedisUtil.get(key, PowerEarningsVO.class);
        if (vo != null) {
            return vo;
        }
        PowerStation station = powerStationService.getByIdThrow(id);
        ZoneId zoneId = Sql.getZoneId(id);
        ZonedDateTime now = ZonedDateTime.now(zoneId);
        if (station.getPowerPriceStrategy() == null || (station.getPowerPriceStrategy() == 1 && station.getBuyPowerPrice() == null)) {
            return new PowerEarningsVO(now);
        }
        if (station.getPowerPriceStrategy() == 1) {
            List<String> iqlList = new ArrayList<>();
            Param param = Param.of(zoneId).stationId(id);
            // 日发电和用电量
            ZonedDateTime beginTime = TimeZoneUtil.getZonedEarlyTime(zoneId, now);
            ZonedDateTime endTime = beginTime.plusDays(1);
            iqlList.add("select sum(data) as data from (select last(dailyEnergy) as data from %s where powerStationId = '{stationId}' and deviceType = 1 and time >= %s and time < %s group by time(1d),deviceId {tz})"
                    .formatted(DeviceData.MN, param.getSecond(beginTime), param.getSecond(endTime)));
            // 月发电和用电量
            beginTime = now.toLocalDate().with(TemporalAdjusters.firstDayOfMonth()).atStartOfDay().atZone(zoneId);
            endTime = beginTime.plusMonths(1);
            iqlList.add("select sum(data) as data from (select last(dailyEnergy) as data from %s where powerStationId = '{stationId}' and deviceType = 1 and time >= %s and time < %s group by time(1d),deviceId {tz})"
                    .formatted(DeviceData.MN, param.getSecond(beginTime), param.getSecond(endTime)));
            // 年发电和用电量
            beginTime = now.toLocalDate().with(TemporalAdjusters.firstDayOfYear()).atStartOfDay().atZone(zoneId);
            endTime = beginTime.plusYears(1);
            iqlList.add("select sum(data) as data from (select last(dailyEnergy) as data from %s where powerStationId = '{stationId}' and deviceType = 1 and time >= %s and time < %s group by time(1d),deviceId {tz})"
                    .formatted(DeviceData.MN, param.getSecond(beginTime), param.getSecond(endTime)));
            // 总发电和用电量
            iqlList.add("select sum(data) as data from (select last(dailyEnergy) as data from %s where powerStationId = '{stationId}' and deviceType = 1 and time >= %s and time <= %s group by time(1d),deviceId {tz})"
                    .formatted(DeviceData.MN, param.getSecond(station.getCreateTime().atZone(zoneId)), param.getSecond(now)));
            List<BigDecimal> list = influxMapper.queryMultiObjData(param.iql(iqlList));
            vo = new PowerEarningsVO(list, station.getBuyPowerPrice(), now, clientType);
        } else {
            vo = new PowerEarningsVO(now);
            Map<BigDecimal, List<PowerStationPrice>> priceMap = powerStationService.getBaseMapper().getPrice(id).stream().collect(Collectors.groupingBy(PowerStationPrice::getPrice));
            if (CollUtil.isEmpty(priceMap)) {
                return vo;
            }
            List<String> iqlList = new ArrayList<>();
            List<PriceSite> priceList = new ArrayList<>();
            Param param = Param.of(zoneId).stationId(id);
            int index = 0;
            for (Map.Entry<BigDecimal, List<PowerStationPrice>> m : priceMap.entrySet()) {
                StringBuilder sb = new StringBuilder();
                List<PowerStationPrice> prices = m.getValue();
                for (PowerStationPrice p : prices) {
                    if (p.getEndTime().equals(TimeZoneUtil.TIME_23_59)) {
                        p.setEndTime(TimeZoneUtil.TIME_23_59);
                    }
                    int beginHm = p.getBeginTime().getHour() * 100 + p.getBeginTime().getMinute();
                    int endHm = p.getEndTime().getHour() * 100 + p.getEndTime().getMinute();
                    if (p.getMonths().size() == 1) {
                        // or (hm >= 0 and hm < 2400 and month = 2)
                        sb.append(" or (hm >= ").append(beginHm).append(" and hm < ").append(endHm).append(" and month = ").append(p.getMonths().get(0)).append(")");
                    } else {
                        StringBuilder msb = new StringBuilder();
                        for (Integer month : p.getMonths()) {
                            msb.append(" or month = ").append(month);
                        }
                        // or (hm >= 0 and hm < 2400 and (month = 2 or month = 1 or month = 3))
                        sb.append(" or (hm >= ").append(beginHm).append(" and hm < ").append(endHm).append(" and (").append(msb.substring(4)).append("))");
                    }
                }
                String iql = "select sum(data) as data from (select last(dailyEnergy) as data from %s where powerStationId = '{stationId}' and (%s) and deviceType = 1".formatted(DeviceData.MN, sb.substring(4));
                //今日
                ZonedDateTime beginTime = TimeZoneUtil.getZonedEarlyTime(zoneId, now);
                ZonedDateTime endTime = beginTime.plusDays(1);
                iqlList.add(iql + " and time >= %s and time <= %s group by time(1d),deviceId {tz})".formatted(param.getSecond(beginTime), param.getSecond(endTime)));
                priceList.add(new PriceSite(1, m.getKey(), index++));
                //本月
                beginTime = now.toLocalDate().with(TemporalAdjusters.firstDayOfMonth()).atStartOfDay().atZone(zoneId);
                endTime = beginTime.plusMonths(1);
                iqlList.add(iql + " and time >= %s and time <= %s group by time(1d),deviceId {tz})".formatted(param.getSecond(beginTime), param.getSecond(endTime)));
                priceList.add(new PriceSite(3, m.getKey(), index++));
                //本年
                if (clientType != ClientTypeEnum.PC) {
                    beginTime = now.toLocalDate().with(TemporalAdjusters.firstDayOfYear()).atStartOfDay().atZone(zoneId);
                    endTime = beginTime.plusYears(1);
                    iqlList.add(iql + " and time >= %s and time <= %s group by time(1d),deviceId {tz})".formatted(param.getSecond(beginTime), param.getSecond(endTime)));
                    priceList.add(new PriceSite(4, m.getKey(), index++));
                }
                //总计
                iqlList.add(iql + " group by time(1d),deviceId {tz})");
                priceList.add(new PriceSite(5, m.getKey(), index++));
            }
            List<BigDecimal> list = influxMapper.queryMultiObjData(param.iql(iqlList));
            for (Map.Entry<Integer, List<PriceSite>> m : priceList.stream().collect(Collectors.groupingBy(PriceSite::getType)).entrySet()) {
                BigDecimal price = m.getValue().stream().map(p -> list.get(p.getIndex()).multiply(p.getPrice())).reduce(BigDecimal.ZERO, BigDecimal::add);
                switch (m.getKey()) {
                    case 1 -> vo.setDayProfit(price);
                    case 3 -> vo.setMonthProfit(price);
                    case 4 -> vo.setYearProfit(price);
                    case 5 -> vo.setAllProfit(price);
                }
            }
            if (clientType == ClientTypeEnum.PC) {
                if (vo.getDayProfit().compareTo(NumUtil.THOUSAND) > 0) {
                    vo.setDayProfit(vo.getDayProfit().divide(NumUtil.THOUSAND, 2, RoundingMode.UP));
                    vo.setDayProfitUnit("K");
                }
                if (vo.getMonthProfit().compareTo(NumUtil.THOUSAND) > 0) {
                    vo.setMonthProfit(vo.getMonthProfit().divide(NumUtil.THOUSAND, 2, RoundingMode.UP));
                    vo.setMonthProfitUnit("K");
                }
                if (vo.getAllProfit().compareTo(NumUtil.THOUSAND) > 0) {
                    vo.setAllProfit(vo.getAllProfit().divide(NumUtil.THOUSAND, 2, RoundingMode.UP));
                    vo.setAllProfitUnit("K");
                }
            }
        }
        RedisUtil.set(key, vo, Duration.ofMinutes(15));
        return vo;
    }

    public EnergyFlowVO energyFlow(Integer id) {
        // todo 能量流动 光伏功率 微逆没有电表数据 考虑电表数据为空的情况 这里光伏发电功率使用的是acTempPower
        List<String> iqlList = new ArrayList<>(2);
        iqlList.add("select last(power) * 1000 as data from %s where powerStationId = '{stationId}' and powerGridSeat = 2 and time >= now() - 15m and time <= now()".formatted(MeterData.MN));
        iqlList.add("select sum(data) as data from (select last(acTempPower) as data from %s where powerStationId = '{stationId}' and deviceType = 1 and time >= now() - 15m and time <= now() group by deviceId) where time >= now() - 15m and time <= now()".formatted(DeviceData.MN));
        Param param = Param.of().stationId(id).iql(iqlList);
        List<BigDecimal> list = influxMapper.queryMultiObjData(param);
        return new EnergyFlowVO(list.get(0), list.get(1));
    }

    public LoadDetailsVO loadDetails(Integer id) {
        ZoneId zoneId = Sql.getZoneId(id);
        List<String> iqlList = new ArrayList<>(2);
        ZonedDateTime beginTime = TimeZoneUtil.getZonedEarlyTime(zoneId);
        ZonedDateTime endTime = beginTime.plusDays(1);
        iqlList.add("select last(power) * 1000 as data from %s where powerStationId = '{stationId}' and powerGridSeat = 2 and time >= now() - 15m and time <= now()".formatted(MeterData.MN));
        iqlList.add("select sum(data) as data from (select last(acTempPower) as data from %s where powerStationId = '{stationId}' and deviceType = 1 and time >= now() - 15m and time <= now() group by deviceId) where time >= now() - 15m and time <= now()".formatted(DeviceData.MN));
        Param param = Param.of().stationId(id).begin(beginTime).end(endTime).iql(iqlList);
        List<BigDecimal> list = influxMapper.queryMultiObjData(param);
        LoadDetailsVO vo = new LoadDetailsVO();
        BigDecimal totalPower = list.get(0).add(list.get(1));
        DataUnit power = Tools.covertPowerUnit(totalPower);
        vo.setFamilyDetails(power.getData());
        vo.setFamilyDetailsUnit(power.getUnit());
        vo.setFamilyAppliance(power.getData());
        vo.setFamilyApplianceUnit(power.getUnit());
        return vo;
    }

    public EnergyFlowRealVO energyFlowReal(Integer id) {
        ZoneId zoneId = Sql.getZoneId(id);
        List<String> iqlList = new ArrayList<>();
        ZonedDateTime beginTime = TimeZoneUtil.getZonedEarlyTime(zoneId);
        ZonedDateTime endTime = beginTime.plusDays(1);
        iqlList.add("select power * 1000, forwardEnergy, reverseEnergy from %s where powerStationId = '{stationId}' and powerGridSeat = 2 and time >= now() - 15m and time <= now() order by time desc limit 1".formatted(MeterData.MN));
        iqlList.add("select sum(stageForwardEnergy) as forwardEnergy, sum(stageReverseEnergy) as reverseEnergy, sum(stageEnergy) as stageEnergy from %s where powerStationId = '{stationId}' and powerGridSeat = 2 and time >= {begin} and time <= {end}".formatted(MeterData.MN));
        iqlList.add("select sum(data) as data from (select last(acTempPower) as data from %s where powerStationId = '{stationId}' and deviceType = 1 and time >= now() - 15m and time <= now() group by deviceId) where time >= now() - 15m and time <= now()".formatted(DeviceData.MN));
        iqlList.add("select sum(data) as data from (select last(dailyEnergy) as data from %s where powerStationId = '{stationId}' and deviceType = 1 and time >= {begin} and time < {end} group by time(1d),deviceId {tz})".formatted(DeviceData.MN));
        iqlList.add("select sum(data) as data from (select last(dailyEnergy) as data from %s where powerStationId = '{stationId}' and deviceType = 1 group by time(1d),deviceId {tz})".formatted(DeviceData.MN));
        iqlList.add("select sum(stageEnergy) as data from %s where powerStationId = '{stationId}' and powerGridSeat = 2".formatted(MeterData.MN));
        iqlList.add("select forwardEnergy, reverseEnergy from %s where powerStationId = '{stationId}' and powerGridSeat = 2 order by time desc limit 1".formatted(MeterData.MN));
        Param param = Param.of(zoneId).stationId(id).begin(beginTime).end(endTime).iql(iqlList);
        List<JSONObject> list = influxMapper.queryMultiObj(param);
        EnergyFlowRealVO vo = new EnergyFlowRealVO();
        vo.setPower(Tools.covertPowerUnitStr(list.get(0).getBigDecimal("power")));
        vo.setForwardEnergy(Tools.covertKwhUnitStr(list.get(1).getBigDecimal("forwardEnergy")));
        vo.setReverseEnergy(Tools.covertKwhUnitStr(list.get(1).getBigDecimal("reverseEnergy")));
        vo.setTotalForwardEnergy(Tools.covertKwhUnitStr(list.get(6).getBigDecimal("forwardEnergy")));
        vo.setTotalReverseEnergy(Tools.covertKwhUnitStr(list.get(6).getBigDecimal("reverseEnergy")));
        vo.setLoadPower(Tools.covertPowerUnitStr(Tools.add(list.get(0).getBigDecimal("power"), list.get(2).getBigDecimal(DATA))));
        vo.setLoadForwardEnergy(Tools.covertKwhUnitStr(Tools.add(list.get(1).getBigDecimal("stageEnergy"), list.get(3).getBigDecimal(DATA))));
        vo.setLoadTotalForwardEnergy(Tools.covertKwhUnitStr(Tools.add(list.get(5).getBigDecimal(DATA), list.get(4).getBigDecimal(DATA))));
        return vo;
    }

    public ElectricityVO electricity(Integer id, ZonedDateTime beginTime, ZonedDateTime endTime, ZoneId zoneId) {
        List<String> iqlList = new ArrayList<>(2);
        iqlList.add("select sum(data) as data from (select last(dailyEnergy) as data from %s where powerStationId = '{stationId}' and deviceType = 1 and time >= {begin} and time < {end} group by time(1d),deviceId {tz})".formatted(DeviceData.MN));
        iqlList.add("select sum(stageForwardEnergy) as forwardEnergy, sum(stageReverseEnergy) as reverseEnergy from %s where powerStationId = '{stationId}' and powerGridSeat = 2 and time >= {begin} and time < {end} {tz}".formatted(MeterData.MN));
        Param param = Param.of().zoneId(zoneId).stationId(id).begin(beginTime).end(endTime).iql(iqlList);
        List<JSONObject> list = influxMapper.queryMultiObj(param);
        JSONObject device = list.get(0);
        JSONObject meter = list.get(1);
        ElectricityVO vo = new ElectricityVO();
        //总发电量
        if (!device.isEmpty()) {
            vo.setFdAll(device.getBigDecimal(DATA));
        }
        //电表数据
        if (!meter.isEmpty()) {
            vo.setFaFeed(meter.getBigDecimal("reverseEnergy"));
            vo.setYdMeter(meter.getBigDecimal("forwardEnergy"));
            vo.setYdAll(vo.getYdMeter().subtract(vo.getFaFeed()).add(vo.getFdAll()));
            if (vo.getFdAll().compareTo(BigDecimal.ZERO) != 0) {
                vo.setFaFeedRate(vo.getFaFeed().multiply(NumUtil.HUNDRED).divide(vo.getFdAll(), 2, RoundingMode.HALF_UP));
            }
            if (vo.getYdAll().compareTo(BigDecimal.ZERO) != 0) {
                vo.setYdMeterRate(vo.getYdMeter().multiply(NumUtil.HUNDRED).divide(vo.getYdAll(), 2, RoundingMode.HALF_UP));
            }
            vo.setFdConsume(vo.getFdAll().subtract(vo.getFaFeed()));
            if (vo.getFdConsume().compareTo(BigDecimal.ZERO) > 0) {
                vo.setFdConsumeRate(NumUtil.HUNDRED.subtract(vo.getFaFeedRate()));
            }
            vo.setYdWn(vo.getYdAll().subtract(vo.getYdMeter()));
            if (vo.getYdWn().compareTo(BigDecimal.ZERO) > 0) {
                vo.setYdWnRate(NumUtil.HUNDRED.subtract(vo.getYdMeterRate()));
            }
        }
        return vo;
    }

    public ElectricityVO degreeElectricity(DegreeElectricityDTO dto) {
        PowerStation station = powerStationService.getByIdThrow(dto.getId());
        ZoneId zoneId = Sql.getZoneId(dto.getId());
        ZonedDateTime beginTime, endTime;
        switch (dto.getType()) {
            case 1 -> {
                beginTime = LocalDateTime.parse(dto.getDate() + " 00:00", TimeZoneUtil.yyyyMMddHHmm).atZone(zoneId);
                endTime = beginTime.plusDays(1);
            }
            case 2 -> {
                List<Integer> time = Arrays.stream(dto.getDate().split("-")).map(Integer::parseInt).toList();
                beginTime = LocalDate.ofYearDay(time.get(0), 1).with(WeekFields.ISO.weekOfYear(), time.get(1)).with(ChronoField.DAY_OF_WEEK, 1).atStartOfDay().atZone(zoneId);
                endTime = beginTime.plusDays(7);
            }
            case 3 -> {
                beginTime = LocalDateTime.parse(dto.getDate() + "-01 00:00", TimeZoneUtil.yyyyMMddHHmm).atZone(zoneId);
                endTime = beginTime.plusMonths(1);
            }
            case 4 -> {
                beginTime = LocalDateTime.parse(dto.getDate() + "-01-01 00:00", TimeZoneUtil.yyyyMMddHHmm).atZone(zoneId);
                endTime = beginTime.plusYears(1);
            }
            default -> {
                beginTime = station.getCreateTime().atZone(zoneId);
                endTime = ZonedDateTime.now(zoneId);
            }
        }
        ElectricityVO vo = electricity(dto.getId(), beginTime, endTime, zoneId);
        vo.setDegree(Tools.degree(vo.getYdAll(), vo.getFdAll()));
        return vo;
    }

    public List<DiagnoseVO> diagnose(Integer id) {
        List<StationDevice> all = stationDeviceService.lambdaQuery().eq(StationDevice::getPowerStationId, id)
                .in(StationDevice::getDeviceType, List.of(0, 1)).list();
        if (all.isEmpty()) {
            return null;
        }
        Map<String, String> faultData = stationDeviceService.getFaultData(LanguageEnum.getLang());
        List<DiagnoseVO> gatewayList = new ArrayList<>();
        Map<String, List<DiagnoseDeviceVO>> deviceMap = new HashMap<>();
        for (StationDevice device : all) {
            if (device.getDeviceType() == DeviceTypeEnum.EMU.getVal()) {
                DiagnoseVO vo = new DiagnoseVO();
                vo.setGatewaySn(device.getNumber());
                vo.setRunningStatus(device.getStatus() == 0 ? 0 : 1);
                vo.setCommType(device.getCommType());
                gatewayList.add(vo);
            } else {
                if (device.getPid() == null) {
                    DiagnoseVO vo = new DiagnoseVO();
                    vo.setGatewaySn(device.getNumber());
                    vo.setRunningStatus(device.getStatus() == 0 ? 0 : 1);
                    vo.setCommType(device.getCommType());
                    gatewayList.add(vo);
                }
                DiagnoseDeviceVO inv = new DiagnoseDeviceVO();
                inv.setSysStatus(device.getStatus());
                inv.setSnNumber(device.getNumber());
                inv.setDeviceType(device.getDeviceType());
                inv.setDeviceModel(device.getDeviceModel());
                JSONObject faultCode = JSON.parseObject(device.getFaultCode());
                if (faultCode != null) {
                    for (Map.Entry<String, Object> map : faultCode.entrySet()) {
                        int data = (Integer) map.getValue();
                        if (data != 0) {
                            char[] bits = Integer.toBinaryString(data).toCharArray();
                            for (int i = 0; i < bits.length; i++) {
                                if (bits[i] == '1') {
                                    String value = faultData.get(Tools.getFaultKey(map.getKey(), i));
                                    if (value != null) {
                                        Fault fault = JSON.parseObject(value, Fault.class);
                                        inv.getAlarmName().add(fault.getFaultDescription());
                                        inv.getHandlingMethod().add(fault.getResolutionSuggestion());
                                    }
                                }
                            }
                        }
                    }
                }
                deviceMap.computeIfAbsent(device.getPid() == null ? device.getNumber() : device.getEmuNumber(), k -> new ArrayList<>()).add(inv);
            }
        }
        for (DiagnoseVO vo : gatewayList) {
            vo.setInv(deviceMap.getOrDefault(vo.getGatewaySn(), Collections.emptyList()));
        }
        return gatewayList;
    }

    public InfoVO stationInfo(Integer stationId) {
        InfoVO info = powerStationService.getBaseMapper().getInfo(getLoginUser().getUserid(), stationId);
        if (info == null) throw new ServiceException("电站不存在");
        List<StationDevice> deviceList = stationDeviceService.getBaseMapper().getDeviceModel(stationId);
        List<Integer> devIds = deviceList.stream().filter(d -> d.getDeviceType() == 1).map(StationDevice::getId).toList();
        List<Integer> meterIds = deviceList.stream().filter(d -> d.getDeviceType() == 2).map(StationDevice::getId).toList();
        List<String> iqlList = new ArrayList<>(2);
        if (!devIds.isEmpty()) {
            String ids = influxMapper.orTag(DEVICE_ID, devIds);
            iqlList.add("select * from %s where powerStationId = '{stationId}' and (%s) and deviceType = 1 group by deviceId order by time desc limit 1".formatted(DeviceData.MN, ids));
        }
        if (!meterIds.isEmpty()) {
            String ids = influxMapper.orTag(DEVICE_ID, meterIds);
            iqlList.add("select * from %s where powerStationId = '{stationId}' and (%s) group by deviceId order by time desc limit 1".formatted(MeterData.MN, ids));
        }
        if (iqlList.isEmpty()) {
            return info;
        }
        Param param = Param.of().stationId(stationId).iql(iqlList);
        List<List<JSONObject>> list = influxMapper.queryMultiList(param);
        if (!devIds.isEmpty()) {
            Map<Integer, String> map = deviceList.stream().filter(d -> d.getDeviceType() == 1).collect(Collectors.toMap(StationDevice::getId, StationDevice::getDeviceModel));
            info.setInvData(list.get(0).stream().filter(ObjUtil::isNotNull).map(json -> {
                InvDataVO invDataVO = json.to(InvDataVO.class);
                invDataVO.setModel(map.get(json.getInteger(DEVICE_ID)));
                return invDataVO;
            }).toList());
        }
        if (!meterIds.isEmpty()) {
            int index = devIds.isEmpty() ? 0 : 1;
            info.setMeterData(list.get(index).stream().filter(ObjUtil::isNotNull).map(json -> {
                MeterDataVO vo = json.to(MeterDataVO.class);
                vo.setType(json.getInteger("powerGridSeat"));
                return vo;
            }).toList());
        }
        return info;
    }

    public CheckStatusVO getSelfCheckStatus(Integer stationId) {
        CheckStatusVO checkStatusVO = new CheckStatusVO();
        PowerStation powerStation = powerStationService.getByIdThrow(stationId);
        ZoneId zoneId = TimeZoneUtil.getZoneId(powerStation.getTimeZone());
        LambdaQueryWrapper<StationDevice> wrapper = new LambdaQueryWrapper<>();
        List<StationDevice> deviceList = stationDeviceService.list(wrapper.eq(StationDevice::getPowerStationId, stationId).orderByAsc(StationDevice::getDeviceType, StationDevice::getNumber));
        for (StationDevice device : deviceList) {
            if (DeviceTypeEnum.EMU.getVal() == device.getDeviceType()) {
                CheckStatusVO.EMU emu = new CheckStatusVO.EMU();
                BeanUtils.copyProperties(device, emu);
                String iql = "select * from {table} where deviceId = '{deviceId}' order by time desc limit 1";
                Param param = Param.of(DeviceData.MN, iql).deviceId(emu.getId());
                DeviceDataVO data = influxMapper.queryObj(param, DeviceDataVO.class);
                if (data == null) {
                    continue;
                }
                ZonedDateTime time = Instant.ofEpochSecond(data.getTime()).atZone(zoneId);
                emu.setSoftwareVersion(Tools.getVersion(device.getSoftwareVersion()));
                emu.setHardwareVersion(Tools.getVersion(device.getHardwareVersion()));
                emu.setLastReportTime(time.toLocalDateTime());
                checkStatusVO.getEmuList().add(emu);
            } else if (DeviceTypeEnum.WN.getVal() == device.getDeviceType()) {
                CheckStatusVO.Inv inv = new CheckStatusVO.Inv();
                BeanUtils.copyProperties(device, inv);
                String iql = "select * from {table} where deviceId = '{deviceId}' order by time desc limit 1";
                Param param = Param.of(DeviceData.MN, iql).deviceId(inv.getId());
                DeviceDataVO data = influxMapper.queryObj(param, DeviceDataVO.class);
                if (data == null) {
                    continue;
                }
                inv.setAcCurrReal(data.getAcCurrReal());
                inv.setAcVolReal(data.getAcVolReal());
                inv.setAcTempPower(data.getAcTempPower());
                ZonedDateTime time = Instant.ofEpochSecond(data.getTime()).atZone(zoneId);
                inv.setLastReportTime(time.toLocalDateTime());
                inv.setSoftwareVersion(Tools.getVersion(device.getSoftwareVersion()));
                inv.setHardwareVersion(Tools.getVersion(device.getHardwareVersion()));
                checkStatusVO.getInvList().add(inv);
            } else if (DeviceTypeEnum.DB.getVal() == device.getDeviceType()) {
                List<String> iqlList = new ArrayList<>();
                ZonedDateTime beginTime = TimeZoneUtil.getZonedEarlyTime(zoneId);
                ZonedDateTime endTime = beginTime.plusDays(1);
                iqlList.add("select time, power * 1000, forwardEnergy, reverseEnergy from %s where powerStationId = '{stationId}' and deviceId = '{deviceId}' order by time desc limit 1".formatted(MeterData.MN));
                Param param = Param.of(zoneId).stationId(stationId).deviceId(device.getId()).begin(beginTime).end(endTime).iql(iqlList);
                JSONObject data = influxMapper.queryObj(param);
                if (data == null) {
                    continue;
                }
                CheckStatusVO.Meter meter = new CheckStatusVO.Meter();
                BeanUtils.copyProperties(device, meter);
                meter.setForwardEnergy(data.getString("forwardEnergy"));
                meter.setReverseEnergy(data.getString("reverseEnergy"));
                meter.setPower(data.getString("power"));
                ZonedDateTime time = Instant.ofEpochSecond(data.getLong("time")).atZone(zoneId);
                meter.setLastReportTime(time.toLocalDateTime());
                checkStatusVO.getMeterList().add(meter);
            } else if (DeviceTypeEnum.DC.getVal() == device.getDeviceType()) {

            }

        }

        return checkStatusVO;
    }


    public MeterVo meterInfo(String sn) {
        MeterVo meter = new MeterVo();
        StationDevice one = stationDeviceService.getOne(new LambdaQueryWrapper<StationDevice>().eq(StationDevice::getNumber, sn));
        if (one == null) {
            throw new ServiceException("设备不存在");
        }
        one.getFinishPeriod();
        BeanUtil.copyProperties(one, meter);
        PowerStation powerStation = powerStationService.getByIdThrow(one.getPowerStationId());
        if (powerStation == null) {
            throw new ServiceException("电站不存在");
        }
        ZoneId zoneId = TimeZoneUtil.getZoneId(powerStation.getTimeZone());
        List<String> iqlList = new ArrayList<>();
        ZonedDateTime beginTime = TimeZoneUtil.getZonedEarlyTime(zoneId);
        ZonedDateTime endTime = beginTime.plusDays(1);
        iqlList.add("select time, power * 1000, forwardEnergy, reverseEnergy from %s where powerStationId = '{stationId}' and deviceId = '{deviceId}' order by time desc limit 1".formatted(MeterData.MN));
        Param param = Param.of(zoneId).stationId(one.getPowerStationId()).deviceId(one.getId()).begin(beginTime).end(endTime).iql(iqlList);
        JSONObject data = influxMapper.queryObj(param);
        meter.setForwardEnergy(data.getString("forwardEnergy"));
        meter.setReverseEnergy(data.getString("reverseEnergy"));
        meter.setPower(data.getString("power"));
        ZonedDateTime time = Instant.ofEpochSecond(data.getLong("time")).atZone(zoneId);
        meter.setLastReportTime(time.toLocalDateTime());
        return meter;
    }

//    public void repairMeterData() {
//        int count = 0;
//        String iql = "select count(sn) as data from {table} where time >= now() - 86400s group by deviceId";
//        Param param = Param.of(MeterData.MN, iql);
//        Map<JSONObject, List<JSONObject>> deviceIdMap = influxMapper.queryGroupList(param);
//        for (Map.Entry<JSONObject, List<JSONObject>> deviceMap : deviceIdMap.entrySet()) {
//            Integer deviceId = deviceMap.getKey().getInteger(DEVICE_ID);
//            int num = deviceMap.getValue().get(0).getIntValue(DATA);
//            log.info("设备号:{}, 总数据量:{}", deviceId, num);
//            iql = "select forwardEnergy,stageForwardEnergy,reverseEnergy,stageReverseEnergy,stageEnergy,deviceId,powerStationId,sn from {table} where deviceId = '{deviceId}' and time >= now() - 86400s order by time asc";
//            param = Param.of(MeterData.MN).iql(iql).deviceId(deviceId).dataNotFormat();
//            List<JSONObject> list = influxMapper.queryList(param);
//            Map<Long, List<JSONObject>> snMap = list.stream().collect(Collectors.groupingBy(json -> json.getLongValue(SN)));
//            for (Map.Entry<Long, List<JSONObject>> map : snMap.entrySet()) {
//                BigDecimal lastForwardEnergy, lastReverseEnergy;
//                iql = "select forwardEnergy,reverseEnergy from {table} where deviceId = '{deviceId}' and sn = '%s' and time < now() - 86400s order by time desc limit 1";
//                param = Param.of(MeterData.MN).iql(iql.formatted(map.getKey())).deviceId(deviceId).dataNotFormat();
//                JSONObject lastJson = influxMapper.queryObj(param);
//                if (lastJson == null) {
//                    lastForwardEnergy = lastReverseEnergy = BigDecimal.ZERO;
//                } else {
//                    lastForwardEnergy = lastJson.getBigDecimal("forwardEnergy");
//                    lastReverseEnergy = lastJson.getBigDecimal("reverseEnergy");
//                }
//                for (JSONObject json : map.getValue()) {
//                    Instant instant = Instant.ofEpochSecond(json.getLongValue(TIME));
//                    String timeStr = TimeZoneUtil.yyyyMMddHHmmss.format(instant.atZone(ZoneId.systemDefault()));
//                    BigDecimal forwardEnergy = json.getBigDecimal("forwardEnergy");
//                    BigDecimal reverseEnergy = json.getBigDecimal("reverseEnergy");
//                    BigDecimal oldStageForwardEnergy = json.getBigDecimal("stageForwardEnergy");
//                    BigDecimal oldStageReverseEnergy = json.getBigDecimal("stageReverseEnergy");
//                    BigDecimal oldStageEnergy = json.getBigDecimal("stageEnergy");
//                    BigDecimal newStageForwardEnergy = forwardEnergy.subtract(lastForwardEnergy).stripTrailingZeros();
//                    if (newStageForwardEnergy.compareTo(BigDecimal.ZERO) <= 0) {
//                        newStageForwardEnergy = BigDecimal.ZERO;
//                    }
//                    BigDecimal newStageReverseEnergy = reverseEnergy.subtract(lastReverseEnergy).stripTrailingZeros();
//                    if (newStageReverseEnergy.compareTo(BigDecimal.ZERO) <= 0) {
//                        newStageReverseEnergy = BigDecimal.ZERO;
//                    }
//                    BigDecimal newStageEnergy = newStageForwardEnergy.subtract(newStageReverseEnergy).stripTrailingZeros();
//                    MeterData data = new MeterData();
//                    if (newStageForwardEnergy.compareTo(oldStageForwardEnergy) != 0) {
//                        data.setStageForwardEnergy(newStageForwardEnergy);
//                        log.info("正向电量数据不一致, 时间:{}, 旧值:{}, 新值:{}", timeStr, oldStageForwardEnergy, newStageForwardEnergy);
//                    }
//                    if (newStageReverseEnergy.compareTo(oldStageReverseEnergy) != 0) {
//                        data.setStageReverseEnergy(newStageReverseEnergy);
//                        log.info("反向电量数据不一致, 时间:{}, 旧值:{}, 新值:{}", timeStr, oldStageReverseEnergy, newStageReverseEnergy);
//                    }
//                    if (newStageEnergy.compareTo(oldStageEnergy) != 0) {
//                        data.setStageEnergy(newStageEnergy);
//                        log.info("电量数据不一致, 时间:{}, 旧值:{}, 新值:{}", timeStr, oldStageEnergy, newStageEnergy);
//                    }
//                    if (data.getStageReverseEnergy() != null || data.getStageForwardEnergy() != null || data.getStageEnergy() != null) {
//                        data.setDeviceId(json.getInteger(DEVICE_ID));
//                        data.setPowerStationId(json.getInteger("powerStationId"));
//                        data.setCreateTime(instant);
//                        influxMapper.save(data);
//                        count++;
//                    }
//                    lastForwardEnergy = forwardEnergy;
//                    lastReverseEnergy = reverseEnergy;
//                }
//            }
//        }
//        log.info("修复了{}条数据", count);
//    }

//    public void repairDeviceData(LocalDate day) {
//        int count = 0;
//        LocalDateTime beginTime = day.atStartOfDay();
//        LocalDateTime endTime = beginTime.plusDays(1);
//        String iql = "select count(sn) as data from {table} where deviceType = 1 and time >= {begin} and time < {end} group by deviceId";
//        Param param = Param.of(DeviceData.MN, iql).begin(beginTime).end(endTime);
//        Map<JSONObject, List<JSONObject>> deviceIdMap = influxMapper.queryGroupList(param);
//        for (Map.Entry<JSONObject, List<JSONObject>> deviceMap : deviceIdMap.entrySet()) {
//            Integer deviceId = deviceMap.getKey().getInteger(DEVICE_ID);
//            int num = deviceMap.getValue().get(0).getIntValue(DATA);
//            log.info("设备号:{}, 总数据量:{}", deviceId, num);
//            iql = "select dailyEnergy,stageDailyEnergy,pid,deviceId,powerStationId,sn from {table} where deviceId = '{deviceId}' and time >= {begin} and time < {end} order by time asc";
//            param = Param.of(DeviceData.MN, iql).begin(beginTime).end(endTime).deviceId(deviceId).dataNotFormat();
//            List<JSONObject> list = influxMapper.queryList(param);
//            Map<String, List<JSONObject>> snMap = list.stream().collect(Collectors.groupingBy(json -> json.getString(SN)));
//            for (Map.Entry<String, List<JSONObject>> map : snMap.entrySet()) {
//                BigDecimal lastDailyEnergy = BigDecimal.ZERO;
//                for (JSONObject json : map.getValue()) {
//                    Instant instant = Instant.ofEpochSecond(json.getLongValue(TIME));
//                    String timeStr = TimeZoneUtil.yyyyMMddHHmmss.format(instant.atZone(ZoneId.systemDefault()));
//                    BigDecimal dailyEnergy = json.getBigDecimal("dailyEnergy");
//                    BigDecimal oldStageDailyEnergy = json.getBigDecimal("stageDailyEnergy");
//                    BigDecimal newStageDailyEnergy = dailyEnergy.subtract(lastDailyEnergy).stripTrailingZeros();
//                    if (newStageDailyEnergy.compareTo(BigDecimal.ZERO) <= 0) {
//                        newStageDailyEnergy = BigDecimal.ZERO;
//                    }
//                    DeviceData data = new DeviceData();
//                    if (newStageDailyEnergy.compareTo(oldStageDailyEnergy) != 0) {
//                        data.setStageDailyEnergy(newStageDailyEnergy);
//                        data.setDeviceId(json.getInteger(DEVICE_ID));
//                        data.setPowerStationId(json.getInteger("powerStationId"));
//                        data.setPid(json.getInteger("pid"));
//                        data.setCreateTime(instant);
//                        influxMapper.save(data);
//                        count++;
//                        log.info("单次数据不一致, 时间:{}, 旧值:{}, 新值:{}", timeStr, oldStageDailyEnergy, newStageDailyEnergy);
//                    }
//                    if (dailyEnergy.compareTo(lastDailyEnergy) > 0) {
//                        lastDailyEnergy = dailyEnergy;
//                    }
//                }
//            }
//        }
//        log.info("修复了{}条数据", count);
//    }

    /**
     * 电站授权校验
     *
     * @param stationId 站点id
     * @return Boolean
     */
    public Boolean judgeStationAuth(Long stationId){
        SysUser user = getSysUser();
        if (SysUser.isStaff(user.getUserType()) && isNotAdminRole(user.getRoleIds())) {
            Long count = powerStationService.lambdaQuery()
                    .eq(PowerStation::getId, stationId)
                    .eq(PowerStation::getPermission, false)
                    .count();
            return count <= 0;
        }
        return true;
    }
}
