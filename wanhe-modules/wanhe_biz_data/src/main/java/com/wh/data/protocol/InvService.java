package com.wh.data.protocol;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.enums.DeviceTypeEnum;
import com.ruoyi.common.core.utils.TimeZoneUtil;
import com.ruoyi.common.core.utils.Tools;
import com.ruoyi.system.api.dto.EnergyFaultCache;
import com.ruoyi.system.api.dto.Inv;
import com.wh.data.domain.PowerStation;
import com.wh.data.domain.StationDevice;
import com.wh.data.enums.CommandEnum;
import com.wh.data.influxdb.entity.DeviceData;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.time.*;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/5
 */
@Slf4j
@Component("Inv")
public class InvService extends TopicService {

    @Resource
    protected RedissonClient redissonClient;

    /**
     * 设置微逆时区
     */
    public void responseInvTimeZone(JSONObject data) {
        updateCommand(data);
    }

    /**
     * wifi微逆版固件升级
     */
    public void responseUpgradeInvFile(JSONObject data) {
        updateCommand(data);
        int status = data.getIntValue(STATUS);
        if (status == 2) {
            stationDeviceService.lambdaUpdate()
                    .eq(StationDevice::getNumber, data.getString(SN))
                    .set(StationDevice::getLastUpgradeTime, LocalDateTime.now())
                    .update();
        }
        switch (status) {
            case 0, 2 -> {
                if (data.getIntValue("type") == 0) {
                    CommandEnum.INV_UPGRADE_FILE.delRepeatRequest(data.getString("sn"));
                } else {
                    CommandEnum.INV_WIFI_UPGRADE_FILE.delRepeatRequest(data.getString("sn"));
                }
            }
        }
    }

    /**
     * 写微逆参数 开机，关机，重启，并网文件升级
     */
    public void responseWriteInvParam(JSONObject data) {
        updateCommand(data);
    }

    /**
     * 给微逆下发组网信息
     */
    public void responseWriteInvNetworking(JSONObject data) {
        updateCommand(data);
    }

    /**
     * 下发并网文件
     */
    public void responseWriteInvGridParam(JSONObject data) {
        updateCommand(data);
    }

    /**
     * 并网文件查看
     */
    public void responseReadInvGridParam(JSONObject data) {
        JSONArray params = data.getJSONArray("params");
        if (CollUtil.isNotEmpty(params)) {
            for (int i = 0; i < params.size(); i++) {
                JSONObject param = params.getJSONObject(i);
                if (param.getIntValue("addr") == 9) {
                    data.put("gridFileId", param.getInteger("value"));
//                    params.remove(i);
                    break;
                }
            }
        }
        updateCommand(data);
    }

    /**
     * 读故障录播文件
     */
    public void responseReadInvFaultLog(JSONObject data) {
        updateCommand(data);
        CommandEnum.INV_READ_FAULT_LOG.delRepeatRequest(data.getString("sn"));
    }

    /**
     * 读取故障录播参数配置
     */
    public void responseReadFaultWaveConfig(JSONObject data) {
        updateCommand(data);
    }

    /**
     * 写故障录播参数配置
     *
     * @param data 数据
     */
    public void responseWriteFaultWaveConfig(JSONObject data) {
        updateCommand(data);
    }

    /**
     * 读取故障录播
     */
    public void responseInvFaultWave(JSONObject data) {
        updateCommand(data);
        CommandEnum.INV_READ_FAULT_WAVE.delRepeatRequest(data.getString("sn"));
    }

    /**
     * 读故障记录文件
     */
    public void responseReadInvFaultLog2(JSONObject data) {
        updateCommand(data);
        CommandEnum.INV_READ_FAULT_LOG.delRepeatRequest(data.getString("inv"));
    }

    /**
     * 微逆数据上传
     */
    public String invTimedUpload(JSONObject data) {
        JSONObject wifi = data.getJSONObject(WIFI);
        if (wifi == null) {
            return "wifi微逆上报数据缺失wifi信息";
        }
        String sn = data.getString(SN);
        if (StrUtil.isEmpty(sn)) {
            return "wifi微逆设备号不能为空";
        }
        Inv inv = stationDeviceService.getInv(sn);
        if (inv == null) {
            return "设备号还未配置到电站" + sn;
        }
        DeviceData device = data.getObject(INV_DATA, DeviceData.class);
        if (device.getDailyEnergy() == null) {
            return "发电量数据缺失";
        }
        Instant instant = Instant.ofEpochSecond(data.getLong(TIMESTAMP));
        copyWifiToDeviceData(device, inv.getStationId(), inv.getInvId(), sn, data, instant, DeviceTypeEnum.WN, wifi);
        if (StrUtil.isEmpty(device.getTimeCode())) {
            return "时区未上传";
        }
        ZoneId zoneId = TimeZoneUtil.getZoneId(device.getTimeCode());
        if (zoneId == null) {
            return "时区参数错误";
        }
        long stationCreateSecond = inv.getStationCreateTime().atZone(zoneId).toEpochSecond();
        if (instant.getEpochSecond() < stationCreateSecond) {
            return "数据上报时间小于建站时间";
        }
        long newSecond = ZonedDateTime.now(zoneId).toEpochSecond();
        // 允许晚一天的数据
        if (instant.getEpochSecond() < newSecond - 86400L) {
            return "数据上报时间小于当前时间";
        }
        // 允许多传一天的数据
        if (instant.getEpochSecond() > newSecond + 86400L) {
            return "数据上报时间大于当前时间";
        }
        //设备上报数据时间
        LocalDateTime createTime = instant.atZone(zoneId).toLocalDateTime();
        LocalDateTime endTime = LocalDateTime.of(createTime.toLocalDate(), TimeZoneUtil.TIME_23_59_59);
        long seconds = Duration.between(createTime, endTime).getSeconds() + 1L;
        String time = instant.atZone(ZoneId.systemDefault()).format(TimeZoneUtil.yyyyMMddHHmmss);
        if (device.getCommStatus() != 1) {
            stationDeviceService.updateInvDevice(time, device, 0);
            return null;
        }
        device.setPid(inv.getInvId());
        device.setEmuSn(sn);
        device.setCommType(0);
        device.setMonth(createTime.getMonthValue());
        device.setHm(createTime.getHour() * 100 + createTime.getMinute());
        buildPvData(device, data.getJSONObject(INV_DATA));
        //获取要缓存的数据对象
        EnergyFaultCache newCache = stationDeviceService.dataToCache(device);
        //计算单次发电量
        EnergyFaultCache oldCache = stationDeviceService.initStageDailyEnergy(seconds, device, newCache);
        threadPoolTaskExecutor.execute(() -> {
            //设备报警处理
            JSONObject oldFault = oldCache == null ? null : oldCache.getFaultCode();
            String format = createTime.format(TimeZoneUtil.yyyyMMddHHmmss);
            stationDeviceService.deviceAlarmHandle(inv.getTenantId(), format, device, newCache.getFaultCode(), oldFault);
        });
        //更新微逆版本信息
        stationDeviceService.updateInvDevice(time, device, Tools.convertDeviceStatus(device.getSysStatus()));
        //更新电站状态
        powerStationService.updateStationStatus(inv.getStationId(), time);
        //保存记录
        influxMapper.save(device);
        return null;
    }


    /**
     * 读Inv参数 收集微逆版本信息
     *
     * @param data 数据
     */
    public void responseReadInvParam(JSONObject data) {
        updateCommand(data);
        JSONObject invData = data.getJSONObject("invData");
        if (invData != null) {
            StationDevice stationDevice = stationDeviceService.lambdaQuery()
                    .select(StationDevice::getId, StationDevice::getNumber, StationDevice::getStatus, StationDevice::getPowerStationId)
                    .eq(StationDevice::getNumber, data.getString(SN))
                    .eq(StationDevice::getDeviceType, DeviceTypeEnum.WN.getVal())
                    .eq(StationDevice::getDeleted, 0)
                    .last("limit 1").one();
            List<StationDevice> list = new ArrayList<>();
            if (stationDevice != null) {
                int status = Tools.convertDeviceStatus(invData.getIntValue("sysStatus"));
                if (stationDevice.getStatus() != status) {
                    stationDevice.setStatus(status);
                    stationDevice.setNumber(null);
                    list.add(stationDevice);
                }
            }
            if (!list.isEmpty()) {
                stationDeviceService.updateBatchById(list);
                Integer powerStationId = list.get(0).getPowerStationId();
                List<Integer> statusList = stationDeviceService.getBaseMapper().getStatus(powerStationId);
                powerStationService.lambdaUpdate().eq(PowerStation::getId, powerStationId)
                        .set(PowerStation::getStationStatus, Tools.convertStationStatus(statusList))
                        .update();
            }
        }
        updateData(data);
    }

    public String updateData(JSONObject data) {
        JSONObject wifi = data.getJSONObject(WIFI);
        if (wifi == null) {
            return "wifi微逆上报数据缺失wifi信息";
        }
        String sn = data.getString(SN);
        if (StrUtil.isEmpty(sn)) {
            return "wifi微逆设备号不能为空";
        }
        Inv inv = stationDeviceService.getInv(sn);
        DeviceData device = data.getObject(INV_DATA, DeviceData.class);
        if (device.getDailyEnergy() == null) {
            return "发电量数据缺失";
        }
        Instant instant = Instant.ofEpochSecond(data.getLong(TIMESTAMP));
        copyWifiToDeviceData(device, inv.getStationId(), inv.getInvId(), sn, data, instant, DeviceTypeEnum.WN, wifi);
        if (StrUtil.isEmpty(device.getTimeCode())) {
            return "时区未上传";
        }
        ZoneId zoneId = TimeZoneUtil.getZoneId(device.getTimeCode());
        if (zoneId == null) {
            return "时区参数错误";
        }
        //设备上报数据时间
        LocalDateTime createTime = instant.atZone(zoneId).toLocalDateTime();
        LocalDateTime endTime = LocalDateTime.of(createTime.toLocalDate(), TimeZoneUtil.TIME_23_59_59);
        long seconds = Duration.between(createTime, endTime).getSeconds() + 1L;
        String time = instant.atZone(ZoneId.systemDefault()).format(TimeZoneUtil.yyyyMMddHHmmss);
        if (device.getCommStatus() == 0) {
            stationDeviceService.updateInvDevice(time, device, 0);
            return null;
        }
        device.setPid(inv.getInvId());
        device.setEmuSn(sn);
        device.setCommType(0);
        device.setMonth(createTime.getMonthValue());
        device.setHm(createTime.getHour() * 100 + createTime.getMinute());
        buildPvData(device, data.getJSONObject(INV_DATA));
        //获取要缓存的数据对象
        EnergyFaultCache newCache = stationDeviceService.dataToCache(device);
        //计算单次发电量
        EnergyFaultCache oldCache = stationDeviceService.initStageDailyEnergy(seconds, device, newCache);
        threadPoolTaskExecutor.execute(() -> {
            //设备报警处理
            JSONObject oldFault = oldCache == null ? null : oldCache.getFaultCode();
            String format = createTime.format(TimeZoneUtil.yyyyMMddHHmmss);
            stationDeviceService.deviceAlarmHandle(inv.getTenantId(), format, device, newCache.getFaultCode(), oldFault);
        });
        //更新微逆版本信息
        stationDeviceService.updateInvDevice(time, device, Tools.convertDeviceStatus(device.getSysStatus()));
        //更新电站状态
        powerStationService.updateStationStatus(inv.getStationId(), time);
        //保存记录
        influxMapper.save(device);
        return null;
    }

}
