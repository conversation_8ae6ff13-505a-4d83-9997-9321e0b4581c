package com.wh.data.influxdb.mapper;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.domain.WritePrecision;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.Tools;
import com.wh.data.influxdb.dto.Param;
import com.wh.data.init.InfluxDbProperties;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.InfluxDB;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.wh.data.protocol.TopicService.DATA;
import static com.wh.data.protocol.TopicService.TIME;

/**
 * <AUTHOR>
 * @date 2024/12/7
 */
@Slf4j
@Component
public class InfluxMapper {

    @Resource
    private InfluxDB influxDB;
    @Resource
    protected InfluxDBClient influxDBClient;
    @Resource
    private InfluxDbProperties properties;
    @Value("#{!'${spring.profiles.active}'.equals('prod')}")
    private boolean profile;

    public <M> void save(M m) {
        influxDBClient.getWriteApiBlocking().writeMeasurement(WritePrecision.S, m);
    }

    public <M> void save(List<M> ms) {
        influxDBClient.getWriteApiBlocking().writeMeasurements(WritePrecision.S, ms);
    }

    /**
     * 拼接查询条件 name = '1' or name = '2'
     */
    public String orTag(String name, List<Integer> ids) {
        StringBuilder sb = new StringBuilder();
        ids.forEach(id -> sb.append(" or ").append(name).append(" = '").append(id).append("'"));
        return sb.substring(4);
    }

    private String influxQL(Param param) {
        String iql = param.iql();
        if (param.measurement() != null) {
            iql = iql.replace("{table}", param.measurement());
        }
        if (param.stationId() != null) {
            iql = iql.replace("{stationId}", param.stationId().toString());
        }
        if (param.deviceId() != null) {
            iql = iql.replace("{deviceId}", param.deviceId().toString());
        }
        if (param.deviceSn() != null) {
            iql = iql.replace("{deviceSn}", param.deviceSn());
        }
        if (param.begin() != null) {
            iql = iql.replace("{begin}", param.getBeginSecond());
            if (profile) {
                log.debug("begin: {}({})", param.begin(), param.getBeginSecond());
            }
        }
        if (param.end() != null) {
            iql = iql.replace("{end}", param.getEndSecond());
            if (profile) {
                log.debug("end:   {}({})", param.end(), param.getEndSecond());
            }
        }
        if (param.pageNum() != null && param.pageSize() != null) {
            iql = iql.replace("{limit,offset}", "limit " + param.pageSize() + " offset " + ((param.pageNum() - 1) * param.pageSize()));
        }
        if (param.iql().lastIndexOf("{tz}") > 0) {
            if (param.zoneId() != null) {
                iql = iql.replace("{tz}", "tz('"+param.zoneId().getId()+"')");
            } else {
                iql = iql.replace("{tz}", properties.getTimeZone());
            }
        }
        if (profile) {
            log.debug(iql);
        }
        return iql;
    }

    /**
     * 查询一条iql数据
     */
    public List<JSONObject> queryList(Param param) {
        return queryMultiList(param).get(0);
    }

    /**
     * 查询一条iql数据
     */
    public Map<String, BigDecimal> queryListTimeDataMap(Param param) {
        return Tools.getMap(queryMultiList(param).get(0));
    }

    /**
     * 查询一个iql一列数据
     */
    public <T> T queryObj(Param param, Class<T> tClass) {
        List<JSONObject> array = queryList(param);
        if (array.isEmpty() || array.get(0) == null) {
            return null;
        }
        return array.get(0).to(tClass);
    }

    public JSONObject queryObj(Param param) {
        List<JSONObject> array = queryList(param);
        if (array.isEmpty() || array.get(0) == null) {
            return null;
        }
        return array.get(0);
    }

    private static final JSONObject JSON_OBJECT = new JSONObject();

    /**
     * 查询多条iql的一列数据
     */
    public List<JSONObject> queryMultiObj(Param param) {
        return queryMultiList(param).stream().map(arr -> {
            if (arr.isEmpty()) {
                return JSON_OBJECT;
            }
            return arr.get(0);
        }).toList();
    }

    /**
     * 查询多条iql的一列数据一个数据
     */
    public List<BigDecimal> queryMultiObjData(Param param) {
        return queryMultiList(param).stream().map(arr -> {
            if (arr.isEmpty()) {
                return BigDecimal.ZERO;
            }
            return arr.get(0).getBigDecimal(DATA);
        }).toList();
    }

    public List<BigDecimal> queryMultiObjDataNull(Param param) {
        return queryMultiList(param).stream().map(arr -> {
            if (arr.isEmpty()) {
                return null;
            }
            return arr.get(0).getBigDecimal(DATA);
        }).toList();
    }

    /**
     * 查询多条iql列表数据
     */
    public List<List<JSONObject>> queryMultiList(Param param) {
        QueryResult query = influxDB.query(new Query(influxQL(param), properties.getDatabase()), TimeUnit.SECONDS);
        if (profile) {
            log.debug(JSON.toJSONString(query, JSONWriter.Feature.WriteNulls));
        }
        if (query.getError() != null) {
            throw new ServiceException(query.getError());
        }
        //查询多个influxQL的结果处理
        List<List<JSONObject>> results = new ArrayList<>(query.getResults().size());
        for (int i = 0; i < query.getResults().size(); i++) {
            QueryResult.Result result = query.getResults().get(i);
            if (result.getError() != null) {
                throw new ServiceException(i + " : " + result.getError());
            }
            if (result.getSeries() == null) {
                results.add(new ArrayList<>());
            } else {
                results.add(buildData(param, result.getSeries()));
            }
        }
        return results;
    }

    private List<JSONObject> buildData(Param param, List<QueryResult.Series> seriesList) {
        int size = seriesList.stream().mapToInt(s -> s.getValues().size()).sum();
        List<JSONObject> data = new ArrayList<>(size);
        for (QueryResult.Series series : seriesList) {
            List<String> columns = series.getColumns();
            Map<String, String> tags = Optional.ofNullable(series.getTags()).orElse(Collections.emptyMap());
            int objSize = tags.size() + columns.size();
            for (List<Object> value : series.getValues()) {
                if (param.dataNull() && value.get(1) == null) {
                    continue;
                }
                JSONObject obj = new JSONObject(objSize);
                long timestamp = ((Double) value.get(0)).longValue();
                if (timestamp != 0 && param.format() != null) {
                    LocalDateTime time = LocalDateTime.ofInstant(Instant.ofEpochSecond(timestamp), param.zoneId());
                    obj.put(columns.get(0), param.format().format(time));
                } else {
                    obj.put(columns.get(0), timestamp);
                }
                // 标签字段处理
                obj.putAll(tags);
                for (int j = 1; j < value.size(); j++) {
                    String key = columns.get(j);
                    Object val = value.get(j);
                    if (val instanceof Double d) {
                        if (d == 0D) {
                            obj.put(key, BigDecimal.ZERO);
                        } else {
                            BigDecimal decimal = new BigDecimal(d.toString());
                            if (param.dataFormat() && decimal.scale() > 2) {
                                obj.put(key, decimal.setScale(2, RoundingMode.HALF_UP));
                            } else {
                                obj.put(key, decimal);
                            }
                        }
                    } else {
                        obj.put(key, val);
                    }
                }
                data.add(obj);
            }
        }
        return data;
    }

    /**
     * 查询一条iql分组数据
     */
    public Map<JSONObject, List<JSONObject>> queryGroupList(Param param) {
        return queryMultiGroupList(param, null, JSONObject.class).get(0);
    }
    public <T> Map<T, List<JSONObject>> queryGroupList(Param param, String tagKey, Class<T> tClass) {
        return queryMultiGroupList(param, tagKey, tClass).get(0);
    }

    /**
     * 查询全部iql的一列分组数据
     */
    public Map<JSONObject, Map<String, JSONObject>> queryMultiGroupObj(Param param) {
        return queryMultiGroupObj(param, null, JSONObject.class);
    }
    public <T> Map<T, Map<String, JSONObject>> queryMultiGroupObj(Param param, String tagKey, Class<T> tClass) {
        List<Map<T, List<JSONObject>>> groupList = queryMultiGroupList(param, tagKey, tClass);
        Map<T, Map<String, JSONObject>> results = new LinkedHashMap<>();
        for (Map<T, List<JSONObject>> map : groupList) {
            for (Map.Entry<T, List<JSONObject>> m : map.entrySet()) {
                JSONObject json = m.getValue().get(0);
                String time = json.getString(TIME);
                Map<String, JSONObject> data = results.get(m.getKey());
                if (data == null) {
                    data =  new LinkedHashMap<>();
                    data.put(time, json);
                    results.put(m.getKey(), data);
                } else {
                    data.put(time, json);
                }
            }
        }
        return results;
    }

    /**
     * 查询多条iql分组列表数据
     */
    public <T> List<Map<T, List<JSONObject>>> queryMultiGroupList(Param param, String tagKey, Class<T> tClass) {
        QueryResult query = influxDB.query(new Query(influxQL(param), properties.getDatabase()), TimeUnit.SECONDS);
        if (profile) {
            log.debug(JSON.toJSONString(query, JSONWriter.Feature.WriteNulls));
        }
        if (query.getError() != null) {
            throw new ServiceException(query.getError());
        }
        //查询多个influxQL的结果处理
        List<Map<T, List<JSONObject>>> results = new ArrayList<>(query.getResults().size());
        for (int i = 0; i < query.getResults().size(); i++) {
            QueryResult.Result result = query.getResults().get(i);
            if (result.getError() != null) {
                throw new ServiceException(i + " : " + result.getError());
            }
            if (result.getSeries() == null) {
                results.add(new HashMap<>());
            } else {
                results.add(buildGroupData(param, result.getSeries(), tagKey, tClass));
            }
        }
        return results;
    }

    @SuppressWarnings("unchecked")
    private <T> Map<T, List<JSONObject>> buildGroupData(Param param, List<QueryResult.Series> seriesList, String tagKey, Class<T> tClass) {
        Map<T, List<JSONObject>> group = new LinkedHashMap<>(seriesList.size());
        for (QueryResult.Series series : seriesList) {
            T tagVal;
            if (tagKey != null) {
                String val = series.getTags().get(tagKey);
                if (tClass == Integer.class) {
                    tagVal = (T) Integer.valueOf(val);
                } else {
                    tagVal = (T) val;
                }
            } else {
                tagVal = (T) new JSONObject(series.getTags());
            }
            List<String> columns = series.getColumns();
            List<JSONObject> data = new ArrayList<>(series.getValues().size());
            for (List<Object> value : series.getValues()) {
                if (param.dataNull() && value.get(1) == null) {
                    continue;
                }
                JSONObject obj = new JSONObject(value.size());
                long timestamp = ((Double) value.get(0)).longValue();
                if (timestamp != 0 && param.format() != null) {
                    LocalDateTime time = LocalDateTime.ofInstant(Instant.ofEpochSecond(timestamp), param.zoneId());
                    obj.put(columns.get(0), param.format().format(time));
                } else {
                    obj.put(columns.get(0), timestamp);
                }
                for (int j = 1; j < value.size(); j++) {
                    String key = columns.get(j);
                    Object val = value.get(j);
                    if (val instanceof Double d) {
                        if (d == 0D) {
                            obj.put(key, BigDecimal.ZERO);
                        } else {
                            BigDecimal decimal = new BigDecimal(d.toString());
                            if (param.dataFormat() && decimal.scale() > 2) {
                                obj.put(key, decimal.setScale(2, RoundingMode.HALF_UP));
                            } else {
                                obj.put(key, decimal);
                            }
                        }
                    } else {
                        obj.put(key, val);
                    }
                }
                data.add(obj);
            }
            group.put(tagVal, data);
        }
        return group;
    }

}
