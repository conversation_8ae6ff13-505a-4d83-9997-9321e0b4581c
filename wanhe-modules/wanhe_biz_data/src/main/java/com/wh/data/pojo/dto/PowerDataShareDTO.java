package com.wh.data.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/12/19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PowerDataShareDTO extends PowerDataPageReqDTO {

    @Schema(description = "邮箱")
    @NotBlank
    @Email
    private String email;

}
