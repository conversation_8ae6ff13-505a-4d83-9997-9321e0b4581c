package com.wh.data.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.security.service.impl.BaseServiceImpl;
import com.wh.data.domain.FaultVersion;
import com.wh.data.mapper.FaultVersionMapper;
import com.wh.data.pojo.dto.FaultVersionQueryDTO;
import com.wh.data.service.IFaultVersionService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

import static com.ruoyi.common.security.utils.SecurityUtils.getUsername;

/**
 * <AUTHOR>
 * @date 2025/6/24
 */
@AllArgsConstructor
@Service
public class FaultVersionServiceImpl extends BaseServiceImpl<FaultVersionMapper, FaultVersion> implements IFaultVersionService {


    @Override
    public void page(FaultVersionQueryDTO dto) {

        baseMapper.page(dto.getVersionName(), dto.getVersionNo());

    }

    @Override
    public void addFaultVersion(FaultVersion faultVersion) {

        LambdaQueryWrapper<FaultVersion> queryWrapper = new LambdaQueryWrapper<FaultVersion>()
                .eq(FaultVersion::getVersionNo, faultVersion.getVersionNo());
        Long count = baseMapper.selectCount(queryWrapper);

        if (count > 0) {
            throw new RuntimeException("故障版本已存在");
        }
        faultVersion.setCreateBy(getUsername());
        faultVersion.setCreateTime(LocalDateTime.now());
        baseMapper.insert(faultVersion);
    }

    @Override
    public void updateFaultVersion(FaultVersion faultVersion) {
        LambdaQueryWrapper<FaultVersion> queryWrapper = new LambdaQueryWrapper<FaultVersion>()
                .eq(FaultVersion::getVersionNo, faultVersion.getVersionNo())
                .ne(FaultVersion::getId, faultVersion.getId());

        Long count = baseMapper.selectCount(queryWrapper);

        if (count > 0) {
            throw new RuntimeException("故障版本已存在");
        }

        baseMapper.updateById(faultVersion);
    }


}
