package com.wh.data.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/22
 */
@Data
public class PowerStationPrice {

    @Schema(description = "月份")
    private List<Integer> months;

    @Schema(description = "时间段 1尖时刻 2峰时刻 3平时刻 4谷时刻")
    private Integer timeBucket;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "HH:mm")
    private LocalTime beginTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "HH:mm")
    private LocalTime endTime;

    @Schema(description = "价格")
    private BigDecimal price;
}
