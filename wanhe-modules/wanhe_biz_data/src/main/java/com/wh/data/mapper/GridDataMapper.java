package com.wh.data.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.api.domain.GridData;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/17
 */
public interface GridDataMapper extends BaseMapper<GridData> {

    @Select("select file_name from wh_grid_file where id = #{id}")
    String getGridFileName(Integer id);

    @Select("select addr, name_en, unit, type from wh_grid_data where addr > 9")
    List<GridData> all();

}
