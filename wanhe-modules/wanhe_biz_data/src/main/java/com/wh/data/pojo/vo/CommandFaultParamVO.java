package com.wh.data.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025/5/13 16:45
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommandFaultParamVO {

    protected Integer id;

    @Schema(description = "参数ID")
    private Integer paramId;

    @Schema(description = "参数分类")
    private String paramGroup;

    @Schema(description = "中文名称")
    private String paramName;

    @Schema(description = "英文名称")
    private String paramNameEn;

    @Schema(description = "值名称")
    private String paramVarName;

    /**
     * 上报值
     */
    @Schema(description = "上报值")
    private Long reportValue;

    /**
     * 真实值
     */
    @Schema(description = "真实值")
    private Number realValue;

    @Schema(description = "宏名称")
    private String paramMacroName;

    @Schema(description = "描述")
    private String paramRemark;

    @Schema(description = "单位")
    private String paramUnit;

    @Schema(description = "读写属性")
    private String rwAttr;

    @Schema(description = "运行读")
    private String rwOfRunAttr;

    @Schema(description = "最大值")
    private String maxValue;

    @Schema(description = "最小值")
    private String minValue;

    @Schema(description = "默认值")
    private String defaultValue;

    @Schema(description = "数据类型")
    private String paramType;

    @Schema(description = "数组保存")
    private String saveAttr;
}
