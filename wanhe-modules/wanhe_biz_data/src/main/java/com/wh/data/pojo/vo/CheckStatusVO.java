package com.wh.data.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 检查状态 VO
 *
 * <AUTHOR>
 * @date 2025/05/09
 */
@Data
public class CheckStatusVO {

    @Schema(description = "网关集合")
    private List<EMU> emuList = new ArrayList<>();

    @Schema(description = "微逆集合")
    private List<Inv> invList = new ArrayList<>();

    @Schema(description = "电表集合")
    private List<Meter> meterList = new ArrayList<>();

    @Schema(description = "电池")
    private List<Battery> batteryList = new ArrayList<>();

    @Schema(description = "充电桩")
    private List<ChargeStation> chargeStationList = new ArrayList<>();

    @Data
    public static class EMU {
        private Integer id;
        @Schema(description = "电站id")
        private Integer powerStationId;

        @Schema(description = "序列号")
        private String number;

        @Schema(description = "设备类型 （0：EMU 1:微逆 2：电表）有pid是emu微逆 没有就是wifi微逆")
        private Integer deviceType;

        @Schema(description = "设备状态 0离线 1正常 2告警 3故障 4停机 5自检")
        private Integer status;

        @Schema(description = "设备型号")
        private String deviceModel;

        @Schema(description = "软件版本")
        private String softwareVersion;

        @Schema(description = "硬件版本")
        private String hardwareVersion;

        @Schema(description = "连接微逆数量")
        private Integer wnNum;

        @Schema(description = "最后报告时间")
        private LocalDateTime lastReportTime;


    }

    @Data
    public static class Inv {

        private Integer id;
        @Schema(description = "序列号")
        private String number;

        @Schema(description = "EMU设备id")
        private Integer pid;

        @Schema(description = "emu设备序列号")
        private String emuNumber;

        @Schema(description = "设备类型 （0：EMU 1:微逆 2：电表）有pid是emu微逆 没有就是wifi微逆")
        private Integer deviceType;

        @Schema(description = "设备状态 0离线 1在线 2告警 3故障 4停机 5自检 6预启动")
        private Integer status;

        @Schema(description = "设备型号")
        private String deviceModel;

        @Schema(description = "软件版本")
        private String softwareVersion;

        @Schema(description = "硬件版本")
        private String hardwareVersion;

        @Schema(description = "AC瞬时功率")
        private BigDecimal acTempPower;

        @Schema(description = "AC电压实际值")
        private BigDecimal acVolReal;

        @Schema(description = "AC电流实际值")
        private BigDecimal acCurrReal;

        @Schema(description = "最后报告时间")
        private LocalDateTime lastReportTime;
    }

    @Data
    public static class Meter {
        private Integer id;
        @Schema(description = "序列号")
        private String number;

        @Schema(description = "设备类型 （0：EMU 1:微逆 2：电表）有pid是emu微逆 没有就是wifi微逆")
        private Integer deviceType;

        @Schema(description = "设备型号")
        private String deviceModel;

        @Schema(description = "电表位置 0负载侧电表 1光伏测电表 2并网点电表")
        private Integer powerGridSeat;

        @Schema(description = "电网-实时功率")
        private String power;

        @Schema(description = "输入电量")
        private String forwardEnergy;

        @Schema(description = "输出电量")
        private String reverseEnergy;

        @Schema(description = "设备状态 0离线 1在线 2告警 3故障 4停机 5自检 6预启动")
        private Integer status;

        @Schema(description = "最后报告时间")
        private LocalDateTime lastReportTime;

    }

    @Data
    public static class Battery {
        private Integer id;
        @Schema(description = "序列号")
        private String number;

        @Schema(description = "设备类型 （0：EMU 1:微逆 2：电表）有pid是emu微逆 没有就是wifi微逆")
        private Integer deviceType;

        @Schema(description = "最后报告时间")
        private LocalDateTime lastReportTime;
    }

    @Data
    public static class ChargeStation {
        private Integer id;
        @Schema(description = "序列号")
        private String number;

        @Schema(description = "设备类型 （0：EMU 1:微逆 2：电表）有pid是emu微逆 没有就是wifi微逆")
        private Integer deviceType;

        @Schema(description = "最后报告时间")
        private LocalDateTime lastReportTime;
    }

}
