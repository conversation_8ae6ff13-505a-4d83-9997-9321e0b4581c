package com.wh.data.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/8
 */
@Getter
@AllArgsConstructor
public enum FaultWaveEnum {

    fw219(219, "采样频率", "R_O", new BigDecimal("10000"), new BigDecimal("30000"), BigDecimal.ZERO, "Hz", "TYPE_U16", 1),
    fw220(220, "采样间隔", "W_O", new BigDecimal("1"), new BigDecimal("10000"), BigDecimal.ZERO, "Hz", "TYPE_U16", 1),
    fw221(221, "触发前百分比", "W_O", new BigDecimal("20"), new BigDecimal("100"), BigDecimal.ZERO, "Hz", "TYPE_U16", 1),
    fw224(224, "触发类型", "R_W", new BigDecimal("2"), new BigDecimal("3"), BigDecimal.ZERO, "Hz", "TYPE_U8", 1),
    fw225(225, "触发ID", "W_O", new BigDecimal("16"), new BigDecimal("100"), BigDecimal.ZERO, "Hz", "TYPE_U16", 1),
    fw226(226, "触发位", "W_O", new BigDecimal("3"), new BigDecimal("15"), BigDecimal.ZERO, "Hz", "TYPE_U16", 1),
    fw227(227, "触发门限", "W_O", new BigDecimal("10"), new BigDecimal("100"), BigDecimal.ZERO, "Hz", "TYPE_F32", 2),
    fw228(228, "通道0", "W_O", new BigDecimal("82"), new BigDecimal("60000"), BigDecimal.ZERO, "Hz", "TYPE_U16", 1),
    fw229(229, "通道1", "W_O", new BigDecimal("55"), new BigDecimal("60000"), BigDecimal.ZERO, "Hz", "TYPE_U16", 1),
    fw230(230, "通道2", "W_O", new BigDecimal("58"), new BigDecimal("60000"), BigDecimal.ZERO, "Hz", "TYPE_U16", 1),
    fw231(231, "通道3", "W_O", new BigDecimal("60"), new BigDecimal("60000"), BigDecimal.ZERO, "Hz", "TYPE_U16", 1),
    fw232(232, "通道4", "W_O", new BigDecimal("65535"), new BigDecimal("60000"), BigDecimal.ZERO, "Hz", "TYPE_U16", 1),
    fw233(233, "通道5", "W_O", new BigDecimal("65535"), new BigDecimal("60000"), BigDecimal.ZERO, "Hz", "TYPE_U16", 0);

    private final Integer addr;
    private final String name;
    private final String rw;
    private final BigDecimal value;
    private final BigDecimal max;
    private final BigDecimal min;
    private final String unit;
    private final String type;
    private final Integer type2;

    public static final Map<Integer, Integer> TYPE_MAP = Arrays.stream(FaultWaveEnum.values()).collect(Collectors.toMap(FaultWaveEnum::getAddr, FaultWaveEnum::getType2));

}
