package com.wh.data.pojo.vo;

import com.wh.data.influxdb.entity.DeviceData;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * emu和微逆数据
 * <AUTHOR>
 * @date 2024/11/27
 */
@Setter
@Getter
public class DeviceDataVO extends DeviceData {

    //设备型号
    private String model;

    //创建时间
    private Long time;

    //最后报告时间
    private LocalDateTime lastReportTime;

}
