package com.wh.data.pojo.station;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ruoyi.common.core.json.BigDecimalSerializer;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/1/12
 */
@Data
public class MeterDataVO {

    // 电表序列号
    private String sn;

    //电表类型 0负载侧电表 1光伏测电表 2并网点电表
    private Integer type;

    // 和EMU的通信状态，0：通信异常，1：正常
    private Integer commStatus;

    // 实时功率,kW
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal power;

    // 反向有功电能,kWh
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal reverseEnergy;

    // 正向有功电能,kWh
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal forwardEnergy;

}
