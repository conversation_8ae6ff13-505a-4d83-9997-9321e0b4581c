package com.wh.data.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.domain.IntStr;
import com.ruoyi.common.core.enums.DeviceTypeEnum;
import com.ruoyi.common.core.enums.LanguageEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.I18nUtil;
import com.ruoyi.common.core.utils.Tools;
import com.ruoyi.common.redis.RedisUtil;
import com.ruoyi.common.security.service.impl.MPJBaseServiceImpl;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.dto.*;
import com.wanhe.common.mail.MailUtil;
import com.wanhe.common.sms.SmsService;
import com.wanhe.common.sms.dto.FaultNotifiedDTO;
import com.wh.data.domain.*;
import com.wh.data.influxdb.dto.Param;
import com.wh.data.influxdb.entity.DeviceData;
import com.wh.data.influxdb.entity.MeterData;
import com.wh.data.influxdb.mapper.InfluxMapper;
import com.wh.data.mapper.DeviceAlarmMapper;
import com.wh.data.mapper.FaultMapper;
import com.wh.data.mapper.StationDeviceMapper;
import com.wh.data.pojo.vo.DeviceDataVO;
import jakarta.annotation.Resource;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/27
 */
@Getter
@Service
public class StationDeviceService extends MPJBaseServiceImpl<StationDeviceMapper, StationDevice> {

    @Resource
    private StationDeviceMapper stationDeviceMapper;
    @Resource
    private PowerStationService powerStationService;
    @Resource
    private DeviceAlarmMapper deviceAlarmMapper;
    @Resource
    private FaultMapper faultMapper;
    @Resource
    private InfluxMapper influxMapper;
    @Resource
    private SmsService smsService;
    @Resource
    private FaultNoticeService faultNoticeService;
    @Value("${emq.basic}")
    private String authorization;
    @Value("${emq.url}")
    private String baseUrl;
    @Value("${fault.language}")
    private String faultLanguage;
    @Resource
    private RestTemplate restTemplate;

    public StationDevice getBySn(String sn) {
        return stationDeviceMapper.getBySn(sn);
    }

    public Long getTenantId(Integer pwId) {
        return powerStationService.getBaseMapper().getTenantId(pwId);
    }

    public boolean isOnline(String sn, Integer deviceType) {
        String url = baseUrl + "/api/v5/clients/";
        switch (deviceType) {
            case 0 -> url += "EMU_" + sn;
            case 1 -> url += "INV_" + sn;
        }
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", authorization);
        try {
            JSONObject obj = restTemplate.exchange(url, HttpMethod.GET, new HttpEntity<>(headers), JSONObject.class).getBody();
            if (obj == null) {
                return false;
            }
            return obj.getBooleanValue("connected");
        } catch (HttpClientErrorException e) {
            JSONObject json = JSON.parseObject(e.getResponseBodyAsString());
            if (json.getString("code").equals("CLIENTID_NOT_FOUND")) {
                return false;
            }
            log.error(json.toString());
            throw new ServiceException("查询客户端信息异常{0}", json.toString());
        } catch (Exception e) {
            log.error(url, e);
            throw new ServiceException("查询客户端信息异常{0}", sn);
        }
    }

    public Inv getInv(String sn) {
        Inv inv = RedisUtil.get(CacheConstants.INV + sn, Inv.class);
        if (inv != null) {
            return inv;
        }
        StationDevice device = baseMapper.getBySn(sn);
        if (device == null) {
            return null;
        }
        inv = new Inv();
        inv.setInvId(device.getId());
        inv.setInvSn(device.getNumber());
        inv.setStationId(device.getPowerStationId());
        inv.setTenantId(device.getTenantId());
        inv.setStationCreateTime(device.getCreateTime());
        RedisUtil.set(CacheConstants.INV + sn, JSON.toJSONString(inv), Duration.ofDays(365));
        return inv;
    }

    public EmuInv getEmuInv(String emuSn) {
        EmuInv emuInv = RedisUtil.get(CacheConstants.EMU_INV + emuSn, EmuInv.class);
        if (emuInv != null) {
            if (emuInv.getInvMap() == null) {
                emuInv.setInvMap(Collections.emptyMap());
            }
            return emuInv;
        }
        StationDevice emu = baseMapper.getBySn(emuSn);
        if (emu == null) {
            return null;
        }
        emuInv = new EmuInv();
        emuInv.setEmuId(emu.getId());
        emuInv.setEmuSn(emu.getNumber());
        emuInv.setStationId(emu.getPowerStationId());
        emuInv.setTenantId(emu.getTenantId());
        emuInv.setStationCreateTime(emu.getCreateTime());
        List<StationDevice> inv = baseMapper.getByStationIdDeviceType(emu.getPowerStationId(), DeviceTypeEnum.WN.getVal());
        if (!inv.isEmpty()) {
            emuInv.setInvMap(inv.stream().collect(Collectors.toMap(StationDevice::getNumber, StationDevice::getId)));
        }
        RedisUtil.set(CacheConstants.EMU_INV + emuSn, JSON.toJSONString(emuInv), Duration.ofDays(365));
        return emuInv;
    }

    public EmuMeter getEmuMeter(String emuSn) {
        EmuMeter emuMeter = RedisUtil.get(CacheConstants.EMU_METER + emuSn, EmuMeter.class);
        if (emuMeter != null) {
            if (emuMeter.getMeterMap() == null) {
                emuMeter.setMeterMap(Collections.emptyMap());
            }
            return emuMeter;
        }
        StationDevice emu = baseMapper.getBySn(emuSn);
        if (emu == null) {
            return null;
        }
        emuMeter = new EmuMeter();
        emuMeter.setEmuId(emu.getId());
        emuMeter.setEmuSn(emu.getNumber());
        emuMeter.setStationId(emu.getPowerStationId());
        emuMeter.setTenantId(emu.getTenantId());
        emuMeter.setStationCreateTime(emu.getCreateTime());
        List<StationDevice> meters = baseMapper.getByStationIdDeviceType(emu.getPowerStationId(), DeviceTypeEnum.DB.getVal());
        if (!meters.isEmpty()) {
            emuMeter.setMeterMap(meters.stream().collect(Collectors.toMap(StationDevice::getNumber, d -> new EmuMeter.Meter(d.getId(), d.getPowerGridSeat()))));
        }
        RedisUtil.set(CacheConstants.EMU_METER + emuSn, JSON.toJSONString(emuMeter), Duration.ofDays(365));
        return emuMeter;
    }

    /**
     * 计算单次用电量
     */
    public void initStageConsumeEnergy(long seconds, MeterData meter) {
        String key = CacheConstants.METER_DATA_KEY + meter.getSn();
        MeterCache oldCache = RedisUtil.get(key, MeterCache.class);
        if (oldCache == null) {
            String iql = "select reverseEnergy, forwardEnergy from {table} where deviceId = '{deviceId}' order by time desc limit 1";
            Param param = Param.of(MeterData.MN, iql).deviceId(meter.getDeviceId());
            MeterData lastData = influxMapper.queryObj(param, MeterData.class);
            if (lastData != null) {
                oldCache = dataToCache(lastData);
            }
        }
        if (oldCache == null) {
            meter.setStageReverseEnergy(meter.getReverseEnergy());
            meter.setStageForwardEnergy(meter.getForwardEnergy());
            meter.setStageEnergy(meter.getStageForwardEnergy().subtract(meter.getStageReverseEnergy()));
        } else {
            MeterCache newCache = dataToCache(meter);
            if (newCache.getForwardEnergy().compareTo(oldCache.getForwardEnergy()) < 0) {
                newCache.setForwardEnergy(oldCache.getForwardEnergy());
            }
            if (newCache.getReverseEnergy().compareTo(oldCache.getReverseEnergy()) < 0) {
                newCache.setReverseEnergy(oldCache.getReverseEnergy());
            }
            RedisUtil.set(key, newCache, Duration.ofSeconds(seconds));
            if (meter.getForwardEnergy().compareTo(oldCache.getForwardEnergy()) <= 0) {
                meter.setStageForwardEnergy(BigDecimal.ZERO);
            } else {
                meter.setStageForwardEnergy(meter.getForwardEnergy().subtract(oldCache.getForwardEnergy()));
            }
            if (meter.getReverseEnergy().compareTo(oldCache.getReverseEnergy()) <= 0) {
                meter.setStageReverseEnergy(BigDecimal.ZERO);
            } else {
                meter.setStageReverseEnergy(meter.getReverseEnergy().subtract(oldCache.getReverseEnergy()));
            }
            meter.setStageEnergy(meter.getStageForwardEnergy().subtract(meter.getStageReverseEnergy()));
        }
    }

    /**
     * 计算单次发电量
     */
    public EnergyFaultCache initStageDailyEnergy(long seconds, DeviceData device, EnergyFaultCache newCache) {
        String key = CacheConstants.ENERGY_FAULT_KEY + device.getSn();
        EnergyFaultCache oldCache = RedisUtil.get(key, EnergyFaultCache.class);
        if (oldCache == null) {
            String iql = "select * from {table} where deviceId = '{deviceId}' order by time desc limit 1";
            Param param = Param.of(DeviceData.MN, iql).deviceId(device.getDeviceId());
            DeviceDataVO lastData = influxMapper.queryObj(param, DeviceDataVO.class);
            if (lastData != null) {
                oldCache = dataToCache(lastData);
            }
        }
        if (oldCache == null) {
            device.setStageDailyEnergy(device.getDailyEnergy());
        } else {
            if (device.getDailyEnergy().compareTo(oldCache.getDailyEnergy()) > 0) {
                device.setStageDailyEnergy(device.getDailyEnergy().subtract(oldCache.getDailyEnergy()));
            } else {
                device.setStageDailyEnergy(BigDecimal.ZERO);
            }
        }
        RedisUtil.set(key, newCache, Duration.ofSeconds(seconds));
        return oldCache;
    }

    public EnergyFaultCache dataToCache(DeviceData data) {
        EnergyFaultCache cache = new EnergyFaultCache();
        cache.setDailyEnergy(data.getDailyEnergy());
//        cache.setTotalEnergy(data.getTotalEnergy());
        cache.setCommStatus(data.getCommStatus());
        cache.setSysStatus(data.getSysStatus());
        cache.setFaultCode(JSON.parseObject(data.getFaultCode()));
        return cache;
    }

    public MeterCache dataToCache(MeterData data) {
        MeterCache cache = new MeterCache();
        cache.setForwardEnergy(data.getForwardEnergy());
        cache.setReverseEnergy(data.getReverseEnergy());
        return cache;
    }

    public void updateInvDevice(String time, DeviceData data, int status) {
        lambdaUpdate().eq(StationDevice::getNumber, data.getSn())
                .setSql("activate_time = ifnull(activate_time, '" + time + "')")
                .set(StationDevice::getLastReportTime, time)
                .set(StationDevice::getCommType, data.getCommType())
                .set(StationDevice::getCommStatus, data.getCommStatus())
                .set(StationDevice::getHardwareVersion, data.getHardVer())
                .set(StationDevice::getSoftwareVersion, data.getSoftVer())
                .set(StationDevice::getHardwareWifiVersion, data.getWifiHardVer())
                .set(StationDevice::getSoftwareWifiVersion, data.getWifiSoftVer())
                .set(StationDevice::getFaultCode, data.getFaultCode())
                .set(StationDevice::getStatus, status)
                .update();
    }

    public void updateEmuDevice(String time, DeviceData data) {
        lambdaUpdate().eq(StationDevice::getNumber, data.getSn())
                .setSql("activate_time = ifnull(activate_time, '" + time + "')")
                .set(StationDevice::getLastReportTime, time)
                .set(StationDevice::getCommType, data.getCommType())
                .set(StationDevice::getHardwareVersion, data.getWifiHardVer())
                .set(StationDevice::getSoftwareVersion, data.getWifiSoftVer())
                .set(StationDevice::getHardwareWifiVersion, data.getWifiHardVer())
                .set(StationDevice::getSoftwareWifiVersion, data.getWifiSoftVer())
                .set(StationDevice::getStatus, 1)
                .update();
    }

    public void updateMeterDevice(String time, MeterData data, int status) {
        lambdaUpdate().eq(StationDevice::getNumber, data.getSn())
                .setSql("activate_time = ifnull(activate_time, '" + time + "')")
                .set(StationDevice::getLastReportTime, time)
                .set(StationDevice::getCommStatus, data.getCommStatus())
                .set(StationDevice::getStatus, status)
                .update();
    }

    /**
     * 通过电站ID查询全部微逆EMU
     */
    public List<String> getEmuByStationId(Integer stationId, DeviceTypeEnum deviceType) {
        return lambdaQuery().select(StationDevice::getNumber)
                .eq(StationDevice::getPowerStationId, stationId)
                .eq(StationDevice::getDeviceType, deviceType.getVal())
                .eq(StationDevice::getDeleted, 0)
                .orderByAsc(StationDevice::getDeviceType, StationDevice::getNumber)
                .list().stream().map(StationDevice::getNumber).toList();
    }


    /**
     * 通过emuSn查询全部微逆sn
     */
    public List<String> getWnByEmu(String emuSn) {
        return lambdaQuery().select(StationDevice::getNumber)
                .eq(StationDevice::getEmuNumber, emuSn)
                .eq(StationDevice::getDeviceType, DeviceTypeEnum.WN.getVal())
                .orderByAsc(StationDevice::getNumber)
                .list().stream().map(StationDevice::getNumber).toList();
    }

    public List<String> getWnByEmu(List<String> emuSnList) {
        return lambdaQuery().select(StationDevice::getNumber)
                .in(StationDevice::getEmuNumber, emuSnList)
                .eq(StationDevice::getDeviceType, DeviceTypeEnum.WN.getVal())
                .orderByAsc(StationDevice::getNumber)
                .list().stream().map(StationDevice::getNumber).toList();
    }

    public Map<String, String> getFaultData(String lang) {
        Map<String, String> faultData = RedisUtil.entries(CacheConstants.FAULT_DATA_KEY + lang);
        if (faultData.isEmpty()) {
            faultData = faultMapper.all(lang).stream().collect(Collectors.toMap(Fault::getFaultKey, JSON::toJSONString));
            RedisUtil.putAll(CacheConstants.FAULT_DATA_KEY + lang, faultData);
        }
        return faultData;
    }


    public static void main(String[] args) {
//        System.out.println(Base64.getEncoder().encodeToString("25c941e678c502a4:oGRUEeUlkWnPnQieNoqGUhXLfaewzQYiJwvA1QqSZWL".getBytes()));

        char[] array = Integer.toBinaryString(64).toCharArray();
        int count = array.length - 1;
        for (int j = count; j >= 0; j--) {
            char c = array[j];
            System.out.println(c + ":" + (count - j));
        }
    }

    private List<DeviceAlarm> getFaultList(Long tenantId, String createTime, DeviceData device, JSONObject newFault) {
        Map<String, String> faultData = getFaultData(faultLanguage);
        List<DeviceAlarm> faultList = new ArrayList<>();
        for (Map.Entry<String, Object> map : newFault.entrySet()) {
            int data = (Integer) map.getValue();
            if (data != 0) {
                char[] bits = Integer.toBinaryString(data).toCharArray();
                int length = bits.length - 1;
                for (int i = length; i >= 0; i--) {
                    if (bits[i] == '1') {
                        int bit = length - i;
                        String faultKey = Tools.getFaultKey(map.getKey(), bit);
                        String value = faultData.get(faultKey);
                        if (value != null) {
                            DeviceAlarm alarm = new DeviceAlarm();
                            alarm.setDeviceId(device.getDeviceId());
                            alarm.setPowerStationId(device.getPowerStationId());
                            alarm.setSnNumber(device.getSn());
                            alarm.setAlarmCode(map.getKey());
                            alarm.setAlarmKey(faultKey);
                            alarm.setAlarmName(JSON.parseObject(value).getString("faultDescription"));
                            alarm.setAlarmSuggest(JSON.parseObject(value).getString("resolutionSuggestion"));
                            alarm.setAlarmData(bit);
                            alarm.setOccurredAt(createTime);
                            alarm.setTenantId(tenantId);
                            alarm.setDeviceType(device.getDeviceType());
                            faultList.add(alarm);
                        }
                    }
                }
            }
        }
        return faultList;
    }

    /**
     * 设备报警处理
     */
    public void deviceAlarmHandle(Long tenantId, String createTime, DeviceData device, JSONObject newFault, JSONObject oldFault) {
        //数据第一次上报
        if (oldFault == null) {
            // 无故障
            if (newFault.values().stream().allMatch(bit -> (Integer) bit == 0)) return;
            // 第一次故障
            List<DeviceAlarm> faultAlarmList = getFaultList(tenantId, createTime, device, newFault);
            if (!faultAlarmList.isEmpty()) {
                deviceAlarmMapper.batchSave(faultAlarmList);
                sendFaultNotice(faultAlarmList);
            }
        } else {
            // 故障已全部处理
            if (newFault.values().stream().allMatch(bit -> (Integer) bit == 0)) {
                int alarmCount = deviceAlarmMapper.getAlarmCount(device.getDeviceId());
                if (alarmCount == 0) {
                    return;
                }
                deviceAlarmMapper.updateAlarmAll(createTime, device.getDeviceId());
            }
            //新故障
            List<DeviceAlarm> faultAlarmList = getFaultList(tenantId, createTime, device, newFault);
            if (faultAlarmList.isEmpty()) {
                return;
            }
            //旧故障
            List<DeviceAlarm> oldAlarm = deviceAlarmMapper.getAlarm(device.getDeviceId());
            //没有旧故障
            if (oldAlarm.isEmpty()) {
                deviceAlarmMapper.batchSave(faultAlarmList);
                sendFaultNotice(faultAlarmList);
                //存在旧故障
            } else {
                Set<String> newSet = faultAlarmList.stream().map(a -> Tools.getFaultKey(a.getAlarmCode(), a.getAlarmData())).collect(Collectors.toUnmodifiableSet());
                Set<String> oldSet = oldAlarm.stream().map(a -> Tools.getFaultKey(a.getAlarmCode(), a.getAlarmData())).collect(Collectors.toUnmodifiableSet());
                //找出已经修复的更新
                List<Integer> repairId = oldAlarm.stream().filter(a -> !newSet.contains(Tools.getFaultKey(a.getAlarmCode(), a.getAlarmData()))).map(DeviceAlarm::getId).toList();
                if (!repairId.isEmpty()) {
                    deviceAlarmMapper.updateAlarms(createTime, join(repairId));
                }
                //找出新增的插入
                List<DeviceAlarm> addList = faultAlarmList.stream().filter(a -> !oldSet.contains(Tools.getFaultKey(a.getAlarmCode(), a.getAlarmData()))).toList();
                if (!addList.isEmpty()) {
                    deviceAlarmMapper.batchSave(addList);
                    sendFaultNotice(addList);
                }
            }
        }
    }

    private List<String> getFaultNoticeCache(Integer powerStationId) {
        String key = CacheConstants.FAULT_NOTICE_KEY + powerStationId;
        List<String> keys = new ArrayList<>(5);
        keys.add(key);
        keys.add(CacheConstants.SYS_CONFIG_KEY + "sms.start");
        keys.add(CacheConstants.SYS_CONFIG_KEY + "mail.start");
        keys.add(CacheConstants.SYS_CONFIG_KEY + "message.start");
        //客户电话
        keys.add(CacheConstants.SYS_CONFIG_KEY + "service.tel");
        List<String> valList = RedisUtil.multiGet(keys);
        if (valList.get(0) == null) {
            valList.set(0, powerStationService.getBaseMapper().getFaultNotice(powerStationId));
            RedisUtil.set(key, valList.get(0));
        }
        if (StrUtil.isBlank(valList.get(0)) || valList.get(0).equals("[]") || valList.stream().filter(StrUtil::isNotBlank).noneMatch("true"::contains)) {
            return null;
        }
        return valList;
    }

    private void sendFaultNotice(List<DeviceAlarm> faultAlarmList) {
        List<String> valList = getFaultNoticeCache(faultAlarmList.get(0).getPowerStationId());
        for (DeviceAlarm alarm : faultAlarmList) {
            sendNotice(valList, alarm);
        }
    }

    private void sendFaultNotice(Long tenantId, Integer powerStationId, Integer deviceId, String sn, DeviceTypeEnum deviceType) {
        DeviceAlarm deviceAlarm = new DeviceAlarm();
        deviceAlarm.setPowerStationId(powerStationId);
        deviceAlarm.setDeviceId(deviceId);
        deviceAlarm.setSnNumber(sn);
        deviceAlarm.setDeviceType(deviceType.getVal());
        deviceAlarm.setAlarmName(I18nUtil.get("设备离线", LanguageEnum.getLocal(faultLanguage)));
        deviceAlarm.setAlarmSuggest("-");
        deviceAlarm.setOccurredAt(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        List<String> valList = getFaultNoticeCache(powerStationId);
        sendNotice(valList, deviceAlarm);
    }
    public void sendNotice(List<String> valList, DeviceAlarm deviceAlarm) {
        if (valList != null) {
            for (SysUser user : powerStationService.getBaseMapper().getStationUser(deviceAlarm.getPowerStationId())) {
                //发送短信
                if (StrUtil.isNotBlank(user.getPhonenumber()) && valList.get(0).contains("0") && "true".equals(valList.get(1))) {
                    FaultNotifiedDTO dto = new FaultNotifiedDTO();
                    dto.setUserName(user.getNickName());
                    dto.setStationName(user.getRemark());
                    dto.setStationId(deviceAlarm.getPowerStationId());
                    dto.setDeviceSN(deviceAlarm.getSnNumber());
                    dto.setFaultName(deviceAlarm.getAlarmName());
                    dto.setPhoneNumber(valList.get(4));
                    dto.setPhone(Tools.getArea(user.getAreaCode()) + user.getPhonenumber());
                    smsService.sendFaultNotified(dto);
                }
                String content = StrUtil.format(I18nUtil.get("设备故障邮件", LanguageEnum.getLocal(faultLanguage)), user.getNickName(),
                        user.getRemark(), deviceAlarm.getPowerStationId(), deviceAlarm.getSnNumber(), deviceAlarm.getAlarmName(),
                        deviceAlarm.getOccurredAt(), deviceAlarm.getAlarmSuggest(), valList.get(4));
                String title = deviceAlarm.getSnNumber() + I18nUtil.get("设备故障通知", LanguageEnum.getLocal(faultLanguage));
                //发送邮件
                if (StrUtil.isNotBlank(user.getEmail()) && valList.get(0).contains("1") && "true".equals(valList.get(2))) {
                    MailUtil.send(user.getEmail(), title, content);
                    MailUtil.send("<EMAIL>", title, content, true);
                }
                //发送站内信
                if (user.getUserId() != null && valList.get(0).contains("2") && "true".equals(valList.get(3))) {
                    JSONObject data = new JSONObject();
                    data.put("nickName", user.getNickName());
                    data.put("stationName", user.getRemark());
                    data.put("plantId", deviceAlarm.getPowerStationId());
                    data.put("deviceSn", deviceAlarm.getSnNumber());
                    data.put("alarmName", deviceAlarm.getAlarmName());
                    data.put("startTime", deviceAlarm.getOccurredAt());
                    data.put("suggest", deviceAlarm.getAlarmSuggest());
                    data.put("phone", valList.get(4));
                    FaultNotice notice = new FaultNotice();
                    notice.setUserId(user.getUserId());
                    notice.setTitle(title);
                    notice.setContent(content);
                    notice.setContentJson(data.toString());
                    notice.setCreateTime(LocalDateTime.now());
                    faultNoticeService.save(notice);
                }
            }
        }
    }

    /**
     * @param sn     设备号
     * @param status 0离线 1在线
     */
    public void emuUpdateStatus(String sn, Integer status) {
        EmuInv emu = getEmuInv(sn);
        if (emu != null) {
            lambdaUpdate().eq(StationDevice::getNumber, sn).set(StationDevice::getStatus, status).update();
            lambdaUpdate().eq(StationDevice::getEmuNumber, sn).set(StationDevice::getStatus, status).update();
            if (status == 0) {
                powerStationService.lambdaUpdate().eq(PowerStation::getId, emu.getStationId()).set(PowerStation::getStationStatus, 2).update();
                sendFaultNotice(emu.getTenantId(), emu.getStationId(), emu.getEmuId(), sn, DeviceTypeEnum.EMU);
            } else {
                Long count = lambdaQuery().eq(StationDevice::getPowerStationId, emu.getStationId()).ne(StationDevice::getStatus, 1).count();
                if (count == 0) {
                    powerStationService.lambdaUpdate().eq(PowerStation::getId, emu.getStationId()).set(PowerStation::getStationStatus, 1).update();
                }
            }
        }
    }

    /**
     * @param sn     设备号
     * @param status 0离线 1在线
     */
    public void invUpdateStatus(String sn, Integer status) {
        Inv inv = getInv(sn);
        if (inv != null) {
            lambdaUpdate().eq(StationDevice::getNumber, sn).set(StationDevice::getStatus, status).update();
            if (status == 0) {
                powerStationService.lambdaUpdate().eq(PowerStation::getId, inv.getStationId()).set(PowerStation::getStationStatus, 2).update();
                sendFaultNotice(inv.getTenantId(), inv.getStationId(), inv.getInvId(), sn, DeviceTypeEnum.WN);
            } else {
                Long count = lambdaQuery().eq(StationDevice::getPowerStationId, inv.getStationId()).ne(StationDevice::getStatus, 1).count();
                if (count == 0) {
                    powerStationService.lambdaUpdate().eq(PowerStation::getId, inv.getStationId()).set(PowerStation::getStationStatus, 1).update();
                }
            }
        }
    }

    /**
     * 判断设备号是否存在
     */
    public StationDevice exists(String sn) {
        StationDevice device = noAuthExists(sn);
        SysUser user = getSysUser();
        if (SysUser.isStaff(user.getUserType()) && isNotAdminRole(user.getRoleIds())) {
            Long count = powerStationService.lambdaQuery()
                    .eq(PowerStation::getId, device.getPowerStationId())
                    .eq(PowerStation::getPermission, false)
                    .count();
            if (count > 0) {
                throw new ServiceException("业主未授权");
            }
        }
        return device;
    }

    public StationDevice noAuthExists(String sn) {
        StationDevice device = baseMapper.getBySn(sn);
        if (device == null) {
            throw new ServiceException("设备不存在");
        }
        //设备状态离线
        if (device.getStatus() == 0) {
            //没有pid的设备是emu或wifi微逆
            if (device.getPid() == null) {
                if (isOnline(sn, device.getDeviceType())) {
                    lambdaUpdate().eq(StationDevice::getId, device.getId()).set(StationDevice::getStatus, 1).update();
                    return device;
                }
            }
            throw new ServiceException("设备已离线");
        }
        return device;
    }

    /**
     * 判断设备号是否存在
     */
    public void exists(List<String> sn) {
        if (lambdaQuery().in(StationDevice::getNumber, sn).gt(StationDevice::getStatus, 0).count() != sn.size()) {
            throw new ServiceException("有设备不存在或已离线");
        }
        SysUser user = getSysUser();
        if (SysUser.isStaff(user.getUserType()) && isNotAdminRole(user.getRoleIds())) {
            Long count = lambdaJoinQuery()
                    .innerJoin(PowerStation.class, i -> i.eq(PowerStation::getId, StationDevice::getPowerStationId)
                            .in(StationDevice::getNumber, sn)
                            .eq(PowerStation::getPermission, false)
                            .eq(PowerStation::getDeleted, 0))
                    .count();
            if (count > 0) {
                throw new ServiceException("业主未授权");
            }
        }
    }

    public void noAuthExists(List<String> sn) {
        if (lambdaQuery().in(StationDevice::getNumber, sn).gt(StationDevice::getStatus, 0).count() != sn.size()) {
            throw new ServiceException("有设备不存在或已离线");
        }
    }

    public Map<Integer, String> getIdSnMap(Collection<Integer> ids) {
        return baseMapper.getIdNumber(join(ids)).stream().collect(Collectors.toMap(IntStr::getK, IntStr::getV));
    }

}
