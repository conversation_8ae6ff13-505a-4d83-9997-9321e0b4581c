package com.wh.data.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.ClientTypeEnum;
import com.ruoyi.common.core.enums.LanguageEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.I18nUtil;
import com.ruoyi.common.core.utils.NumUtil;
import com.ruoyi.common.core.utils.TimeZoneUtil;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.Sql;
import com.ruoyi.system.api.dto.PowerStationListDataVO;
import com.wanhe.common.mail.MailUtil;
import com.wh.data.domain.PowerStation;
import com.wh.data.influxdb.service.DeviceDataService;
import com.wh.data.influxdb.service.ReportDataService;
import com.wh.data.pojo.dto.*;
import com.wh.data.pojo.vo.*;
import com.wh.data.service.PowerStationExtendService;
import com.wh.data.service.StationDeviceService;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 设备数据
 */
@RestController
@RequestMapping("/inv/data")
public class DeviceDataController extends BaseController {

    @Resource
    private DeviceDataService deviceDataService;
    @Resource
    private ReportDataService reportDataService;
    @Resource
    private PowerStationExtendService powerStationExtendService;
    @Resource
    private StationDeviceService stationDeviceService;

    public static void main(String[] args) {
        BigDecimal b1 = new BigDecimal("0.2518814504146576");
        BigDecimal b2 = new BigDecimal("0.253693163394928");
        System.out.println(b2.subtract(b1));
    }

    /**
     * 定时任务 统计acTempPower功率和发电量，检查是否离线
     */
    @GetMapping(value = "/sync/data")
    public R<Void> syncData() {
        List<Map<String, String>> timeZone = powerStationExtendService.getBaseMapper().getTimeZone();
        for (Map<String, String> map : timeZone) {
            powerStationExtendService.syncData(map);
        }
        powerStationExtendService.syncData1();
        for (Map<String, String> map : timeZone) {
            powerStationExtendService.syncData2(map);
        }
        return R.ok();
    }

    /**
     * 定时任务 发电量每天凌晨置空
     */
    @GetMapping(value = "/generation")
    public R<Void> generation() {
//        deviceDataService.repairMeterData();
//        deviceDataService.repairDeviceData(LocalDate.now().plusDays(-1));
        return R.ok();
    }

    /**
     * 修复数据
     */
    @GetMapping(value = "/repair/data")
    public R<Void> repairData() {
//        deviceDataService.repairMeterData();
//        for (int i = 6; i <= 12; i++) {
//            deviceDataService.repairDeviceData(LocalDate.of(2025, 3, i));
//        }
        return R.ok();
    }

    /**
     * 获取电站数据-今日发电量，本月发电量，本年发电量，累计发电量
     */
    @GetMapping(value = "/energy")
    public R<PowerStationListDataVO> energy() {
        return R.ok(deviceDataService.energy());
    }

    /**
     * 电站概览页-历史数据 发电功率&用电功率(ac)
     */
    @GetMapping(value = "/queryPowerStationHistoryData")
    public R<PowerStationHistoryDataVO> queryPowerStationHistoryData(@Validated StationQueryDataDTO dto) {
        if (dto.getType() != 5 && StrUtil.isEmpty(dto.getDate())) {
            throw new ServiceException("请选择日期");
        }
        return R.ok(deviceDataService.queryPowerStationHistoryData(dto));
    }

    /**
     * 电站维护
     */
    @GetMapping(value = "/maintain")
    public R<StationMaintainVO> maintain(@RequestParam Integer stationId) {
        return R.ok(deviceDataService.maintain(stationId));
    }

    /**
     * 电站概览-每日发电量对比
     */
    @GetMapping(value = "/queryPowerStationDailyEnergyData")
    public R<StationDailyEnergyDataVO> queryPowerStationDailyEnergyData(@RequestParam Integer stationId) {
        return R.ok(deviceDataService.queryPowerStationDailyEnergyData(stationId));
    }

    /**
     * 电站概览-每日发电量对比下载
     */
    @Log(title = "每日发电量对比", businessType = BusinessType.EXPORT)
    @PostMapping(value = "/daily/energy/data/download")
    public void energyDataDownload(@RequestParam Integer stationId, HttpServletResponse response) throws IOException {
        deviceDataService.queryPowerStationDailyEnergyDataDownload(response, stationId);
    }

    /**
     * 电站详细-组件布局 微逆实时数据
     */
    @GetMapping(value = "/queryNowInvAttrData")
    public R<DeviceDataVO> queryNowInvAttrData(@RequestParam Integer invId) {
        DeviceDataVO device = deviceDataService.queryNowInvAttrData(invId);
        if (device != null) {
            ZonedDateTime time = Instant.ofEpochSecond(device.getTime()).atZone(TimeZoneUtil.getZoneId(device.getTimeCode()));
            device.setLastReportTime(time.toLocalDateTime());
            BigDecimal efficiency = device.getEfficiency() == null ? new BigDecimal(0.97) : device.getEfficiency();
            device.setEfficiency(efficiency.multiply(NumUtil.HUNDRED));
            device.setModel(stationDeviceService.getBaseMapper().getModel(device.getSn()));
        }
        return R.ok(device);
    }

    /**
     * 电站详细-组件布局 功率|电量
     */
    @GetMapping(value = "/queryHistoryInvAttrData")
    public R<Object> queryHistoryInvAttrData(@Validated QueryDataDTO dto) {
        return R.ok(deviceDataService.queryHistoryInvAttrData(dto));
    }

    /**
     * 历史数据页 时间轴、环保效益
     */
    @GetMapping(value = "/queryYearOfEnergy")
    public R<List<EnvironmentalBenefitsVO>> queryYearOfEnergy(@RequestParam Integer stationId) {
        return R.ok(deviceDataService.queryYearOfEnergy(stationId));
    }

    /**
     * 历史数据页 每月发电量
     */
    @GetMapping(value = "/queryMonthOfEnergy")
    public R<MonthOfEnergyVO> queryMonthOfEnergy(@Validated MonthOfEnergyDTO dto) {
        return R.ok(deviceDataService.queryMonthOfEnergy(dto));
    }

    /**
     * 历史数据页 发电详细信息
     */
    @GetMapping(value = "/queryEnergyByStation")
    public R<List<EnergyByStationVO>> queryEnergyByStation(@Validated QueryEnergyByStationDTO dto) {
        return R.ok(deviceDataService.queryEnergyByStation(dto));
    }

    /**
     * 历史数据页 EMU数据
     */
    @GetMapping(value = "/queryEmuEnergyData")
    public R<List<EmuEnergyDataVO>> queryEmuEnergyData(@Validated QueryEmuEnergyDataDTO dto) {
        if (dto.getType() != 5 && dto.getDate() == null) {
            throw new ServiceException("请选择日期");
        }
        return R.ok(deviceDataService.queryEmuEnergyData(dto));
    }

    /**
     * 历史数据页 微逆数据
     */
    @GetMapping(value = "/queryMicroInverterData")
    public R<List<QueryEnergyDataVO>> queryMicroInverterData(@Validated QueryMicroInverterDataDTO dto) {
        return R.ok(deviceDataService.queryMicroInverterData(dto));
    }

    /**
     * 历史数据页 微逆数据导出
     */
    @Log(title = "微逆数据", businessType = BusinessType.EXPORT)
    @PostMapping(value = "/queryMicroInverterDataExport")
    public void queryMicroInverterDataExport(@Validated QueryMicroInverterDataDTO dto, HttpServletResponse response, HttpServletRequest request) {
        List<QueryEnergyDataExportVO> list = deviceDataService.queryMicroInverterDataExport(dto);
        ExcelUtil<QueryEnergyDataExportVO> util = new ExcelUtil<>(QueryEnergyDataExportVO.class);
        util.setLocal(LanguageEnum.getLocal(request));
        util.exportExcel(response, list, "微逆发电数据");
    }

    /**
     * 功率报表
     */
    @GetMapping(value = "/power/data")
    public TableDataInfo<PowerDataDTO> powerData(@Validated PowerDataPageReqDTO dto) {
        return reportDataService.powerData(dto);
    }

    /**
     * 功率报表导出
     */
    @Log(title = "功率报表", businessType = BusinessType.EXPORT)
    @PostMapping("/power/data/export")
    public void powerDataExport(HttpServletResponse response, HttpServletRequest request, @Validated PowerDataPageReqDTO dto) {
        List<PowerDataDTO> list = reportDataService.powerDataExport(dto);
        ExcelUtil<PowerDataDTO> util = new ExcelUtil<>(PowerDataDTO.class);
        util.setLocal(LanguageEnum.getLocal(request));
        util.exportExcel(response, list, "功率报表");
    }

    /**
     * 功率报表分享
     */
    @Log(title = "功率报表", businessType = BusinessType.SHARE)
    @PostMapping(value = "/power/data/share")
    public R<Void> powerDataShare(HttpServletRequest request, @Validated @RequestBody PowerDataShareDTO dto) {
        List<PowerDataDTO> list = reportDataService.powerDataExport(dto);
        ExcelUtil<PowerDataDTO> util = new ExcelUtil<>(PowerDataDTO.class);
        util.setLocal(LanguageEnum.getLocal(request));
        ByteArrayOutputStream stream = util.exportExcel(list, "功率报表");
        String name = I18nUtil.get("功率报表");
        PowerStation station = this.reportDataService.getPowerStationService().getById(dto.getStationId());
        String timeRange = "-";
        if(CollUtil.isNotEmpty(list)) {
            if (list.size() >= 1) {
                String startTime = LocalDateTime.parse(list.get(list.size() - 1).getDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                        .toLocalTime().format(DateTimeFormatter.ofPattern("HH:mm"));
                timeRange = startTime;
            }
            if (list.size() >= 2) {
                String endTime = LocalDateTime.parse(list.get(0).getDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                        .toLocalTime().format(DateTimeFormatter.ofPattern("HH:mm"));
                timeRange += " - " + endTime;
            }
        }
        String content = StrUtil.format(I18nUtil.get("功率报表邮件"), station.getId(), station.getStationName(), dto.getDate(), timeRange);
        MailUtil.sendFile(dto.getEmail(), name, true, content, name + ".xlsx", stream);
        return R.ok();
    }

    /**
     * 电量报表
     */
    @GetMapping(value = "/energy/data")
    public PageVO<EnergyDataVO> energyData(@Validated PowerDataPageReqDTO dto) {
        return switch (dto.getType()) {
            case 1 -> reportDataService.energyDataDay(dto);
            case 3 -> reportDataService.energyDataMonth(dto);
            case 4 -> reportDataService.energyDataYear(dto);
            default -> null;
        };
    }

    /**
     * 电量报表导出
     */
    @Log(title = "电量报表", businessType = BusinessType.EXPORT)
    @PostMapping("/energy/data/export")
    public void energyDataExport(HttpServletResponse response, HttpServletRequest request, @Validated PowerDataPageReqDTO dto) {
        List<EnergyDataVO> list = switch (dto.getType()) {
            case 1 -> reportDataService.energyDataDayExport(dto);
            case 3 -> reportDataService.energyDataMonthExport(dto);
            case 4 -> reportDataService.energyDataYearExport(dto);
            default -> null;
        };
        ExcelUtil<EnergyDataVO> util = new ExcelUtil<>(EnergyDataVO.class);
        util.setLocal(LanguageEnum.getLocal(request));
        util.exportExcel(response, list, "电量报表");
    }

    /**
     * 电量报表分享
     */
    @Log(title = "电量报表", businessType = BusinessType.SHARE)
    @PostMapping("/energy/data/share")
    public R<Void> energyDataShare(HttpServletRequest request, @RequestBody @Validated PowerDataShareDTO dto) {
        List<EnergyDataVO> list = switch (dto.getType()) {
            case 1 -> reportDataService.energyDataDayExport(dto);
            case 3 -> reportDataService.energyDataMonthExport(dto);
            case 4 -> reportDataService.energyDataYearExport(dto);
            default -> null;
        };
        ExcelUtil<EnergyDataVO> util = new ExcelUtil<>(EnergyDataVO.class);
        util.setLocal(LanguageEnum.getLocal(request));
        ByteArrayOutputStream stream = util.exportExcel(list, "电量报表");
        PowerStation station = this.reportDataService.getPowerStationService().getById(dto.getStationId());
        String name = I18nUtil.get("电量报表");
        String content = StrUtil.format(I18nUtil.get("电量报表邮件"), station.getStationName(),
                dto.getDate(), station.getId(), station.getStationName());
        MailUtil.sendFile(dto.getEmail(), name, true, content, name + ".xlsx", stream);
        return R.ok();
    }

    /**
     * 电网依赖度 发电和用电量、环境效益
     */
    @GetMapping("/power/consumption")
    public R<PowerConsumptionVO> powerConsumption(@Schema(description = "电站id") @RequestParam Integer id) {
        return R.ok(deviceDataService.powerConsumption(id));
    }

    /**
     * 电站收益
     */
    @GetMapping("/earnings")
    public R<PowerEarningsVO> earnings(@Schema(description = "电站id") @RequestParam Integer id,
                                       @RequestHeader(SecurityConstants.CLIENT_TYPE) String clientType) {
        return R.ok(deviceDataService.earnings(id, ClientTypeEnum.of(clientType)));
    }

    /**
     * app电网依赖度,发电&用电量
     */
    @GetMapping("/degree/electricity")
    public R<ElectricityVO> degreeElectricity(DegreeElectricityDTO dto) {
        return R.ok(deviceDataService.degreeElectricity(dto));
    }

    /**
     * 发电&用电量
     */
    @GetMapping("/electricity")
    public R<ElectricityVO> electricity(@Schema(description = "电站id") @RequestParam Integer id) {
        ZoneId zoneId = Sql.getZoneId(id);
        ZonedDateTime beginTime = LocalDate.now().atStartOfDay().atZone(zoneId);
        ZonedDateTime endTime = beginTime.plusDays(1);
        return R.ok(deviceDataService.electricity(id, beginTime, endTime, zoneId));
    }

    /**
     * 能量流动
     */
    @GetMapping("/energy/flow")
    public R<EnergyFlowVO> energyFlow(@Schema(description = "电站id") @RequestParam Integer id) {
        return R.ok(deviceDataService.energyFlow(id));
    }

    /**
     * 能量流动实时数据
     */
    @GetMapping("/energy/flow/real")
    public R<EnergyFlowRealVO> energyFlowReal(@Schema(description = "电站id") @RequestParam Integer id) {
        return R.ok(deviceDataService.energyFlowReal(id));
    }

    /**
     * 负载详情
     */
    @GetMapping("/loadDetails")
    public R<LoadDetailsVO> loadDetails(@Schema(description = "电站id") @RequestParam Integer id) {
        return R.ok(deviceDataService.loadDetails(id));
    }

    /**
     * 诊断
     */
    @GetMapping("/diagnose")
    public R<List<DiagnoseVO>> diagnose(@Schema(description = "电站id") @RequestParam Integer id) {
        return R.ok(deviceDataService.diagnose(id));
    }

    /**
     * 设备上线，离线状态
     */
    @PostMapping("/device/status")
    public R<Void> deviceState(@RequestBody DeviceStateDTO dto) {
        if (StrUtil.isNotEmpty(dto.getClientid()) && StrUtil.isNotEmpty(dto.getEvent())) {
            Integer status = switch (dto.getEvent()) {
                case "client.connected" -> 1;
                case "client.disconnected" -> 0;
                default -> null;
            };
            if (status != null) {
                if (dto.getClientid().startsWith("EMU_")) {
                    stationDeviceService.emuUpdateStatus(dto.getClientid().substring(4), status);
                } else if (dto.getClientid().startsWith("INV_")) {
                    stationDeviceService.invUpdateStatus(dto.getClientid().substring(4), status);
                }
            }
        }
        return R.ok();
    }


    /**
     * 设备自检2
     * 接口1、前端每10秒发送一次请求，后端下发命令
     * 接口2、前端每10秒获取设备状态
     */
    @Log(title = "发送设备自检命令", businessType = BusinessType.COMMAND)
    @GetMapping(value = "/getSelfCheckStatus")
    public R<CheckStatusVO> getSelfCheckStatus(@RequestParam Integer stationId) {
        CheckStatusVO checkStatusVO = deviceDataService.getSelfCheckStatus(stationId);
        return R.ok(checkStatusVO);
    }


    /**
     * 获取电表实时数据
     */
    @GetMapping("/meter/info")
    public R<MeterVo> meterInfo(@RequestParam String sn) {
        MeterVo info = deviceDataService.meterInfo(sn);
        return R.ok(info);
    }

    /**
     * 电站授权校验
     *
     * @param stationId 站点id
     * @return {@link R }<{@link Boolean }>
     */
    @GetMapping("/station/auth/{stationId}")
    public R<Boolean> judgeStationAuth(@PathVariable("stationId") Long stationId) {
        return R.ok(deviceDataService.judgeStationAuth(stationId));
    }
}
