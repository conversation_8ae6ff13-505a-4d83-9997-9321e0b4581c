package com.wh.data.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.domain.base.ICUDDEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024-10-26
 */
@Data
@TableName("wh_firmware_file")
@Schema(name = "FirmwareFile", description = "固件文件表")
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = false)
public class FirmwareFile extends ICUDDEntity {

    @Schema(description = "设备类型")
    private Integer deviceType;

    @Schema(description = "设备型号")
    private String deviceModel;

    @Schema(description = "版本号")
    private String versionNumber;

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "文件地址")
    private String filePath;

    @Schema(description = "文件md5")
    private String fileMd5;

    @Schema(description = "文件大小")
    private String fileSize;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "是否默认开启")
    @TableField("is_default_enabled")
    private Boolean defaultEnabled;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "设备类型名称")
    @TableField(exist = false)
    private String deviceTypeName;

}
