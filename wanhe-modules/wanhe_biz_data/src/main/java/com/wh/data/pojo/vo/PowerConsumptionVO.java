package com.wh.data.pojo.vo;

import com.ruoyi.common.core.domain.DataUnit;
import com.ruoyi.common.core.utils.NumUtil;
import com.ruoyi.common.core.utils.TimeZoneUtil;
import com.ruoyi.common.core.utils.Tools;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/1
 */
@Setter
@Getter
public class PowerConsumptionVO {

    @Schema(description = "更新时间")
    private String updateTime;

    @Schema(description = "日发电量")
    private BigDecimal dayKwh;
    private String dayKwhUnit;

    @Schema(description = "月发电量")
    private BigDecimal monthKwh;
    private String monthKwhUnit;

    @Schema(description = "年发电量")
    private BigDecimal yearKwh;
    private String yearKwhUnit;

    @Schema(description = "总发电量")
    private BigDecimal allKwh;
    private String allKwhUnit;

    @Schema(description = "日用电量")
    private BigDecimal dayUseKwh;

    @Schema(description = "月用电量")
    private BigDecimal monthUseKwh;

    @Schema(description = "年用电量")
    private BigDecimal yearUseKwh;

    @Schema(description = "总用电量")
    private BigDecimal allUseKwh;

    @Schema(description = "电网依赖度")
    private BigDecimal degree;

    @Schema(description = "总排减")
    private BigDecimal coz;

    @Schema(description = "相当于植树")
    private BigDecimal tree;

    @Schema(description = "等效汽油")
    private BigDecimal gasoline;

    @Schema(description = "节约标准煤,单位：kg")
    private BigDecimal coal;

    public PowerConsumptionVO(List<BigDecimal> list, LocalDateTime updateTime) {
        if (updateTime != null) {
            this.updateTime = updateTime.format(TimeZoneUtil.yyyyMMddHHmmss);
        }
        dayKwh = list.get(0);
        monthKwh = list.get(2);
        yearKwh = list.get(4);
        allKwh = list.get(6);
        dayUseKwh = dayKwh.add(list.get(1));
        monthUseKwh = monthKwh.add(list.get(3));
        yearUseKwh = yearKwh.add(list.get(5));
        allUseKwh = allKwh.add(list.get(7));
        degree = Tools.degree(dayUseKwh, dayKwh);
        coz = allKwh.multiply(NumUtil.COZ).divide(NumUtil.THOUSAND, 2, RoundingMode.HALF_UP);
        tree = allKwh.multiply(NumUtil.TREE).divide(NumUtil.THOUSAND, 2, RoundingMode.HALF_UP);
        gasoline = allKwh.multiply(NumUtil.GASOLINE).divide(NumUtil.THOUSAND, 2, RoundingMode.HALF_UP);
        coal = allKwh.multiply(NumUtil.COAL).setScale(2, RoundingMode.HALF_UP);
        DataUnit day = Tools.covertKwhUnit(dayKwh);
        dayKwh = day.getData();
        dayKwhUnit = day.getUnit();
        DataUnit month = Tools.covertKwhUnit(monthKwh);
        monthKwh = month.getData();
        monthKwhUnit = month.getUnit();
        DataUnit year = Tools.covertKwhUnit(yearKwh);
        yearKwh = year.getData();
        yearKwhUnit = year.getUnit();
        DataUnit all = Tools.covertKwhUnit(allKwh);
        allKwh = all.getData();
        allKwhUnit = all.getUnit();
    }
}
