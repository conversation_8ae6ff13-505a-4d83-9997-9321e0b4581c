package com.wh.data.pojo.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/8
 */
@Data
public class CommandDTO {

    @NotBlank
    private String sn;

    @NotNull
    @Range(min = 0, max = 2)
    private Integer qos;

    @Valid
    @NotEmpty
    private List<GridParamDTO> data;

}
