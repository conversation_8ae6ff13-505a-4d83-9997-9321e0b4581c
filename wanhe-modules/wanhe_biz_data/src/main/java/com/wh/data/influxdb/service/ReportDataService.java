package com.wh.data.influxdb.service;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.utils.TimeZoneUtil;
import com.ruoyi.common.core.utils.Tools;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.security.utils.Sql;
import com.wh.data.domain.PowerStation;
import com.wh.data.influxdb.dto.Param;
import com.wh.data.influxdb.entity.DeviceData;
import com.wh.data.influxdb.entity.MeterData;
import com.wh.data.pojo.dto.PowerDataPageReqDTO;
import com.wh.data.pojo.vo.EnergyDataVO;
import com.wh.data.pojo.vo.PageVO;
import com.wh.data.pojo.vo.PowerDataDTO;
import com.wh.data.protocol.TopicService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/6
 */
@Slf4j
@Service
public class ReportDataService extends TopicService {

    public TableDataInfo<PowerDataDTO> powerData(PowerDataPageReqDTO dto) {
        ZoneId zoneId = Sql.getZoneId(dto.getStationId());
        String name = nullThrow(powerStationService.getBaseMapper().get(dto.getStationId())).getStationName();
        ZonedDateTime beginTime = dto.getBeginTime().atZone(zoneId);
        ZonedDateTime endTime = beginTime.plusDays(1);
        String iql1 = "select count(mid) as total from {table} where powerStationId = '{stationId}' and deviceType = 1 and time >= {begin} and time < {end}";
        String iql2 = "select sn, acTempPower as data from {table} where powerStationId = '{stationId}' and deviceType = 1 and time >= {begin} and time < {end} order by time desc {limit,offset}";
        Param param = Param.of(DeviceData.MN, zoneId, dto.getStationId(), beginTime, endTime).page(dto).format(TimeZoneUtil.yyyyMMddHHmmss);
        List<List<JSONObject>> lists = influxMapper.queryMultiList(param.iql(List.of(iql1, iql2)));
        List<PowerDataDTO> list = lists.get(1).stream().map(json -> {
            PowerDataDTO data = new PowerDataDTO();
            data.setStationName(name);
            data.setSn(json.getString(SN));
            data.setDate(json.getString(TIME));
            data.setPower(json.getBigDecimal(DATA));
            return data;
        }).toList();
        return TableDataInfo.of(lists.get(0), list);
    }

    public List<PowerDataDTO> powerDataExport(PowerDataPageReqDTO dto) {
        ZoneId zoneId = Sql.getZoneId(dto.getStationId());
        String name = nullThrow(powerStationService.getBaseMapper().get(dto.getStationId())).getStationName();
        ZonedDateTime beginTime = dto.getBeginTime().atZone(zoneId);
        ZonedDateTime endTime = beginTime.plusDays(1);
        String iql = "select sn, acTempPower as data from {table} where powerStationId = '{stationId}' and deviceType = 1 and time >= {begin} and time < {end} order by time desc";
        Param param = Param.of(DeviceData.MN, zoneId, dto.getStationId(), beginTime, endTime).format(TimeZoneUtil.yyyyMMddHHmmss);
        return influxMapper.queryList(param.iql(iql)).stream().map(json -> {
            PowerDataDTO data = new PowerDataDTO();
            data.setStationName(name);
            data.setSn(json.getString(SN));
            data.setDate(json.getString(TIME));
            data.setPower(json.getBigDecimal(DATA));
            return data;
        }).toList();
    }

    public PageVO<EnergyDataVO> energyDataDay(PowerDataPageReqDTO dto) {
        ZoneId zoneId = Sql.getZoneId(dto.getStationId());
        PowerStation station = nullThrow(powerStationService.getBaseMapper().get(dto.getStationId()));
        ZonedDateTime beginTime = dto.getBeginTime().atZone(zoneId);
        ZonedDateTime endTime = dto.getEndTime().atZone(zoneId);
        ZonedDateTime now = ZonedDateTime.now(zoneId);
        if (endTime.isAfter(now)) {
            endTime = now;
        }
        //总计数据
        String iql1 = "select count(data) as total,last(data) as data from (select sum(data) as data from (select last(dailyEnergy) as data from {table} where powerStationId = '{stationId}' and deviceType = 1 and time >= {begin} and time < {end} group by time(15m),deviceId {tz}) where time >= {begin} and time < {end} group by time(15m) fill(0) {tz})";
        //分页数据
        String iql2 = "select sum(data) as data from (select last(dailyEnergy) as data from {table} where powerStationId = '{stationId}' and deviceType = 1 and time >= {begin} and time < {end} group by time(15m),deviceId {tz}) where time >= {begin} and time < {end} group by time(15m) fill(0) order by time desc {limit,offset} {tz}";
        Param param = Param.of(DeviceData.MN, zoneId, dto.getStationId(), beginTime, endTime).page(dto);
        List<List<JSONObject>> lists = influxMapper.queryMultiList(param.dataNotFormat().iql(List.of(iql1, iql2)));
        List<JSONObject> kwhData = lists.get(1);
        //转换分页数据
        List<EnergyDataVO> list = kwhData.stream().map(json -> {
            EnergyDataVO data = new EnergyDataVO();
            data.setStationName(station.getStationName());
            ZonedDateTime time = Instant.ofEpochSecond(json.getLongValue(TIME)).atZone(zoneId);
            data.setDate(TimeZoneUtil.yyyyMMddHHmm.format(time.toLocalDateTime()));
            data.setStageDailyEnergy(json.getBigDecimal(DATA));
            return data;
        }).collect(Collectors.toList());
        List<List<JSONObject>> listList = null;
        if (!station.getStationType().equals("1") && !list.isEmpty()) {
            iql1 = "select sum(stageEnergy) as data from {table} where powerStationId = '{stationId}' and powerGridSeat = 2 and time >= {begin} and time < {end}";
            iql2 = "select sum(stageEnergy) as data from {table} where powerStationId = '{stationId}' and powerGridSeat = 2 and time >= %ds and time < %ds group by time(15m) fill(0) order by time desc";
            long beginSecond = kwhData.get(kwhData.size() - 1).getLongValue(TIME);
            long endSecond = beginSecond + kwhData.size() * 900L;
            param = Param.of(MeterData.MN, zoneId, dto.getStationId(), beginTime, endTime).iql(List.of(iql1, iql2.formatted(beginSecond, endSecond))).format(TimeZoneUtil.yyyyMMddHHmm);
            listList = influxMapper.queryMultiList(param.dataNotFormat());
            Map<String, BigDecimal> map = Tools.getMap(listList.get(1));
            list.forEach(d -> d.setConsumption(map.getOrDefault(d.getDate(), BigDecimal.ZERO).add(d.getStageDailyEnergy())));
        }
        if (list.isEmpty()) {
            do {
                EnergyDataVO data = new EnergyDataVO();
                data.setStationName(station.getStationName());
                data.setDate(TimeZoneUtil.yyyyMMddHHmm.format(beginTime.toLocalDateTime()));
                data.setStageDailyEnergy(BigDecimal.ZERO);
                list.add(data);
            } while ((beginTime = beginTime.plusMinutes(15)).isBefore(endTime));
            Collections.reverse(list);
            int size = list.size();
            int from = (dto.getPageNum() - 1) * dto.getPageSize();
            int to = dto.getPageNum() * dto.getPageSize();
            list = list.subList(from, Math.min(to, size));
            return PageVO.of(list, size);
        }
        return PageVO.of(lists.get(0), listList, list);
    }

    public List<EnergyDataVO> energyDataDayExport(PowerDataPageReqDTO dto) {
        ZoneId zoneId = Sql.getZoneId(dto.getStationId());
        PowerStation station = nullThrow(powerStationService.getBaseMapper().get(dto.getStationId()));
        ZonedDateTime beginTime = dto.getBeginTime().atZone(zoneId);
        ZonedDateTime endTime = dto.getEndTime().atZone(zoneId);
        ZonedDateTime now = ZonedDateTime.now(zoneId);
        if (endTime.isAfter(now)) {
            endTime = now;
        }
        String iql = "select sum(data) as data from (select last(dailyEnergy) as data from {table} where powerStationId = '{stationId}' and deviceType = 1 and time >= {begin} and time < {end} group by time(15m),deviceId {tz}) where time >= {begin} and time < {end} group by time(15m) fill(0) order by time desc {tz}";
        Param param = Param.of(DeviceData.MN, zoneId, dto.getStationId(), beginTime, endTime, iql);
        List<JSONObject> lists = influxMapper.queryList(param.dataNotFormat());
        List<EnergyDataVO> list = lists.stream().map(json -> {
            EnergyDataVO data = new EnergyDataVO();
            data.setStationName(station.getStationName());
            ZonedDateTime time = Instant.ofEpochSecond(json.getLongValue(TIME)).atZone(zoneId);
            data.setDate(TimeZoneUtil.yyyyMMddHHmm.format(time.toLocalDateTime()));
            data.setStageDailyEnergy(json.getBigDecimal(DATA));
            return data;
        }).collect(Collectors.toList());
        if (!station.getStationType().equals("1") && !list.isEmpty()) {
            iql = "select sum(stageEnergy) as data from {table} where powerStationId = '{stationId}' and powerGridSeat = 2 and time >= %ss and time < %ss group by time(15m) fill(0) order by time desc";
            long beginSecond = lists.get(lists.size() - 1).getLongValue(TIME);
            long endSecond = beginSecond + lists.size() * 900L;
            param = Param.of(MeterData.MN, zoneId, dto.getStationId()).iql(iql.formatted(beginSecond, endSecond)).format(TimeZoneUtil.yyyyMMddHHmm);
            Map<String, BigDecimal> map = influxMapper.queryListTimeDataMap(param.dataNotFormat());
            list.forEach(d -> d.setConsumption(map.getOrDefault(d.getDate(), BigDecimal.ZERO).add(d.getStageDailyEnergy())));
        }
        if (list.isEmpty()) {
            do {
                EnergyDataVO data = new EnergyDataVO();
                data.setStationName(station.getStationName());
                data.setDate(TimeZoneUtil.yyyyMMddHHmm.format(beginTime.toLocalDateTime()));
                data.setStageDailyEnergy(BigDecimal.ZERO);
                list.add(data);
            } while ((beginTime = beginTime.plusMinutes(15)).isBefore(endTime));
            Collections.reverse(list);
        }
        return list;
    }

    public PageVO<EnergyDataVO> energyDataMonth(PowerDataPageReqDTO dto) {
        ZoneId zoneId = Sql.getZoneId(dto.getStationId());
        PowerStation station = nullThrow(powerStationService.getBaseMapper().get(dto.getStationId()));
        ZonedDateTime beginTime = dto.getBeginTime().atZone(zoneId);
        ZonedDateTime endTime = dto.getEndTime().atZone(zoneId);
        ZonedDateTime now = ZonedDateTime.now(zoneId);
        if (endTime.isAfter(now)) {
            endTime = now;
        }
        //总计数据
        String iql1 = "select count(data) as total,sum(data) as data from (select sum(data) as data from (select last(dailyEnergy) as data from {table} where powerStationId = '{stationId}' and deviceType = 1 and time >= {begin} and time < {end} group by time(1d),deviceId {tz}) where time >= {begin} and time < {end} group by time(1d) fill(0) {tz})";
        //分页数据
        String iql2 = "select sum(data) as data from (select last(dailyEnergy) as data from {table} where powerStationId = '{stationId}' and deviceType = 1 and time >= {begin} and time < {end} group by time(1d),deviceId {tz}) where time >= {begin} and time < {end} group by time(1d) fill(0) order by time desc {limit,offset} {tz}";
        Param param = Param.of(DeviceData.MN, zoneId, dto.getStationId(), beginTime, endTime).page(dto);
        List<List<JSONObject>> lists = influxMapper.queryMultiList(param.dataNotFormat().iql(List.of(iql1, iql2)));
        List<JSONObject> kwhData = lists.get(1);
        List<EnergyDataVO> list = kwhData.stream().map(json -> {
            EnergyDataVO data = new EnergyDataVO();
            data.setStationName(station.getStationName());
            ZonedDateTime time = Instant.ofEpochSecond(json.getLongValue(TIME)).atZone(zoneId);
            data.setDate(TimeZoneUtil.yyyyMMdd.format(time.toLocalDateTime()));
            data.setStageDailyEnergy(json.getBigDecimal(DATA));
            return data;
        }).collect(Collectors.toList());
        List<List<JSONObject>> listList = null;
        if (!station.getStationType().equals("1") && !list.isEmpty()) {
            iql1 = "select sum(stageEnergy) as data from {table} where powerStationId = '{stationId}' and powerGridSeat = 2 and time >= {begin} and time < {end}";
            iql2 = "select sum(stageEnergy) as data from {table} where powerStationId = '{stationId}' and powerGridSeat = 2 and time >= %ds and time < %ds group by time(1d) fill(0) order by time desc {tz}";
            long beginSecond = kwhData.get(kwhData.size() - 1).getLongValue(TIME);
            long endSecond = beginSecond + kwhData.size() * 86400L;
            param = Param.of(MeterData.MN, zoneId, dto.getStationId(), beginTime, endTime).iql(List.of(iql1, iql2.formatted(beginSecond, endSecond))).format(TimeZoneUtil.yyyyMMdd);
            listList = influxMapper.queryMultiList(param.dataNotFormat());
            Map<String, BigDecimal> map = Tools.getMap(listList.get(1));
            list.forEach(d -> d.setConsumption(map.getOrDefault(d.getDate(), BigDecimal.ZERO).add(d.getStageDailyEnergy())));
        }
        if (list.isEmpty()) {
            do {
                EnergyDataVO data = new EnergyDataVO();
                data.setStationName(station.getStationName());
                data.setDate(TimeZoneUtil.yyyyMMdd.format(beginTime.toLocalDateTime()));
                data.setStageDailyEnergy(BigDecimal.ZERO);
                list.add(data);
            } while ((beginTime = beginTime.plusDays(1)).isBefore(endTime));
            Collections.reverse(list);
            int size = list.size();
            int from = (dto.getPageNum() - 1) * dto.getPageSize();
            int to = dto.getPageNum() * dto.getPageSize();
            list = list.subList(from, Math.min(to, size));
            return PageVO.of(list, size);
        }
        return PageVO.of(lists.get(0), listList, list);
    }

    public List<EnergyDataVO> energyDataMonthExport(PowerDataPageReqDTO dto) {
        ZoneId zoneId = Sql.getZoneId(dto.getStationId());
        PowerStation station = nullThrow(powerStationService.getBaseMapper().get(dto.getStationId()));
        ZonedDateTime beginTime = dto.getBeginTime().atZone(zoneId);
        ZonedDateTime endTime = dto.getEndTime().atZone(zoneId);
        ZonedDateTime now = ZonedDateTime.now(zoneId);
        if (endTime.isAfter(now)) {
            endTime = now;
        }
        String iql = "select sum(data) as data from (select last(dailyEnergy) as data from {table} where powerStationId = '{stationId}' and deviceType = 1 and time >= {begin} and time < {end} group by time(1d),deviceId {tz}) where time >= {begin} and time < {end} group by time(1d) fill(0) order by time desc {tz}";
        Param param = Param.of(DeviceData.MN, zoneId, dto.getStationId(), beginTime, endTime, iql);
        List<JSONObject> lists = influxMapper.queryList(param.dataNotFormat());
        List<EnergyDataVO> list = lists.stream().map(json -> {
            EnergyDataVO data = new EnergyDataVO();
            data.setStationName(station.getStationName());
            ZonedDateTime time = Instant.ofEpochSecond(json.getLongValue(TIME)).atZone(zoneId);
            data.setDate(TimeZoneUtil.yyyyMMdd.format(time.toLocalDateTime()));
            data.setStageDailyEnergy(json.getBigDecimal(DATA));
            return data;
        }).collect(Collectors.toList());
        if (!station.getStationType().equals("1") && !list.isEmpty()) {
            iql = "select sum(stageEnergy) as data from {table} where powerStationId = '{stationId}' and powerGridSeat = 2 and time >= %ss and time < %ss group by time(1d) fill(0) order by time desc";
            long beginSecond = lists.get(lists.size() - 1).getLongValue(TIME);
            long endSecond = beginSecond + lists.size() * 86400L;
            param = Param.of(MeterData.MN, zoneId, dto.getStationId()).iql(iql.formatted(beginSecond, endSecond)).format(TimeZoneUtil.yyyyMMdd);
            Map<String, BigDecimal> map = influxMapper.queryListTimeDataMap(param.dataNotFormat());
            list.forEach(d -> d.setConsumption(map.getOrDefault(d.getDate(), BigDecimal.ZERO).add(d.getStageDailyEnergy())));
        }
        if (list.isEmpty()) {
            do {
                EnergyDataVO data = new EnergyDataVO();
                data.setStationName(station.getStationName());
                data.setDate(TimeZoneUtil.yyyyMMdd.format(beginTime.toLocalDateTime()));
                data.setStageDailyEnergy(BigDecimal.ZERO);
                list.add(data);
            } while ((beginTime = beginTime.plusDays(1)).isBefore(endTime));
            Collections.reverse(list);
        }
        return list;
    }

    public PageVO<EnergyDataVO> energyDataYear(PowerDataPageReqDTO dto) {
        ZoneId zoneId = Sql.getZoneId(dto.getStationId());
        PowerStation station = nullThrow(powerStationService.getBaseMapper().get(dto.getStationId()));
        ZonedDateTime beginTime = dto.getBeginTime().atZone(zoneId);
        ZonedDateTime endTime = dto.getEndTime().atZone(zoneId);
        Param param = Param.of(zoneId, dto.getStationId());
        List<String> iqlList = new ArrayList<>();
        List<String> months = new ArrayList<>();
        while (beginTime.isBefore(endTime)) {
            months.add(TimeZoneUtil.yyyyMM.format(beginTime.toLocalDateTime()));
            String iql1 = "select sum(data) as data from (select last(dailyEnergy) as data from %s where powerStationId = '{stationId}' and deviceType = 1 and time >= %s and time < %s group by time(1d),deviceId {tz})";
            String iql2 = "select sum(stageEnergy) as data from %s where powerStationId = '{stationId}' and powerGridSeat = 2 and time >= %s and time < %s";
            String beginSecond = param.getSecond(beginTime);
            String endSecond = param.getSecond(beginTime.plusMonths(1));
            iqlList.add(iql1.formatted(DeviceData.MN, beginSecond, endSecond));
            iqlList.add(iql2.formatted(MeterData.MN, beginSecond, endSecond));
            beginTime = beginTime.plusMonths(1);
        }
        List<BigDecimal> list = influxMapper.queryMultiObjData(param.dataNotFormat().iql(iqlList));
        PageVO<EnergyDataVO> vo = new PageVO<>(new ArrayList<>());
        for (int i = 0; i < list.size(); i++) {
            int j = i / 2;
            if (i % 2 == 0) {
                EnergyDataVO data = new EnergyDataVO();
                data.setStationName(station.getStationName());
                data.setDate(months.get(j));
                data.setStageDailyEnergy(list.get(i));
                vo.getRows().add(data);
            } else {
                EnergyDataVO data = vo.getRows().get(j);
                data.setConsumption(data.getStageDailyEnergy().add(list.get(i)));
            }
        }
        BigDecimal energy = vo.getRows().stream().map(EnergyDataVO::getStageDailyEnergy).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal consume = vo.getRows().stream().map(EnergyDataVO::getConsumption).reduce(BigDecimal.ZERO, BigDecimal::add);
        vo.setEnergy(energy.setScale(2, RoundingMode.HALF_UP));
        vo.setConsume(consume.setScale(2, RoundingMode.HALF_UP));
        vo.setTotal(list.size() / 2);
        Collections.reverse(vo.getRows());
        if (dto.getPageNum() == 1) {
            if (vo.getRows().size() > dto.getPageSize()) {
                vo.setRows(vo.getRows().subList(0, dto.getPageSize()));
            }
        } else if (dto.getPageNum() == 2) {
            if (dto.getPageSize() > vo.getRows().size()) {
                vo.setRows(Collections.emptyList());
            } else {
                vo.setRows(vo.getRows().subList(dto.getPageSize(), vo.getRows().size()));
            }
        } else {
            vo.setRows(Collections.emptyList());
        }
        return vo;
    }

    public List<EnergyDataVO> energyDataYearExport(PowerDataPageReqDTO dto) {
        ZoneId zoneId = Sql.getZoneId(dto.getStationId());
        PowerStation station = nullThrow(powerStationService.getBaseMapper().get(dto.getStationId()));
        ZonedDateTime beginTime = dto.getBeginTime().atZone(zoneId);
        ZonedDateTime endTime = dto.getEndTime().atZone(zoneId);
        Param param = Param.of(zoneId, dto.getStationId());
        List<String> iqlList = new ArrayList<>();
        List<String> months = new ArrayList<>();
        while (beginTime.isBefore(endTime)) {
            months.add(TimeZoneUtil.yyyyMM.format(beginTime.toLocalDateTime()));
            String iql1 = "select sum(data) as data from (select last(dailyEnergy) as data from %s where powerStationId = '{stationId}' and deviceType = 1 and time >= %s and time < %s group by time(1d),deviceId {tz})";
            String iql2 = "select sum(stageEnergy) as data from %s where powerStationId = '{stationId}' and powerGridSeat = 2 and time >= %s and time < %s";
            String beginSecond = param.getSecond(beginTime);
            String endSecond = param.getSecond(beginTime.plusMonths(1));
            iqlList.add(iql1.formatted(DeviceData.MN, beginSecond, endSecond));
            iqlList.add(iql2.formatted(MeterData.MN, beginSecond, endSecond));
            beginTime = beginTime.plusMonths(1);
        }
        List<BigDecimal> lists = influxMapper.queryMultiObjData(param.dataNotFormat().iql(iqlList));
        List<EnergyDataVO> list = new ArrayList<>();
        for (int i = 0; i < lists.size(); i++) {
            int j = i / 2;
            if (i % 2 == 0) {
                EnergyDataVO data = new EnergyDataVO();
                data.setStationName(station.getStationName());
                data.setDate(months.get(j));
                data.setStageDailyEnergy(lists.get(i));
                list.add(data);
            } else {
                EnergyDataVO data = list.get(j);
                data.setConsumption(data.getStageDailyEnergy().add(lists.get(i)));
            }
        }
        Collections.reverse(list);
        return list;
    }
}
