package com.wh.data.influxdb.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.influxdb.annotations.Column;
import com.influxdb.annotations.Measurement;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * <AUTHOR>
 * @date 2024/11/27
 */
@Data
@Measurement(name = "wh_meter_data")
public class MeterData {

    public static final String MN = "wh_meter_data";

    /**
     * 标签： deviceId powerStationId
     */

    //设备id
    @Column(tag = true)
    private Integer deviceId;

    //站点id
    @Column(tag = true)
    private Integer powerStationId;

    //上报时间
    @Column(timestamp = true)
    @JsonIgnore
    private Instant createTime;

    //电表序列号
    @Column
    private String sn;

    //电表位置 0负载侧电表 1光伏测电表 2并网点电表
    @Column
    private Integer powerGridSeat;

    //mid
    @Column
    private String mid;

    //emu序列号
    @Column
    private String emuSn;

    // 和EMU的通信状态，0：通信异常，1：正常
    @Column
    private Integer commStatus;

    // 功率
    @Column
    private BigDecimal power;

    // 正向电能
    @Column
    private BigDecimal forwardEnergy;

    // 反向能量 用电量
    @Column
    private BigDecimal reverseEnergy;

    // 单次正向用电量
    @Column
    private BigDecimal stageForwardEnergy;

    // 单次反向用电量
    @Column
    private BigDecimal stageReverseEnergy;

    // 单次正向-负向
    @Column
    private BigDecimal stageEnergy;

}
