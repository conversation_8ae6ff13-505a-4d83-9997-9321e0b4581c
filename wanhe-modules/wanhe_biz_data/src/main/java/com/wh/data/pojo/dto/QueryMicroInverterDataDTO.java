package com.wh.data.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class QueryMicroInverterDataDTO {

    @Schema(description = "电站id")
    @NotNull
    private Integer stationId;

    @Schema(description = "微逆Id")
    @NotEmpty
    private List<Integer> ids;

    @Schema(description = "时间类型 1天 2周")
    private Integer type;

    @Schema(description = "查询时间 yyyy-MM-dd")
    @NotBlank
    private String date;

    public Integer getType() {
        return type == null ? 1 : type;
    }

}
