package com.wh.data.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/2/19
 */
@Data
public class LoadDetailsVO {

    @Schema(description = "家庭负载")
    private BigDecimal familyDetails;
    private String familyDetailsUnit;

    //家庭负载减去充电桩
    @Schema(description = "家庭电器")
    private BigDecimal familyAppliance;
    private String familyApplianceUnit;

    @Schema(description = "充电桩")
    private BigDecimal chargingPile = BigDecimal.ZERO;
    private String chargingPileUnit = "W";

}
