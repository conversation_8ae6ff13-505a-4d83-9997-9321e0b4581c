package com.wh.data.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/11/1
 */
@Data
public class EnergyFlowRealVO {

    //并网电表
    @Schema(description = "电网-实时功率")
    private String power;

    @Schema(description = "电网-今日输入电量")
    private String forwardEnergy;

    @Schema(description = "电网-今日输出电量")
    private String reverseEnergy;

    @Schema(description = "电网-累计输入电量")
    private String totalForwardEnergy;

    @Schema(description = "电网-累计输出电量")
    private String totalReverseEnergy;

    @Schema(description = "负载-实时功率")
    private String loadPower;

    @Schema(description = "负载-今日消耗电量")
    private String loadForwardEnergy;

    @Schema(description = "负载-累计消耗电量")
    private String loadTotalForwardEnergy;

}
