package com.wh.data.pojo.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/2/10
 */
@Data
public class FaultWaveSettingVO {

    //ID
    private Integer addr;
    //配置项
    private String name;
    //读写
    private String rw;
    //值
    private BigDecimal value;
    //最大值
    private BigDecimal max;
    //最小值
    private BigDecimal min;
    //单位
    private String unit;
    //数据类型
    private String type;
    //数据类型
    private Integer type2;

    public void setValue(BigDecimal value) {
        if (value != null) {
            this.value = value;
        }
    }

}
