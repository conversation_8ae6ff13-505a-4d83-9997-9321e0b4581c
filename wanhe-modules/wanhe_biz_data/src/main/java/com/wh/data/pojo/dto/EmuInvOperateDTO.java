package com.wh.data.pojo.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Setter
@Getter
public class EmuInvOperateDTO extends DeviceDataDTO {

    private List<InvData> invData = new ArrayList<>();

    public EmuInvOperateDTO(String sn) {
        super(sn);
    }

    @Setter
    @Getter
    public static class InvData {

        private String sn;

        private List<AddrValueDTO> data;

    }

}
