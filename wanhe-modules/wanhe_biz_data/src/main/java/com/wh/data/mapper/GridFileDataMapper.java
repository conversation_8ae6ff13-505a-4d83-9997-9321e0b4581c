package com.wh.data.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.api.domain.GridFileData;
import com.wh.data.pojo.dto.GridParamDTO;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-10-26
 */
public interface GridFileDataMapper extends BaseMapper<GridFileData> {

    // 删除掉并网文件下发is_issue = 1查询条件
//    @Select("select fd.addr,fd.val as `value`,if(d.type = 'int', 1, 2) as type from wh_grid_data d inner join wh_grid_file_data fd on d.addr = fd.addr and fd.grid_file_id = #{fileId} and fd.is_issue = 1 and fd.val is not null")
    @Select("select fd.addr,fd.val as `value`,if(d.type = 'int', 1, 2) as type from wh_grid_data d inner join wh_grid_file_data fd on d.addr = fd.addr and fd.grid_file_id = #{fileId} and fd.val is not null")
    List<GridParamDTO> getParam(Integer fileId);

    // 删除掉并网文件下发is_issue = 1查询条件
//    @Select("select addr from wh_grid_file_data where grid_file_id = #{fileId} and is_issue = 1")
    @Select("select addr from wh_grid_file_data where grid_file_id = #{fileId}")
    List<Integer> getAddrParam(Integer fileId);

}
