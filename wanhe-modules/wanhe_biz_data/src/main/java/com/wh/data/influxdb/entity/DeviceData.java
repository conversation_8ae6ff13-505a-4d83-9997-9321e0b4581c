package com.wh.data.influxdb.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonRawValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.influxdb.annotations.Column;
import com.influxdb.annotations.Measurement;
import com.ruoyi.common.core.json.BigDecimalSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * emu和微逆数据
 * <AUTHOR>
 * @date 2024/11/27
 */
@Data
@Measurement(name = "wh_device_data")
public class DeviceData {

    public static final String MN = "wh_device_data";

    /**
     * 标签： pid deviceId powerStationId
     */

    //emu id
    @Column(tag = true)
    private Integer pid;

    //设备id
    @Column(tag = true)
    private Integer deviceId;

    //站点id
    @Column(tag = true)
    private Integer powerStationId;

    //序列号
    @Column(tag = true)
    private String sn;

    //月
    @Column
    private Integer month;

    //时分
    @Column
    private Integer hm;

    //上报时间
    @Column(timestamp = true)
    @JsonIgnore
    private Instant createTime;

    //类型 0：emu ，1：微逆
    @Column
    private Integer deviceType;

    //联网模式，0：WIFI,1:有线网，2：4G
    @Column
    private Integer commType;

    //emu序列号
    @Column
    private String emuSn;

    //mid
    @Column
    private String mid;

    // emu连接微逆数量
    @Column
    private Integer monitorNum;

    // 网关或wifi 运行状态，1：正常
    @Column
    private Integer runningStatus;

    //数据上传间隔时间 分钟
    @Column
    private Integer uploadInterval;

    // 网关或wifi 软件版本号
    @Column
    private String wifiSoftVer;

    // 网关或wifi 硬件版本号
    @Column
    private String wifiHardVer;

    //时区码
    @Column
    private String timeCode;

    // PV总功率
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pvTotalPower;

    // PV瞬时功率 图形化列表显示
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pvTempPower;

    // AC瞬时功率 电站列表显示
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal acTempPower;

    // 电网频率
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal gridFreq;

    // 系统状态 1自检ok 2停机 4预启动 8运行 16故障 32告警
    @Column
    private Integer sysStatus;

    //启机控制,0/停止  1/启动
    @Column
    private Integer sysCtrlCmd;

    // PV电压1实际值
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pvVolReal;

    // PV电流1实际值
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal PvCurrReal;

    // todo 一拖二新增开始

    /**
     * pv1 发电量
     */
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pvEnergy;

    /**
     * PV2瞬时功率
     */
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pv2TempPower;

    /**
     * PV2电压实际值
     */
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pv2VolReal;

    /**
     * PV2电流实际值
     */
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pv2CurrReal;

    /**
     * pv2 发电量
     */
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pv2Energy;

    /**
     * PV3瞬时功率
     */
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pv3TempPower;

    /**
     * PV3电压实际值
     */
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pv3VolReal;

    /**
     * PV3电流实际值
     */
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pv3CurrReal;

    /**
     * pv3 发电量
     */
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pv3Energy;

    /**
     * PV4瞬时功率
     */
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pv4TempPower;

    /**
     * PV4电压实际值
     */
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pv4VolReal;

    /**
     * PV4电流实际值
     */
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pv4CurrReal;

    /**
     * pv4 发电量
     */
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pv4Energy;

    /**
     * PV5瞬时功率
     */
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pv5TempPower;

    /**
     * PV5电压实际值
     */
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pv5VolReal;

    /**
     * PV5电流实际值
     */
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pv5CurrReal;

    /**
     * pv5 发电量
     */
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pv5Energy;

    /**
     * PV6瞬时功率
     */
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pv6TempPower;

    /**
     * PV6电压实际值
     */
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pv6VolReal;

    /**
     * PV6电流实际值
     */
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pv6CurrReal;

    /**
     * pv6 发电量
     */
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pv6Energy;

    /**
     * PV7瞬时功率
     */
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pv7TempPower;

    /**
     * PV7电压实际值
     */
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pv7VolReal;

    /**
     * PV7电流实际值
     */
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pv7CurrReal;

    /**
     * pv7 发电量
     */
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pv7Energy;

    /**
     * PV8瞬时功率
     */
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pv8TempPower;


    /**
     * PV8电压实际值
     */
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pv8VolReal;

    /**
     * PV8电流实际值
     */
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pv8CurrReal;

    /**
     * pv8 发电量
     */
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pv8Energy;

    // todo 一拖二新增结束

    // AC电压实际值
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal acVolReal;

    // AC电流实际值
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal acCurrReal;

    // todo :: 一拖二 修改与新增 开始

    // AC MOS温度实际值
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal acMosTempReal;

    // PV MOS温度实际值
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pVMosTempReal;

    /**
     * 环境温度实际值
     */
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal enviroMosTempReal;

    /**
     * DAB MOS温度实际值
     */
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal dabMosTempReal;

    /**
     * boost MOS温度实际值
     */
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal boostMosTempReal;

    // todo :: 一拖二 修改与新增 结束

    // 日运行时间，小时
    @Column
    private Integer dayRunTime;

    // 日发电量
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal dailyEnergy;

    // 总发电量
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal totalEnergy;

    // 单次发电量
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal stageDailyEnergy;

    // 微逆发电效率,%
    @Column
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal efficiency;

    // 连接组件数量
    @Column
    private Integer pvCount;

    // 和EMU的通信状态，0：通信异常，1：正常 2-代表待升级(可能由于升级过程中掉电或升级失败，微逆处于Boot中，未起来需要再次升级)
    @Column
    private Integer commStatus;

    // 故障码
    @Column
    @JsonRawValue
    private String faultCode;

    // 微逆软件版本号
    @Column
    private String softVer;

    // 微逆硬件版本号
    @Column
    private String hardVer;

}
