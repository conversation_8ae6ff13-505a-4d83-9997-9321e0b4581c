package com.wh.data.protocol;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.enums.DeviceTypeEnum;
import com.ruoyi.common.core.utils.TimeZoneUtil;
import com.ruoyi.common.core.utils.Tools;
import com.ruoyi.common.redis.RedisUtil;
import com.ruoyi.system.api.dto.EmuInv;
import com.ruoyi.system.api.dto.EmuMeter;
import com.ruoyi.system.api.dto.EnergyFaultCache;
import com.wh.data.domain.PowerStation;
import com.wh.data.domain.StationDevice;
import com.wh.data.domain.StationDeviceRemote;
import com.wh.data.enums.CommandEnum;
import com.wh.data.influxdb.entity.DeviceData;
import com.wh.data.influxdb.entity.MeterData;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/5
 */
@Slf4j
@Component("Emu")
public class EmuService extends TopicService {

    @Resource
    protected RedissonClient redissonClient;

    /**
     * 写Emu参数
     */
    public void responseWriteEmuParam(JSONObject data) {
        updateCommand(data);
    }

    /**
     * 读Inv参数 收集微逆版本信息
     */
    public void responseReadInvOfEmuParam(JSONObject data) {
        updateCommand(data);
        JSONArray array = data.getJSONArray("invData");
        if (CollUtil.isNotEmpty(array)) {
            Map<String, StationDevice> map = stationDeviceService.lambdaQuery()
                    .select(StationDevice::getId, StationDevice::getNumber, StationDevice::getStatus, StationDevice::getPowerStationId)
                    .eq(StationDevice::getEmuNumber, data.getString(SN))
                    .eq(StationDevice::getDeviceType, DeviceTypeEnum.WN.getVal())
                    .list().stream().collect(Collectors.toMap(StationDevice::getNumber, e -> e));
            List<StationDevice> list = new ArrayList<>();
            for (int i = 0; i < array.size(); i++) {
                JSONObject obj = array.getJSONObject(i);
                StationDevice wn = map.get(obj.getString(SN));
                if (wn != null) {
                    int status = Tools.convertDeviceStatus(obj.getJSONObject("data").getIntValue("sysStatus"));
                    if (wn.getStatus() != status) {
                        wn.setStatus(status);
                        wn.setNumber(null);
                        list.add(wn);
                    }
                }
            }
            if (!list.isEmpty()) {
                stationDeviceService.updateBatchById(list);
                Integer powerStationId = list.get(0).getPowerStationId();
                List<Integer> statusList = stationDeviceService.getBaseMapper().getStatus(powerStationId);
                powerStationService.lambdaUpdate().eq(PowerStation::getId, powerStationId)
                        .set(PowerStation::getStationStatus, Tools.convertStationStatus(statusList))
                        .update();
            }
        }
        updateData(data);
    }

    public String updateData(JSONObject data) {
        String emuSn = data.getString(SN);
        String mid = data.getString(MID);
        JSONArray invData = data.getJSONArray(INV_DATA);
        if (invData == null) {
            invData = new JSONArray(0);
        }
        EmuInv emuInv = stationDeviceService.getEmuInv(emuSn);
        if (emuInv == null) {
            return "emuSn未配置：" + emuSn;
        }
        PowerStation powerStation = powerStationService.getBaseMapper().getStationByEmu(emuSn);
        Instant instant = Instant.ofEpochSecond(data.getLongValue(TIMESTAMP));
        DeviceData emuData = new DeviceData();
        ZoneId zoneId = TimeZoneUtil.getZoneId(powerStation.getTimeZone());
        if (zoneId == null) {
            return "时区参数错误";
        }
        LocalDateTime createTime = instant.atZone(zoneId).toLocalDateTime();
        LocalDateTime endTime = LocalDateTime.of(createTime.toLocalDate(), TimeZoneUtil.TIME_23_59_59);
        long seconds = Duration.between(createTime, endTime).getSeconds() + 1L;
        String time = instant.atZone(ZoneId.systemDefault()).format(TimeZoneUtil.yyyyMMddHHmmss);
        List<DeviceData> deviceList = new ArrayList<>(invData.size() + 1);
        emuData.setMid(mid);
        emuData.setTimeCode(powerStation.getTimeZone());
        emuData.setRunningStatus(1);
        emuData.setMonth(createTime.getMonthValue());
        emuData.setHm(createTime.getHour() * 100 + createTime.getMinute());
        emuData.setEmuSn(emuSn);
        emuData.setSn(emuSn);
        emuData.setDeviceId(emuInv.getEmuId());
        emuData.setDeviceType(DeviceTypeEnum.EMU.getVal());
        emuData.setPowerStationId(powerStation.getId());
        emuData.setTimeCode(powerStation.getTimeZone());
        emuData.setCreateTime(instant);
        emuData.setSysStatus(8);
        deviceList.add(emuData);
        for (int i = 0; i < invData.size(); i++) {
            JSONObject json = invData.getJSONObject(i);
            String sn = json.getString(SN);
            Integer deviceId = emuInv.getInvMap().get(sn);
            if (deviceId == null) {
                continue;
            }
            DeviceData device = json.getObject(DATA, DeviceData.class);
            if (device.getDailyEnergy() == null) {
                continue;
            }
            device.setPowerStationId(powerStation.getId());
            device.setMid(mid);
            device.setDeviceId(deviceId);
            device.setPowerStationId(powerStation.getId());
            device.setEmuSn(emuSn);
            device.setSn(sn);
            device.setDeviceType(DeviceTypeEnum.WN.getVal());
            device.setPvCount(json.getInteger("pvCount"));
            device.setCommStatus(json.getInteger("commStatus"));
            device.setFaultCode(json.getString("faultCode"));
            device.setSoftVer(json.getString("softVer"));
            device.setHardVer(json.getString("hardVer"));
            device.setPid(emuInv.getEmuId());
            device.setMonth(emuData.getMonth());
            device.setHm(emuData.getHm());
            device.setTimeCode(powerStation.getTimeZone());
            device.setCreateTime(instant);
            buildPvData(device, json.getJSONObject(DATA));
            if (device.getCommStatus() == 0) {
                stationDeviceService.updateInvDevice(time, device, 0);
                continue;
            }
            //获取要缓存的数据对象
            EnergyFaultCache newCache = stationDeviceService.dataToCache(device);
            //计算单次发电量
            EnergyFaultCache oldCache = stationDeviceService.initStageDailyEnergy(seconds, device, newCache);
            threadPoolTaskExecutor.execute(() -> {
                //设备报警处理
                JSONObject oldFault = oldCache == null ? null : oldCache.getFaultCode();
                String format = createTime.format(TimeZoneUtil.yyyyMMddHHmmss);
                stationDeviceService.deviceAlarmHandle(emuInv.getTenantId(), format, device, newCache.getFaultCode(), oldFault);
            });
            //更新微逆版本信息
            stationDeviceService.updateInvDevice(time, device, Tools.convertDeviceStatus(device.getSysStatus()));
            deviceList.add(device);

        }
        //更新电站状态
        powerStationService.updateStationStatus(emuInv.getStationId(), time);
        //保存微逆运行数据到时序数据库
        influxMapper.save(deviceList);
        log.info("*****收集数据成功，mid={}", mid);
        log.info("*****保存数据成功，json={}", JSONArray.toJSONString(deviceList));
        return null;
    }


    /**
     * 读取EMU参数
     *
     * @param data 数据
     */
    public void responseReadEmuParam(JSONObject data) {
        updateCommand(data);
    }

    /**
     * 升级EMU固件
     */
    public void responseUpgradeEmuFile(JSONObject data) {
        updateCommand(data);
        int status = data.getIntValue(STATUS);
        if (status == 2) {
            stationDeviceService.lambdaUpdate()
                    .eq(StationDevice::getNumber, data.getString(SN))
                    .set(StationDevice::getLastUpgradeTime, LocalDateTime.now())
                    .update();
        }
        switch (status) {
            case 0, 2 -> CommandEnum.EMU_UPGRADE_FILE.delRepeatRequest(data.getString("sn"));
        }
    }

    /**
     * 升级微逆固件
     * 状态：0失败 1升级中 2升级成功
     */
    public void responseUpgradeInvOfEmuFile(JSONObject data) {
        String responseId = data.getString(RESPONSE_ID);
        JSONObject result = data.getJSONObject("result");
        int status = result.getIntValue(STATUS);
        String invSn = result.getString(SN);
        String key = CacheConstants.TEMP_KEY + responseId;
        JSONArray results = RedisUtil.getJSONArray(key);
        if (results != null) {
            for (int i = 0; i < results.size(); i++) {
                JSONObject json = results.getJSONObject(i);
                if (json.getString(SN).equals(invSn) && json.getIntValue(STATUS) != 2) {
                    json.put(STATUS, status);
                    json.put("progress", result.getIntValue("progress"));
                    json.put("errorCode", result.getIntValue("errorCode"));
                    break;
                }
            }
            long count = results.stream().filter(json -> ((JSONObject) json).getIntValue(STATUS) == 2).count();
            RedisUtil.set(key, results.toString(), Duration.ofHours(1));
            data.put("results", results);
            data.put(STATUS, count == results.size());
        }
        stationDeviceRemoteService.lambdaUpdate()
                .eq(StationDeviceRemote::getMid, responseId)
                .set(StationDeviceRemote::getResponseResult, data.toString())
                .set(StationDeviceRemote::getResponseTime, LocalDateTime.now())
                .update();
        if (status == 2) {
            stationDeviceService.lambdaUpdate()
                    .eq(StationDevice::getNumber, invSn)
                    .set(StationDevice::getLastUpgradeTime, LocalDateTime.now())
                    .update();
        }
        switch (status) {
            case 0, 2 -> CommandEnum.EMU_INV_UPGRADE_FILE.delRepeatRequest(invSn);
        }
    }

    /**
     * 设置Emu时区
     */
    public void responseEmuTimeZone(JSONObject data) {
        updateCommand(data);
    }

    /**
     * 写微逆参数  开机，关机，重启，并网文件升级
     */
    public void responseWriteInvOfEmuParam(JSONObject data) {
        updateCommand(data);
    }

    /**
     * EMU自动组网 读emu组网信息
     */
    public void responseReadInvNetworking(JSONObject data) {
        updateCommand(data);
        if (data.getIntValue("status") == 0 && data.getIntValue("type") == 1) {
            JSONArray array = data.getJSONArray("inv");
            if (CollUtil.isNotEmpty(array)) {
                JSONObject obj = new JSONObject();
                obj.put("inv", array);
                obj.put("time", LocalDateTime.now());
                RedisUtil.set(CacheConstants.EMU_ONLINE_KEY + data.getString("sn"), obj.toString(), Duration.ofMinutes(15));
            }
        }
    }

    /**
     * 给EMU下发组网信息
     */
    public void responseWriteEmuNetworking(JSONObject data) {
        updateCommand(data);
    }

    /**
     * 下发并网文件
     */
    public void responseWriteGridParam(JSONObject data) {
        updateCommand(data);
    }

    /**
     * 并网文件查看
     */
    public void responseReadGridParam(JSONObject data) {
        JSONArray params = data.getJSONArray("params");
        if (CollUtil.isNotEmpty(params)) {
            for (int i = 0; i < params.size(); i++) {
                JSONObject param = params.getJSONObject(i);
                if (param.getIntValue("addr") == 9) {
                    data.put("gridFileId", param.getInteger("value"));
//                    params.remove(i);
                    break;
                }
            }
        }
        updateCommand(data);
    }

    /**
     * 读取故障录播
     */
    public void responseInvFaultWave(JSONObject data) {
        updateCommand(data);
        CommandEnum.EMU_READ_FAULT_WAVE.delRepeatRequest(data.getString("inv"));
    }

    /**
     * 读故障记录文件
     */
    public void responseReadInvFaultLog(JSONObject data) {
        updateCommand(data);
        CommandEnum.EMU_READ_FAULT_LOG.delRepeatRequest(data.getString("inv"));
    }

    /**
     * 读取故障录播参数配置
     */
    public void responseReadFaultWaveConfig(JSONObject data) {
        updateCommand(data);
    }

    public static void main(String[] args) {
        TimeZone.setDefault(TimeZone.getTimeZone("Europe/Berlin"));
        System.out.println(LocalDateTime.now());
        System.out.println(Instant.ofEpochSecond(1746531390).atZone(ZoneId.of("Asia/Shanghai")));
        LocalDateTime localDateTime = Instant.ofEpochSecond(1746531390).atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime();
        System.out.println(localDateTime);
    }

    /**
     * emu数据上传
     */
    public String emuTimedUpload(JSONObject data) {
        JSONObject gateWay = data.getJSONObject(GATE_WAY_DATA);
        if (gateWay == null) {
            return "emu上报数据缺失网关信息";
        }
        String emuSn = gateWay.getString(SN);
        JSONArray invData = data.getJSONArray(INV_DATA);
        if (invData == null) {
            invData = new JSONArray(0);
        }
        EmuInv emuInv = stationDeviceService.getEmuInv(emuSn);
        if (emuInv == null) {
            return "emuSn未配置：" + emuSn;
        }
        Instant instant = Instant.ofEpochSecond(data.getLongValue(TIMESTAMP));
        DeviceData emuData = new DeviceData();
        copyWifiToDeviceData(emuData, emuInv.getStationId(), emuInv.getEmuId(), emuSn, data, instant, DeviceTypeEnum.EMU, gateWay);
        if (StrUtil.isEmpty(emuData.getTimeCode())) {
            return "时区未上传";
        }
        ZoneId zoneId = TimeZoneUtil.getZoneId(emuData.getTimeCode());
        if (zoneId == null) {
            return "时区参数错误";
        }
        long stationCreateSecond = emuInv.getStationCreateTime().atZone(zoneId).toEpochSecond();
        if (instant.getEpochSecond() < stationCreateSecond) {
            return "数据上报时间小于建站时间";
        }
        long newSecond = ZonedDateTime.now(zoneId).toEpochSecond();
        // 允许晚一天的数据
        if (instant.getEpochSecond() < newSecond - 86400L) {
            return "数据上报时间小于当前时间";
        }
        // 允许多传一天的数据
        if (instant.getEpochSecond() > newSecond + 86400L) {
            return "数据上报时间大于当前时间";
        }
        //设备上报数据时间
        LocalDateTime createTime = instant.atZone(zoneId).toLocalDateTime();
        LocalDateTime endTime = LocalDateTime.of(createTime.toLocalDate(), TimeZoneUtil.TIME_23_59_59);
        long seconds = Duration.between(createTime, endTime).getSeconds() + 1L;
        String time = instant.atZone(ZoneId.systemDefault()).format(TimeZoneUtil.yyyyMMddHHmmss);
        List<DeviceData> deviceList = new ArrayList<>(invData.size() + 1);
        emuData.setMonth(createTime.getMonthValue());
        emuData.setHm(createTime.getHour() * 100 + createTime.getMinute());
        deviceList.add(emuData);
        for (int i = 0; i < invData.size(); i++) {
            JSONObject json = invData.getJSONObject(i);
            String sn = json.getString(SN);
            Integer deviceId = emuInv.getInvMap().get(sn);
            if (deviceId == null) {
                continue;
            }
            DeviceData device = json.getObject(DATA, DeviceData.class);
            if (device.getDailyEnergy() == null) {
                continue;
            }
            device.setEmuSn(emuSn);
            device.setPvCount(json.getInteger("pvCount"));
            device.setCommStatus(json.getInteger("commStatus"));
            device.setFaultCode(json.getString("faultCode"));
            device.setSoftVer(json.getString("softVer"));
            device.setHardVer(json.getString("hardVer"));
            device.setPid(emuInv.getEmuId());
            device.setMonth(emuData.getMonth());
            device.setHm(emuData.getHm());
            buildPvData(device, json.getJSONObject(DATA));
            copyWifiToDeviceData(device, emuInv.getStationId(), deviceId, sn, data, instant, DeviceTypeEnum.WN, gateWay);
            if (device.getCommStatus() == 0) {
                stationDeviceService.updateInvDevice(time, device, 0);
                continue;
            }
            //获取要缓存的数据对象
            EnergyFaultCache newCache = stationDeviceService.dataToCache(device);
            //计算单次发电量
            EnergyFaultCache oldCache = stationDeviceService.initStageDailyEnergy(seconds, device, newCache);
            threadPoolTaskExecutor.execute(() -> {
                //设备报警处理
                JSONObject oldFault = oldCache == null ? null : oldCache.getFaultCode();
                String format = createTime.format(TimeZoneUtil.yyyyMMddHHmmss);
                stationDeviceService.deviceAlarmHandle(emuInv.getTenantId(), format, device, newCache.getFaultCode(), oldFault);
            });
            //更新微逆版本信息
            stationDeviceService.updateInvDevice(time, device, Tools.convertDeviceStatus(device.getSysStatus()));
            deviceList.add(device);
        }
        //更新EMU设备版本信息
        stationDeviceService.updateEmuDevice(time, emuData);
        //更新电站状态
        powerStationService.updateStationStatus(emuInv.getStationId(), time);
        //保存微逆运行数据到时序数据库
        influxMapper.save(deviceList);
        return null;
    }

    /**
     * 电表数据上传
     */
    public String timedUploadMeterData(JSONObject data) {
        JSONArray array = data.getJSONArray("meterData");
        if (CollUtil.isEmpty(array)) {
            return "emu上报数据meterData数据缺失";
        }
        String emuSn = data.getString("EmuSn");
        EmuMeter emuMeter = stationDeviceService.getEmuMeter(emuSn);
        if (emuMeter == null) {
            return "emuSn未配置：" + emuSn;
        }
        String mid = data.getString(MID);
        String timeCode = data.getString("timeCode");
        if (StrUtil.isEmpty(timeCode)) {
            return "时区未上传";
        }
        ZoneId zoneId = TimeZoneUtil.getZoneId(timeCode);
        if (zoneId == null) {
            return "时区参数错误";
        }
        Instant instant = Instant.ofEpochSecond(data.getLongValue(TIMESTAMP));
        long stationCreateSecond = emuMeter.getStationCreateTime().atZone(zoneId).toEpochSecond();
        if (instant.getEpochSecond() < stationCreateSecond) {
            return "数据上报时间小于建站时间";
        }
        long newSecond = ZonedDateTime.now(zoneId).toEpochSecond();
        // 允许晚一天的数据
        if (instant.getEpochSecond() < newSecond - 86400L) {
            return "数据上报时间小于当前时间";
        }
        // 允许多传一天的数据
        if (instant.getEpochSecond() > newSecond + 86400L) {
            return "数据上报时间大于当前时间";
        }
        LocalDateTime createTime = instant.atZone(zoneId).toLocalDateTime();
        LocalDateTime endTime = LocalDateTime.of(createTime.toLocalDate(), TimeZoneUtil.TIME_23_59_59);
        long seconds = Duration.between(createTime, endTime).getSeconds() + 1L;
        String time = instant.atZone(ZoneId.systemDefault()).format(TimeZoneUtil.yyyyMMddHHmmss);
        List<MeterData> deviceList = new ArrayList<>(array.size());
        for (int i = 0; i < array.size(); i++) {
            MeterData meter = array.getObject(i, MeterData.class);
            EmuMeter.Meter mm = emuMeter.getMeterMap().get(meter.getSn());
            if (mm == null) {
                continue;
            }
            meter.setDeviceId(mm.getMeterId());
            meter.setPowerStationId(emuMeter.getStationId());
            meter.setPowerGridSeat(mm.getPowerGridSeat());
            meter.setCreateTime(instant);
            meter.setMid(mid);
            meter.setEmuSn(emuSn);
            if (meter.getCommStatus() != 1) {
                stationDeviceService.updateMeterDevice(time, meter, 0);
                continue;
            }
            //计算单次用电量
            stationDeviceService.initStageConsumeEnergy(seconds, meter);
            //更新电表数据
            stationDeviceService.updateMeterDevice(time, meter, 1);
            deviceList.add(meter);
        }
        //保存电表数据
        if (!deviceList.isEmpty()) influxMapper.save(deviceList);
        return null;
    }

}
