package com.wh.data.pojo.dto;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/2/10
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class GridParamDTO {

    //地址
    @NotNull
    private Integer addr;

    //数据类型 1int 2float
    private Integer type;

    //值
    @NotNull
    private BigDecimal value;

}
