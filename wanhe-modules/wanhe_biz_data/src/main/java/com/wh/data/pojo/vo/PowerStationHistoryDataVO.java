package com.wh.data.pojo.vo;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.utils.TimeZoneUtil;
import com.ruoyi.common.core.utils.Tools;
import lombok.Data;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/12/6
 */
@Data
public class PowerStationHistoryDataVO {

    //发电功率
    private List<List<Object>> consumer = new ArrayList<>();
    private String consumerUnit;
    //用电功率
    private List<List<Object>> producer = new ArrayList<>();
    private String producerUnit;

    public PowerStationHistoryDataVO(List<List<Object>> consumer, List<List<Object>> producer) {
        this.consumer = consumer;
        this.producer = producer;
        consumerUnit = Tools.covertUnit(consumer);
        producerUnit = Tools.covertUnit(producer);
    }

    public PowerStationHistoryDataVO(List<List<JSONObject>> list, Integer type, ZonedDateTime beginTime, ZonedDateTime endTime) {
        Map<String, BigDecimal> consumerMap = Tools.getMap(list.get(0));
        Map<String, BigDecimal> producerMap = Tools.getMap(list.get(1));
        switch (type) {
            case 1 -> {
                do {
                    String time = TimeZoneUtil.HHmm.format(beginTime.toLocalDateTime());
                    consumer.add(Tools.getTimeDataGtZero(time, consumerMap.get(time)));
                    producer.add(Tools.getTimeDataGtZero(time, Tools.add(consumerMap.get(time), producerMap.get(time))));
                } while ((beginTime = beginTime.plusMinutes(15)).isBefore(endTime));
                consumerUnit = Tools.covertPowerWUnit(consumer);
                producerUnit = Tools.covertPowerWUnit(producer);
            }
            case 2 -> {
                do {
                    String time = TimeZoneUtil.MMddHHmm.format(beginTime.toLocalDateTime());
                    consumer.add(Tools.getTimeDataGtZero(time, consumerMap.get(time)));
                    producer.add(Tools.getTimeDataGtZero(time, Tools.add(consumerMap.get(time), producerMap.get(time))));
                } while ((beginTime = beginTime.plusMinutes(15)).isBefore(endTime));
                consumerUnit = Tools.covertPowerWUnit(consumer);
                producerUnit = Tools.covertPowerWUnit(producer);
            }
            case 3 -> {
//                ZonedDateTime now = ZonedDateTime.now(beginTime.getZone());
                do {
                    String time = TimeZoneUtil.MMdd.format(beginTime.toLocalDateTime());
                    BigDecimal consumerVal = consumerMap.get(time);
                    BigDecimal producerVal = producerMap.get(time);
                    consumer.add(Tools.getTimeData(time, consumerVal));
                    if (consumerVal != null && producerVal != null) {
                        producer.add(Tools.getTimeData(time, producerVal.add(consumerVal)));
                    } else if (producerVal != null) {
                        producer.add(Tools.getTimeData(time, producerVal));
                    } else if (consumerVal != null) {
                        producer.add(Tools.getTimeData(time, consumerVal));
                    } else {
                        producer.add(Tools.getTimeData(time, null));
                    }
                } while ((beginTime = beginTime.plusDays(1)).isBefore(endTime)/* && beginTime.isBefore(now)*/);
                consumerUnit = Tools.covertUnit(consumer);
                producerUnit = Tools.covertUnit(producer);
            }
        }
    }
}
