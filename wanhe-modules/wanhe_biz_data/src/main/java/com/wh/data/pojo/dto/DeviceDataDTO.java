package com.wh.data.pojo.dto;

import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2024/12/2
 */
@Setter
@Getter
public class DeviceDataDTO {

    private String mid;

    private long timeStamp;

    private String sn;

    public DeviceDataDTO(String sn) {
        this.sn = sn;
        this.mid = UUID.randomUUID().toString();
        this.timeStamp = Instant.now().getEpochSecond();
    }
}
