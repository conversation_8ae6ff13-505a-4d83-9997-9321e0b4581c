package com.wh.data.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/23
 */
@Data
public class DiagnoseVO {

    @Schema(description = "网关设备号")
    private String gatewaySn;

    @Schema(description = "网关或wifi 运行状态，1：正常")
    private Integer runningStatus;

    @Schema(description = "联网模式 0:WIFI 1:有线网 2:4G")
    private Integer commType;

    @Schema(description = "设备数据")
    private List<DiagnoseDeviceVO> inv;

}
