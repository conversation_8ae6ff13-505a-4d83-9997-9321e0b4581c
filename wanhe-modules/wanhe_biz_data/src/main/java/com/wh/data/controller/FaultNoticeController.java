package com.wh.data.controller;

import com.github.pagehelper.Page;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.wh.data.domain.FaultNotice;
import com.wh.data.pojo.dto.FaultNoticePageReqDTO;
import com.wh.data.service.FaultNoticeService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 故障告警
 * <AUTHOR>
 * @date 2024/12/30
 */
@RestController
@RequestMapping("/fault/notice")
public class FaultNoticeController extends BaseController {

    @Resource
    private FaultNoticeService faultNoticeService;

    /**
     * 故障告警通知分页
     */
    @GetMapping(value = "/page")
    public TableDataInfo<FaultNotice> page(@Validated FaultNoticePageReqDTO dto) {
        Page<FaultNotice> page = startPage(dto);
        faultNoticeService.page(dto);
        return getDataTable(page);
    }

    /**
     * 故障告警通知详情
     */
    @GetMapping
    public R<FaultNotice> get(@RequestParam Integer id) {
        return R.ok(faultNoticeService.get(id));
    }

    /**
     * 一键已读所有故障通知
     *
     * @return {@link R }<{@link Void }>
     */
    @GetMapping("/readAll")
    public R<Void> readAllFaultNotice() {
        faultNoticeService.readAllFaultNotice();
        return R.ok();
    }

    /**
     * 未读统计 0是公告 1预警
     */
    @GetMapping(value = "/unreadCount")
    public R<List<Integer>> unreadCount() {
        return R.ok(faultNoticeService.unreadCount());
    }

}
