package com.wh.data.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonRawValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024-11-19
 */
@Data
@TableName("wh_station_device_remote")
@Schema(name = "StationDeviceRemote", description = "设备表")
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class StationDeviceRemote {

    @Schema(description = "主键")
    @EqualsAndHashCode.Include
    @TableId(type = IdType.AUTO)
    private Integer id;

    @Schema(description = "电站id")
    private Integer powerStationId;

    @Schema(description = "设备id")
    private Integer stationDeviceId;

    @Schema(description = "下发唯一标识")
    private String mid;

    @Schema(description = "设备号")
    private String number;

    @Schema(description = "微逆设备号集合")
    @JsonRawValue
    private String invSn;

    @Schema(description = "设备类型 （0：EMU 1:微逆 2：电表）")
    private Integer deviceType;

    @Schema(description = "操作类型 字典表emu：emu_command wifi微逆下发类型 inv_command ")
    private Integer operate;

    @Schema(description = "下发内容")
    @JsonRawValue
    private String issueContent;

    @Schema(description = "响应结果")
    @JsonRawValue
    private String responseResult;

    @Schema(description = "响应时间")
    private LocalDateTime responseTime;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "命令")
    @TableField(exist = false)
    private String command;

    @Schema(description = "电站名称")
    @TableField(exist = false)
    private String stationName;

    @Schema(description = "时区")
    @TableField(exist = false)
    private String timeZone;

}
