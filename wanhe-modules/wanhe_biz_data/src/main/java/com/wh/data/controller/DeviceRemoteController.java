package com.wh.data.controller;

import com.github.pagehelper.Page;
import com.ruoyi.common.core.domain.IntStr;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.LanguageEnum;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.core.utils.I18nUtil;
import com.wh.data.domain.StationDeviceRemote;
import com.wh.data.enums.CommandEnum;
import com.wh.data.pojo.dto.StationDeviceRemotePageDTO;
import com.wh.data.service.StationDeviceRemoteService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Locale;

/**
 * 远程控制
 */
@RestController
@RequestMapping("/device/remote")
public class DeviceRemoteController extends BaseController {

    @Resource
    private StationDeviceRemoteService stationDeviceRemoteService;

    /**
     * EMU命令
     */
    @GetMapping("/emu/command")
    public R<List<IntStr>> emuCommand(HttpServletRequest request) {
        Locale locale = LanguageEnum.getLocal(request);
        List<IntStr> list = CommandEnum.EMU.stream().map(item -> new IntStr(item.getK(), I18nUtil.get(item.getV(), locale))).toList();
        return R.ok(list);
    }

    /**
     * 微逆命令
     */
    @GetMapping("/inv/command")
    public R<List<IntStr>> invCommand(HttpServletRequest request) {
        Locale locale = LanguageEnum.getLocal(request);
        List<IntStr> list = CommandEnum.INV.stream().map(item -> new IntStr(item.getK(), I18nUtil.get(item.getV(), locale))).toList();
        return R.ok(list);
    }

    /**
     * 远程控制列表
     */
    @GetMapping("/page")
    public TableDataInfo<StationDeviceRemote> page(@Validated StationDeviceRemotePageDTO dto) {
        Page<StationDeviceRemote> page = startPage(dto);
        stationDeviceRemoteService.page(dto);
        return getDataTable(page);
    }

}
