package com.wh.data.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wh.data.domain.FaultParam;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/8
 */
public interface FaultParamMapper extends BaseMapper<FaultParam> {

    @Select("<script>" +
            "SELECT param.* FROM wh_fault_param AS param " +
            "LEFT JOIN wh_fault_version AS version ON version.id = param.version_id " +
            "WHERE param_id IN(${ids}) " +
            "<if test='versionNo != null and versionNo != \"\"'>" +
            "AND version.version_no = #{versionNo} " +
            "</if>" +
            "ORDER BY FIELD(param_id, ${ids})" +
            "</script>")
    List<FaultParam> listByParamIds(String ids, String versionNo);

//    @Delete("truncate table wh_fault_param")
    @Delete("delete from wh_fault_param where version_id = #{versionId}")
    void deleteAll(@Param("versionId") Long versionId);

    @Select("select param_group from wh_fault_param group by param_group")
    List<String> typeList();

}
