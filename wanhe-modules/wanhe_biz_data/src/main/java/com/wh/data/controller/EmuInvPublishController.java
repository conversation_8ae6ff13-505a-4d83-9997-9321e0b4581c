package com.wh.data.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.wh.data.biz.EmuInvPublishBiz;
import com.wh.data.domain.StationDevice;
import com.wh.data.pojo.dto.*;
import com.wh.data.pojo.vo.CommandResultVO;
import com.wh.data.pojo.vo.CommandVO;
import com.wh.data.pojo.vo.InvReadCommandVO;
import com.wh.data.service.StationDeviceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

/**
 * emu或emu微逆设备相关操作
 *
 * <AUTHOR>
 * @date 2024/11/8 0008
 */
@RestController
@RequestMapping("/emu/publish")
public class EmuInvPublishController {

    @Resource
    private EmuInvPublishBiz emuInvPublishBiz;
    @Resource
    private StationDeviceService stationDeviceService;

    /**
     * wifi微逆批量操作 开机，关机，重启，并网文件升级，WIFI版固件升级，固件升级
     */
    @Log(title = "EMU微逆批量操作", businessType = BusinessType.COMMAND)
    @PostMapping(value = "/batch/operate")
    public R<List<Integer>> batchOperate(@Validated @RequestBody EmuBatchOperateDTO dto) {
        List<String> deviceList = dto.getDevices().stream().flatMap(d -> d.getNumbers().stream()).toList();
        if (dto.getOperateType() == 4) {
            stationDeviceService.noAuthExists(deviceList);
        } else {
            stationDeviceService.exists(deviceList);
        }
        return R.ok(emuInvPublishBiz.batchOperate(dto));
    }

    /**
     * emu重启
     */
    @Log(title = "EMU重启", businessType = BusinessType.COMMAND)
    @PostMapping(value = "/emuOperate")
    public R<Integer> emuOperate(@Validated @RequestBody EmuParamDTO dto) {
        stationDeviceService.exists(dto.getSn());
        return R.ok(emuInvPublishBiz.emuOperate(dto));
    }

    /**
     * emu收集微逆版本信息
     */
    @Log(title = "EMU收集微逆版本信息", businessType = BusinessType.COMMAND)
    @PostMapping(value = "/emuInfoCollect")
    public R<Integer> emuInfoCollect(@Validated @RequestBody EmuParamDTO dto) {
        stationDeviceService.exists(dto.getSn());
        return R.ok(emuInvPublishBiz.emuInfoCollect(dto));
    }

    /**
     * emu /emu微逆 固件升级
     */
    @Log(title = "EMU或微逆固件升级", businessType = BusinessType.COMMAND)
    @PostMapping(value = "/emuUpgradeFile")
    public R<Integer> emuUpgradeFile(@Validated @RequestBody EmuUpgradeDTO dto) {
        StationDevice device = stationDeviceService.exists(dto.getSn());
        return R.ok(emuInvPublishBiz.emuUpgradeFile(dto, device));
    }

    /**
     * emu设备时区下发
     */
    @Log(title = "EMU时区下发", businessType = BusinessType.COMMAND)
    @PostMapping(value = "/setEmuTimeZone")
    public R<Integer> setEmuTimeZone(@Validated @RequestBody SetTimeZoneDTO dto) {
        stationDeviceService.exists(dto.getSn());
        return R.ok(emuInvPublishBiz.setEmuTimeZone(dto));
    }

    /**
     * emu微逆 开机，关机，重启，并网文件升级，并网文件查看
     */
    @Log(title = "EMU微逆操作", businessType = BusinessType.COMMAND)
    @PostMapping(value = "/emuInvOperate")
    public R<Integer> emuInvOperate(@Validated @RequestBody EmuInvOpeDTO dto) {
        StationDevice device;
        if (dto.getOperateType() == 4) {
            device = stationDeviceService.noAuthExists(dto.getSn());
        } else {
            device = stationDeviceService.exists(dto.getSn());
        }
        return R.ok(emuInvPublishBiz.emuInvOperate(dto, device));
    }

    /**
     * 查询下发结果
     */
    @GetMapping(value = "/queryCallBackResult")
    public R<Object> queryCallBackResult(@RequestParam Integer id) {
        return R.ok(emuInvPublishBiz.getStationDeviceRemoteService().queryCallBackResult(id));
    }

    /**
     * 查询多个下发结果
     */
    @PostMapping(value = "/queryCallBackResults")
    public R<List<Object>> queryCallBackResults(@Validated @NotEmpty @RequestBody List<Integer> ids) {
        return R.ok(emuInvPublishBiz.getStationDeviceRemoteService().queryCallBackResults(ids));
    }

    /**
     * 通过emu查询所有微逆组网
     */
    @GetMapping(value = "/getEmuInvByEmu")
    public R<Integer> getEmuInvByEmu(@Schema(description = "emu序列号") @RequestParam String sn,
                                     @Schema(description = "组网方式 0:手动组网 1：自动组网") @RequestParam Integer type,
                                     @Schema(description = "电站id") @RequestParam Integer powerStationId) {
        StationDevice device = stationDeviceService.getBaseMapper().getBySn(sn);
        if (device != null && device.getStatus() == 0) {
            throw new ServiceException("设备已离线");
        }
        return R.ok(emuInvPublishBiz.getEmuInvByEmu(sn, type, powerStationId));
    }

    /**
     * 下发组网信息
     * todo 已支持wifi微逆
     */
    @GetMapping("/networking")
    public R<Void> networking(@RequestParam Integer powerStationId) {
        emuInvPublishBiz.networking(powerStationId);
        return R.ok();
    }

    /**
     * 配置电表
     */
    @GetMapping(value = "/config/meter")
    public R<Boolean> configMeter(@RequestParam Integer powerStationId) {
        emuInvPublishBiz.configMeter(powerStationId);
        return R.ok(true);
    }

    /**
     * 读取故障录播
     */
    @Log(title = "读取故障录播", businessType = BusinessType.COMMAND)
    @GetMapping(value = "/read/fault/wave")
    public R<Integer> readFaultWave(@RequestParam @Schema(description = "微逆序列号") String sn) {
        StationDevice device = stationDeviceService.noAuthExists(sn);
        return R.ok(emuInvPublishBiz.readFaultWave(device.getEmuNumber(), device.getNumber()));
    }

    /**
     * 读故障记录文件
     */
    @Log(title = "读故障记录文件", businessType = BusinessType.COMMAND)
    @GetMapping(value = "/read/fault/log")
    public R<Integer> readFaultLog(@RequestParam @Schema(description = "微逆序列号") String sn) {
        StationDevice device = stationDeviceService.noAuthExists(sn);
        return R.ok(emuInvPublishBiz.readFaultLog(device.getEmuNumber(), device.getNumber()));
    }

    /**
     * 命令下发
     */
    @Log(title = "命令下发", businessType = BusinessType.COMMAND)
    @PostMapping(value = "/command")
    public R<CommandVO> command(@Validated @RequestBody CommandDTO dto) {
        StationDevice device = stationDeviceService.noAuthExists(dto.getSn());
        return R.ok(emuInvPublishBiz.command(dto, device));
    }

    /**
     * 命令下发结果查询
     */
    @GetMapping(value = "/command/result")
    public R<CommandResultVO> commandResult(@RequestParam Integer id, @RequestParam String topic) {
        return R.ok(emuInvPublishBiz.getStationDeviceRemoteService().commandResult(id, topic));
    }

    /**
     * 读取故障录播默认参数
     */
    @Log(title = "读取故障录播默认参数", businessType = BusinessType.COMMAND)
    @GetMapping(value = "/read/fault/wave/setting")
    public R<Integer> readFaultWaveSetting(@RequestParam @Schema(description = "微逆序列号") String sn) {
        StationDevice device = stationDeviceService.noAuthExists(sn);
        return R.ok(emuInvPublishBiz.readFaultWaveSetting(device.getEmuNumber(), device.getNumber()));
    }

    /**
     * 设置故障录播参数
     */
    @Log(title = "设置故障录播参数", businessType = BusinessType.COMMAND)
    @PostMapping(value = "/read/fault/wave/setting")
    public R<Integer> faultWaveSetting(@Validated @RequestBody FaultWaveSettingDTO dto) {
        StationDevice device = stationDeviceService.noAuthExists(dto.getSn());
        return R.ok(emuInvPublishBiz.faultWaveSetting(dto, device));
    }

    /**
     * 导出下发结果txt
     */
    @Log(title = "导出下发结果", businessType = BusinessType.EXPORT)
    @PostMapping(value = "/command/result/export")
    public void commandResult(@RequestParam Integer id, HttpServletResponse response) throws IOException {
        emuInvPublishBiz.getStationDeviceRemoteService().commandResultExport(id, response);
    }


    /**
     * 设备自检1
     * 接口1、前端每10秒发送一次请求，后端下发命令
     * 接口2、前端每10秒获取设备状态
     */
    @Log(title = "发送设备自检命令", businessType = BusinessType.COMMAND)
    @PostMapping(value = "/sendSelfCheckCommand")
    public R<Integer> sendSelfCheckCommand(@RequestParam Integer stationId) {
        return R.ok(emuInvPublishBiz.sendSelfCheckCommand(stationId));
    }

    /**
     * 读取微逆参数
     *
     * @param dto 到
     * @return {@link R }<{@link Integer }>
     */
    @Operation(summary = "读取微逆参数")
    @Log(title = "读取微逆参数", businessType = BusinessType.COMMAND)
    @PostMapping(value = "/readInvCommand")
    public R<Integer> readInvCommand(@Validated @RequestBody InvReadCommandDTO dto) {
        stationDeviceService.noAuthExists(dto.getSn());
        return R.ok(emuInvPublishBiz.readInvCommand(dto));
    }


    /**
     * 读取微逆参数结果
     *
     * @param id id
     * @return {@link R }<{@link InvReadCommandVO }>
     */
    @Operation(summary = "读取微逆参数结果")
    @GetMapping(value = "/command/invResult")
    public R<InvReadCommandVO> commandInvResult(@RequestParam("id") Integer id,
                                                @RequestParam(value = "timezone", required = false, defaultValue = "UTC+8") String timezone) {
        return R.ok(emuInvPublishBiz.commandInvResult(id, timezone));
    }

    /**
     * 获取电表数据
     *
     * @param sn 微逆序列号
     * @return {@link R }<{@link Integer }>
     */
    @Operation(summary = "获取电表数据")
    @GetMapping(value = "/getMeterData")
    public R<Integer> getMeterData(@RequestParam @Schema(description = "微逆序列号") String sn) {
        StationDevice stationDevice = stationDeviceService.exists(sn);
        return R.ok(emuInvPublishBiz.getMeterData(stationDevice));
    }
}
