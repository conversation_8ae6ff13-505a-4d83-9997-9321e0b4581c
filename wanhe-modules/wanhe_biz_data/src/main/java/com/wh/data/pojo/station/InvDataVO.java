package com.wh.data.pojo.station;

import com.fasterxml.jackson.annotation.JsonRawValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ruoyi.common.core.json.BigDecimalSerializer;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/1/12
 */
@Data
public class InvDataVO {

    //序列号
    private String sn;

    //微逆型号
    private String model;

    // PV瞬时功率,W
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pvTempPower;

    // AC瞬时功率,W
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal acTempPower;

    // 电网频率,Hz
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal gridFreq;

    // 系统状态 1自检ok 2停机 4预启动 8运行 16故障 32告警
    private Integer sysStatus;

    //启机控制,0/停止  1/启动
    private Integer sysCtrlCmd;

    // PV电压1实际值,V
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pvVolReal;

    // PV电流1实际值，A
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal PvCurrReal;

    // AC电压实际值,V
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal acVolReal;

    // AC电流实际值,A
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal acCurrReal;

    // AC MOS温度实际值,℃
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal acMosTempReal;

    // PV MOS温度实际值,℃
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal pVMosTempReal;

    // 日运行时间，h
    private Integer dayRunTime;

    // 日发电量,kWh
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal dailyEnergy;

    // 微逆发电效率,%
    @JsonSerialize(using = BigDecimalSerializer.class, nullsUsing = BigDecimalSerializer.class)
    private BigDecimal efficiency;

    // 连接组件数量
    private Integer pvCount;

    // 和EMU的通信状态，0：通信异常，1：正常
    private Integer commStatus;

    // 故障码
    @JsonRawValue
    private String faultCode;

    // 微逆软件版本号
    private String softVer;

    // 微逆硬件版本号
    private String hardVer;

}
