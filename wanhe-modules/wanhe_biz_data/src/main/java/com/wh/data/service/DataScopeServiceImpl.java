package com.wh.data.service;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.github.yulichang.base.MPJBaseMapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.ruoyi.common.security.service.impl.MPJBaseServiceImpl;
import com.ruoyi.common.security.utils.Sql;
import com.ruoyi.system.api.domain.SysRole;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.domain.SysUserStation;
import com.ruoyi.system.api.dto.UserDistrict;
import com.wh.data.domain.PowerStation;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/10
 */
public abstract class DataScopeServiceImpl<M extends MPJBaseMapper<T>, T> extends MPJBaseServiceImpl<M, T> {

    protected <F> MPJLambdaWrapper<T> lambdaQueryAll(SFunction<T, ?> stationId, Integer stationIdVal) {
        MPJLambdaWrapper<T> wrapper = lambdaJoinQuery()
                .innerJoin(PowerStation.class, i -> i.eq(PowerStation::getId, stationId).eq(PowerStation::getDeleted, 0));
        if (stationIdVal != null) {
            return wrapper.eq(stationId, stationIdVal);
        }
        SysUser user = getSysUser();
        if (SysUser.isProprietor(user.getUserType())) {
            wrapper.innerJoin(SysUserStation.class, i -> i
                    .eq(SysUserStation::getPowerStationId, stationId)
                    .eq(SysUserStation::getUserId, user.getUserId()));
            return wrapper;
        }
        UserDistrict ud = getUd(user.getUserId());
        if (!ud.getAllCountry()) {
            if (!ud.getCountryIds().isEmpty() && !ud.getProvinceIds().isEmpty()) {
                wrapper.and(i -> i.in(PowerStation::getCountryId, ud.getCountryIds()).or().in(PowerStation::getProvinceId, ud.getProvinceIds()));
            } else if (!ud.getCountryIds().isEmpty()) {
                wrapper.in(PowerStation::getCountryId, ud.getCountryIds());
            } else if (!ud.getProvinceIds().isEmpty()) {
                wrapper.in(PowerStation::getProvinceId, ud.getProvinceIds());
            } else {
                wrapper.eq(stationId, 0);
            }
        }
        //角色不是超级管理员就要按数据权限查询
        //数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限 5：仅本人数据权限）
        Set<Integer> dataScopes = user.getRoles().stream().map(SysRole::getDataScope).collect(Collectors.toSet());
        if (dataScopes.contains(1) || (dataScopes.contains(4) && user.getTenantId().equals(user.getDeptId()))) {
            wrapper.inSql(isNotAdminRole(user.getRoleIds()), PowerStation::getTenantId, dataScope(user.getTenantId()));
        } else if (dataScopes.contains(2)) {
            //自定义部门ID
            Set<Long> zdyDeptIds = user.getRoles().stream().filter(role -> role.getDataScope() == 2)
                    .flatMap(role -> role.getCustomDeptIds().stream()).collect(Collectors.toSet());
            if (dataScopes.contains(4)) {
                //查询当前机构全部部门
                List<Long> deptIds = Sql.getDeptIds(user.getTenantId());
                //移除掉需要查询的部门
                deptIds.remove(user.getDeptId());
                //异常需要查询的部门，剩余的就是不需要查询的部门
                deptIds.removeAll(zdyDeptIds);
                //查询全部数据不查询自定义之外的部门
                wrapper.inSql(PowerStation::getTenantId, dataScope(user.getTenantId()))
                        .notIn(!deptIds.isEmpty(), PowerStation::getDeptId, deptIds);
            } else if (dataScopes.contains(3)) {
                //只查询本部门加自定义部门
                zdyDeptIds.add(user.getDeptId());
                wrapper.in(PowerStation::getDeptId, zdyDeptIds);
            } else {
                wrapper.in(PowerStation::getDeptId, zdyDeptIds);
            }
        } else if (dataScopes.contains(4)) {
            //查询当前机构全部部门
            List<Long> deptIds = Sql.getDeptIds(user.getTenantId());
            deptIds.remove(user.getDeptId());
            //查询全部数据当前部门
            wrapper.inSql(PowerStation::getTenantId, dataScope(user.getTenantId())).notIn(!deptIds.isEmpty(), PowerStation::getDeptId, deptIds);
        } else if (dataScopes.contains(3)) {
            wrapper.eq(PowerStation::getDeptId, user.getDeptId());
        } else {
            wrapper.eq(PowerStation::getTenantId, user.getTenantId()).eq(PowerStation::getCreateId, user.getUserId());
        }
        return wrapper;
    }

}
