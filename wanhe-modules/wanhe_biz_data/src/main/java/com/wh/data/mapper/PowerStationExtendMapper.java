package com.wh.data.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.ruoyi.system.api.domain.PowerStationExtend;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-11-01
 */
public interface PowerStationExtendMapper extends MPJBaseMapper<PowerStationExtend> {

    @Update("update wh_power_station_extend set day_kwh = 0 where id in(${ids})")
    void resetDayKwh(String ids);

    @Select("select time_zone as tz,group_concat(id) as ids from wh_power_station where deleted = 0 group by time_zone")
    List<Map<String, String>> getTimeZone();

    //更新功率和电量数据
    @Update("update wh_power_station_extend set day_kwh = ${data}, kw = ${kw}, update_time = '${time}' where id = ${id}")
    void updateData(Integer id, BigDecimal kw, BigDecimal data, String time);

    //重置功率
    @Update("update wh_power_station_extend set kw = 0 where update_time > '${begin}' and update_time < '${end}' and kw > 0")
    void resetPower(String begin, String end);

    //电站上线
    @Update("""
    update wh_power_station s inner join
    (select power_station_id,count(*) as total from wh_station_device where last_report_time > '${time}' and `status` = 1 and deleted = 0 group by power_station_id) t
    on s.id = t.power_station_id set s.station_status = 1 where s.station_status in(2,3) and s.num = t.total
    """)
    void stationOnLine(String time);

    //电站离线 最后更新时间超过15分钟还没有离线的设备电站和设备状态都改成离线
    @Update("update wh_power_station set station_status = 2 where id in(select power_station_id from wh_station_device where last_report_time > '${begin}' and last_report_time < '${end}' and deleted = 0 and status > 0 group by power_station_id) and deleted = 0")
    void stationOffLine(String begin, String end);

    @Update("update wh_power_station set station_status = 0 where station_status > 0 and num = 0 and deleted = 0")
    void stationOffLine2();

    //设备离线
    @Update("update wh_station_device set status = 0 where last_report_time > '${begin}' and last_report_time < '${end}' and deleted = 0 and status > 0")
    void deviceOffLine(String begin, String end);

}
