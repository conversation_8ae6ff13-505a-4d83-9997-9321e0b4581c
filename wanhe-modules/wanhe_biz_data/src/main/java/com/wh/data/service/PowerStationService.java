package com.wh.data.service;

import com.ruoyi.common.core.utils.Tools;
import com.ruoyi.common.security.utils.Sql;
import com.ruoyi.system.api.domain.PowerStationExtend;
import com.wh.data.domain.PowerStation;
import com.wh.data.mapper.PowerStationMapper;
import com.wh.data.mapper.StationDeviceMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.Year;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/1
 */
@Service
public class PowerStationService extends DataScopeServiceImpl<PowerStationMapper, PowerStation> {

    @Resource
    private StationDeviceMapper stationDeviceMapper;

    public PowerStation getBySn(String sn) {
        return baseMapper.getBySn(sn);
    }

    public List<Integer> stationList() {
        return baseMapper.getStationIds(getLoginUser().getUserid());
    }

    public String getTermIds() {
        String dataScopeSql = dataScopeSqlId();
        if (dataScopeSql.isEmpty()) {
            return "";
        }
        if (dataScopeSql.equals(FALSE)) {
            return " and false";
        }
        List<Integer> ids = Sql.getStationId(dataScopeSql);
        if (ids.isEmpty()) {
            return " and false";
        }
        StringBuilder sb = new StringBuilder();
        ids.forEach(id -> sb.append(" or powerStationId = '").append(id).append("'"));
        return " and (" + sb.substring(4) + ")";
    }

    public Integer getYear(Integer id) {
        LocalDateTime createTime = baseMapper.getCreateTime(id);
        if (createTime == null) {
            return Year.now().getValue();
        }
        return createTime.getYear();
    }

    public void updateStationStatus(Integer stationId, String updateTime) {
        List<Integer> statusList = stationDeviceMapper.getStatus(stationId);
        baseMapper.updateStatus(stationId, Tools.convertStationStatus(statusList), updateTime);
    }

}
