package com.wh.data.pojo.dto;

import cn.hutool.core.collection.CollUtil;
import com.ruoyi.common.core.utils.Tools;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/8
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class AddrValueDTO {

    //地址
    private int addr;

    //值
    private int value;

    public AddrValueDTO(GridParamDTO dto) {
        this.addr = dto.getAddr();
        if (dto.getType() == 1) {
            this.value = dto.getValue().intValue();
        } else {
            this.value = Tools.floatToIntBits(dto.getValue());
        }
    }

    public static List<AddrValueDTO> to(List<GridParamDTO> list) {
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(AddrValueDTO::new).collect(Collectors.toList());
    }

}
