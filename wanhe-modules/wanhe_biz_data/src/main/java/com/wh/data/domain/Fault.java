package com.wh.data.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024-10-26
 */
@Data
@TableName("wh_fault")
@Schema(name = "Fault", description = "故障表")
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = false)
public class Fault {

    @Schema(description = "主键id")
    @EqualsAndHashCode.Include
    @TableId(type = IdType.AUTO)
    protected Integer id;

    @Schema(description = "故障字")
    private String faultCode;

    @Schema(description = "故障key")
    private String faultKey;

    @Schema(description = "bit位")
    private Integer bit;

    @Schema(description = "故障描述")
    private String faultDescription;

    @Schema(description = "处理意见")
    private String resolutionSuggestion;

    @Schema(description = "故障等级")
    private String faultLevel;

    @Schema(description = "逻辑删除（0正常 非0删除) 唯一索引使用", hidden = true)
    @TableField(select = false)
    @TableLogic
    @JsonIgnore
    protected Integer deleted;

}
