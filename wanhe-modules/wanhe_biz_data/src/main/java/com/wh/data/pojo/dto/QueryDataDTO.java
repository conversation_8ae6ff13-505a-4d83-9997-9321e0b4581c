package com.wh.data.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class QueryDataDTO {

    @Schema(description = "电站id")
    @NotNull
    private Integer powerStationId;

    @Schema(description = "微逆Id")
    private Integer invId;

    @Schema(description = "数据类型 1功率 2电量")
    @NotNull
    private Integer dataType;

    @Schema(description = "查询类型 1 日 2 周")
    @NotNull
    private Integer queryType;

    @Schema(description = "查询时间")
    @NotBlank
    private String queryTime;

}
