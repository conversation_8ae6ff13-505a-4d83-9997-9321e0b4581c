package com.wh.data.biz;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.core.enums.DeviceTypeEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.wh.data.domain.FirmwareFile;
import com.wh.data.domain.StationDevice;
import com.wh.data.enums.CommandEnum;
import com.wh.data.enums.FaultWaveEnum;
import com.wh.data.enums.SendTopicEnum;
import com.wh.data.mapper.GridFileDataMapper;
import com.wh.data.pojo.dto.*;
import com.wh.data.service.FirmwareFileService;
import com.wh.data.service.StationDeviceRemoteService;
import com.wh.data.service.StationDeviceService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/8 0008
 */
@Component
@Slf4j
public class WifiInvPublishBiz {

    @Resource
    private StationDeviceService stationDeviceService;
    @Resource
    private FirmwareFileService firmwareFileService;
    @Resource
    private GridFileDataMapper gridFileDataMapper;
    @Resource
    private StationDeviceRemoteService stationDeviceRemoteService;

    /**
     * inv设备时区下发
     */
    public Integer setInvTimeZone(SetTimeZoneDTO dto) {
        SetInvTimeZoneDTO time = new SetInvTimeZoneDTO(dto.getSn());
        time.setTimeCode(dto.getTimeZone().getTimeCode());
        time.setTime(time.getTimeStamp());
        String message = JSON.toJSONString(time);
        String topic = SendTopicEnum.INV_TIME_ZONE.getTopic(dto.getSn());
        return stationDeviceRemoteService.sendCommand(dto.getSn(), time.getMid(), topic, message, CommandEnum.INV_TIME_ZONE, DeviceTypeEnum.WN);
    }

    public Integer invUpgradeFile(UpgradeInvFileDTO dto, Integer type) {
        CommandEnum command;
        if (type == 0) {
            CommandEnum.INV_UPGRADE_FILE.noRepeatRequest(dto.getSn());
            command = CommandEnum.INV_UPGRADE_FILE;
        } else {
            CommandEnum.INV_WIFI_UPGRADE_FILE.noRepeatRequest(dto.getSn());
            command = CommandEnum.INV_WIFI_UPGRADE_FILE;
        }
        FirmwareFile file = firmwareFileService.getByIdThrow(dto.getFileId());
        InvUpgradeFileDTO upgrade = new InvUpgradeFileDTO(dto.getSn());
        upgrade.setType(type);
        upgrade.setUrl(file.getFilePath());
        upgrade.setMd5(file.getFileMd5());
        String message = JSON.toJSONString(upgrade);
        String topic = SendTopicEnum.INV_GRADE_FILE.getTopic(dto.getSn());
        return stationDeviceRemoteService.sendCommand(dto.getSn(), upgrade.getMid(), topic, message, command, DeviceTypeEnum.WN);
    }

    public Integer invOperate(InvOperateDTO dto) {
        //4 并网文件升级
        if (dto.getOperateType() == 4) {
            WriteInvGridParamDTO write = new WriteInvGridParamDTO(dto.getSn());
            List<AddrValueDTO> addrList = AddrValueDTO.to(gridFileDataMapper.getParam(dto.getFileId()));
            // 将addr 值为501的参数放到列表最后
            List<AddrValueDTO> preAddrList = new ArrayList<>(addrList.parallelStream().filter(addr -> addr.getAddr() != 501).toList());
            List<AddrValueDTO> lastArrdList = addrList.parallelStream().filter(addr -> addr.getAddr() == 501).toList();
            preAddrList.addAll(lastArrdList);
            write.setParams(preAddrList);
            String message = JSON.toJSONString(write);
            String topic = SendTopicEnum.INV_WRITE_GRID.getTopic(dto.getSn());
            Integer id = stationDeviceRemoteService.sendCommand(dto.getSn(), write.getMid(), topic, message, CommandEnum.INV_WRITE_GRID, DeviceTypeEnum.WN);
            stationDeviceService.lambdaUpdate().eq(StationDevice::getNumber, dto.getSn()).set(StationDevice::getGridFileId, dto.getFileId()).update();
            return id;
        }
        //5并网文件查看
        if (dto.getOperateType() == 5) {
            ReadInvGridParamDTO write = new ReadInvGridParamDTO(dto.getSn());
            StationDevice device = stationDeviceService.getBaseMapper().getBySn(dto.getSn());
            if (device == null || device.getGridFileId() == null) {
                write.setParams(new ArrayList<>());
            } else {
                write.setParams(gridFileDataMapper.getAddrParam(device.getGridFileId()));
            }
            String message = JSON.toJSONString(write);
            String topic = SendTopicEnum.INV_READ_GRID.getTopic(dto.getSn());
            return stationDeviceRemoteService.sendCommand(dto.getSn(), write.getMid(), topic, message, CommandEnum.INV_READ_GRID, DeviceTypeEnum.WN);
        }
        WriteInvParamDTO write = new WriteInvParamDTO(dto.getSn());
        //1 开机，2 关机，3 重启 开关机地址address：18，0：停止，1：启动
        CommandEnum command;
        switch (dto.getOperateType()) {
            case 1 -> {
                write.getParams().add(new AddrValueDTO(24, 1));
                command = CommandEnum.INV_ON;
            }
            case 2 -> {
                write.getParams().add(new AddrValueDTO(24, 0));
                command = CommandEnum.INV_OFF;
            }
            case 3 -> {
                write.getParams().add(new AddrValueDTO(28, 1));
                command = CommandEnum.INV_RESTART;
            }
            default -> throw new ServiceException("参数错误");
        }
        String message = JSON.toJSONString(write);
        String topic = SendTopicEnum.INV_WRITE_PARAM.getTopic(dto.getSn());
        return stationDeviceRemoteService.sendCommand(dto.getSn(), write.getMid(), topic, message, command, DeviceTypeEnum.WN);
    }

    /**
     * 批量操作
     */
    public List<Integer> batchOperate(InvBatchOperateDTO dto) {
        return dto.getSns().stream().map(sn -> switch (dto.getOperateType()) {
            case 5 -> invUpgradeFile(new UpgradeInvFileDTO(sn, dto.getFileId()), 0);
            case 6 -> invUpgradeFile(new UpgradeInvFileDTO(sn, dto.getFileId()), 1);
            default -> invOperate(new InvOperateDTO(sn, dto.getOperateType(), dto.getFileId()));
        }).toList();
    }

    /**
     * 收集微逆版本信息
     *
     * @param dto
     * @return {@link Integer }
     */
    public Integer readInvAllParam(EmuParamDTO dto) {
        String sn = dto.getSn();
        DeviceDataDTO deviceDataDTO = new DeviceDataDTO(sn);
        String message = JSON.toJSONString(deviceDataDTO);
        String topic = SendTopicEnum.INV_READ_PARAM.getTopic(sn);
        return stationDeviceRemoteService.sendCommand(sn, deviceDataDTO.getMid(), topic, message, CommandEnum.INV_READ_PARAM, DeviceTypeEnum.WN);
    }

    /**
     * 读取故障录播参数配置
     *
     * @param sn sn
     * @return {@link Integer }
     */
    public Integer readFaultWaveSetting(String sn) {
        DeviceDataDTO write = new DeviceDataDTO(sn);
        String message = JSON.toJSONString(write);
        String topic = SendTopicEnum.INV_READ_FAULT_WAVE_PARAM.getTopic(sn);
        return stationDeviceRemoteService.sendCommand(sn, write.getMid(), topic, message, CommandEnum.INV_READ_FAULT_WAVE_PARAM, DeviceTypeEnum.WN);
    }

    /**
     * 写故障录播参数配置
     *
     * @param dto    到
     * @return {@link Integer }
     */
    public Integer faultWaveSetting(FaultWaveSettingDTO dto) {
        InvFaultParamDTO inv = new InvFaultParamDTO(dto.getSn());
        for (GridParamDTO p : dto.getData()) {
            p.setType(FaultWaveEnum.TYPE_MAP.get(p.getAddr()));
        }
        inv.setData(AddrValueDTO.to(dto.getData()));
        String message = JSON.toJSONString(inv);
        String topic = SendTopicEnum.INV_WRITE_FAULT_WAVE_PARAM.getTopic(dto.getSn());
        return stationDeviceRemoteService.sendCommand(dto.getSn(), inv.getMid(), topic, message, CommandEnum.INV_WRITE_FAULT, DeviceTypeEnum.WN);
    }

    /**
     * 读取故障录播
     */
    public Integer readFaultWave(String sn) {
        CommandEnum.INV_READ_FAULT_WAVE.noRepeatRequest(sn);
        DeviceDataDTO dto = new DeviceDataDTO(sn);
        String message = JSON.toJSONString(dto);
        String topic = SendTopicEnum.INV_READ_FAULT_WAVE.getTopic(dto.getSn());
        return stationDeviceRemoteService.sendCommand(dto.getSn(), dto.getMid(), topic, message, CommandEnum.INV_READ_FAULT_WAVE, DeviceTypeEnum.WN);
    }

    /**
     * 读故障记录文件
     */
    public Integer readFaultLog(String sn, Integer pageNum) {
        CommandEnum.INV_READ_FAULT_LOG.noRepeatRequest(sn);
        InvFaultLogDTO dto = new InvFaultLogDTO(sn);
        dto.setPageNum(pageNum);
        String message = JSON.toJSONString(dto);
        String topic = SendTopicEnum.INV_READ_FAULT_LOG.getTopic(dto.getSn());
        return stationDeviceRemoteService.sendCommand(dto.getSn(), dto.getMid(), topic, message, CommandEnum.INV_READ_FAULT_LOG, DeviceTypeEnum.WN);
    }
}
