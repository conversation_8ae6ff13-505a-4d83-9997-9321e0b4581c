package com.wh.data.mapper;


import com.github.yulichang.base.MPJBaseMapper;
import com.wh.data.domain.StationDeviceRemote;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @date 2024-11-19
*/
public interface StationDeviceRemoteMapper extends MPJBaseMapper<StationDeviceRemote> {

    @Select("select issue_content, operate, response_result, response_time from wh_station_device_remote where id = #{id}")
    StationDeviceRemote getResponseResult(Integer id);

    @Select("select id, operate, response_result from wh_station_device_remote where id in(${id})")
    List<StationDeviceRemote> getResponseResults(String ids);

}
