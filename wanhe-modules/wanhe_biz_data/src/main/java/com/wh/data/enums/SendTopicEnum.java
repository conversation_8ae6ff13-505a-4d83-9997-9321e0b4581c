package com.wh.data.enums;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum SendTopicEnum {

    //写EMU参数
    EMU_WRITE_PARAM("WriteParam/Emu/{SN}/WriteEmuParam"),
    //EMU收集微逆版本信息
    EMU_READ_PARAM("ReadParam/Emu/{SN}/ReadInvOfEmuParam"),
    //EMU固件升级
    EMU_UPGRADE_FILE("Upgrade/Emu/{SN}/UpgradeEmuFile"),
    //EMU微逆固件升级
    EMU_INV_UPGRADE_FILE("Upgrade/Emu/{SN}/UpgradeInvOfEmuFile"),
    //EMU时区下发
    EMU_TIME_ZONE("SetTime/Emu/{SN}/EmuTimeZone"),
    //EMU微逆 开机，关机，重启，并网文件升级，写故障录播参数配置
    EMU_INV_WRITE_PARAM("WriteParam/Emu/{SN}/WriteInvOfEmuParam"),
    //读EMU组网信息
    EMU_READ_NETWORK("ReadParam/Emu/{SN}/ReadInvNetworking"),
    //给EMU下发组网信息
    EMU_WRITE_NETWORK("WriteParam/Emu/{SN}/WriteEmuNetworking"),
    //配置电表
    EMU_CONFIG_METER("Meter/Emu/{SN}/configMeter"),
    //下发并网文件
    EMU_WRITE_GRID("WriteParam/Emu/{SN}/WriteGridParam"),
    //读参数命令
    EMU_READ_GRID("WriteParam/Emu/{SN}/ReadGridParam"),
    //读取故障录播
    EMU_READ_FAULT_WAVE("ReadFile/Emu/{SN}/InvFaultWave"),
    //读故障记录文件
    EMU_READ_FAULT_LOG("ReadFile/Emu/{SN}/ReadInvFaultLog"),
    //读取故障录播参数配置
    EMU_READ_FAULT_WAVE_PARAM("ReadFile/Emu/{SN}/ReadFaultWaveConfig"),
    //获取电表数据
    EMU_READ_METER_DATA("Param/Emu/{SN}/GetMeterData"),


    //读取微逆参数
    INV_READ_PARAM("ReadParam/Inv/{SN}/ReadInvParam"),
    //wifi微逆 开机，关机，重启，并网文件升级
    INV_WRITE_PARAM("WriteParam/Inv/{SN}/WriteInvParam"),
    //wifi微逆版固件升级
    INV_GRADE_FILE("Upgrade/Inv/{SN}/UpgradeInvFile"),
    //wifi微逆设置时区
    INV_TIME_ZONE("SetTime/Inv/{SN}/InvTimeZone"),
    //给微逆下发组网信息
    INV_WRITE_NETWORK("WriteParam/Inv/{SN}/WriteInvNetworking"),
    //下发并网文件
    INV_WRITE_GRID("WriteParam/Inv/{SN}/WriteInvGridParam"),
    //查看并网文件
    INV_READ_GRID("WriteParam/Inv/{SN}/ReadInvGridParam"),

    //读取故障录播参数配置
    INV_READ_FAULT_WAVE_PARAM("ReadFile/Inv/{SN}/ReadFaultWaveConfig2"),
    //写故障录播参数配置
    INV_WRITE_FAULT_WAVE_PARAM("WriteParam/Inv/{SN}/WriteFaultWaveConfig2"),
    //读取故障录播
    INV_READ_FAULT_WAVE("ReadFile/Inv/{SN}/InvFaultWave2"),
    //读故障记录文件
    INV_READ_FAULT_LOG("ReadFile/Inv/{SN}/ReadInvFaultLog"),
    //读参数命令
    INV_READ_GRID_PARAM("WriteParam/Inv/{SN}/ReadInvGridParam"),

    ;
    private final String topic;

    public String getTopic(String number) {
        return topic.replace("{SN}", number);
    }

}
