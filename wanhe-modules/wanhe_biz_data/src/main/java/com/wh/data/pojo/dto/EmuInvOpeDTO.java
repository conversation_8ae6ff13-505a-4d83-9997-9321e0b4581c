package com.wh.data.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

@Data
public class EmuInvOpeDTO {

    @Schema(description = "微逆sn")
    @NotBlank
    private String sn;

    @Schema(description = "操作类型 1 开机，2 关机，3 重启，4 并网文件升级，5并网文件查看")
    @NotNull
    @Range(min = 1, max = 5)
    private Integer operateType;

    @Schema(description = "文件id  operateType是4 时必填")
    private Integer fileId;

}
