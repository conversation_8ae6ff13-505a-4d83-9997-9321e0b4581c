package com.wh.data.pojo.vo;

import com.ruoyi.common.core.domain.DataUnit;
import com.ruoyi.common.core.utils.NumUtil;
import com.ruoyi.common.core.utils.Tools;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

@Setter
@Getter
@NoArgsConstructor
public class HomeEnvironmentalBenefitsVO {

    //月发电量数据
    private BigDecimal monthData;
    private String monthDataUnit;
    //总电量数据
    private BigDecimal allData;
    private String allDataUnit;
    // 树
    private BigDecimal treeNum = BigDecimal.ZERO;
    //co2
    private BigDecimal co2 = BigDecimal.ZERO;
    //同步月份发电量占比
    private BigDecimal rate;

    public HomeEnvironmentalBenefitsVO(List<BigDecimal> list) {
        DataUnit monthData = Tools.covertKwhUnit(list.get(0));
        this.monthData = monthData.getData();
        this.monthDataUnit = monthData.getUnit();
        DataUnit allData = Tools.covertKwhUnit(list.get(1));
        this.allData = allData.getData();
        this.allDataUnit = allData.getUnit();
        if (list.get(0).compareTo(BigDecimal.ZERO) > 0) {
            treeNum = this.allData.multiply(NumUtil.TREE).divide(NumUtil.THOUSAND, 2, RoundingMode.HALF_UP);
            co2 = this.allData.multiply(NumUtil.COZ).divide(NumUtil.THOUSAND, 2, RoundingMode.HALF_UP);
        }
        if (list.size() == 3) {
            //0是本月 2是上月
            if (list.get(0).compareTo(list.get(2)) == 0) {
                rate = BigDecimal.ZERO;
            } else if (list.get(2).compareTo(BigDecimal.ZERO) == 0) {
                rate = NumUtil.HUNDRED;
            } else {
                rate = list.get(0).subtract(list.get(2)).multiply(NumUtil.HUNDRED).divide(list.get(2), 2, RoundingMode.HALF_UP);
            }
        }
    }

}
