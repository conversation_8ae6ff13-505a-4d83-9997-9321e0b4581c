package com.wh.data.pojo.vo;

import com.ruoyi.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryEnergyDataExportVO {

    //1pv电压 2pv电流 3ac电压 4ac电流 5ac mos温度 6pv mos温度 7发电功率 8电网频率
    @Excel(name = "时间")
    private String time;

    @Excel(name = "发电功率")
    private Object power;

    @Excel(name = "电网电压")
    private Object volReal;

    @Excel(name = "电网频率")
    private Object gridFreq;

    @Excel(name = "温度")
    private Object mosTempReal;

}
