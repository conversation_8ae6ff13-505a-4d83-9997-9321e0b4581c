package com.wh.data.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 电表 vo
 *
 * <AUTHOR>
 * @date 2025/05/13
 */
@Data
public class MeterVo {

    @Schema(description = "电站id")
    private Integer powerStationId;

    @Schema(description = "序列号")
    private String number;

    @Schema(description = "设备状态 0离线 1在线 2告警 3故障 4停机 5自检 6预启动")
    private Integer status;

    @Schema(description = "设备型号")
    private String deviceModel;

    @Schema(description = "电表位置 0负载侧电表 1光伏测电表 2并网点电表")
    private Integer powerGridSeat;

    @Schema(description = "设备替换次数")
    private Integer replaceNum;

    @Schema(description = "质保期（月）")
    private Integer guaranteePeriod;

    @Schema(description = "激活时间")
    private LocalDateTime activateTime;

    @Schema(description = "质保截止期限")
    private String finishPeriod;

    @Schema(description = "电网-实时功率")
    private String power;

    @Schema(description = "输入电量")
    private String forwardEnergy;

    @Schema(description = "输出电量")
    private String reverseEnergy;

    @Schema(description = "最后报告时间")
    private LocalDateTime lastReportTime;


}
