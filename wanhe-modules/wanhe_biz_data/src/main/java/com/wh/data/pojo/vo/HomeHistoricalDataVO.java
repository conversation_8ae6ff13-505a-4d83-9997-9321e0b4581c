package com.wh.data.pojo.vo;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.common.core.utils.TimeZoneUtil;
import com.ruoyi.common.core.utils.Tools;
import com.wh.data.protocol.TopicService;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/11
 */
@Data
@NoArgsConstructor
public class HomeHistoricalDataVO {

    //时间
    @JsonProperty("xAxisData")
    private List<String> xAxisData = new ArrayList<>();
    //数据
    private List<BigDecimal> dailyEnergy = new ArrayList<>();
    //单位
    private String unit;

    public HomeHistoricalDataVO(List<JSONObject> data, ZonedDateTime beginTime, ZonedDateTime endTime) {
        ZonedDateTime now = ZonedDateTime.now(ZoneId.systemDefault());
        Map<String, BigDecimal> map = data.stream().collect(Collectors.toMap(json -> json.getString(TopicService.TIME), json -> json.getBigDecimal(TopicService.DATA)));
        while (beginTime.isBefore(endTime) && beginTime.isBefore(now)) {
            String d = TimeZoneUtil.d.format(beginTime);
            xAxisData.add(d);
            dailyEnergy.add(map.getOrDefault(d, BigDecimal.ZERO));
            beginTime = beginTime.plusDays(1);
        }
        unit = Tools.covertKwhUnit(dailyEnergy);
    }

    public HomeHistoricalDataVO(List<String> months, List<JSONObject> data) {
        for (int i = 0; i < months.size(); i++) {
            xAxisData.add(months.get(i));
            if (data.size() - 1 < i) {
                dailyEnergy.add(BigDecimal.ZERO);
            } else {
                dailyEnergy.add(Optional.ofNullable(data.get(i).getBigDecimal(TopicService.DATA)).orElse(BigDecimal.ZERO));
            }
        }
        unit = Tools.covertKwhUnit(dailyEnergy);
    }

}
