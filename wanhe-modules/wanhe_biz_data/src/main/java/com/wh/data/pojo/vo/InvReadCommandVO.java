package com.wh.data.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/13 17:16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InvReadCommandVO {

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "请求时间")
    private LocalDateTime requestTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "响应时间")
    private LocalDateTime responseTime;

    @Schema(description = "参数列表")
    private List<CommandFaultParamVO> paramList;

}
