package com.wh.data.biz;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.enums.DeviceTypeEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DataHandleUtil;
import com.ruoyi.common.core.utils.Tools;
import com.ruoyi.common.redis.RedisUtil;
import com.wh.data.domain.*;
import com.wh.data.enums.CommandEnum;
import com.wh.data.enums.FaultWaveEnum;
import com.wh.data.enums.SendTopicEnum;
import com.wh.data.mapper.GridFileDataMapper;
import com.wh.data.pojo.dto.*;
import com.wh.data.pojo.vo.CommandFaultParamVO;
import com.wh.data.pojo.vo.CommandVO;
import com.wh.data.pojo.vo.GridParamVO;
import com.wh.data.pojo.vo.InvReadCommandVO;
import com.wh.data.service.*;
import jakarta.annotation.Resource;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.wh.data.protocol.TopicService.SN;
import static com.wh.data.protocol.TopicService.STATUS;

/**
 * <AUTHOR>
 * @date 2024/11/8 0008
 */
@Slf4j
@Getter
@Component
public class EmuInvPublishBiz {

    @Resource
    private StationDeviceService stationDeviceService;
    @Resource
    private FirmwareFileService firmwareFileService;
    @Resource
    private GridFileDataMapper gridFileDataMapper;
    @Resource
    private StationDeviceRemoteService stationDeviceRemoteService;
    @Resource
    private PowerStationService powerStationService;

    @Resource
    private FaultParamService faultParamService;

    /**
     * 配置电表
     */
    public void configMeter(Integer powerStationId) {
        Map<Integer, List<StationDevice>> deviceTypeMap = stationDeviceService.lambdaQuery().eq(StationDevice::getPowerStationId, powerStationId)
                .in(StationDevice::getDeviceType, List.of(DeviceTypeEnum.EMU.getVal(), DeviceTypeEnum.DB.getVal()))
                .list().stream().collect(Collectors.groupingBy(StationDevice::getDeviceType));
        List<StationDevice> emus = deviceTypeMap.get(DeviceTypeEnum.EMU.getVal());
        if (CollUtil.isEmpty(emus) || emus.stream().noneMatch(emu -> emu.getStatus() > 0)) {
            throw new ServiceException("没有在线Gateway设备");
        }
        //电表位置 0负载侧电表 1光伏测电表 2并网点电表
        List<StationDevice> meterList = deviceTypeMap.get(DeviceTypeEnum.DB.getVal());
        Map<Integer, StationDevice> meterMap;
        if (CollUtil.isEmpty(meterList)) {
            meterMap = Collections.emptyMap();
        } else {
            meterMap = meterList.stream().collect(Collectors.toMap(StationDevice::getPowerGridSeat, Function.identity()));
        }
        emus.forEach(emu -> {
            if (emu.getStatus() == 0) {
                return;
            }
            ConfigMeterDTO dto = new ConfigMeterDTO(emu.getNumber());
            StationDevice load = meterMap.get(0);
            StationDevice photovoltaic = meterMap.get(1);
            StationDevice grid = meterMap.get(2);
            if (grid != null) {
                PowerStation station = powerStationService.getByIdThrow(powerStationId);
                dto.getGridMeter().setSn(grid.getNumber());
                dto.getGridMeter().setEnableReflux(station.getNetwork() ? 1 : 0);
                if (station.getNetworkKw() != null) {
                    dto.getGridMeter().setPower(station.getNetworkKw().stripTrailingZeros());
                }
            }
            if (photovoltaic != null) {
                dto.getPhotovoltaicMeter().setSn(photovoltaic.getNumber());
            }
            if (load != null) {
                dto.getLoadMeter().setSn(load.getNumber());
            }
            String message = JSON.toJSONString(dto);
            String topic = SendTopicEnum.EMU_CONFIG_METER.getTopic(emu.getNumber());
            stationDeviceRemoteService.sendCommand(emu.getNumber(), dto.getMid(), topic, message, CommandEnum.EMU_CONFIG_METER, DeviceTypeEnum.EMU);
        });
    }

    /**
     * emu 重启
     */
    public Integer emuOperate(EmuParamDTO dto) {
        WriteEmuParamDTO write = new WriteEmuParamDTO(dto.getSn());
        write.setGateWayData(List.of(new AddrValueDTO(4000, 1)));
        String message = JSON.toJSONString(write);
        String topic = SendTopicEnum.EMU_WRITE_PARAM.getTopic(dto.getSn());
        return stationDeviceRemoteService.sendCommand(dto.getSn(), write.getMid(), topic, message, CommandEnum.EMU_RESTART, DeviceTypeEnum.EMU);
    }

    /**
     * emu 收集微逆版本信息
     */
    public Integer emuInfoCollect(EmuParamDTO dto) {
        List<String> invSnList = stationDeviceService.getWnByEmu(dto.getSn());
        InvInfoCollectDTO collect = new InvInfoCollectDTO(dto.getSn());
        collect.setInvData(new InvInfoCollectDTO.InvData(invSnList));
        String message = JSON.toJSONString(collect);
        String topic = SendTopicEnum.EMU_READ_PARAM.getTopic(dto.getSn());
        return stationDeviceRemoteService.sendCommand(dto.getSn(), collect.getMid(), topic, message, CommandEnum.EMU_COLLECT_WN_VERSION, DeviceTypeEnum.EMU);
    }


    public Integer sendSelfCheckCommand(Integer stationId) {
        PowerStation station = powerStationService.getByIdThrow(stationId);
        // 屋顶光伏
        if (station.getStationType().equals("0") || station.getStationType().equals("2")) {
            // 根据电站ID获取所有EMU设备
            List<String> emuList = stationDeviceService.getEmuByStationId(stationId, DeviceTypeEnum.EMU);
            if (CollUtil.isEmpty(emuList)) {
                return 0;
            }
            for (String emu : emuList) {
                List<String> invSnList = stationDeviceService.getWnByEmu(emu);
                InvInfoCollectDTO collect = new InvInfoCollectDTO(emu);
                collect.setInvData(new InvInfoCollectDTO.InvData(invSnList));
                String message = JSON.toJSONString(collect);
                String topic = SendTopicEnum.EMU_READ_PARAM.getTopic(emu);
                stationDeviceRemoteService.sendCommand(emu, collect.getMid(), topic, message, CommandEnum.EMU_COLLECT_WN_VERSION, DeviceTypeEnum.EMU);
            }
            // todo 发送收集电表信息命令

            return emuList.size();
        } else {
            List<String> invList = stationDeviceService.getEmuByStationId(stationId, DeviceTypeEnum.WN);
            if (CollUtil.isEmpty(invList)) {
                return 0;
            }
            for (String inv : invList) {
                DeviceDataDTO deviceDataDTO = new DeviceDataDTO(inv);
                String message = JSON.toJSONString(deviceDataDTO);
                String topic = SendTopicEnum.INV_READ_PARAM.getTopic(inv);
                stationDeviceRemoteService.sendCommand(inv, deviceDataDTO.getMid(), topic, message, CommandEnum.INV_READ_PARAM, DeviceTypeEnum.WN);
            }
            return invList.size();
        }
    }

    /**
     * emu /emu微逆 固件升级
     */
    public Integer emuUpgradeFile(EmuUpgradeDTO dto, StationDevice device) {
        FirmwareFile file = firmwareFileService.getByIdThrow(dto.getFileId());
        if (device.getDeviceType() == 0) {
            CommandEnum.EMU_UPGRADE_FILE.noRepeatRequest(dto.getSn());
            EmuUpgradeFileDTO emu = new EmuUpgradeFileDTO(dto.getSn());
            emu.getEmu().setUrl(file.getFilePath());
            emu.getEmu().setMd5(file.getFileMd5());
            String message = JSON.toJSONString(emu);
            String topic = SendTopicEnum.EMU_UPGRADE_FILE.getTopic(dto.getSn());
            return stationDeviceRemoteService.sendCommand(dto.getSn(), emu.getMid(), topic, message, CommandEnum.EMU_UPGRADE_FILE, DeviceTypeEnum.EMU);
        } else {
            CommandEnum.EMU_INV_UPGRADE_FILE.noRepeatRequest(dto.getSn());
            EmuInvUpgradeFileDTO emu = new EmuInvUpgradeFileDTO(device.getEmuNumber());
            emu.getInv().setUrl(file.getFilePath());
            emu.getInv().setMd5(file.getFileMd5());
            emu.getInv().setSn(List.of(dto.getSn()));
            String message = JSON.toJSONString(emu);
            String topic = SendTopicEnum.EMU_INV_UPGRADE_FILE.getTopic(device.getEmuNumber());
            return stationDeviceRemoteService.sendCommand(device.getEmuNumber(), emu.getInv().getSn(), emu.getMid(), topic, message, CommandEnum.EMU_INV_UPGRADE_FILE, DeviceTypeEnum.EMU);
        }
    }

    /**
     * emu设备时区下发
     */
    public Integer setEmuTimeZone(SetTimeZoneDTO dto) {
        SetInvTimeZoneDTO time = new SetInvTimeZoneDTO(dto.getSn());
        time.setTime(time.getTimeStamp());
        time.setTimeCode(dto.getTimeZone().getTimeCode());
        String message = JSON.toJSONString(time);
        String topic = SendTopicEnum.EMU_TIME_ZONE.getTopic(dto.getSn());
        return stationDeviceRemoteService.sendCommand(dto.getSn(), time.getMid(), topic, message, CommandEnum.EMU_TIME_ZONE, DeviceTypeEnum.EMU);
    }

    /**
     * Emu微逆 操作类型 1 开机，2 关机，3 重启，4 并网文件升级, 5并网文件查看
     */
    public Integer emuInvOperate(EmuInvOpeDTO dto, StationDevice device) {
        if (dto.getOperateType() == 4) {
            WriteEmuGridParamDTO write = new WriteEmuGridParamDTO(device.getEmuNumber());
            List<AddrValueDTO> addrList = AddrValueDTO.to(gridFileDataMapper.getParam(dto.getFileId()));
            // 将addr 值为501的参数放到列表最后
            List<AddrValueDTO> preAddrList = new ArrayList<>(addrList.parallelStream().filter(addr -> addr.getAddr() != 501).toList());
            List<AddrValueDTO> lastArrdList = addrList.parallelStream().filter(addr -> addr.getAddr() == 501).toList();
            preAddrList.addAll(lastArrdList);
            write.setParams(preAddrList);
            write.setInv(List.of(dto.getSn()));
            String message = JSON.toJSONString(write);
            String topic = SendTopicEnum.EMU_WRITE_GRID.getTopic(device.getEmuNumber());
            Integer id = stationDeviceRemoteService.sendCommand(device.getEmuNumber(), write.getInv(), write.getMid(), topic, message, CommandEnum.EMU_WRITE_GRID, DeviceTypeEnum.EMU);
            stationDeviceService.lambdaUpdate().eq(StationDevice::getId, device.getId()).set(StationDevice::getGridFileId, dto.getFileId()).update();
            return id;
        }
        if (dto.getOperateType() == 5) {
            ReadEmuGridParamDTO write = new ReadEmuGridParamDTO(device.getEmuNumber());
            write.setInv(dto.getSn());
            if (device.getGridFileId() == null) {
                write.setParams(new ArrayList<>());
            } else {
                write.setParams(gridFileDataMapper.getAddrParam(device.getGridFileId()));
            }
            String message = JSON.toJSONString(write);
            String topic = SendTopicEnum.EMU_READ_GRID.getTopic(device.getEmuNumber());
            return stationDeviceRemoteService.sendCommand(device.getEmuNumber(), List.of(dto.getSn()), write.getMid(), topic, message, CommandEnum.EMU_READ_GRID, DeviceTypeEnum.EMU);
        }
        EmuInvOperateDTO emu = new EmuInvOperateDTO(device.getEmuNumber());
        //开关机地址address：18，0：停止，1：启动
        EmuInvOperateDTO.InvData invData = new EmuInvOperateDTO.InvData();
        emu.getInvData().add(invData);
        invData.setSn(dto.getSn());
        CommandEnum command;
        switch (dto.getOperateType()) {
            case 1 -> {
                invData.setData(List.of(new AddrValueDTO(24, 1)));
                command = CommandEnum.EMU_INV_ON;
            }
            case 2 -> {
                invData.setData(List.of(new AddrValueDTO(24, 0)));
                command = CommandEnum.EMU_INV_OFF;
            }
            case 3 -> {
                invData.setData(List.of(new AddrValueDTO(28, 1)));
                command = CommandEnum.EMU_INV_RESTART;
            }
            default -> throw new ServiceException("参数错误");
        }
        String message = JSON.toJSONString(emu);
        String topic = SendTopicEnum.EMU_INV_WRITE_PARAM.getTopic(device.getEmuNumber());
        return stationDeviceRemoteService.sendCommand(device.getEmuNumber(), List.of(dto.getSn()), emu.getMid(), topic, message, command, DeviceTypeEnum.EMU);
    }

    /**
     * emu组网
     *
     * @param type 0:手动组网，1：自动组网
     */
    public Integer getEmuInvByEmu(String sn, Integer type, Integer powerStationId) {
        ReadInvNetworkDTO emu = new ReadInvNetworkDTO(sn);
        emu.setType(type);
        String message = JSON.toJSONString(emu);
        String topic = SendTopicEnum.EMU_READ_NETWORK.getTopic(sn);
        Integer id = stationDeviceRemoteService.sendCommand(sn, null, emu.getMid(), topic, message, CommandEnum.EMU_READ_NETWORK, DeviceTypeEnum.EMU, powerStationId, 0);
        stationDeviceService.lambdaUpdate().eq(StationDevice::getNumber, sn).isNull(StationDevice::getNetworkType)
                .set(StationDevice::getNetworkType, type).update();
        return id;
    }

    /**
     * 批量操作 1开机 2关机 3重启 4并网文件升级 5微逆固件升级
     */
    public List<Integer> batchOperate(EmuBatchOperateDTO dto) {
        return dto.getDevices().stream().map(device -> switch (dto.getOperateType()) {
            case 1, 2, 3 -> {
                EmuInvOperateDTO emu = new EmuInvOperateDTO(device.getEmuNumber());
                //开关机地址address：18，0：停止，1：启动
                CommandEnum command = null;
                for (String number : device.getNumbers()) {
                    EmuInvOperateDTO.InvData invData = new EmuInvOperateDTO.InvData();
                    emu.getInvData().add(invData);
                    invData.setSn(number);
                    switch (dto.getOperateType()) {
                        case 1 -> {
                            invData.setData(List.of(new AddrValueDTO(24, 1)));
                            command = CommandEnum.EMU_INV_ON;
                        }
                        case 2 -> {
                            invData.setData(List.of(new AddrValueDTO(24, 0)));
                            command = CommandEnum.EMU_INV_OFF;
                        }
                        case 3 -> {
                            invData.setData(List.of(new AddrValueDTO(28, 1)));
                            command = CommandEnum.EMU_INV_RESTART;
                        }
                        default -> throw new ServiceException("参数错误");
                    }
                }
                String message = JSON.toJSONString(emu);
                String topic = SendTopicEnum.EMU_INV_WRITE_PARAM.getTopic(device.getEmuNumber());
                yield stationDeviceRemoteService.sendCommand(device.getEmuNumber(), device.getNumbers(), emu.getMid(), topic, message, command, DeviceTypeEnum.EMU);
            }
            case 4 -> {
                WriteEmuGridParamDTO write = new WriteEmuGridParamDTO(device.getEmuNumber());
                List<AddrValueDTO> addrList = AddrValueDTO.to(gridFileDataMapper.getParam(dto.getFileId()));
                // 将addr 值为501的参数放到列表最后
                List<AddrValueDTO> preAddrList = new ArrayList<>(addrList.parallelStream().filter(addr -> addr.getAddr() != 501).toList());
                List<AddrValueDTO> lastArrdList = addrList.parallelStream().filter(addr -> addr.getAddr() == 501).toList();
                preAddrList.addAll(lastArrdList);
                write.setParams(preAddrList);
                write.setInv(device.getNumbers());
                String message = JSON.toJSONString(write);
                String topic = SendTopicEnum.EMU_WRITE_GRID.getTopic(device.getEmuNumber());
                Integer id = stationDeviceRemoteService.sendCommand(device.getEmuNumber(), write.getInv(), write.getMid(), topic, message, CommandEnum.EMU_WRITE_GRID, DeviceTypeEnum.EMU);
                stationDeviceService.lambdaUpdate().in(StationDevice::getNumber, write.getInv()).set(StationDevice::getGridFileId, dto.getFileId()).update();
                yield id;
            }
            default -> {
                FirmwareFile file = firmwareFileService.getByIdThrow(dto.getFileId());
                EmuInvUpgradeFileDTO emu = new EmuInvUpgradeFileDTO(device.getEmuNumber());
                emu.getInv().setUrl(file.getFilePath());
                emu.getInv().setMd5(file.getFileMd5());
                emu.getInv().setSn(device.getNumbers());
                String message = JSON.toJSONString(emu);
                String topic = SendTopicEnum.EMU_INV_UPGRADE_FILE.getTopic(device.getEmuNumber());
                JSONArray result = new JSONArray(device.getNumbers().size());
                for (String sn : device.getNumbers()) {
                    CommandEnum.EMU_INV_UPGRADE_FILE.noRepeatRequest(sn, device.getNumbers().size());
                    JSONObject json = new JSONObject(4);
                    json.put(SN, sn);
                    json.put(STATUS, -1);
                    json.put("progress", 0);
                    json.put("errorCode", 0);
                    result.add(json);
                }
                RedisUtil.set(CacheConstants.TEMP_KEY + emu.getMid(), result.toString(), Duration.ofHours(1));
                yield stationDeviceRemoteService.sendCommand(device.getEmuNumber(), emu.getInv().getSn(), emu.getMid(), topic, message, CommandEnum.EMU_INV_UPGRADE_FILE, DeviceTypeEnum.EMU);
            }
        }).toList();
    }

    /**
     * 读取微逆参数命令
     *
     * @param dto 到
     * @return {@link Integer }
     */
    public Integer readInvCommand(InvReadCommandDTO dto) {
        StationDevice device = stationDeviceService.getBaseMapper().getBySn(dto.getSn());
        ReadEmuGridParamDTO write = new ReadEmuGridParamDTO(device.getEmuNumber());
        write.setInv(dto.getSn());

        List<Integer> arr = new ArrayList<>();
        Integer startAddr = dto.getStartAddr();
        for (int i = 0; i < dto.getParamNum(); i++) {
            arr.add(startAddr);
            startAddr += 1;
        }
        write.setParams(arr);
        String topic;
        if (device.getEmuNumber() != null) {
            topic = SendTopicEnum.EMU_READ_GRID.getTopic(device.getEmuNumber());
        } else {
            write.setSn(dto.getSn());
            topic = SendTopicEnum.INV_READ_GRID_PARAM.getTopic(dto.getSn());
        }

        String message = JSON.toJSONString(write);
        return stationDeviceRemoteService.sendCommand(dto.getSn(), write.getMid(), topic, message, CommandEnum.INV_READ_GRID, DeviceTypeEnum.WN);
    }

    public InvReadCommandVO commandInvResult(Integer id, String timezone) {
        StationDeviceRemote result = stationDeviceRemoteService.getBaseMapper().getResponseResult(id);
        if (result == null || result.getOperate() == null || result.getResponseResult() == null) {
            return null;
        }

        JSONObject request = JSON.parseObject(result.getIssueContent());
        List<Integer> paramIdList = request.getList("params", Integer.class);
        if (CollUtil.isEmpty(paramIdList)) {
            return null;
        }

        JSONObject resultJson = JSON.parseObject(result.getResponseResult());
        String invSn = resultJson.getString("inv");
        if (StrUtil.isBlank(invSn)) {
            invSn = resultJson.getString("sn");
        }
        StationDevice device = stationDeviceService.getBySn(invSn);
        if (StrUtil.isEmpty(device.getSoftwareVersion())) {
            throw new RuntimeException("设备软件版本为空");
        }
        List<FaultParam> faultParams = faultParamService.listByParamIds(paramIdList, Tools.getVersion(device.getSoftwareVersion()));

        JSONObject response = JSON.parseObject(result.getResponseResult());
        List<GridParamVO> gridParamList = response.getList("params", GridParamVO.class);

        Map<Integer, List<GridParamVO>> gridParamMap = gridParamList.parallelStream().collect(Collectors.groupingBy(GridParamVO::getAddr));

        List<CommandFaultParamVO> commandFaultParamList = BeanUtil.copyToList(faultParams, CommandFaultParamVO.class);
        for (CommandFaultParamVO commandFaultParam : commandFaultParamList) {
            List<GridParamVO> gridParamVOS = gridParamMap.get(commandFaultParam.getParamId());
            if (CollUtil.isNotEmpty(gridParamVOS) && gridParamVOS.get(0).getValue() != null) {
                commandFaultParam.setReportValue(gridParamVOS.get(0).getValue());
                commandFaultParam.setRealValue(DataHandleUtil.getNumber(commandFaultParam.getParamType(), commandFaultParam.getReportValue()));
            }
        }
        InvReadCommandVO vo = new InvReadCommandVO();
        vo.setRequestTime(LocalDateTime.ofInstant(Instant.ofEpochSecond(request.getLongValue("timeStamp")), ZoneId.of(timezone)));
        vo.setResponseTime(LocalDateTime.ofInstant(Instant.ofEpochSecond(response.getLongValue("timeStamp")), ZoneId.of(timezone)));
        vo.setParamList(commandFaultParamList);

        return vo;
    }


    public void networking(Integer powerStationId) {
        PowerStation powerStation = powerStationService.getByIdThrow(powerStationId);
        JSONObject layout = JSON.parseObject(powerStation.getLayout());
        //前端存的0 垂直  1水平    甲方需要的0：水平，1：垂直
        int direction = layout.getIntValue("layoutType") == 0 ? 1 : 0;
        BigDecimal angle = layout.getBigDecimal("angle");
        BigDecimal elevation = layout.getBigDecimal("elevation");
        JSONArray array = layout.getJSONArray("row_col_arr");
        List<StationDevice> devices = stationDeviceService.lambdaQuery().eq(StationDevice::getPowerStationId, powerStationId).list();
        if (!devices.isEmpty()) {
            //阳台光伏 wifi微逆
            if (powerStation.getStationType().equals("1")) {
                devices.stream().filter(d -> d.getDeviceType() == 1 && d.getPid() == null).forEach(wn -> {
                    WriteInvNetworkDTO dto = new WriteInvNetworkDTO(wn.getNumber());
                    dto.setPv(array.stream().filter(o -> wn.getNumber().equals(((JSONObject) o).getString("number"))).map(o -> {
                        JSONObject obj = (JSONObject) o;
                        String num = obj.getString("name");
                        WriteInvNetworkDTO.Pv pv = new WriteInvNetworkDTO.Pv(direction, angle, elevation);
                        pv.setNum(Integer.parseInt(num.substring(num.lastIndexOf("-") + 1)));
                        pv.setLocation(obj.getString("row_col").replace(" - ", ","));
                        return pv;
                    }).toList());
                    dto.setPcCount(dto.getPv().size());
                    String message = JSON.toJSONString(dto);
                    String topic = SendTopicEnum.INV_WRITE_NETWORK.getTopic(wn.getNumber());
                    stationDeviceRemoteService.sendCommand(wn.getNumber(), dto.getMid(), topic, message, CommandEnum.INV_WRITE_NETWORK, DeviceTypeEnum.WN);
                });
            }
            //emu微逆
            else {
                Map<Integer, List<StationDevice>> emuGroup = devices.stream().filter(d -> d.getPid() != null).collect(Collectors.groupingBy(StationDevice::getPid));
                devices.stream().filter(d -> d.getDeviceType() == 0)
                        //没有组网的emu需要先调用一次组网，默认手动
                        .peek(d -> {
                            if (d.getNetworkType() == null) {
                                getEmuInvByEmu(d.getNumber(), 0, powerStationId);
                                d.setNetworkType(0);
                            }
                        })
                        .forEach(emu -> {
                            WriteEmuNetworkDTO dto = new WriteEmuNetworkDTO(emu.getNumber());
                            dto.setType(emu.getNetworkType());
                            dto.setInv(emuGroup.getOrDefault(emu.getId(), Collections.emptyList()).stream().map(wn -> {
                                WriteEmuNetworkDTO.Inv inv = new WriteEmuNetworkDTO.Inv();
                                inv.setSn(wn.getNumber());
                                inv.setPv(array.stream().filter(o -> wn.getNumber().equals(((JSONObject) o).getString("number"))).map(o -> {
                                    JSONObject obj = (JSONObject) o;
                                    String num = obj.getString("name");
                                    WriteEmuNetworkDTO.Pv pv = new WriteEmuNetworkDTO.Pv(direction, angle, elevation);
                                    pv.setNum(Integer.parseInt(num.substring(num.lastIndexOf("-") + 1)));
                                    pv.setLocation(obj.getString("row_col").replace(" - ", ","));
                                    return pv;
                                }).toList());
                                inv.setPcCount(inv.getPv().size());
                                return inv;
                            }).toList());
                            String message = JSON.toJSONString(dto);
                            String topic = SendTopicEnum.EMU_WRITE_NETWORK.getTopic(emu.getNumber());
                            stationDeviceRemoteService.sendCommand(emu.getNumber(), dto.getMid(), topic, message, CommandEnum.EMU_WRITE_NETWORK, DeviceTypeEnum.EMU);
                        });
            }
        }
    }

    /**
     * 读故障记录文件
     */
    public Integer readFaultLog(String emuSn, String sn) {
        CommandEnum.EMU_READ_FAULT_LOG.noRepeatRequest(sn);
        EmuFaultLogDTO dto = new EmuFaultLogDTO(emuSn);
        dto.setInv(sn);
        String message = JSON.toJSONString(dto);
        String topic = SendTopicEnum.EMU_READ_FAULT_LOG.getTopic(dto.getSn());
        return stationDeviceRemoteService.sendCommand(dto.getSn(), dto.getMid(), topic, message, CommandEnum.EMU_READ_FAULT_LOG, DeviceTypeEnum.EMU);
    }

    /**
     * 读取故障录播
     */
    public Integer readFaultWave(String emuSn, String sn) {
        CommandEnum.EMU_READ_FAULT_WAVE.noRepeatRequest(sn);
        EmuFaultLogDTO dto = new EmuFaultLogDTO(emuSn);
        dto.setInv(sn);
        String message = JSON.toJSONString(dto);
        String topic = SendTopicEnum.EMU_READ_FAULT_WAVE.getTopic(dto.getSn());
        return stationDeviceRemoteService.sendCommand(dto.getSn(), dto.getMid(), topic, message, CommandEnum.EMU_READ_FAULT_WAVE, DeviceTypeEnum.EMU);
    }

    public CommandVO command(CommandDTO dto, StationDevice device) {
        //EMU设备
        if (device.getDeviceType() == 0) {
            WriteEmuParamDTO write = new WriteEmuParamDTO(dto.getSn());
            write.setGateWayData(AddrValueDTO.to(dto.getData()));
            String message = JSON.toJSONString(write);
            String topic = SendTopicEnum.EMU_WRITE_PARAM.getTopic(dto.getSn());
            Integer id = stationDeviceRemoteService.sendCommand(dto.getSn(), null, write.getMid(), topic, message, CommandEnum.EMU_WRITE, DeviceTypeEnum.EMU, null, dto.getQos());
            return new CommandVO(id, topic, dto.getQos(), message);
        }
        //wifi微逆设备
        if (StrUtil.isBlank(device.getEmuNumber())) {
            WriteInvParamDTO write = new WriteInvParamDTO(dto.getSn());
            write.setParams(AddrValueDTO.to(dto.getData()));
            String message = JSON.toJSONString(write);
            String topic = SendTopicEnum.INV_WRITE_PARAM.getTopic(dto.getSn());
            Integer id = stationDeviceRemoteService.sendCommand(dto.getSn(), null, write.getMid(), topic, message, CommandEnum.INV_WRITE, DeviceTypeEnum.WN, null, dto.getQos());
            return new CommandVO(id, topic, dto.getQos(), message);
        }
        //emu微逆设备
        EmuInvOperateDTO write = new EmuInvOperateDTO(device.getEmuNumber());
        EmuInvOperateDTO.InvData invData = new EmuInvOperateDTO.InvData();
        write.getInvData().add(invData);
        invData.setSn(dto.getSn());
        invData.setData(AddrValueDTO.to(dto.getData()));
        String message = JSON.toJSONString(write);
        String topic = SendTopicEnum.EMU_INV_WRITE_PARAM.getTopic(device.getEmuNumber());
        Integer id = stationDeviceRemoteService.sendCommand(device.getEmuNumber(), List.of(dto.getSn()), write.getMid(), topic, message, CommandEnum.EMU_INV_WRITE, DeviceTypeEnum.EMU, null, dto.getQos());
        return new CommandVO(id, topic, dto.getQos(), message);
    }

    /**
     * 读取故障录播默认配置
     */
    public Integer readFaultWaveSetting(String emuSn, String sn) {
        EmuFaultLogDTO write = new EmuFaultLogDTO(emuSn);
        write.setInv(sn);
        String message = JSON.toJSONString(write);
        String topic = SendTopicEnum.EMU_READ_FAULT_WAVE_PARAM.getTopic(emuSn);
        return stationDeviceRemoteService.sendCommand(emuSn, List.of(sn), write.getMid(), topic, message, CommandEnum.EMU_READ_FAULT_WAVE_PARAM, DeviceTypeEnum.EMU);
    }

    /**
     * 设置故障录播参数
     *
     * @param dto    到
     * @param device 装置
     * @return {@link Integer }
     */
    public Integer faultWaveSetting(FaultWaveSettingDTO dto, StationDevice device) {
        EmuInvFaultParamDTO emu = new EmuInvFaultParamDTO(device.getEmuNumber());
        EmuInvFaultParamDTO.InvData invData = new EmuInvFaultParamDTO.InvData();
        emu.getInvData().add(invData);
        invData.setSn(dto.getSn());
        for (GridParamDTO p : dto.getData()) {
            p.setType(FaultWaveEnum.TYPE_MAP.get(p.getAddr()));
        }
        invData.setData(AddrValueDTO.to(dto.getData()));
        String message = JSON.toJSONString(emu);
        String topic = SendTopicEnum.EMU_INV_WRITE_PARAM.getTopic(device.getEmuNumber());
        return stationDeviceRemoteService.sendCommand(device.getEmuNumber(), List.of(dto.getSn()), emu.getMid(), topic, message, CommandEnum.EMU_WRITE_FAULT, DeviceTypeEnum.EMU);
    }

    /**
     * 获取电表数据
     *
     * @param stationDevice 车站设备
     * @return {@link Integer }
     */
    public Integer getMeterData(StationDevice stationDevice) {
        Integer powerStationId = stationDevice.getPowerStationId();
        PowerStation station = powerStationService.getById(powerStationId);
        String timeZone = station.getTimeZone();
        MeterDataDTO meterDataDTO = new MeterDataDTO(stationDevice.getNumber());
        meterDataDTO.setTimeCode(timeZone);
        String message = JSON.toJSONString(meterDataDTO);
        String topic = SendTopicEnum.EMU_READ_METER_DATA.getTopic(stationDevice.getNumber());
        return stationDeviceRemoteService.sendCommand(stationDevice.getNumber(), meterDataDTO.getMid(), topic, message, CommandEnum.INV_READ_GRID, DeviceTypeEnum.WN);
    }

}
