package com.wh.data.pojo.vo;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.utils.TimeZoneUtil;
import com.ruoyi.common.core.utils.Tools;
import com.wh.data.pojo.dto.QueryEmuEnergyDataDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.*;

import static com.wh.data.protocol.TopicService.*;

/**
 * <AUTHOR>
 * @date 2024/12/10
 */
@Data
@NoArgsConstructor
public class EmuEnergyDataVO {

    private Integer id;

    private List<List<Object>> data = new ArrayList<>();

    private String unit;

    public EmuEnergyDataVO(Integer id) {
        this.id = id;
    }

    public static List<EmuEnergyDataVO> of(QueryEmuEnergyDataDTO dto, Map<JSONObject, List<JSONObject>> map, ZonedDateTime beginTime, ZonedDateTime endTime, ZonedDateTime now) {
        List<EmuEnergyDataVO> result = dto.getIds().stream().map(EmuEnergyDataVO::new).toList();
        Map<Integer, Map<String, BigDecimal>> deviceMap = new HashMap<>(dto.getIds().size());
        map.forEach((tags, list) -> deviceMap.put(tags.getInteger(PID), Tools.getMap(list)));
        switch (dto.getType()) {
            case 1 -> {
                do {
                    String time = TimeZoneUtil.HHmm.format(beginTime.toLocalDateTime());
                    if (beginTime.isBefore(now)) {
                        for (EmuEnergyDataVO vo : result) {
                            vo.getData().add(Tools.getTimeData(time, deviceMap.getOrDefault(vo.getId(), Collections.emptyMap()).get(time), dto.getDataType() == 1));
                        }
                    } else {
                        for (EmuEnergyDataVO vo : result) {
                            vo.getData().add(List.of(time, "-"));
                        }
                    }
                } while ((beginTime = beginTime.plusMinutes(15)).isBefore(endTime));
            }
            case 3 -> {
                do {
                    String time = TimeZoneUtil.yyyyMMdd.format(beginTime.toLocalDateTime());
                    for (EmuEnergyDataVO vo : result) {
                        vo.getData().add(Tools.getTimeData(time, deviceMap.getOrDefault(vo.getId(), Collections.emptyMap()).get(time), true));
                    }
                } while ((beginTime = beginTime.plusDays(1)).isBefore(endTime) && beginTime.isBefore(now));
            }
        }
        if (dto.getDataType() == 1) {
            for (EmuEnergyDataVO vo : result) {
                vo.setUnit(Tools.covertUnit(vo.getData()));
            }
        } else {
            for (EmuEnergyDataVO vo : result) {
                vo.setUnit(Tools.covertPowerWUnit(vo.getData()));
            }
        }
        return result;
    }

    public static List<EmuEnergyDataVO> of(QueryEmuEnergyDataDTO dto, List<String> months, List<Map<Integer, List<JSONObject>>> list) {
        List<EmuEnergyDataVO> result = new ArrayList<>(dto.getIds().size());
        for (Integer id : dto.getIds()) {
            EmuEnergyDataVO vo = new EmuEnergyDataVO();
            vo.setId(id);
            List<List<Object>> dataList = new ArrayList<>(months.size());
            for (int i = 0; i < months.size(); i++) {
                List<JSONObject> arr = list.get(i).get(id);
                if (arr == null) {
                    dataList.add(List.of(months.get(i), BigDecimal.ZERO));
                } else {
                    JSONObject json = arr.get(0);
                    dataList.add(Tools.getTimeData(months.get(i), json.getBigDecimal(DATA), true));
                }
            }
            vo.setData(dataList);
            vo.setUnit(Tools.covertUnit(vo.getData()));
            result.add(vo);
        }
        return result;
    }

    public static List<EmuEnergyDataVO> of(QueryEmuEnergyDataDTO dto, List<String> months, Map<JSONObject, Map<String, JSONObject>> map) {
        List<EmuEnergyDataVO> result = new ArrayList<>(dto.getIds().size());
        for (Map.Entry<JSONObject, Map<String, JSONObject>> m : map.entrySet()) {
            EmuEnergyDataVO vo = new EmuEnergyDataVO();
            vo.setId(m.getKey().getInteger(PID));
            vo.setData(months.stream().map(month -> {
                JSONObject json = m.getValue().get(month);
                if (json == null) {
                    return Tools.getTimeData(month, BigDecimal.ZERO);
                }
                return Tools.getTimeData(json.getString(TIME), json.getBigDecimal(DATA), true);
            }).toList());
            vo.setUnit(Tools.covertUnit(vo.getData()));
            result.add(vo);
        }
        return result;
    }

}
