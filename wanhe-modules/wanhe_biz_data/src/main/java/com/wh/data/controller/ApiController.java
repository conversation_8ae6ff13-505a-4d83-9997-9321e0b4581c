package com.wh.data.controller;

import com.ruoyi.common.core.domain.R;
import com.wh.data.influxdb.service.DeviceDataService;
import com.wh.data.pojo.station.InfoVO;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 给第三方提供的接口
 * <AUTHOR>
 * @date 2025/1/12
 */
@RestController
@RequestMapping("/api")
public class ApiController {

    @Resource
    private DeviceDataService deviceDataService;

    /**
     * 查询电站列表
     */
    @GetMapping(value = "/station/list")
    public R<List<Integer>> stationList() {
        return R.ok(deviceDataService.getPowerStationService().stationList());
    }

    /**
     * 查询电站详细信息
     */
    @GetMapping(value = "/station/info")
    public R<InfoVO> stationInfo(@RequestParam Integer stationId) {
        return R.ok(deviceDataService.stationInfo(stationId));
    }

}
