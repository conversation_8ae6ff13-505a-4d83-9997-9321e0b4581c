package com.wh.data.pojo.dto;

import com.ruoyi.common.core.domain.PageReqDTO;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/1/5
 */
@Setter
@Getter
public class StationDeviceRemotePageDTO extends PageReqDTO {

    // 电站id
    private Integer stationId;

    // 设备类型
    @NotNull
    private Integer deviceType;

    // 命令类型
    private Integer operate;

    // 设备序列化
    private String String;

    // 下发开始时间
    private LocalDateTime startTime;
    // 下发结束时间
    private LocalDateTime endTime;

    // 响应开始时间
    private LocalDateTime responseStartTime;
    // 响应结束时间
    private LocalDateTime responseEndTime;

}
