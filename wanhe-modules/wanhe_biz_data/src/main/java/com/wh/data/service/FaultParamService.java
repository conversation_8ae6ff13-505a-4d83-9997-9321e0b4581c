package com.wh.data.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.security.service.impl.BaseServiceImpl;
import com.wh.data.domain.FaultParam;
import com.wh.data.mapper.FaultParamMapper;
import com.wh.data.pojo.dto.FaultParamPageReqDTO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/8
 */
@Service
public class FaultParamService extends BaseServiceImpl<FaultParamMapper, FaultParam> {

    public boolean add(FaultParam dto) {
        dto.setId(null);
        return save(dto);
    }

    public boolean update(FaultParam dto) {
        if (dto.getId() == null) {
            return false;
        }
        FaultParam param = getByIdThrow(dto.getId());
        BeanUtil.copyProperties(dto, param);
        return updateById(param);
    }

    public JSONObject exportData() {
        JSONObject json = new JSONObject();
        Map<String, List<FaultParam>> map = list().stream().collect(Collectors.groupingBy(FaultParam::getParamGroup));
        if (CollUtil.isNotEmpty(map)) {
            json.put("group", map.keySet());
            for (Map.Entry<String, List<FaultParam>> m : map.entrySet()) {
                JSONArray data = new JSONArray(m.getValue().size());
                for (FaultParam fp : m.getValue()) {
                    JSONObject obj = new JSONObject();
                    obj.put("paraId", fp.getParamId().toString());
                    obj.put("paraNameCn", fp.getParamName());
                    obj.put("paraNameEn", fp.getParamNameEn());
                    obj.put("paraVarName", fp.getParamVarName());
                    obj.put("paraMacroName", fp.getParamMacroName());
                    obj.put("paraDescription", fp.getParamRemark());
                    obj.put("paraUnit", fp.getParamUnit());
                    obj.put("r_wAttr", fp.getRwAttr());
                    obj.put("r_wOfRunAttr", fp.getRwOfRunAttr());
                    obj.put("maxValue", fp.getMaxValue());
                    obj.put("minValue", fp.getMinValue());
                    obj.put("defaultValue", fp.getDefaultValue());
                    obj.put("dataType", fp.getParamType());
                    obj.put("saveAttr", fp.getSaveAttr());
                    data.add(obj);
                }
                json.put(m.getKey(), data);
            }
        }
        return json;
    }

    @Transactional
    public boolean importData(JSONObject json, Long versionId) {
        baseMapper.deleteAll(versionId);
        JSONArray array = json.getJSONArray("group");
        List<FaultParam> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(array)) {
            for (int i = 0; i < array.size(); i++) {
                String paramGroup = array.getString(i);
                JSONArray data = json.getJSONArray(paramGroup);
                if (CollUtil.isNotEmpty(data)) {
                    for (int j = 0; j < data.size(); j++) {
                        JSONObject obj = data.getJSONObject(j);
                        FaultParam fp = new FaultParam();
                        fp.setParamId(obj.getInteger("paraId"));
                        fp.setParamGroup(paramGroup);
                        fp.setVersionId(versionId);
                        fp.setParamName(obj.getString("paraNameCn"));
                        fp.setParamNameEn(obj.getString("paraNameEn"));
                        fp.setParamVarName(obj.getString("paraVarName"));
                        fp.setParamMacroName(obj.getString("paraMacroName"));
                        fp.setParamRemark(obj.getString("paraDescription"));
                        fp.setParamUnit(obj.getString("paraUnit"));
                        fp.setRwAttr(obj.getString("r_wAttr"));
                        fp.setRwOfRunAttr(obj.getString("r_wOfRunAttr"));
                        fp.setMaxValue(obj.getString("maxValue"));
                        fp.setMinValue(obj.getString("minValue"));
                        fp.setDefaultValue(obj.getString("defaultValue"));
                        fp.setParamType(obj.getString("dataType"));
                        fp.setSaveAttr(obj.getString("saveAttr"));
                        list.add(fp);
                    }
                }
            }
        }
        return saveBatch(list);
    }

    public List<FaultParam> listByParamIds(List<Integer> paramIds, String versionNo) {
        return baseMapper.listByParamIds(join(paramIds), versionNo);
    }

    public List<String> typeList() {
        return baseMapper.typeList();
    }

    public void page(FaultParamPageReqDTO dto) {
        lambdaQuery()
                .eq(StrUtil.isNotBlank(dto.getParamGroup()), FaultParam::getParamGroup, dto.getParamGroup())
                .eq(dto.getVersionId() != null, FaultParam::getVersionId, dto.getVersionId())
                .like(dto.getParamId() != null, FaultParam::getParamId, dto.getParamId())
                .orderByAsc(FaultParam::getParamId)
                .list();
    }

}
