package com.wh.data.controller;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.ClientTypeEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.wh.data.influxdb.service.DeviceDataService;
import com.wh.data.pojo.dto.StationDataDTO;
import com.wh.data.pojo.vo.HomeEnvironmentalBenefitsVO;
import com.wh.data.pojo.vo.HomeHistoricalDataVO;
import com.wh.data.pojo.vo.StationAttrDataVO;
import com.wh.data.service.PowerStationExtendService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 首页
 * <AUTHOR>
 * @date 2024/11/5 0005
 */
@RestController
@RequestMapping("/home/<USER>")
public class HomeChartController {

    @Resource
    private DeviceDataService deviceDataService;
    @Resource
    private PowerStationExtendService powerStationExtendService;

    /**
     * 首页电站概览
     */
    @GetMapping(value = "/queryYearOfEnergy")
    public R<HomeEnvironmentalBenefitsVO> queryYearOfEnergy(@RequestHeader(SecurityConstants.CLIENT_TYPE) String clientType) {
        return R.ok(deviceDataService.queryYearOfEnergy(ClientTypeEnum.of(clientType)));
    }

    /**
     * 年发电量top10
     */
    @GetMapping(value = "/queryPowerStationTop5")
    public R<List<StationAttrDataVO>> queryPowerStationTop5() {
        return R.ok(powerStationExtendService.queryPowerStationTop5());
    }

    /**
     * 历史发电量
     */
    @GetMapping(value = "/queryPowerStationEnergyData")
    public R<HomeHistoricalDataVO> queryPowerStationEnergyData(StationDataDTO dto) {
        if (dto.getQueryType() != 5 && dto.getQueryTime() == null) {
            throw new ServiceException("请输入日期");
        }
        return R.ok(deviceDataService.queryPowerStationEnergyData(dto));
    }

}
