package com.wh.data.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wh.data.domain.Fault;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface FaultMapper extends BaseMapper<Fault> {

    @Select("""
    select f.fault_code,f.fault_key,f.bit,n.name_${faultLanguage} as fault_description,i.name_${faultLanguage} as resolution_suggestion
    from wh_fault f
    left join sys_i18n n on n.type = 4 and n.data_id = f.id
    left join sys_i18n i on i.type = 5 and i.data_id = f.id
    where f.deleted = 0
    """)
    List<Fault> all(String faultLanguage);

}
