package com.wanhe.business.pojo.station;

import com.wanhe.business.domain.PowerStationPrice;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PowerStationPriceMonthVO {

    @Schema(description = "月份")
    private List<Integer> months;

    @Schema(description = "时间集合")
    private List<PowerStationPrice> times;

}
