package com.wanhe.business.pojo.station;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-10-28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WnDTO {

    @Schema(description = "电站id")
    @NotNull
    private Integer powerStationId;

    @Schema(description = "设备序列号")
    @NotEmpty
    private List<String> number;

}
