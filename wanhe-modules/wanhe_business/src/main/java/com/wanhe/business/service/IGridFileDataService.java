package com.wanhe.business.service;

import com.ruoyi.common.security.service.IMPJBaseService;
import com.ruoyi.system.api.domain.GridData;
import com.ruoyi.system.api.domain.GridFileData;
import com.wanhe.business.pojo.product.GridFileDataImport;
import com.wanhe.business.pojo.product.GridFileDataUpdateDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/15
 */
public interface IGridFileDataService extends IMPJBaseService<GridFileData> {

    boolean update(List<GridFileDataUpdateDTO> dto);

    boolean importData(Integer gridFileId, List<GridFileDataImport> dto);

    boolean importBaseData(List<GridData> list);

    List<GridFileData> get(Integer gridFileId);

    void save(Integer fileId);

}
