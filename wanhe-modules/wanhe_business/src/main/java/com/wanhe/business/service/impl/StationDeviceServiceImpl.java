package com.wanhe.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.domain.IntInt;
import com.ruoyi.common.core.domain.IntStr;
import com.ruoyi.common.core.enums.DeviceTypeEnum;
import com.ruoyi.common.core.enums.LanguageEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.TimeZoneUtil;
import com.ruoyi.common.core.utils.Tools;
import com.ruoyi.common.redis.RedisUtil;
import com.ruoyi.common.security.utils.DictUtils;
import com.ruoyi.common.security.utils.Sql;
import com.ruoyi.system.api.RemoteBizDataService;
import com.ruoyi.system.api.dto.EmuInv;
import com.ruoyi.system.api.dto.EmuMeter;
import com.ruoyi.system.api.dto.Inv;
import com.wanhe.business.domain.*;
import com.wanhe.business.mapper.*;
import com.wanhe.business.pojo.device.EmuOnline;
import com.wanhe.business.pojo.device.FirmwareVersionsVO;
import com.wanhe.business.pojo.station.*;
import com.wanhe.business.service.IStationDeviceService;
import jakarta.annotation.Resource;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.Year;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-10-26
 */
@Getter
@Service
public class StationDeviceServiceImpl extends DataScopeServiceImpl<StationDeviceMapper, StationDevice> implements IStationDeviceService {

    @Resource
    private PowerStationMapper powerStationMapper;
    @Resource
    private ProductCategoryMapper productCategoryMapper;
    @Resource
    private DeviceReplaceRecordMapper deviceReplaceRecordMapper;
    @Resource
    private ProductMapper productMapper;
    @Resource
    private GridFileMapper gridFileMapper;
    @Resource
    private RemoteBizDataService remoteBizDataService;
    @Value("${emq.basic}")
    private String authorization;
    @Value("${emq.url}")
    private String baseUrl;
    @Resource
    private RestTemplate restTemplate;

//    public static void main(String[] args) {
//        System.out.println(Base64.getEncoder().encodeToString("25c941e678c502a4:BOk1pTDA9BbNplzeerCT2iW4KNCXvtR3Ab01AzJIyJfI".getBytes()));
//    }

    //更新电站状态和电站绑定emu和微逆数量
    private void updateStationStatusNumEmu(StationDevice device, LocalDateTime stationCreateTime) {
        //查询所有emu和微逆
        List<StationDevice> devices = baseMapper.getEmuInvList(device.getPowerStationId());
        //获取所有设备状态
        List<Integer> statusList = devices.stream().map(StationDevice::getStatus).distinct().toList();
        int status;
        if (statusList.contains(0)) {
            status = 2;
        } else if (statusList.contains(2) || statusList.contains(3) || statusList.contains(4)) {
            status = 3;
        } else if (statusList.contains(1) || statusList.contains(5)) {
            status = 1;
        } else {
            status = 0;
        }
        powerStationMapper.updateStatusNum(device.getPowerStationId(), status, devices.size());
        String key = CacheConstants.EMU_INV + device.getNumber();
        if (Boolean.TRUE.equals(device.getDelete())) {
            String meterKey = CacheConstants.EMU_METER + device.getNumber();
            RedisUtil.delete(List.of(key, meterKey));
        } else {
            EmuInv emuInv = new EmuInv();
            emuInv.setEmuId(device.getId());
            emuInv.setEmuSn(device.getNumber());
            emuInv.setStationId(device.getPowerStationId());
            emuInv.setTenantId(device.getTenantId());
            emuInv.setStationCreateTime(stationCreateTime);
            Map<String, Integer> invMap = devices.stream().filter(d -> device.getId().equals(d.getPid()))
                    .collect(Collectors.toMap(StationDevice::getNumber, StationDevice::getId));
            emuInv.setInvMap(invMap);
            RedisUtil.set(key, JSON.toJSONString(emuInv), Duration.ofDays(3650));
        }
    }

    private void updateStationStatusNumInv(Integer stationId, LocalDateTime stationCreateTime) {
        List<StationDevice> devices = baseMapper.getInvList(stationId);
        //获取所有设备状态
        List<Integer> statusList = devices.stream().map(StationDevice::getStatus).distinct().toList();
        int status;
        if (statusList.contains(0)) {
            status = 2;
        } else if (statusList.contains(2) || statusList.contains(3) || statusList.contains(4)) {
            status = 3;
        } else if (statusList.contains(1) || statusList.contains(5)) {
            status = 1;
        } else {
            status = 0;
        }
        powerStationMapper.updateStatusNum(stationId, status, devices.size());
        for (StationDevice device : devices) {
            Inv inv = new Inv();
            inv.setInvId(device.getId());
            inv.setInvSn(device.getNumber());
            inv.setStationId(device.getPowerStationId());
            inv.setTenantId(device.getTenantId());
            inv.setStationCreateTime(stationCreateTime);
            RedisUtil.set(CacheConstants.INV + device.getNumber(), JSON.toJSONString(inv), Duration.ofDays(3650));
        }
    }

    private void updateStationStatusNumMeter(Integer stationId, LocalDateTime stationCreateTime) {
        List<StationDevice> devices = baseMapper.getEmuMeterList(stationId);
        Map<Integer, List<StationDevice>> collect = devices.stream().collect(Collectors.groupingBy(StationDevice::getDeviceType));
        List<StationDevice> emuList = collect.get(0);
        List<StationDevice> meterList = collect.get(2);
        Map<String, EmuMeter.Meter> invMap = null;
        if (CollUtil.isNotEmpty(meterList)) {
            invMap = meterList.stream().collect(Collectors.toMap(StationDevice::getNumber, d -> new EmuMeter.Meter(d.getId(), d.getPowerGridSeat())));
        }
        if (CollUtil.isNotEmpty(emuList)) {
            for (StationDevice emu : emuList) {
                EmuMeter meter = new EmuMeter();
                meter.setEmuId(emu.getId());
                meter.setEmuSn(emu.getNumber());
                meter.setStationId(emu.getPowerStationId());
                meter.setTenantId(emu.getTenantId());
                meter.setStationCreateTime(stationCreateTime);
                meter.setMeterMap(invMap);
                RedisUtil.set(CacheConstants.EMU_METER + emu.getNumber(), JSON.toJSONString(meter), Duration.ofDays(3650));
            }
        }
    }

    private boolean isOnline(String sn, DeviceTypeEnum deviceType) {
        String url = baseUrl + "/api/v5/clients/";
        switch (deviceType) {
            case EMU -> url += "EMU_" + sn;
            case WN -> url += "INV_" + sn;
            default -> {
                return false;
            }
        }
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", authorization);
        try {
            JSONObject obj = restTemplate.exchange(url, HttpMethod.GET, new HttpEntity<>(headers), JSONObject.class).getBody();
            if (obj == null) {
                return false;
            }
            return obj.getBooleanValue("connected");
        } catch (HttpClientErrorException e) {
            JSONObject json = JSON.parseObject(e.getResponseBodyAsString());
            if (json.getString("code").equals("CLIENTID_NOT_FOUND")) {
                return false;
            }
            log.error(json.toString());
            throw new ServiceException("查询客户端信息异常{0}", json.toString());
        } catch (Exception e) {
            log.error(url, e);
            throw new ServiceException("查询客户端信息异常{0}", sn);
        }
    }

    @Override
    public StationDevice get(Integer id) {
        StationDevice d = baseMapper.get(id);
        if (d != null) {
            d.setSoftwareVersion(Tools.getVersion(d.getSoftwareVersion()));
            d.setHardwareVersion(Tools.getVersion(d.getHardwareVersion()));
            d.setSoftwareWifiVersion(Tools.getVersion(d.getSoftwareWifiVersion()));
            d.setHardwareWifiVersion(Tools.getVersion(d.getHardwareWifiVersion()));
            d.setCreateTime(TimeZoneUtil.getZonedTime(d.getCreateTime(), d.getTimeZone()));
            if (d.getActivateTime() != null) {
                d.setActivateTime(TimeZoneUtil.getZonedTime(d.getActivateTime(), d.getTimeZone()));
            }
            if (d.getLastReportTime() != null) {
                d.setLastReportTime(TimeZoneUtil.getZonedTime(d.getLastReportTime(), d.getTimeZone()));
            }
            if (d.getLastUpgradeTime() != null) {
                d.setLastUpgradeTime(TimeZoneUtil.getZonedTime(d.getLastUpgradeTime(), d.getTimeZone()));
            }
        }
        return d;
    }

    @Override
    public StationDevice getInfoBySn(String sn) {
        StationDevice d = baseMapper.getInfoBySn(sn);
        if (d != null) {
            d.setSoftwareVersion(Tools.getVersion(d.getSoftwareVersion()));
            d.setHardwareVersion(Tools.getVersion(d.getHardwareVersion()));
            d.setSoftwareWifiVersion(Tools.getVersion(d.getSoftwareWifiVersion()));
            d.setHardwareWifiVersion(Tools.getVersion(d.getHardwareWifiVersion()));
            d.setCreateTime(TimeZoneUtil.getZonedTime(d.getCreateTime(), d.getTimeZone()));
            if (d.getActivateTime() != null) {
                d.setActivateTime(TimeZoneUtil.getZonedTime(d.getActivateTime(), d.getTimeZone()));
            }
            if (d.getLastReportTime() != null) {
                d.setLastReportTime(TimeZoneUtil.getZonedTime(d.getLastReportTime(), d.getTimeZone()));
            }
            if (d.getLastUpgradeTime() != null) {
                d.setLastUpgradeTime(TimeZoneUtil.getZonedTime(d.getLastUpgradeTime(), d.getTimeZone()));
            }
        }
        return d;
    }

    private Map<String, Product> validNumber(DeviceTypeEnum type, List<String> code) {
        if (CollUtil.isEmpty(code)) {
            return Collections.emptyMap();
        }
        switch (type) {
            case EMU, WN -> {
                for (String c : code) {
                    Tools.snCheck(c);
                }
                Set<String> cs = code.stream().map(Tools::snModel).collect(Collectors.toSet());
                Map<String, Product> map = productMapper.getByTypeCodes(type.getVal(), joinStr(cs))
                        .stream().collect(Collectors.toMap(Product::getProductCode, Function.identity()));
                if (map.size() != cs.size()) {
                    throw new ServiceException("设备型号错误");
                }
                return map;
            }
            case DB -> {
                Set<String> cs = code.stream().map(Tools::snModel).collect(Collectors.toSet());
                return productMapper.getByTypeCodes(type.getVal(), joinStr(cs))
                        .stream().collect(Collectors.toMap(Product::getProductCode, Function.identity()));
            }
        }
        return code.stream().collect(Collectors.toMap(Tools::snModel, c -> Product.DEFAULT));
    }

    private Product validNumber(DeviceTypeEnum type, String c) {
        switch (type) {
            case EMU, WN -> {
                Tools.snCheck(c);
                Product product = productMapper.getByTypeCode(type.getVal(), Tools.snModel(c));
                if (product == null) {
                    throw new ServiceException("设备型号错误");
                }
                return product;
            }
        }
        return Product.DEFAULT;
    }

    /**
     * 对删除的设备id进行恢复
     *
     * @param sns            微逆sn号
     * @param sn             emu sn号
     * @param powerStationId 电站id
     */
    private Map<String, Integer> getSnId(List<String> sns, String sn, Integer powerStationId) {
        List<String> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(sns)) {
            list.addAll(sns);
        }
        if (StrUtil.isNotBlank(sn)) {
            list.add(sn);
        }
        if (list.isEmpty()) {
            return Collections.emptyMap();
        }
        String snStr = joinStr(list);
        List<IntStr> list2 = baseMapper.getDelSn(snStr, powerStationId);
        if (list2.isEmpty()) {
            return Collections.emptyMap();
        }
        try {
            baseMapper.recover(join(list2.stream().map(IntStr::getK).toList()), powerStationId);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ServiceException("设备已在其他电站添加，请联系管理员");
        }
        return list2.stream().collect(Collectors.toMap(IntStr::getV, IntStr::getK));
    }

    @Override
    @Transactional
    public boolean addEmu(EmuDTO dto) {
        List<String> snList = new ArrayList<>(dto.getNumber());
        snList.add(dto.getEmuNumber());
        if (lambdaQuery().in(StationDevice::getNumber, snList).exists()) {
            throw new ServiceException("序列号重复");
        }
        PowerStation powerStation = getThrow(powerStationMapper.get(dto.getPowerStationId()));
        if (powerStation.getStationType().equals("1")) {
            throw new ServiceException("阳台光伏不能添加EMU设备");
        }
        Product product = validNumber(DeviceTypeEnum.EMU, dto.getEmuNumber());
        Map<String, Product> productMap = validNumber(DeviceTypeEnum.WN, dto.getNumber());
        Map<String, Integer> snIdMap = getSnId(dto.getNumber(), dto.getEmuNumber(), dto.getPowerStationId());
        StationDevice emu = new StationDevice();
        emu.setId(snIdMap.get(dto.getEmuNumber()));
        emu.setPowerStationId(dto.getPowerStationId());
        emu.setNumber(dto.getEmuNumber());
        emu.setDeviceType(product.getDeviceType());
        emu.setWnNum(dto.getNumber().size());
        emu.setConnectNum(product.getConnectNum());
        emu.setDeviceModel(product.getProductName());
        emu.setGuaranteePeriod(product.getGuaranteePeriod());
        emu.setTenantId(powerStation.getTenantId());
        EmuOnline emuOnline = RedisUtil.get(CacheConstants.EMU_ONLINE_KEY + emu.getNumber(), EmuOnline.class);
        if (emuOnline != null) {
            emu.setStatus(1);
            emu.setLastReportTime(emuOnline.getTime());
        } else {
            if (isOnline(emu.getNumber(), DeviceTypeEnum.EMU)) {
                emu.setStatus(1);
            }
        }
        saveOrUpdate(emu);
        List<StationDevice> list = dto.getNumber().stream().map(num -> {
            Product pro = productMap.get(Tools.snModel(num));
            StationDevice wn = new StationDevice();
            wn.setId(snIdMap.get(num));
            wn.setPowerStationId(emu.getPowerStationId());
            wn.setNumber(num);
            wn.setPid(emu.getId());
            wn.setEmuNumber(emu.getNumber());
            wn.setDeviceType(pro.getDeviceType());
            wn.setConnectNum(pro.getConnectNum());
            wn.setDeviceModel(pro.getProductName());
            wn.setGuaranteePeriod(pro.getGuaranteePeriod());
            wn.setTenantId(emu.getTenantId());
            if (emuOnline != null && emuOnline.getInv() != null && emuOnline.getInv().contains(wn.getNumber())) {
                wn.setStatus(1);
                wn.setLastReportTime(emuOnline.getTime());
            }
            return wn;
        }).collect(Collectors.toList());
        saveOrUpdateBatch(list);
        updateStationStatusNumEmu(emu, powerStation.getCreateTime());
        return true;
    }

    @Override
    @Transactional
    public boolean addWn(WnDTO dto) {
        if (lambdaQuery().in(StationDevice::getNumber, dto.getNumber()).exists()) {
            throw new ServiceException("序列号重复");
        }
        PowerStation powerStation = getThrow(powerStationMapper.get(dto.getPowerStationId()));
        if (!powerStation.getStationType().equals("1")) {
            throw new ServiceException("只有阳台光伏添加WIFI版微逆设备");
        }
        Map<String, Product> product = validNumber(DeviceTypeEnum.WN, dto.getNumber());
        Map<String, Integer> snIdMap = getSnId(dto.getNumber(), null, dto.getPowerStationId());
        List<StationDevice> list = dto.getNumber().stream().map(num -> {
            Product pro = product.get(Tools.snModel(num));
            StationDevice wn = new StationDevice();
            wn.setId(snIdMap.get(num));
            wn.setPowerStationId(dto.getPowerStationId());
            wn.setNumber(num);
            wn.setDeviceType(pro.getDeviceType());
            wn.setConnectNum(pro.getConnectNum());
            wn.setDeviceModel(pro.getProductName());
            wn.setGuaranteePeriod(pro.getGuaranteePeriod());
            wn.setTenantId(powerStation.getTenantId());
            if (isOnline(num, DeviceTypeEnum.WN)) {
                wn.setStatus(1);
            }
            return wn;
        }).toList();
        boolean save = saveOrUpdateBatch(list);
        updateStationStatusNumInv(dto.getPowerStationId(), powerStation.getCreateTime());
        return save;
    }

    @Override
    @Transactional
    public void addMeter(MeterDTO dto) {
        PowerStation powerStation = getThrow(powerStationMapper.get(dto.getPowerStationId()));
        List<String> numbers = dto.getMeter().stream().map(Meter::getNumber).toList();
        List<StationDevice> oldMeterList = lambdaQuery().eq(StationDevice::getPowerStationId, dto.getPowerStationId())
                .eq(StationDevice::getDeviceType, DeviceTypeEnum.DB.getVal()).list();
        powerStationMapper.update(Wrappers.<PowerStation>lambdaUpdate().eq(PowerStation::getId, dto.getPowerStationId())
                .set(PowerStation::getReflux, dto.getReflux())
                .set(PowerStation::getPowerGridType, dto.getPowerGridType())
                .set(PowerStation::getNetwork, Boolean.TRUE.equals(dto.getNetwork()))
                .set(PowerStation::getNetworkKw, dto.getNetworkKw()));
        if (dto.getReflux()) {
            List<Integer> delIdList = new ArrayList<>();
            oldMeterList.stream().filter(old -> dto.getMeter().stream().noneMatch(m -> m.getSeat().equals(old.getPowerGridSeat())))
                    .forEach(old -> delIdList.add(old.getId()));
            Map<String, Product> productMap = validNumber(DeviceTypeEnum.DB, numbers);
            Map<String, Integer> snIdMap = getSnId(numbers, null, dto.getPowerStationId());
            for (Meter m : dto.getMeter()) {
                //判断这个位置是否存在设备号
                StationDevice device = oldMeterList.stream().filter(old -> old.getPowerGridSeat().equals(m.getSeat())).findAny().orElse(new StationDevice());
                //不存在设备
                if (device.getId() == null) {
                    //判断当前设备号是否被删除过
                    Integer oldId = snIdMap.get(m.getNumber());
                    if (oldId != null) {
                        device.setId(oldId);
                    }
                    //当前位置存在设备号
                } else {
                    //设备号被替换了
                    if (!m.getNumber().equals(device.getNumber())) {
                        //判断当前设备号是否被删除过
                        Integer oldId = snIdMap.get(m.getNumber());
                        if (oldId != null) {
                            //删除旧设备ID使用新设备ID
                            delIdList.add(device.getId());
                            device.setId(oldId);
                        }
                    }
                }
                device.setNumber(m.getNumber());
                Product product = productMap.getOrDefault(Tools.snModel(device.getNumber()), Product.DEFAULT);
                if (device.getId() == null) {
                    device.setPowerStationId(dto.getPowerStationId());
                    device.setDeviceType(DeviceTypeEnum.DB.getVal());
                    device.setConnectNum(product.getConnectNum());
                    device.setDeviceModel(product.getProductName());
                    device.setGuaranteePeriod(product.getGuaranteePeriod());
                    device.setPowerGridSeat(m.getSeat());
                    device.setTenantId(powerStation.getTenantId());
                    save(device);
                } else {
                    lambdaUpdate().eq(StationDevice::getId, device.getId())
                            .set(StationDevice::getNumber, device.getNumber())
                            .set(StationDevice::getConnectNum, product.getConnectNum())
                            .set(StationDevice::getDeviceModel, product.getProductName())
                            .set(StationDevice::getGuaranteePeriod, product.getGuaranteePeriod())
                            .update();
                }
            }
            if (!delIdList.isEmpty()) {
                lambdaUpdate().in(StationDevice::getId, delIdList).set(StationDevice::getDelete, 1).setSql("deleted = id").update();
            }
        } else {
            lambdaUpdate().eq(StationDevice::getPowerStationId, dto.getPowerStationId())
                    .eq(StationDevice::getDeviceType, DeviceTypeEnum.DB.getVal())
                    .set(StationDevice::getDelete, 1)
                    .setSql("deleted = id")
                    .update();
        }
        updateStationStatusNumMeter(dto.getPowerStationId(), powerStation.getCreateTime());
    }

    @Override
    @Transactional
    public boolean addWnApp(WnAppDTO dto) {
        Map<String, Integer> newWnMap = new HashMap<>();
        for (WnUpdateDTO wn : dto.getNumbers()) {
            newWnMap.put(wn.getNumber(), wn.getId());
        }
        List<StationDevice> oldList = baseMapper.getInvList(dto.getPowerStationId());
        //不存在微逆
        if (oldList.isEmpty()) {
            addWn(new WnDTO(dto.getPowerStationId(), dto.getNumbers().stream().map(WnUpdateDTO::getNumber).collect(Collectors.toList())));
            return false;
        } else {
            for (StationDevice device : oldList) {
                Integer wnId = newWnMap.get(device.getNumber());
                if (wnId != null && !device.getId().equals(wnId)) {
                    throw new ServiceException("设备号对应ID不能更改");
                }
            }
        }
        List<String> wnSnList = dto.getNumbers().stream().map(WnUpdateDTO::getNumber).toList();
        //存在微逆，并且新增的数量和原数量一致
        if (oldList.size() == dto.getNumbers().size()) {
            long count = oldList.stream().filter(old -> wnSnList.contains(old.getNumber())).count();
            //微逆号没有发生变化
            if (count == dto.getNumbers().size()) {
                return false;
            }
        }
        if (oldList.size() == dto.getNumbers().size() && oldList.size() == 1) {
            WnUpdateDTO wnUpdateDTO = dto.getNumbers().get(0);
            if (wnUpdateDTO.getId() == null) {
                throw new ServiceException("参数错误");
            }
            editWn(wnUpdateDTO);
            return true;
        }
        Map<String, StationDevice> oldWnMap = oldList.stream().collect(Collectors.toMap(StationDevice::getNumber, e -> e));
        List<String> newWnList = wnSnList.stream().filter(wn -> !oldWnMap.containsKey(wn)).toList();
        if (!newWnList.isEmpty()) {
            addWn(new WnDTO(dto.getPowerStationId(), newWnList));
        }
        oldList.stream().filter(old -> !wnSnList.contains(old.getNumber())).forEach(old -> delete(old.getId()));
        return true;
    }

    @Override
    @Transactional
    public Integer updateEmu(EmuUpdateDTO dto) {
        StationDevice emu = getByIdThrow(dto.getId());
        if (!emu.getNumber().equals(dto.getEmuNumber())) {
            if (lambdaQuery().eq(StationDevice::getNumber, dto.getEmuNumber()).exists()) {
                throw new ServiceException("序列号重复");
            }
        }
        Product product = validNumber(DeviceTypeEnum.EMU, dto.getEmuNumber());
        PowerStation powerStation = getThrow(powerStationMapper.get(emu.getPowerStationId()));
        Map<String, Product> productMap = validNumber(DeviceTypeEnum.WN, dto.getNumber());
        String oldEmuNumber = emu.getNumber();
        JSONObject layout = JSON.parseObject(powerStation.getLayout());
        List<StationDevice> oldWnList = lambdaQuery().eq(StationDevice::getPid, dto.getId()).list();
        Map<String, StationDevice> oldWnMap = oldWnList.stream().collect(Collectors.toMap(StationDevice::getNumber, e -> e));
        List<String> newWnList = dto.getNumber().stream().filter(wn -> !oldWnMap.containsKey(wn)).toList();
        if (!newWnList.isEmpty()) {
            if (lambdaQuery().eq(StationDevice::getNumber, newWnList).exists()) {
                throw new ServiceException("序列号重复");
            }
        }
        Map<String, Integer> oldSnId = getSnId(dto.getNumber(), dto.getEmuNumber(), emu.getPowerStationId());
        boolean layoutChange = false;
        String key = null;
        //判断是否更换设备号
        if (!oldEmuNumber.equals(dto.getEmuNumber())) {
            key = CacheConstants.EMU_INV + oldEmuNumber;
            emu.setNumber(dto.getEmuNumber());
            emu.setStatus(0);
            if (layout != null) {
                JSONArray array = layout.getJSONArray("row_col_arr");
                for (int i = 0; i < array.size(); i++) {
                    array.getJSONObject(i).put("dtu", dto.getEmuNumber());
                    layoutChange = true;
                }
            }
        }
        //判断设备是否在线
        EmuOnline emuOnline = RedisUtil.get(CacheConstants.EMU_ONLINE_KEY + emu.getNumber(), EmuOnline.class);
        if (emuOnline != null) {
            emu.setStatus(1);
        } else {
            if (isOnline(emu.getNumber(), DeviceTypeEnum.EMU)) {
                emu.setStatus(1);
            } else {
                emu.setStatus(0);
            }
        }
        List<Integer> delIdList = new ArrayList<>();
        Integer oldEmuId = oldSnId.get(dto.getEmuNumber());
        if (oldEmuId != null) {
            //存在旧设备ID删除现存的设备ID
            delIdList.add(emu.getId());
            emu.setId(oldEmuId);
        }
        lambdaUpdate().eq(StationDevice::getId, emu.getId())
                .set(StationDevice::getNumber, dto.getEmuNumber())
                .set(StationDevice::getWnNum, dto.getNumber().size())
                .set(StationDevice::getConnectNum, product.getConnectNum())
                .set(StationDevice::getDeviceModel, product.getProductName())
                .set(StationDevice::getGuaranteePeriod, product.getGuaranteePeriod())
                .set(StationDevice::getStatus, emu.getStatus())
                .update();
        List<Integer> delList = oldWnList.stream().filter(old -> !dto.getNumber().contains(old.getNumber()))
                .map(StationDevice::getId).peek(delIdList::add).toList();
        if (!delList.isEmpty() && layout != null) {
            for (Integer delId : delList) {
                JSONArray array = layout.getJSONArray("row_col_arr");
                for (int i = array.size() - 1; i >= 0; i--) {
                    if (array.getJSONObject(i).getIntValue("parentId") == delId) {
                        array.remove(i);
                        layoutChange = true;
                    }
                }
            }
        }
        for (String num : dto.getNumber()) {
            Product pro = productMap.get(Tools.snModel(num));
            StationDevice wn = oldWnList.stream().filter(o -> o.getNumber().equals(num)).findAny().orElse(new StationDevice());
            Integer oldInvId = oldSnId.get(num);
            if (oldInvId != null) {
                if (wn.getId() != null) {
                    delIdList.add(wn.getId());
                }
                wn.setId(oldInvId);
            }
            if (wn.getId() == null) {
                wn.setPowerStationId(emu.getPowerStationId());
                wn.setNumber(num);
                wn.setPid(emu.getId());
                wn.setEmuNumber(emu.getNumber());
                wn.setDeviceType(pro.getDeviceType());
                wn.setConnectNum(pro.getConnectNum());
                wn.setDeviceModel(pro.getProductName());
                wn.setGuaranteePeriod(pro.getGuaranteePeriod());
                wn.setTenantId(emu.getTenantId());
                save(wn);
            } else {
                lambdaUpdate().eq(StationDevice::getId, wn.getId())
                        .set(StationDevice::getPid, emu.getId())
                        .set(StationDevice::getEmuNumber, emu.getNumber())
                        .set(StationDevice::getConnectNum, pro.getConnectNum())
                        .set(StationDevice::getDeviceModel, pro.getProductName())
                        .set(StationDevice::getGuaranteePeriod, pro.getGuaranteePeriod())
                        .update();
            }
        }
        Integer powerStationId = null;
        if (layout != null && layoutChange) {
            powerStationMapper.updateLayout(emu.getPowerStationId(), layout.toJSONString(JSONWriter.Feature.WriteNulls));
            powerStationId = emu.getPowerStationId();
        }
        if (!delIdList.isEmpty()) {
            lambdaUpdate().in(StationDevice::getId, delIdList).set(StationDevice::getDelete, 1).setSql("deleted = id").update();
        }
        updateStationStatusNumEmu(emu, powerStation.getCreateTime());
        if (key != null) {
            RedisUtil.delete(key);
        }
        return powerStationId;
    }

    @Override
    @Transactional
    public Integer editWn(WnUpdateDTO dto) {
        Product product = validNumber(DeviceTypeEnum.WN, dto.getNumber());
        StationDevice wn = getByIdThrow(dto.getId());
        Map<String, Integer> snIdMap = getSnId(null, dto.getNumber(), wn.getPowerStationId());
        PowerStation powerStation = getThrow(powerStationMapper.get(wn.getPowerStationId()));
        JSONObject layout = JSON.parseObject(powerStation.getLayout());
        boolean layoutChange = false;
        Integer oldSnId = snIdMap.get(dto.getNumber());
        if (oldSnId != null) {
            lambdaUpdate().eq(StationDevice::getId, wn.getId()).set(StationDevice::getDelete, 1).setSql("deleted = id").update();
            if (layout != null) {
                JSONArray array = layout.getJSONArray("row_col_arr");
                for (int i = array.size() - 1; i >= 0; i--) {
                    if (array.getJSONObject(i).getIntValue("parentId") == wn.getId()) {
                        array.remove(i);
                        layoutChange = true;
                    }
                }
            }
            wn.setId(oldSnId);
        }
        wn.setStatus(isOnline(dto.getNumber(), DeviceTypeEnum.WN) ? 1 : 0);
        lambdaUpdate().eq(StationDevice::getId, wn.getId())
                .set(StationDevice::getNumber, dto.getNumber())
                .set(StationDevice::getConnectNum, product.getConnectNum())
                .set(StationDevice::getDeviceModel, product.getProductName())
                .set(StationDevice::getGuaranteePeriod, product.getGuaranteePeriod())
                .update();
        if (layout != null) {
            JSONArray array = layout.getJSONArray("row_col_arr");
            for (int i = 0; i < array.size(); i++) {
                JSONObject obj = array.getJSONObject(i);
                if (obj.getIntValue("parentId") == wn.getId()) {
                    obj.put("number", dto.getNumber());
                    obj.put("name", obj.getString("name").replace(wn.getNumber(), dto.getNumber()));
                    layoutChange = true;
                }
            }
        }
        Integer powerStationId = null;
        if (layout != null && layoutChange) {
            powerStationMapper.updateLayout(wn.getPowerStationId(), layout.toJSONString(JSONWriter.Feature.WriteNulls));
            powerStationId = wn.getPowerStationId();
        }
        updateStationStatusNumInv(wn.getPowerStationId(), powerStation.getCreateTime());
        return powerStationId;
    }

    @Override
    @Transactional
    public StationDevice replace(ReplaceDTO dto) {
        StationDevice device = getByIdThrow(dto.getDeviceId());
        if (dto.getNewNumber().equals(device.getNumber())) {
            throw new ServiceException("新设备号和旧设备号一样，无需替换");
        }
        if (lambdaQuery().eq(StationDevice::getNumber, dto.getNewNumber()).exists()) {
            throw new ServiceException("数据存在重复");
        }
        if (device.getDeviceType() == 0 && lambdaQuery().eq(StationDevice::getPid, device.getId()).exists()) {
            throw new ServiceException("该EMU有子设备，请先删除子设备");
        }
        Product product = validNumber(DeviceTypeEnum.of(device.getDeviceType()), dto.getNewNumber());
        PowerStation powerStation = getThrow(powerStationMapper.get(device.getPowerStationId()));
        JSONObject layout = JSON.parseObject(powerStation.getLayout());
        boolean layoutChange = false;
        DeviceReplaceRecord record = BeanUtil.copyProperties(dto, DeviceReplaceRecord.class);
        Map<String, Integer> oldSnMap = getSnId(null, dto.getNewNumber(), device.getPowerStationId());
        record.setOldNumber(device.getNumber());
        record.setDeviceType(device.getDeviceType());
        record.setGuaranteePeriod(device.getGuaranteePeriod());
        record.setUseDate(device.getCreateTime().toLocalDate());
        record.setPowerStationId(device.getPowerStationId());
        deviceReplaceRecordMapper.insert(record);
        Integer oldId = oldSnMap.get(dto.getNewNumber());
        if (oldId != null) {
            lambdaUpdate().eq(StationDevice::getId, device.getId()).set(StationDevice::getDelete, 1).setSql("deleted = id").update();
            device.setId(oldId);
        }
        DeviceTypeEnum deviceType = DeviceTypeEnum.of(device.getDeviceType());
        if (deviceType == DeviceTypeEnum.EMU || (deviceType == DeviceTypeEnum.WN && device.getPid() == null)) {
            device.setStatus(isOnline(dto.getNewNumber(), deviceType) ? 1 : 0);
        } else {
            device.setStatus(0);
        }
        device.setNumber(dto.getNewNumber());
        lambdaUpdate().eq(StationDevice::getId, device.getId())
                .set(StationDevice::getNumber, device.getNumber())
                .set(StationDevice::getHardwareVersion, null)
                .set(StationDevice::getSoftwareVersion, null)
                .set(StationDevice::getHardwareWifiVersion, null)
                .set(StationDevice::getSoftwareWifiVersion, null)
                .set(StationDevice::getDeviceModel, product.getProductName())
                .set(StationDevice::getStatus, device.getStatus())
                .setIncrBy(StationDevice::getReplaceNum, 1)
                .set(StationDevice::getConnectNum, product.getConnectNum())
                .set(StationDevice::getGuaranteePeriod, product.getGuaranteePeriod())
                .set(StationDevice::getActivateTime, null)
                .set(StationDevice::getLastReportTime, null)
                .set(StationDevice::getLastUpgradeTime, null)
                .set(StationDevice::getCommType, null)
                .set(StationDevice::getNetworkType, null)
                .update();
        switch (deviceType) {
            case EMU -> {
                lambdaUpdate().eq(StationDevice::getEmuNumber, record.getOldNumber())
                        .set(StationDevice::getEmuNumber, device.getNumber())
                        .set(StationDevice::getPid, device.getId())
                        .set(StationDevice::getPowerStationId, device.getPowerStationId())
                        .update();
                if (layout != null) {
                    JSONArray array = layout.getJSONArray("row_col_arr");
                    for (int i = 0; i < array.size(); i++) {
                        JSONObject obj = array.getJSONObject(i);
                        if (obj.getString("dtu").equals(record.getOldNumber())) {
                            obj.put("dtu", device.getNumber());
                            layoutChange = true;
                        }
                    }
                }
            }
            case WN -> {
                if (layout != null) {
                    JSONArray array = layout.getJSONArray("row_col_arr");
                    for (int i = 0; i < array.size(); i++) {
                        JSONObject obj = array.getJSONObject(i);
                        if (obj.getString("number").equals(record.getOldNumber())) {
                            obj.put("number", dto.getNewNumber());
                            obj.put("name", obj.getString("name").replace(record.getOldNumber(), device.getNumber()));
                            obj.put("parentId", device.getId());
                            String id = obj.getString("id");
                            obj.put("id", device.getId() + "-" + id.substring(id.lastIndexOf("-") + 1));
                            layoutChange = true;
                        }
                    }
                }
            }
        }
        if (layout != null && layoutChange) {
            powerStationMapper.updateLayout(device.getPowerStationId(), layout.toJSONString(JSONWriter.Feature.WriteNulls));
            device.setDelete(Boolean.TRUE);
        } else {
            device.setDelete(Boolean.FALSE);
        }
        switch (deviceType) {
            case EMU -> updateStationStatusNumEmu(device, powerStation.getCreateTime());
            case WN -> {
                if (device.getPid() == null) {
                    updateStationStatusNumInv(device.getPowerStationId(), powerStation.getCreateTime());
                } else {
                    updateStationStatusNumEmu(getByIdThrow(device.getPid()), powerStation.getCreateTime());
                }
            }
            case DB -> updateStationStatusNumMeter(device.getPowerStationId(), powerStation.getCreateTime());
        }
        return device;
    }

    @Override
    public void page(StationDevicePageReqDTO dto) {
        List<StationDevice> list = lambdaQueryAll(StationDevice::getPowerStationId, dto.getPowerStationId())
                .selectAll(StationDevice.class)
                .select(PowerStation::getStationName, PowerStation::getTimeZone)
                .like(StrUtil.isNotBlank(dto.getNumber()), StationDevice::getNumber, dto.getNumber())
                .eq(dto.getStatus() != null, StationDevice::getStatus, dto.getStatus())
                .eq(dto.getDeviceType() != null, StationDevice::getDeviceType, dto.getDeviceType())
                .isNotNull(dto.getWnType() != null && dto.getWnType() == 0, StationDevice::getPid)
                .isNull(dto.getWnType() != null && dto.getWnType() == 1, StationDevice::getPid)
                .orderByAsc(StationDevice::getDeviceType, StationDevice::getNumber)
                .list();
        if (CollUtil.isNotEmpty(list)) {
            Set<Integer> tid = list.stream().map(StationDevice::getDeviceType).collect(Collectors.toSet());
            Map<Integer, String> tmap = productCategoryMapper.listByIds(join(tid), LanguageEnum.getLang()).stream().collect(Collectors.toMap(ProductCategory::getId, ProductCategory::getName));
            list.forEach(p -> {
                p.setDeviceTypeName(tmap.get(p.getDeviceType()));
                p.setSoftwareVersion(Tools.getVersion(p.getSoftwareVersion()));
                p.setHardwareVersion(Tools.getVersion(p.getHardwareVersion()));
                p.setSoftwareWifiVersion(Tools.getVersion(p.getSoftwareWifiVersion()));
                p.setHardwareWifiVersion(Tools.getVersion(p.getHardwareWifiVersion()));
                ZoneId zoneId = TimeZoneUtil.getZoneId(p.getTimeZone());
                p.setCreateTime(TimeZoneUtil.getZonedTime(p.getCreateTime(), zoneId));
                if (p.getLastReportTime() != null) {
                    p.setLastReportTime(TimeZoneUtil.getZonedTime(p.getLastReportTime(), zoneId));
                }
                if (p.getLastUpgradeTime() != null) {
                    p.setLastUpgradeTime(TimeZoneUtil.getZonedTime(p.getLastUpgradeTime(), zoneId));
                }
                if (p.getActivateTime() != null) {
                    p.setActivateTime(TimeZoneUtil.getZonedTime(p.getActivateTime(), zoneId));
                }
            });
        }
    }

    @Override
    public List<StationDevice> networkingModeList(Integer powerStationId) {
        List<StationDevice> list = lambdaQuery().eq(StationDevice::getPowerStationId, powerStationId)
                .in(StationDevice::getDeviceType, List.of(0, 1))
                .orderByDesc(StationDevice::getId)
                .list();
        if (CollUtil.isNotEmpty(list)) {
            Map<String, Integer> gateway = list.stream().filter(d -> d.getPid() == null).collect(Collectors.toMap(StationDevice::getNumber, d -> d.getStatus() == 0 ? 0 : 1));
            list.forEach(d -> {
                if (d.getPid() == null) {
                    d.setNetworkStatus(gateway.get(d.getNumber()));
                } else {
                    d.setNetworkStatus(gateway.get(d.getEmuNumber()));
                }
            });
        }
        return list;
    }

    @Override
    public void checkSn(String sn) {
        Tools.snCheck(sn);
        if (!productMapper.exists(Wrappers.<Product>lambdaQuery().eq(Product::getProductCode, Tools.snModel(sn)))) {
            throw new ServiceException("设备型号错误");
        }
    }

    @Override
    public void checkSn2(String sn, Integer type) {
        Tools.snCheck(sn);
        //0-EMU 1-微逆 2-电表 3-组件 4-电池 5-充电桩
        boolean exists = productMapper.exists(Wrappers.<Product>lambdaQuery()
                .eq(Product::getProductCode, Tools.snModel(sn))
                .eq(Product::getDeviceType, type));
        if (!exists) {
            throw new ServiceException("设备型号错误");
        }
    }

    @Override
    public List<DeviceCountVo> countByType(Integer powerStationId) {
        return baseMapper.countByType(powerStationId);
    }

    @Override
    public Integer networkingMode(Integer deviceId) {
        IntInt status = baseMapper.getStatus(deviceId);
        if (status.getV() != null) {
            return status.getV() == 0 ? 0 : 1;
        } else {
            return status.getK() == 0 ? 0 : 1;
        }
    }

    @Override
    @Transactional
    public StationDevice delete(Integer id) {
        StationDevice device = getByIdThrow(id);
        if (device.getDeviceType() == 0 && lambdaQuery().eq(StationDevice::getPid, id).exists()) {
            throw new ServiceException("该EMU有子设备，请先删除子设备");
        }
        PowerStation powerStation = getThrow(powerStationMapper.get(device.getPowerStationId()));
        JSONObject layout = JSON.parseObject(powerStation.getLayout());
        boolean layoutChange = false;
        lambdaUpdate().eq(StationDevice::getId, id).set(StationDevice::getDelete, 1).setSql("deleted = id").update();
//        baseMapper.resolved(id);
        device.setDelete(Boolean.TRUE);
        switch (device.getDeviceType()) {
            case 0 -> {
                if (layout != null) {
                    JSONArray array = layout.getJSONArray("row_col_arr");
                    for (int i = array.size() - 1; i >= 0; i--) {
                        if (device.getNumber().equals(array.getJSONObject(i).getString("dtu"))) {
                            array.remove(i);
                            layoutChange = true;
                        }
                    }
                }
                updateStationStatusNumEmu(device, powerStation.getCreateTime());
            }
            case 1 -> {
                if (layout != null) {
                    JSONArray array = layout.getJSONArray("row_col_arr");
                    for (int i = array.size() - 1; i >= 0; i--) {
                        if (device.getNumber().equals(array.getJSONObject(i).getString("number"))) {
                            array.remove(i);
                            layoutChange = true;
                        }
                    }
                }
                if (device.getPid() == null) {
                    updateStationStatusNumInv(device.getPowerStationId(), powerStation.getCreateTime());
                } else {
                    updateStationStatusNumEmu(getByIdThrow(device.getPid()), powerStation.getCreateTime());
                }
            }
            case 2 -> updateStationStatusNumMeter(device.getPowerStationId(), powerStation.getCreateTime());
        }
        device.setDelete(Boolean.FALSE);
        if (layout != null && layoutChange) {
            powerStationMapper.updateLayout(device.getPowerStationId(), layout.toJSONString(JSONWriter.Feature.WriteNulls));
            device.setDelete(Boolean.TRUE);
        }
        return device;
    }

    @Override
    public Map<Integer, List<StationDevice>> mapList(Integer powerStationId) {
        ZoneId zoneId = Sql.getZoneId(powerStationId);
        Map<Integer, List<StationDevice>> map = new LinkedHashMap<>();
        lambdaQuery().eq(StationDevice::getPowerStationId, powerStationId).list()
                .forEach(d -> {
                    d.setLastReportTime(TimeZoneUtil.getZonedTime(d.getLastReportTime(), zoneId));
                    d.setSoftwareVersion(Tools.getVersion(d.getSoftwareVersion()));
                    map.computeIfAbsent(d.getDeviceType(), k -> new ArrayList<>()).add(d);
                });
        return map;
    }

    @Override
    public void pageEmu(EmuPageReqDTO dto) {
        List<StationDevice> list = lambdaQuery()
                .eq(StationDevice::getPowerStationId, dto.getPowerStationId())
                .eq(StationDevice::getDeviceType, dto.getStationType() == 1 ? 1 : 0)
                .like(StrUtil.isNotBlank(dto.getEmuSn()), StationDevice::getNumber, dto.getEmuSn())
                .orderByDesc(StationDevice::getId)
                .list();
        if (dto.getStationType() != 1 && !list.isEmpty()) {
            Map<Integer, StationDevice> map = list.stream().collect(Collectors.toMap(StationDevice::getId, Function.identity()));
            List<Integer> ids = list.stream().peek(e -> e.setWnList(new ArrayList<>())).map(StationDevice::getId).toList();
            lambdaQuery().in(StationDevice::getPid, ids).list().forEach(wn -> map.get(wn.getPid()).getWnList().add(wn));
        }
        for (StationDevice d : list) {
            d.setSoftwareVersion(Tools.getVersion(d.getSoftwareVersion()));
            d.setHardwareVersion(Tools.getVersion(d.getHardwareVersion()));
            d.setSoftwareWifiVersion(Tools.getVersion(d.getSoftwareWifiVersion()));
            d.setHardwareWifiVersion(Tools.getVersion(d.getHardwareWifiVersion()));
        }
    }

    @Override
    public List<StationDevice> emuList(Integer powerStationId) {
        List<StationDevice> list = lambdaQueryAll(StationDevice::getPowerStationId, powerStationId)
                .in(StationDevice::getDeviceType, 0)
                .orderByDesc(StationDevice::getId)
                .list();
        for (StationDevice d : list) {
            d.setSoftwareVersion(Tools.getVersion(d.getSoftwareVersion()));
            d.setHardwareVersion(Tools.getVersion(d.getHardwareVersion()));
            d.setSoftwareWifiVersion(Tools.getVersion(d.getSoftwareWifiVersion()));
            d.setHardwareWifiVersion(Tools.getVersion(d.getHardwareWifiVersion()));
        }
        return list;
    }

    @Override
    public List<StationDevice> wnList(Integer powerStationId) {
        List<StationDevice> list = lambdaQueryAll(StationDevice::getPowerStationId, powerStationId)
                .eq(StationDevice::getDeviceType, 1)
                .orderByAsc(StationDevice::getNumber)
                .list();
        for (StationDevice d : list) {
            d.setSoftwareVersion(Tools.getVersion(d.getSoftwareVersion()));
            d.setHardwareVersion(Tools.getVersion(d.getHardwareVersion()));
            d.setSoftwareWifiVersion(Tools.getVersion(d.getSoftwareWifiVersion()));
            d.setHardwareWifiVersion(Tools.getVersion(d.getHardwareWifiVersion()));
        }
        return list;
    }

    @Override
    public List<StationDevice> tree(StationDeviceReqDTO dto) {
        List<StationDevice> all = lambdaQueryAll(StationDevice::getPowerStationId, dto.getPowerStationId())
                .selectAll(StationDevice.class)
                .selectAs(PowerStation::getTimeZone, StationDevice::getTimeZone)
                .and(dto.getEmuId() != null, i -> i.eq(StationDevice::getId, dto.getEmuId()).or().eq(StationDevice::getPid, dto.getEmuId()))
                .eq(dto.getDeviceType() != null, StationDevice::getDeviceType, dto.getDeviceType())
                .eq(dto.getStatus() != null, StationDevice::getStatus, dto.getStatus())
                .like(StrUtil.isNotBlank(dto.getNumber()), StationDevice::getNumber, dto.getNumber())
                .orderByAsc(dto.getDeviceType() == null, StationDevice::getDeviceType)
                .orderByDesc(StationDevice::getId)
                .list();
        Map<Integer, String> gridFileMap = null;
        Map<String, String> m = DictUtils.getDeviceType();
        if (CollUtil.isNotEmpty(all)) {
            Set<Integer> gridFileIds = all.stream().map(StationDevice::getGridFileId).filter(Objects::nonNull).collect(Collectors.toSet());
            if (CollUtil.isNotEmpty(gridFileIds)) {
                gridFileMap = gridFileMapper.getIdName(join(gridFileIds)).stream().collect(Collectors.toMap(IntStr::getK, IntStr::getV));
            }
        }
        if (dto.getDeviceType() != null || StrUtil.isNotBlank(dto.getNumber())) {
            for (StationDevice d : all) {
                d.setSoftwareVersion(Tools.getVersion(d.getSoftwareVersion()));
                d.setHardwareVersion(Tools.getVersion(d.getHardwareVersion()));
                d.setSoftwareWifiVersion(Tools.getVersion(d.getSoftwareWifiVersion()));
                d.setHardwareWifiVersion(Tools.getVersion(d.getHardwareWifiVersion()));
            }
            return all;
        } else {
            List<StationDevice> main = new ArrayList<>();
            Map<Integer, List<StationDevice>> map = new HashMap<>();
            for (StationDevice device : all) {
                device.setSoftwareVersion(Tools.getVersion(device.getSoftwareVersion()));
                device.setHardwareVersion(Tools.getVersion(device.getHardwareVersion()));
                device.setSoftwareWifiVersion(Tools.getVersion(device.getSoftwareWifiVersion()));
                device.setHardwareWifiVersion(Tools.getVersion(device.getHardwareWifiVersion()));
                if (gridFileMap != null) {
                    device.setGridFileVersions(gridFileMap.get(device.getGridFileId()));
                }
                if (device.getPid() == null) {
                    main.add(device);
                } else {
                    map.computeIfAbsent(device.getPid(), k -> new ArrayList<>()).add(device);
                }
                device.setDeviceTypeName(m.get(device.getDeviceType().toString()));
            }
            for (StationDevice device : main) {
                device.setWnList(map.getOrDefault(device.getId(), Collections.emptyList()));
            }
            return main;
        }
    }

    @Override
    public MeterDTO meter(Integer powerStationId) {
        PowerStation station = powerStationMapper.selectById(powerStationId);
        if (station == null) return null;
        MeterDTO dto = new MeterDTO();
        dto.setPowerStationId(powerStationId);
        dto.setReflux(station.getReflux());
        dto.setPowerGridType(station.getPowerGridType());
        dto.setNetwork(station.getNetwork());
        dto.setNetworkKw(station.getNetworkKw());
        List<Meter> meter = lambdaQuery().eq(StationDevice::getPowerStationId, powerStationId)
                .eq(StationDevice::getDeviceType, 2)
                .list().stream().map(m -> new Meter(m.getPowerGridSeat(), m.getNumber())).toList();
        dto.setMeter(meter);
        return dto;
    }

    @Override
    public boolean activate(Integer powerStationId) {
        PowerStation powerStation = getThrow(powerStationMapper.selectById(powerStationId));
        if (powerStationMapper.countOwner(powerStationId) == 0) {
            throw new ServiceException("还未配置业主信息");
        }
        if (!lambdaQuery().eq(StationDevice::getPowerStationId, powerStationId).exists()) {
            throw new ServiceException("还未配置设备");
        }
        remoteBizDataService.networking(powerStationId);
        return SqlHelper.retBool(powerStationMapper.update(Wrappers.<PowerStation>lambdaUpdate()
                .eq(PowerStation::getId, powerStationId)
                .set(PowerStation::getStationStatus, 2)));
    }

    @Override
    public List<FirmwareVersionsVO> versions(Integer powerStationId) {
        Integer stationType = powerStationMapper.getStationType(powerStationId);
        List<FirmwareVersionsVO> versions;
        if (stationType == 1) {
            versions = baseMapper.versions(powerStationId, 1, 2);
        } else {
            versions = baseMapper.versions(powerStationId, 0, 1);
        }
        for (FirmwareVersionsVO version : versions) {
            switch (version.getDeviceType()) {
                case 0 -> version.setOperateType(1);
                case 1 -> version.setOperateType(2);
            }
        }
        return versions;
    }

    /**
     * 设备类型 （0：EMU 1:微逆 2：电表）
     */
    @Override
    public PowerStationStatisticsVO queryPowerStationDevice() {
        Map<Integer, Integer> map = baseMapper.queryPowerStationDevice(dataScopeSqlSuper()).stream().collect(Collectors.toMap(StationDevice::getDeviceType, StationDevice::getId));
        int total = 0;
        for (int i = 0; i <= 2; i++) {
            map.putIfAbsent(i, 0);
            total += map.get(i);
        }
        return new PowerStationStatisticsVO(total, map);
    }

    /**
     * 设备统计 按月份
     */
    @Override
    public List<HomeDeviceStatisticsVO> queryDeviceStatistics() {
        int year = Year.now().getValue();
        String dataScopeSql = dataScopeSqlSuper();
        Map<String, Integer> deviceMap = baseMapper.queryDeviceStatistics(year, dataScopeSql)
                .stream().collect(Collectors.toMap(vo -> vo.getDeviceType() + "-" + vo.getMonth(), DeviceStatisticsVO::getStationNum));
        Map<String, Integer> replaceMap = baseMapper.queryDeviceReplaceStatistics(year, dataScopeSql)
                .stream().collect(Collectors.toMap(vo -> vo.getDeviceType() + "-" + vo.getMonth(), DeviceStatisticsVO::getStationNum));
        List<HomeDeviceStatisticsVO> list = new ArrayList<>(2);
        list.add(new HomeDeviceStatisticsVO(0, new ArrayList<>(12)));
        list.add(new HomeDeviceStatisticsVO(1, new ArrayList<>(12)));
        for (int i = 1; i <= 12; i++) {
            String key0 = "0-" + i;
            Integer count0 = deviceMap.getOrDefault(key0, 0) + replaceMap.getOrDefault(key0, 0);
            list.get(0).getData().add(new DeviceStatisticsVO(i, count0));
            String key1 = "1-" + i;
            Integer count1 = deviceMap.getOrDefault(key1, 0) + replaceMap.getOrDefault(key1, 0);
            list.get(1).getData().add(new DeviceStatisticsVO(i, count1));
        }
        return list;
    }

    @Override
    public void devicePage(DevicePageReqDTO dto) {
        List<StationDevice> list = lambdaQuery().eq(StationDevice::getPowerStationId, dto.getPowerStationId())
                .eq(StationDevice::getDeviceType, 1)
                .orderByAsc(StationDevice::getDeviceType, StationDevice::getNumber)
                .list();
        for (StationDevice d : list) {
            d.setSoftwareVersion(Tools.getVersion(d.getSoftwareVersion()));
            d.setHardwareVersion(Tools.getVersion(d.getHardwareVersion()));
            d.setSoftwareWifiVersion(Tools.getVersion(d.getSoftwareWifiVersion()));
            d.setHardwareWifiVersion(Tools.getVersion(d.getHardwareWifiVersion()));
            d.getFinishPeriod();
        }
    }

    @Override
    public List<String> sns(Integer powerStationId, Integer deviceType) {
        return baseMapper.sns(powerStationId, deviceType);
    }

}
