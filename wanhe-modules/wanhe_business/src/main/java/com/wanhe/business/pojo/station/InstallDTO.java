package com.wanhe.business.pojo.station;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-10-28
 */
@Data
public class InstallDTO {

    @Schema(description = "电站id")
    @NotNull
    @Positive
    private Integer id;

    @Schema(description = "安装图片地址")
    @NotNull
    private List<String> installImageUrl;

}
