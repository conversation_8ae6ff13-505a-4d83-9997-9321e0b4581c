package com.wanhe.business.pojo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(name = "ProductCategoryUpdateDTO", description = "产品分类表")
public class ProductCategoryUpdateDTO extends ProductCategoryDTO {

    @Schema(description = "产品分类id")
    @NotNull
    @Positive
    private Long id;

}
