package com.wanhe.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.github.yulichang.wrapper.enums.DefaultFuncEnum;
import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.io.image.ImageData;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.UnitValue;
import com.itextpdf.layout.properties.VerticalAlignment;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.domain.*;
import com.ruoyi.common.core.enums.DeviceTypeEnum;
import com.ruoyi.common.core.enums.LanguageEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.I18nUtil;
import com.ruoyi.common.core.utils.ServletUtils;
import com.ruoyi.common.core.utils.TimeZoneUtil;
import com.ruoyi.common.core.utils.Tools;
import com.ruoyi.common.redis.RedisUtil;
import com.ruoyi.common.security.service.impl.MPJBaseServiceImpl;
import com.ruoyi.common.security.utils.DictUtils;
import com.ruoyi.common.security.utils.Sql;
import com.ruoyi.system.api.RemoteBizDataService;
import com.ruoyi.system.api.domain.PowerStationExtend;
import com.ruoyi.system.api.domain.SysDept;
import com.ruoyi.system.api.domain.SysRole;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.dto.PowerStationListDataVO;
import com.ruoyi.system.api.dto.UserDistrict;
import com.wanhe.business.domain.PowerStation;
import com.wanhe.business.domain.PowerStationCollect;
import com.wanhe.business.domain.PowerStationPrice;
import com.wanhe.business.domain.StationDevice;
import com.wanhe.business.enums.WeatherPhenomenaEnum;
import com.wanhe.business.mapper.PowerStationMapper;
import com.wanhe.business.pojo.product.PermissionDTO;
import com.wanhe.business.pojo.station.Module;
import com.wanhe.business.pojo.station.*;
import com.wanhe.business.service.*;
import com.wanhe.common.mail.MailUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Getter;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024-10-26
 */
@Service
public class PowerStationServiceImpl extends MPJBaseServiceImpl<PowerStationMapper, PowerStation> implements IPowerStationService {

    @Resource
    private IPowerStationExtendService powerStationExtendService;
    @Resource
    private IPowerStationCollectService powerStationCollectService;
    @Resource
    private IPowerStationPriceService powerStationPriceService;
    @Resource
    private IStationDeviceService stationDeviceService;
    @Getter
    @Resource
    private RemoteBizDataService remoteBizDataService;
    @Resource
    private RestTemplate restTemplate;

    @Override
    public PowerStation getInfo(Integer id) {
        PowerStation info = baseMapper.getInfo(id, getUserId());
        if (info != null) {
            ZoneId zoneId = TimeZoneUtil.getZoneId(info.getTimeZone());
            info.setCreateTime(TimeZoneUtil.getZonedTime(info.getCreateTime(), zoneId));
            if (info.getLastReportTime() != null) {
                info.setLastReportTime(TimeZoneUtil.getZonedTime(info.getLastReportTime(), zoneId));
            }
        }
        return info;
    }

    @Override
    @Transactional
    public Integer add(PowerStationDTO dto) {
        PowerStation entity = BeanUtil.copyProperties(dto, PowerStation.class);
        SysUser user = getSysUser();
        entity.setTenantId(user.getTenantId());
        entity.setCreateId(user.getUserId());
        entity.setFaultNotificationMethod(List.of(2));
        entity.setInstallerId(user.getUserId());
        save(entity);
        powerStationExtendService.save(new PowerStationExtend().setId(entity.getId()));
        RedisUtil.set(CacheConstants.TIMEZONE + entity.getId(), dto.getTimeZone());
        return entity.getId();
    }

    @Override
    public boolean update(PowerStationUpdateDTO dto) {
        PowerStation entity = getByIdThrow(dto.getId());
        BeanUtil.copyProperties(dto, entity);
        boolean b = updateById(entity);
        RedisUtil.set(CacheConstants.TIMEZONE + entity.getId(), dto.getTimeZone());
        return b;
    }

    @Override
    public PowerStationListDataVO data() {
        R<PowerStationListDataVO> r = remoteBizDataService.energy();
        if (r.getCode() != 200) {
            throw new ServiceException(r.getMsg());
        }
        PowerStationListDataVO data = r.getData();
        Map<Integer, Integer> map = baseMapper.deviceData(dataScopeSqlSuper())
                .stream().collect(Collectors.toMap(StationDevice::getDeviceType, StationDevice::getWnNum));
        data.setEmu(map.getOrDefault(0, 0));
        data.setWn(map.getOrDefault(1, 0));
        return data;
    }

    @Override
    public List<PowerStationPriceMonthVO> powerPrice(Integer id) {
        Map<List<Integer>, List<PowerStationPrice>> map = new LinkedHashMap<>();
        powerStationPriceService.lambdaQuery().eq(PowerStationPrice::getPowerStationId, id)
                .list().forEach(price -> map.computeIfAbsent(price.getMonths(), k -> new ArrayList<>()).add(price));
        List<PowerStationPriceMonthVO> list = new ArrayList<>();
        map.forEach((months, prices) -> list.add(new PowerStationPriceMonthVO(months, prices)));
        return list;
    }

    @Override
    @Transactional
    public boolean powerPrice(PowerStationPriceDTO dto) {
        boolean update = lambdaUpdate().eq(PowerStation::getId, dto.getPowerStationId())
                .set(PowerStation::getMonetaryUnit, dto.getMonetaryUnit())
                .set(PowerStation::getPowerPriceStrategy, dto.getPowerPriceStrategy())
                .set(PowerStation::getPowerPrice, dto.getPowerPrice())
                .set(PowerStation::getBuyPowerPrice, dto.getBuyPowerPrice())
                .update();
        if (update && dto.getPowerPriceStrategy() == 2 && CollUtil.isNotEmpty(dto.getTimeStrategy())) {
            List<PowerStationPrice> list = dto.getTimeStrategy().stream().flatMap(d -> d.getTimes().stream().map(t -> {
                PowerStationPrice price = new PowerStationPrice();
                price.setPowerStationId(dto.getPowerStationId());
                price.setMonths(d.getMonths());
                price.setTimeBucket(t.getTimeBucket());
                price.setBeginTime(t.getBeginTime());
                price.setEndTime(t.getEndTime());
                price.setPrice(t.getPrice());
                return price;
            })).toList();
            powerStationPriceService.remove(Wrappers.<PowerStationPrice>lambdaQuery().eq(PowerStationPrice::getPowerStationId, dto.getPowerStationId()));
            powerStationPriceService.saveBatch(list);
        }
        return update;
    }

    @Override
    public StationStatusVO stationStatus(Integer id) {
        StationStatusVO vo = new StationStatusVO();
        List<Integer> gatewayStatus = baseMapper.getGatewayStatus(id);
        vo.setRunningStatus(gatewayStatus.isEmpty() || gatewayStatus.contains(0) ? 0 : 1);
        List<Integer> sysStatus = baseMapper.getSysStatus(id);
        if (sysStatus.isEmpty()) {
            vo.setSysStatus(0);
        } else {
            if (sysStatus.contains(0)) {
                vo.setSysStatus(2);
            } else if (sysStatus.contains(2) || sysStatus.contains(3)) {
                vo.setSysStatus(3);
            } else {
                vo.setSysStatus(1);
            }
        }
        vo.setProprietor(baseMapper.getUserData(id));
        return vo;
    }

    private MPJLambdaWrapper<PowerStation> lambdaJoinQueryDataScope() {
        SysUser user = getSysUser();
        MPJLambdaWrapper<PowerStation> wrapper = lambdaJoinQuery();
        if (SysUser.isProprietor(user.getUserType())) {
            wrapper.innerJoin("sys_user_station us on us.power_station_id = t.id and us.user_id = " + user.getUserId());
            return wrapper;
        }
        UserDistrict ud = getUd(user.getUserId());
        if (!ud.getAllCountry()) {
            if (!ud.getCountryIds().isEmpty() && !ud.getProvinceIds().isEmpty()) {
                wrapper.and(i -> i.in(PowerStation::getCountryId, ud.getCountryIds()).or().in(PowerStation::getProvinceId, ud.getProvinceIds()));
            } else if (!ud.getCountryIds().isEmpty()) {
                wrapper.in(PowerStation::getCountryId, ud.getCountryIds());
            } else if (!ud.getProvinceIds().isEmpty()) {
                wrapper.in(PowerStation::getProvinceId, ud.getProvinceIds());
            } else {
                wrapper.eq(PowerStation::getId, 0);
            }
        }
        //角色不是超级管理员就要按数据权限查询
        //数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限 5：仅本人数据权限）
        Set<Integer> dataScopes = user.getRoles().stream().map(SysRole::getDataScope).collect(Collectors.toSet());
        if (dataScopes.contains(1) || (dataScopes.contains(4) && user.getTenantId().equals(user.getDeptId()))) {
            wrapper.inSql(isNotAdminRole(user.getRoleIds()), PowerStation::getTenantId, dataScope(user.getTenantId()));
        } else if (dataScopes.contains(2)) {
            //自定义部门ID
            Set<Long> zdyDeptIds = user.getRoles().stream().filter(role -> role.getDataScope() == 2)
                    .flatMap(role -> role.getCustomDeptIds().stream()).collect(Collectors.toSet());
            if (dataScopes.contains(4)) {
                //查询当前机构全部部门
                List<Long> deptIds = Sql.getDeptIds(user.getTenantId());
                //移除掉需要查询的部门
                deptIds.remove(user.getDeptId());
                //异常需要查询的部门，剩余的就是不需要查询的部门
                deptIds.removeAll(zdyDeptIds);
                //查询全部数据不查询自定义之外的部门
                wrapper.inSql(PowerStation::getTenantId, dataScope(user.getTenantId()))
                        .notIn(!deptIds.isEmpty(), PowerStation::getDeptId, deptIds);
            } else if (dataScopes.contains(3)) {
                //只查询本部门加自定义部门
                zdyDeptIds.add(user.getDeptId());
                wrapper.in(PowerStation::getDeptId, zdyDeptIds);
            } else {
                wrapper.in(PowerStation::getDeptId, zdyDeptIds);
            }
        } else if (dataScopes.contains(4)) {
            //查询当前机构全部部门
            List<Long> deptIds = Sql.getDeptIds(user.getTenantId());
            deptIds.remove(user.getDeptId());
            //查询全部数据当前部门
            wrapper.inSql(PowerStation::getTenantId, dataScope(user.getTenantId())).notIn(!deptIds.isEmpty(), PowerStation::getDeptId, deptIds);
        } else if (dataScopes.contains(3)) {
            wrapper.eq(PowerStation::getDeptId, user.getDeptId());
        } else {
            wrapper.eq(PowerStation::getTenantId, user.getTenantId()).eq(PowerStation::getCreateId, user.getUserId());
        }
        return wrapper;
    }

    @Override
    public void page(PowerStationPageReqDTO dto) {
        MPJLambdaWrapper<PowerStation> wrapper = lambdaJoinQueryDataScope()
                .selectAll(PowerStation.class)
                .innerJoin(PowerStationExtend.class, PowerStationExtend::getId, PowerStation::getId)
                .eq(dto.getId() != null, PowerStation::getId, dto.getId())
                .eq(dto.getStationStatus() != null, PowerStation::getStationStatus, dto.getStationStatus())
                .eq(dto.getCountryId() != null, PowerStation::getCountryId, dto.getCountryId())
                .eq(dto.getProvinceId() != null, PowerStation::getProvinceId, dto.getProvinceId())
                .eq(StrUtil.isNotBlank(dto.getStationType()), PowerStation::getStationType, dto.getStationType())
                .between(dto.getBeginTime() != null && dto.getEndTime() != null, PowerStation::getCreateTime, dto.getBeginTime(), dto.getEndTime())
                .between(dto.getInstalledCapacityMin() != null && dto.getInstalledCapacityMax() != null, PowerStation::getInstalledCapacity, dto.getInstalledCapacityMin(), dto.getInstalledCapacityMax());
        if (StrUtil.isBlank(dto.getSortField()) || dto.getSort() == null || dto.getSort() == 2) {
            if (dto.isCollect()) {
                wrapper.orderByDesc(PowerStationCollect::getCreateTime);
            } else {
                wrapper.orderByDesc(PowerStation::getId);
            }
        } else {
            switch (dto.getSortField()) {
                case "stationName" -> wrapper.orderBy(true, dto.getSort() == 0, PowerStation::getStationName);
                case "installedCapacity" ->
                        wrapper.orderBy(true, dto.getSort() == 0, PowerStation::getInstalledCapacity);
                case "dayKwh" -> wrapper.orderBy(true, dto.getSort() == 0, PowerStationExtend::getDayKwh);
                case "kw" -> wrapper.orderBy(true, dto.getSort() == 0, PowerStationExtend::getKw);
                case "createTime" -> wrapper.orderBy(true, dto.getSort() == 0, PowerStation::getId);
            }
        }
        if (StrUtil.isNotBlank(dto.getStationName())) {
            if (Tools.isNum(dto.getStationName())) {
                wrapper.and(i -> i.like(PowerStation::getStationName, dto.getStationName()).or()
                        .eq(PowerStation::getId, Integer.valueOf(dto.getStationName())));
            } else {
                wrapper.like(PowerStation::getStationName, dto.getStationName());
            }
        }
        Long userId = getUserId();
        if (dto.isCollect()) {
            wrapper.innerJoin(PowerStationCollect.class, PowerStationCollect::getPowerStationId, PowerStation::getId)
                    .eq(PowerStationCollect::getUserId, userId);
        }
        List<PowerStation> list = wrapper.list();
        if (!list.isEmpty()) {
            List<Integer> ids = list.stream().flatMap(p -> Stream.of(p.getCountryId(), p.getProvinceId())).distinct().toList();
            Map<Integer, String> map = getDistrictName(ids);
            List<Integer> powerStationId = list.stream().map(PowerStation::getId).toList();
            List<Integer> collect = powerStationCollectService.lambdaQuery().eq(PowerStationCollect::getUserId, userId)
                    .in(PowerStationCollect::getPowerStationId, powerStationId).list().stream().map(PowerStationCollect::getPowerStationId).toList();
            Map<Integer, PowerStationExtend> extend = powerStationExtendService.lambdaQuery().in(PowerStationExtend::getId, powerStationId).list()
                    .stream().collect(Collectors.toMap(PowerStationExtend::getId, Function.identity()));
            Set<Long> tid = list.stream().map(PowerStation::getTenantId).collect(Collectors.toSet());
            Map<Long, String> tmap = baseMapper.getTenantList(join(tid)).stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
            Map<Integer, String> uMap = baseMapper.getUserDataList(join(powerStationId)).stream().collect(Collectors.toMap(IntStr::getK, IntStr::getV));
            for (PowerStation p : list) {
                p.setCountry(map.get(p.getCountryId()));
                p.setProvince(map.get(p.getProvinceId()));
                p.setCollect(collect.contains(p.getId()));
                p.setExtend(extend.get(p.getId()));
                p.setTenantName(tmap.get(p.getTenantId()));
                p.setProprietor(uMap.get(p.getId()));
                p.getExtend().setUpdateTime(p.getLastReportTime());
                p.setCreateTime(TimeZoneUtil.getZonedTime(p.getCreateTime(), p.getTimeZone()));
            }
        }
    }

    @Override
    @Transactional
    public boolean delete(Integer id) {
        Long count = stationDeviceService.lambdaQuery().eq(StationDevice::getPowerStationId, id).count();
        if (count > 0) {
            throw new ServiceException("该电站已绑定设备，请先移除设备，再进行该操作");
        }
        boolean update = lambdaUpdate().eq(PowerStation::getId, id)
                .set(PowerStation::getDelete, 1)
                .set(PowerStation::getDeleted, id)
                .update();
        baseMapper.deleteApiTokenStation(id);
        baseMapper.deleteUserStation(id);
        powerStationCollectService.delete(id);
        powerStationExtendService.removeById(id);
        powerStationPriceService.remove(Wrappers.<PowerStationPrice>lambdaQuery().eq(PowerStationPrice::getPowerStationId, id));
        RedisUtil.delete(List.of(
                CacheConstants.FAULT_NOTICE_KEY + id,
                CacheConstants.TIMEZONE + id
        ));
        return update;
    }

    @Override
    public boolean layout(LayoutDTO dto) {
        lambdaUpdate().eq(PowerStation::getId, dto.getId())
                .set(PowerStation::getLayout, dto.getLayout().toString())
                .update();
        remoteBizDataService.networking(dto.getId());
        return true;
    }

    @Override
    public boolean install(InstallDTO dto) {
        return lambdaUpdate().eq(PowerStation::getId, dto.getId())
                .set(PowerStation::getInstallImageUrl, JSON.toJSONString(dto.getInstallImageUrl()))
                .set(PowerStation::getInstallTime, LocalDateTime.now())
                .update();
    }

    @Override
    public boolean fault(MoreDTO dto) {
        String key = CacheConstants.FAULT_NOTICE_KEY + dto.getId();
        String value = JSON.toJSONString(dto.getFaultNotificationMethod());
        RedisUtil.set(key, value);
        return lambdaUpdate().eq(PowerStation::getId, dto.getId())
                .set(PowerStation::getFaultNotificationMethod, value)
                .set(PowerStation::getAllowOwnerViewLayout, dto.getAllowOwnerViewLayout())
                .set(PowerStation::getDefaultLayoutDisplay, dto.getDefaultLayoutDisplay())
                .set(PowerStation::getMaxComponentPower, dto.getMaxComponentPower())
                .set(dto.getReflux() != null, PowerStation::getReflux, dto.getReflux())
                .update();
    }

    @Override
    public List<KV> all() {
        return baseMapper.all(dataScopeSqlSuperId());
    }

    @Override
    public List<PowerStationSiteVO> allSite(Integer all) {
//        if (all != null && all == 1) {
//            return baseMapper.allSite2(dataScopeSqlSuperId());
//        }
        return baseMapper.allSite(dataScopeSqlSuperId());
    }

    @Override
    public boolean collect(Integer id) {
        PowerStationCollect collect = new PowerStationCollect(getUserId(), id);
        if (powerStationCollectService.lambdaQuery().eq(PowerStationCollect::getUserId, collect.getUserId())
                .eq(PowerStationCollect::getPowerStationId, collect.getPowerStationId()).exists()) {
            return powerStationCollectService.remove(Wrappers.<PowerStationCollect>lambdaQuery()
                    .eq(PowerStationCollect::getUserId, collect.getUserId())
                    .eq(PowerStationCollect::getPowerStationId, collect.getPowerStationId()));
        }
        return powerStationCollectService.save(collect);
    }

    @Override
    public StationReportVO report(Integer id) {
        StationReportVO vo = new StationReportVO();
        vo.setStation(getByIdThrow(id));
        if (CollUtil.isNotEmpty(vo.getStation().getModule())) {
            Set<Integer> vendorIds = vo.getStation().getModule().stream().map(Module::getVendorId).collect(Collectors.toSet());
            Set<Integer> productIds = vo.getStation().getModule().stream().map(Module::getProductId).collect(Collectors.toSet());
            Map<Integer, String> vendorMap = baseMapper.getVendors(join(vendorIds)).stream().collect(Collectors.toMap(IntStr::getK, IntStr::getV));
            Map<Integer, String> productMap = baseMapper.getProducts(join(productIds)).stream().collect(Collectors.toMap(IntStr::getK, IntStr::getV));
            for (Module module : vo.getStation().getModule()) {
                module.setVendorName(vendorMap.get(module.getVendorId()));
                module.setDeviceModel(productMap.get(module.getProductId()));
            }
        }
        Map<Integer, String> districtMap = getDistrictName(List.of(vo.getStation().getCountryId(), vo.getStation().getProvinceId()));
        vo.getStation().setCountry(districtMap.get(vo.getStation().getCountryId()));
        vo.getStation().setProvince(districtMap.get(vo.getStation().getProvinceId()));
        vo.setUsers(baseMapper.getUsers(id));
        for (SysUser user : vo.getUsers()) {
            user.setPhonenumber(Tools.getArea(user.getAreaCode()) + user.getPhonenumber());
        }
        Map<Integer, List<StationDevice>> map = stationDeviceService.mapList(id);
        vo.setEmus(map.getOrDefault(DeviceTypeEnum.EMU.getVal(), Collections.emptyList()));
        vo.setWns(map.getOrDefault(DeviceTypeEnum.WN.getVal(), Collections.emptyList()));
        if (vo.getStation().getReflux()) {
            vo.setDbs(map.getOrDefault(DeviceTypeEnum.DB.getVal(), Collections.emptyList()));
        } else {
            vo.setDbs(Collections.emptyList());
        }
        if (Objects.equals(vo.getStation().getPowerPriceStrategy(), 2)) {
            vo.setPrices(powerPrice(id));
        } else {
            vo.setPrices(Collections.emptyList());
        }
        vo.setDept(baseMapper.getTenant(vo.getStation().getTenantId()));
        vo.getDept().setPhone(Tools.getArea(vo.getDept().getAreaCode()) + vo.getDept().getPhone());
        return vo;
    }

    private final Color gray = new DeviceRgb(215, 215, 215);
    private final DateTimeFormatter formatter = DatePattern.createFormatter("yyyy年MM月dd日 HH:mm:ss");

    public Paragraph paragraph(PdfFont font, String text) {
        return new Paragraph(text).setHeight(20f).setVerticalAlignment(VerticalAlignment.MIDDLE)
                .setFont(font).setFontSize(12f).simulateBold();
    }

    private Cell cellTit(PdfFont font, int colspan, String text) {
        Cell cell = new Cell(1, colspan == 0 ? 1 : colspan).setHeight(20f).setVerticalAlignment(VerticalAlignment.MIDDLE).setBackgroundColor(gray);
        if (colspan == 0) {
            cell.setTextAlignment(TextAlignment.CENTER);
        } else {
            cell.setPaddingLeft(4f);
        }
        if (text == null) {
            return cell;
        }
        return cell.add(new Paragraph(text).setFont(font).setFontSize(10.5f).simulateBold());
    }

    private Cell cellVal(PdfFont font, int colspan, String text) {
        Cell cell = new Cell(1, colspan == 0 ? 1 : colspan).setHeight(20f).setVerticalAlignment(VerticalAlignment.MIDDLE);
        if (colspan == 0) {
            cell.setTextAlignment(TextAlignment.CENTER);
        } else {
            cell.setPaddingLeft(4f);
        }
        if (text == null) {
            return cell;
        }
        return cell.add(new Paragraph(text).setFont(font).setFontSize(10.5f));
    }

    @Override
    public List<String> reportImg(Integer id) throws Exception {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        reportDownload(id, outputStream);
        List<String> list = new ArrayList<>();
        try (PDDocument document = PDDocument.load(new ByteArrayInputStream(outputStream.toByteArray()))) {
            PDFRenderer pdfRenderer = new PDFRenderer(document);
            for (int i = 0; i < document.getNumberOfPages(); i++) {
                BufferedImage image = pdfRenderer.renderImageWithDPI(i, 300);
                ByteArrayOutputStream output = new ByteArrayOutputStream();
                ImageIO.write(image, "JPEG", output);
                list.add("data:image/jpeg;base64," + Base64.getEncoder().encodeToString(output.toByteArray()));
            }
        }
        return list;
    }

    @Override
    public void reportShare(ReportShareDTO dto) throws Exception {
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        PowerStation station = reportDownload(dto.getId(), stream);
        String content = StrUtil.format(I18nUtil.get("开站报告邮件"), station.getStationName(), station.getId(), station.getStationName(),
                station.getInstalledCapacity(), station.getStationDetails());
        MailUtil.sendFile(dto.getEmail(), station.getStationName(), true, content, station.getStationName() + ".pdf", stream);
    }

    @Override
    public PowerStation reportDownload(Integer id, OutputStream outputStream) throws Exception {
        HttpServletRequest request = ServletUtils.getRequest();
        Locale local = LanguageEnum.getLocal(request);
        String lang = LanguageEnum.getLang(request);
        StationReportVO data = report(id);
        PdfFont font = PdfFontFactory.createFont(FileUtil.isWindows() ? "C:/Windows/Fonts/simsun.ttc,0" : "/usr/share/fonts/STSONG.TTF", PdfEncodings.IDENTITY_H);
        PdfDocument pdfDocument = new PdfDocument(new PdfWriter(outputStream));
        try (Document document = new Document(pdfDocument)) {
            document.setTopMargin(50f);
            document.add(new Paragraph(I18nUtil.get("开站报告", local)).setFont(font).setFontSize(16f).simulateBold().setTextAlignment(TextAlignment.CENTER));
            document.add(new Paragraph(formatter.format(LocalDateTime.now())).setFont(font).setFontSize(10.5f).setTextAlignment(TextAlignment.RIGHT));

            document.add(paragraph(font, I18nUtil.get("1、系统信息", local)));
            Table table = new Table(UnitValue.createPercentArray(3)).useAllAvailableWidth();
            table.addCell(cellTit(font, 1, I18nUtil.get("电站名称", local)));
            table.addCell(cellTit(font, 1, I18nUtil.get("电站类型", local)));
            table.addCell(cellTit(font, 1, I18nUtil.get("装机容量", local)));
            table.addCell(cellVal(font, 1, data.getStation().getStationName()));
            table.addCell(cellVal(font, 1, baseMapper.getLabel(lang, "wh_station_type", data.getStation().getStationType())));
            table.addCell(cellVal(font, 1, data.getStation().getInstalledCapacity().stripTrailingZeros().toPlainString() + "kW"));
            table.addCell(cellTit(font, 1, I18nUtil.get("国家", local)));
            table.addCell(cellTit(font, 1, I18nUtil.get("地区", local)));
            table.addCell(cellTit(font, 1, I18nUtil.get("邮政编码", local)));
            table.addCell(cellVal(font, 1, data.getStation().getCountry()));
            table.addCell(cellVal(font, 1, data.getStation().getProvince()));
            table.addCell(cellVal(font, 1, data.getStation().getPostalCode()));
            table.addCell(cellTit(font, 3, I18nUtil.get("详细地址", local)));
            table.addCell(cellVal(font, 3, data.getStation().getStationDetails()));
            document.add(table);

            document.add(paragraph(font, I18nUtil.get("2、业主账号", local)).setMarginTop(30f));
            table = new Table(UnitValue.createPercentArray(4)).useAllAvailableWidth();
            table.addCell(cellTit(font, 1, I18nUtil.get("登录账号", local)));
            table.addCell(cellTit(font, 1, I18nUtil.get("姓名", local)));
            table.addCell(cellTit(font, 1, I18nUtil.get("邮箱", local)));
            table.addCell(cellTit(font, 1, I18nUtil.get("电话", local)));
            for (SysUser user : data.getUsers()) {
                table.addCell(cellVal(font, 1, user.getUserName()));
                table.addCell(cellVal(font, 1, user.getNickName()));
                table.addCell(cellVal(font, 1, user.getEmail()));
                table.addCell(cellVal(font, 1, user.getPhonenumber()));
            }
            document.add(table);

            document.add(paragraph(font, I18nUtil.get("3、EMU-{0}个", local, data.getEmus().size())).setMarginTop(30f));
            document.add(table(font, data.getEmus(), local));

            document.add(paragraph(font, I18nUtil.get("4、微逆-{0}个", local, data.getWns().size())).setMarginTop(30f));
            document.add(table(font, data.getWns(), local));

            document.add(paragraph(font, I18nUtil.get("5、电表-{0}个", local, data.getDbs().size())).setMarginTop(30f));
            table = new Table(UnitValue.createPercentArray(4)).useAllAvailableWidth();
            table.addCell(cellTit(font, 0, I18nUtil.get("接入位置", local)));
            table.addCell(cellTit(font, 0, I18nUtil.get("序列号", local)));
            table.addCell(cellTit(font, 0, I18nUtil.get("型号", local)));
            table.addCell(cellTit(font, 0, I18nUtil.get("数据更新时间", local)));
            for (StationDevice device : data.getDbs()) {
                table.addCell(cellVal(font, 0, switch (device.getPowerGridSeat()) {
                    case 0 -> I18nUtil.get("负载侧电表", local);
                    case 1 -> I18nUtil.get("光伏测电表", local);
                    case 2 -> I18nUtil.get("并网点电表", local);
                    default -> null;
                }));
                table.addCell(cellVal(font, 0, device.getNumber()));
                table.addCell(cellVal(font, 0, device.getDeviceModel()));
                table.addCell(cellVal(font, 0, device.getLastReportTime() == null ? null : DatePattern.NORM_DATETIME_FORMATTER.format(device.getLastReportTime())));
            }
            document.add(table);

            document.add(paragraph(font, I18nUtil.get("6、电价设置", local)).setMarginTop(30f));
            Map<String, String> currencyUnitMap = DictUtils.getDictCacheMap("currency_unit");
            if (data.getStation().getPowerPriceStrategy() != null) {
                if (data.getStation().getPowerPriceStrategy() == 1) {
                    table = new Table(UnitValue.createPercentArray(4)).useAllAvailableWidth();
                    table.addCell(cellTit(font, 0, I18nUtil.get("电价类型", local)));
                    table.addCell(cellTit(font, 0, I18nUtil.get("买入电价", local)));
                    table.addCell(cellTit(font, 0, I18nUtil.get("卖出电价", local)));
                    table.addCell(cellTit(font, 0, I18nUtil.get("货币单位", local)));
                    table.addCell(cellVal(font, 0, I18nUtil.get("统一电价", local)));
                    table.addCell(cellVal(font, 0, Optional.ofNullable(data.getStation().getBuyPowerPrice()).orElse(BigDecimal.ZERO).stripTrailingZeros().toPlainString()));
                    table.addCell(cellVal(font, 0, Optional.ofNullable(data.getStation().getPowerPrice()).orElse(BigDecimal.ZERO).stripTrailingZeros().toPlainString()));
                    table.addCell(cellVal(font, 0, currencyUnitMap.get(data.getStation().getMonetaryUnit())));
                    document.add(table);
                } else {
                    table = new Table(UnitValue.createPercentArray(3)).useAllAvailableWidth();
                    table.addCell(cellTit(font, 0, I18nUtil.get("电价类型", local)));
                    table.addCell(cellTit(font, 0, I18nUtil.get("卖出电价", local)));
                    table.addCell(cellTit(font, 0, I18nUtil.get("货币单位", local)));
                    table.addCell(cellVal(font, 0, I18nUtil.get("分时电价", local)));
                    if (data.getStation().getPowerPrice() == null) {
                        table.addCell(cellVal(font, 0, null));
                    } else {
                        table.addCell(cellVal(font, 0, data.getStation().getPowerPrice().stripTrailingZeros().toPlainString()));
                    }
                    table.addCell(cellVal(font, 0, currencyUnitMap.get(data.getStation().getMonetaryUnit())));
                    document.add(table);
                    table = new Table(UnitValue.createPercentArray(4)).useAllAvailableWidth();
                    int i = 1;
                    for (PowerStationPriceMonthVO price : data.getPrices()) {
                        table.addCell(cellTitPrice(font, i++, price.getMonths(), local));
                        table.addCell(cellVal(font, 0, I18nUtil.get("时段名称", local)).simulateBold());
                        table.addCell(cellVal(font, 0, I18nUtil.get("开始时间", local)).simulateBold());
                        table.addCell(cellVal(font, 0, I18nUtil.get("结束时间", local)).simulateBold());
                        table.addCell(cellVal(font, 0, I18nUtil.get("买入电价", local)).simulateBold());
                        for (PowerStationPrice time : price.getTimes()) {
                            table.addCell(cellVal(font, 0, switch (time.getTimeBucket()) {
                                case 1 -> I18nUtil.get("尖时刻", local);
                                case 2 -> I18nUtil.get("峰时刻", local);
                                case 3 -> I18nUtil.get("平时刻", local);
                                case 4 -> I18nUtil.get("谷时刻", local);
                                default -> null;
                            }));
                            table.addCell(cellVal(font, 0, time.getBeginTime().toString()));
                            table.addCell(cellVal(font, 0, time.getEndTime().toString()));
                            table.addCell(cellVal(font, 0, time.getPrice().stripTrailingZeros().toPlainString()));
                        }
                    }
                    document.add(table);
                }
            }

            document.add(paragraph(font, I18nUtil.get("7、组件型号", local)).setMarginTop(30f));
            table = new Table(UnitValue.createPercentArray(2)).useAllAvailableWidth();
            table.addCell(cellTit(font, 1, I18nUtil.get("组件厂家", local)));
            table.addCell(cellTit(font, 1, I18nUtil.get("组件型号", local)));
            if (data.getStation().getModule() != null) {
                for (Module module : data.getStation().getModule()) {
                    table.addCell(cellVal(font, 1, module.getVendorName()));
                    table.addCell(cellVal(font, 1, module.getDeviceModel()));
                }
            }
            document.add(table);

            document.add(paragraph(font, I18nUtil.get("8、安装商", local)).setMarginTop(30f));
            table = new Table(UnitValue.createPercentArray(3)).useAllAvailableWidth();
            table.addCell(cellTit(font, 0, I18nUtil.get("安装商名称", local)));
            table.addCell(cellTit(font, 0, I18nUtil.get("联系电话", local)));
            table.addCell(cellTit(font, 0, I18nUtil.get("邮箱", local)));
            table.addCell(cellVal(font, 0, data.getDept().getDeptName()));
            table.addCell(cellVal(font, 0, data.getDept().getPhone()));
            table.addCell(cellVal(font, 0, data.getDept().getEmail()));
            document.add(table);

            ImageData logo = ImageDataFactory.create(FileUtil.isWindows() ? System.getProperty("user.dir") + "\\logo1.png" : "/www/wwwroot/jar/logo1.png");
            for (int i = 1; i <= pdfDocument.getNumberOfPages(); i++) {
                PdfPage page = pdfDocument.getPage(i);
                float y = page.getPageSize().getTop() - document.getTopMargin();
                //400 80 = 100 22
                //439 164 = 110 41
                Rectangle imageRect = new Rectangle(document.getLeftMargin(), y, 110f, 41f);
                new PdfCanvas(page).addImageFittedIntoRectangle(logo, imageRect, false);
            }
        }
        return data.getStation();
    }

    private Cell cellTitPrice(PdfFont font, int i, List<Integer> months, Locale local) {
        return new Cell(1, 4).setHeight(20f).setVerticalAlignment(VerticalAlignment.MIDDLE).setBackgroundColor(gray)
                .setPaddingLeft(4f).add(new Paragraph(new Text(I18nUtil.get("分时策略", local) + i + " （").simulateBold())
                        .add(new Text(String.join("、", months.stream().map(m -> I18nUtil.get(m + "月", local)).toList())))
                        .add(new Text("）").simulateBold()).setFont(font).setFontSize(10.5f));
    }

    private Table table(PdfFont font, List<StationDevice> list, Locale local) {
        Table table;
        if (list.isEmpty()) {
            table = new Table(UnitValue.createPercentArray(6)).useAllAvailableWidth();
        } else {
            table = new Table(new float[]{25f, 8f, 15f, 12f, 15f, 25f}).useAllAvailableWidth();
        }
        table.addCell(cellTit(font, 0, I18nUtil.get("序列号", local)));
        table.addCell(cellTit(font, 0, I18nUtil.get("型号", local)));
        table.addCell(cellTit(font, 0, I18nUtil.get("软件版本号", local)));
        table.addCell(cellTit(font, 0, I18nUtil.get("质保时长", local)));
        table.addCell(cellTit(font, 0, I18nUtil.get("质保截止期", local)));
        table.addCell(cellTit(font, 0, I18nUtil.get("数据更新时间", local)));
        for (StationDevice device : list) {
            table.addCell(cellVal(font, 0, device.getNumber()));
            table.addCell(cellVal(font, 0, device.getDeviceModel()));
            table.addCell(cellVal(font, 0, device.getSoftwareVersion()));
            if (device.getGuaranteePeriod() == null) {
                table.addCell(cellVal(font, 0, null));
            } else {
                table.addCell(cellVal(font, 0, I18nUtil.get("{0}月", local, device.getGuaranteePeriod())));
            }
            table.addCell(cellVal(font, 0, device.getFinishPeriod()));
            if (device.getLastReportTime() == null) {
                table.addCell(cellVal(font, 0, null).add(new Paragraph().setWidth(100f)));
            } else {
                table.addCell(cellVal(font, 0, DatePattern.NORM_DATETIME_FORMATTER.format(device.getLastReportTime())));
            }
        }
        return table;
    }

    @Override
    public Map<Integer, String> getDistrictName(List<Integer> districtIds) {
        return baseMapper.getDistrict(join(districtIds)).stream().collect(Collectors.toMap(IntStr::getK, IntStr::getV));
    }

    /**
     * 电站状态 （0建设中 1在线 2离线 3告警）
     */
    @Override
    public PowerStationStatisticsVO queryPowerStationStatus(Integer collect) {
        MPJLambdaWrapper<PowerStation> lambdaWrapper = lambdaJoinQueryDataScope()
                .select(PowerStation::getStationStatus)
                .selectFunc(DefaultFuncEnum.COUNT, PowerStation::getId, PowerStation::getId)
                .selectFunc(DefaultFuncEnum.SUM, PowerStation::getInstalledCapacity, PowerStation::getInstalledCapacity);
        // 统计收藏电站
        if (collect != null && collect == 1) {
            Long userId = getUserId();
            MPJLambdaWrapper<PowerStation> wrapper = lambdaJoinQueryDataScope();
            wrapper.innerJoin(PowerStationCollect.class, PowerStationCollect::getPowerStationId, PowerStation::getId)
                    .eq(PowerStationCollect::getUserId, userId);
            List<PowerStation> list = wrapper.list();
            if (!list.isEmpty()) {
                List<Integer> powerStationId = list.stream().map(PowerStation::getId).toList();
                lambdaWrapper.in(PowerStation::getId, powerStationId);
            } else {
                lambdaWrapper.in(PowerStation::getId, 0);
            }
        }

        lambdaWrapper.groupBy(PowerStation::getStationStatus);
        List<PowerStation> powerStationList = lambdaWrapper.list();
        Map<Integer, PowerStation> dataMap = lambdaWrapper.list().stream().collect(Collectors.toMap(PowerStation::getStationStatus, Function.identity()));
        Map<Integer, Integer> map = new HashMap<>();
        int total = 0;
        BigDecimal installedCapacity = BigDecimal.ZERO;
        for (int i = 0; i <= 3; i++) {
            PowerStation powerStation = dataMap.get(i);
            if (powerStation != null) {
                map.put(i, powerStation.getId());
                total += powerStation.getId();
                installedCapacity = installedCapacity.add(powerStation.getInstalledCapacity());
            } else {
                map.put(i, 0);
            }
        }
        return new PowerStationStatisticsVO(total, map, installedCapacity);
    }

    /**
     * 电站类型（0:屋顶光伏 1:阳台光伏 2:商业光伏）
     */
    @Override
    public PowerStationStatisticsVO queryPowerStationType() {
        Map<Integer, Integer> map = lambdaJoinQueryDataScope()
                .select(PowerStation::getStationType)
                .selectFunc(DefaultFuncEnum.COUNT, PowerStation::getId, PowerStation::getId)
                .groupBy(PowerStation::getStationType)
                .list().stream().collect(Collectors.toMap(s -> Integer.valueOf(s.getStationType()), PowerStation::getId));
        int total = 0;
        for (int i = 0; i <= 2; i++) {
            map.putIfAbsent(i, 0);
            total += map.get(i);
        }
        return new PowerStationStatisticsVO(total, map);
    }

    /**
     * 电站统计
     */
    @Override
    public List<StationNumStatisticsVO> queryPowerStationStatistics() {
        List<StationNumStatisticsVO> list = new ArrayList<>(2);
        YearMonth now = YearMonth.now();
        int endYear = now.getYear();
        int beginYear = endYear - 1;
        list.add(new StationNumStatisticsVO(beginYear, new ArrayList<>(12)));
        list.add(new StationNumStatisticsVO(endYear, new ArrayList<>(12)));
        Map<Integer, Integer> map = baseMapper.queryPowerStationStatistics(beginYear, endYear, dataScopeSqlSuperId())
                .stream().collect(Collectors.toMap(IntInt::getK, IntInt::getV));
        int month = beginYear * 100;
        for (int i = 1; i <= 12; i++) {
            list.get(0).getData().add(new StationStatisticsVO(i, map.getOrDefault(month + i, 0)));
        }
        month = endYear * 100;
        for (int i = 1; i <= 12; i++) {
            if (now.getMonthValue() >= i) {
                list.get(1).getData().add(new StationStatisticsVO(i, map.getOrDefault(month + i, 0)));
            } else {
                list.get(1).getData().add(new StationStatisticsVO(i, "-"));
            }
        }
        return list;
    }

    @Override
    @Transactional
    public boolean transfer(TransferDTO dto) {
        SysUser user = baseMapper.getUser(dto.getTenantId());
        if (user == null) {
            throw new ServiceException("该机构下不存在管理员");
        }
        baseMapper.transferDeviceAlarm(dto.getId(), dto.getTenantId());
        baseMapper.transferDeviceReplaceRecord(dto.getId(), dto.getTenantId());
        baseMapper.transferPowerStation(dto.getId(), dto.getTenantId(), user.getDeptId(), user.getUserId());
        baseMapper.transferStationDevice(dto.getId(), dto.getTenantId());
        baseMapper.transferStationDeviceRemote(dto.getId(), dto.getTenantId());
        return true;
    }

    @Override
    public PowerInfoVO owner(Integer id) {
        PowerInfoVO vo = new PowerInfoVO();
        vo.setProprietor(baseMapper.getUserData(id));
        Map<Integer, Integer> map = baseMapper.deviceDataById(id)
                .stream().collect(Collectors.toMap(StationDevice::getDeviceType, StationDevice::getWnNum));
        vo.setEmu(map.getOrDefault(0, 0));
        vo.setWn(map.getOrDefault(1, 0));
        return vo;
    }

    @Override
    public HomeMapDataVO mapData() {
        LocalDateTime byBegin = YearMonth.now().atDay(1).atStartOfDay();
        LocalDateTime byEnd = byBegin.plusMonths(1).plusSeconds(-1);
        LocalDateTime syBegin = byBegin.plusYears(-1);
        LocalDateTime syEnd = byEnd.plusYears(-1);
        List<HomeMapDataVO> stationList = baseMapper.stationData(dataScopeSqlSuperId(), byBegin, byEnd, syBegin, syEnd);
        List<Integer> wnList = baseMapper.wnData(dataScopeSqlSuper(), byBegin, byEnd, syBegin, syEnd);
        return new HomeMapDataVO(stationList, wnList);
    }

    @Override
    public JSONObject weather(Integer id) {
        String key = "weather:" + id;
        JSONObject json = RedisUtil.getJSONObject(key);
        if (json != null) {
            return json;
        }
        synchronized (id.toString()) {
            PowerStation station = baseMapper.getLonLat(id);
            if (station != null) {
                HttpHeaders headers = new HttpHeaders();
                headers.set("X-APISpace-Token", "qhq65bbb6guf2vfk03s08bbiojntb048");
                HttpEntity<String> entity = new HttpEntity<>(headers);
                String param = station.getLongitude() + "," + station.getLatitude();
                String url = "https://eolink.o.apispace.com/456456/weather/v001/now?lonlat=" + param;
                JSONObject body;
                try {
                    body = restTemplate.exchange(url, HttpMethod.GET, entity, JSONObject.class).getBody();
                } catch (HttpClientErrorException e) {
                    log.error("天气接口调用失败:" + e.getResponseBodyAsString());
                    throw new ServiceException("天气接口调用失败");
                }
                if (body != null) {
                    JSONObject realtime = body.getJSONObject("result").getJSONObject("realtime");
                    String code = realtime.getString("code");
                    realtime.put("textEn", WeatherPhenomenaEnum.getEn(code, station.getTimeZone()));
                    String icon = WeatherPhenomenaEnum.getIcon(code, station.getTimeZone());
                    realtime.put("img", "https://whyn.oss-cn-beijing.aliyuncs.com/wh/weather/" + icon + ".png");
                    RedisUtil.set(key, realtime.toString(), Duration.ofHours(1));
                    return realtime;
                }
            }
            return null;
        }
    }

    @Override
    public void permissionPage(PageReqDTO dto) {
        baseMapper.getPermissionList(getUserId());
    }

    @Override
    public boolean permission(PermissionDTO dto) {
        return SqlHelper.retBool(baseMapper.updatePermission(getUserId(), dto.getId(), dto.getPermission()));
    }

}
