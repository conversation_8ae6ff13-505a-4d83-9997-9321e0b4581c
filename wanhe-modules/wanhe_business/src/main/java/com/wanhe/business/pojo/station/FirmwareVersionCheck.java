package com.wanhe.business.pojo.station;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/11/1
 */
@Data
public class FirmwareVersionCheck {

    @Schema(description = "更新时间")
    private LocalDateTime updateTime = LocalDateTime.now();

    @Schema(description = "总排量")
    private BigDecimal cc = BigDecimal.ZERO;

    @Schema(description = "相当于植树")
    private BigDecimal tree = BigDecimal.ZERO;

}
