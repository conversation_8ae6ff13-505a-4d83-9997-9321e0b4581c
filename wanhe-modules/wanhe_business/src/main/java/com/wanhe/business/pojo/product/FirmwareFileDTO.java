package com.wanhe.business.pojo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-10-26
 */
@Data
@Schema(name = "FirmwareFileDTO", description = "固件文件表")
public class FirmwareFileDTO {

    @Schema(description = "固件类型")
    @NotNull
    private Integer type;

    @Schema(description = "设备类型")
    @NotNull
    private Integer deviceType;

    @Schema(description = "设备型号编号")
    @NotNull
    private Integer productId;

    @Schema(description = "版本号")
    @NotBlank
    @Pattern(regexp = "^[0-9]{1,3}(\\.[0-9]{1,3}){2}$", message = "固件版本号格式不正确")
    private String versionNumber;

    @Schema(description = "文件名称")
    @NotBlank
    private String fileName;

    @Schema(description = "文件地址")
    @NotBlank
    private String filePath;

    @Schema(description = "备注")
    private String description;

    @Schema(description = "是否默认开启")
    @NotNull
    private Boolean defaultEnabled;

}
