package com.wanhe.business.controller;

import com.github.pagehelper.Page;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.LanguageEnum;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.wanhe.business.domain.Fault;
import com.wanhe.business.pojo.product.FaultDTO;
import com.wanhe.business.pojo.product.FaultPageReqDTO;
import com.wanhe.business.pojo.product.FaultUpdateDTO;
import com.wanhe.business.pojo.product.FaultVO;
import com.wanhe.business.service.IFaultService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 故障库
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@RestController
@RequestMapping("/fault")
public class FaultController extends BaseController {

    @Resource
    private IFaultService faultService;

    /**
     * 新增 business:fault:add
     */
    @RequiresPermissions("business:fault:add")
    @Log(title = "故障库", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@Validated @RequestBody FaultDTO dto) {
        return R.ok(faultService.add(dto));
    }

    /**
     * 修改 business:fault:edit
     */
    @RequiresPermissions("business:fault:edit")
    @Log(title = "故障库", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@Validated @RequestBody FaultUpdateDTO dto) throws Exception {
        return R.ok(faultService.update(dto));
    }

    /**
     * 详情
     */
    @GetMapping(value = "/{id}")
    public R<Fault> getInfo(@PathVariable Integer id) {
        return R.ok(faultService.getById(id));
    }

    /**
     * 导出 business:fault:export
     */
    @Log(title = "故障库", businessType = BusinessType.EXPORT)
    @RequiresPermissions("business:fault:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, HttpServletRequest request, FaultPageReqDTO dto) {
        List<Fault> list = faultService.page(dto);
        ExcelUtil<Fault> util = new ExcelUtil<>(Fault.class);
        util.setLocal(LanguageEnum.getLocal(request));
        util.exportExcel(response, list, "故障库数据");
    }

    /**
     * 导入 business:fault:import
     */
    @Log(title = "故障库", businessType = BusinessType.IMPORT)
    @RequiresPermissions("business:fault:import")
    @PostMapping("/import")
    public R<Boolean> importData(HttpServletRequest request, MultipartFile file) throws Exception {
        ExcelUtil<Fault> util = new ExcelUtil<>(Fault.class);
        util.setLocal(LanguageEnum.getLocal(request));
        List<Fault> list = util.importExcel(file.getInputStream());
        return R.ok(faultService.importData(list));
    }

    /**
     * 分页 business:fault:page
     */
    @RequiresPermissions("business:fault:page")
    @GetMapping("/page")
    public TableDataInfo<Fault> page(@Validated FaultPageReqDTO dto) {
        Page<Fault> page = startPage(dto);
        faultService.page(dto);
        return getDataTable(page);
    }

    /**
     * 删除 business:fault:remove
     */
    @RequiresPermissions("business:fault:remove")
    @Log(title = "故障库", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public R<Boolean> remove(@PathVariable Integer id) {
        return R.ok(faultService.delete(id));
    }

    /**
     * 批量删除
     *
     * @param ids id列表
     * @return {@link R }<{@link Void }>
     */
    @RequiresPermissions("business:fault:remove")
    @Log(title = "故障库", businessType = BusinessType.DELETE)
    @DeleteMapping("/batch")
    public R<Void> batchRemove(@RequestParam("ids") List<Integer> ids) {
        faultService.batchDelete(ids);
        return R.ok();
    }

    /**
     * 查询全部故障数据
     */
    @GetMapping("/all")
    public R<List<FaultVO>> all() {
        return R.ok(faultService.all());
    }

}
