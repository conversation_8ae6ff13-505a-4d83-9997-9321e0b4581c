package com.wanhe.business.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.ruoyi.common.core.domain.IntStr;
import com.wanhe.business.domain.FirmwareFile;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @date 2024-10-26
*/
public interface FirmwareFileMapper extends MPJBaseMapper<FirmwareFile> {

    @Select("select count(*) from wh_firmware_file where product_id = #{productId} and deleted = 0")
    int getCountByProductId(Integer productId);

    @Select("""
    select f.id as k, f.version_number as v
    from wh_firmware_file f inner join wh_product p on p.product_name = #{deviceModel} and p.id = f.product_id
    and f.type = #{type} and f.device_type = #{deviceType} and f.deleted = 0
    """)
    List<IntStr> all(Integer type, Integer deviceType, String deviceModel);

}
