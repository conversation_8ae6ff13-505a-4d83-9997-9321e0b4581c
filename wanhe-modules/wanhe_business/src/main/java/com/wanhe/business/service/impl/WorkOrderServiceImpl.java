package com.wanhe.business.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.enums.LanguageEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.I18nUtil;
import com.ruoyi.common.core.utils.TimeZoneUtil;
import com.ruoyi.common.core.utils.bean.BeanUtils;
import com.ruoyi.common.redis.RedisUtil;
import com.ruoyi.common.security.service.impl.BaseServiceImpl;
import com.ruoyi.system.api.domain.SysUser;
import com.wanhe.business.domain.*;
import com.wanhe.business.mapper.*;
import com.wanhe.business.pojo.product.FaultVO;
import com.wanhe.business.pojo.workOrder.dto.*;
import com.wanhe.business.pojo.workOrder.vo.WorkOrderVO;
import com.wanhe.business.service.IWorkOrderService;
import com.wanhe.common.mail.MailUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.Map;

/**
 * 工单服务impl
 *
 * <AUTHOR>
 * @date 2025/06/21
 */
@Service
@AllArgsConstructor
public class WorkOrderServiceImpl extends BaseServiceImpl<WorkOrderMapper, WorkOrder> implements IWorkOrderService {

    private final OrderProcessMapper orderProcessMapper;

    private final OrderEvaluateMapper orderEvaluateMapper;

    private final DeviceAlarmMapper deviceAlarmMapper;

    private final PowerStationMapper powerStationMapper;

    private final FaultMapper faultMapper;

    @Override
    public void page(WorkOrderPageDTO dto) {
        String orderBy = "wo.commit_time DESC";
        if (dto.getCommitTimeAsc() != null) {
            orderBy = dto.getCommitTimeAsc() ? "wo.commit_time ASC" : "wo.commit_time DESC";
        }
        if (dto.getConfirmTimeAsc() != null) {
            orderBy = dto.getConfirmTimeAsc() ? "process.confirm_time ASC" : "process.confirm_time DESC";
        }
        if (dto.getCompletionTimeAsc() != null) {
            orderBy = dto.getCompletionTimeAsc() ? "process.completion_time ASC" : "process.completion_time DESC";
        }
        orderBy += ", wo.id DESC";

        SysUser sysUser = getSysUser();
        Long deptId = sysUser.getDeptId();
        baseMapper.page(dto, sysUser.getUserId(), deptId, orderBy);
    }

    @Override
    public WorkOrderVO getDetail(Long orderId) {
        WorkOrderVO detail = baseMapper.getDetail(orderId);
        if (detail.getFaultCode() != null) {
            FaultVO faultVO = faultMapper.getByKey(detail.getFaultKey(), LanguageEnum.getLang());
            detail.setFaultDescription(faultVO.getFaultDescription());
            detail.setResolutionSuggestion(faultVO.getResolutionSuggestion());
        }
        return detail;
    }

    @Override
    public void saveWorkOrder(WorkOrderDTO dto) {
        WorkOrder workOrder = new WorkOrder();
        BeanUtils.copyBeanProp(workOrder, dto);

        fillWorkOrder(workOrder);
        // 生成工单编号
        workOrder.setOrderSn(getNextOrderSn(workOrder.getCommitTime()));

        baseMapper.insert(workOrder);
    }

    private static String getNextOrderSn(LocalDateTime commitTime) {

        String redisKey = commitTime.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        Long s = RedisUtil.get(redisKey, Long.class);
        if (s == null) {
            RedisUtil.set(redisKey, 1L, Duration.of(60 * 60 * 48, ChronoUnit.SECONDS));
            s = 1L;
        } else {
            s = RedisUtil.incr(redisKey);
        }
        return redisKey + String.format("%04d", s);
    }

    /**
     * 填写工单
     *
     * @param workOrder 工单
     */
    private void fillWorkOrder(WorkOrder workOrder) {
        workOrder.setUserId(getUserId());
        // 填充故障信息
        if (workOrder.getFaultId() != null) {
            DeviceAlarm deviceAlarm = deviceAlarmMapper.selectById(workOrder.getFaultId());
            if (deviceAlarm != null) {
                workOrder.setFaultCode(deviceAlarm.getAlarmCode());
                workOrder.setFaultKey(deviceAlarm.getAlarmKey());
                workOrder.setFaultStartTime(deviceAlarm.getOccurredAt());
                workOrder.setFaultEndTime(deviceAlarm.getResolvedAt());
            }
        }
        // 填充确认部门信息
        if (workOrder.getStationId() != null) {
            PowerStation powerStation = powerStationMapper.selectById(workOrder.getStationId());
            Long deptId = powerStation.getDeptId();
            workOrder.setConfirmDeptId(deptId);
        }
    }

    @Override
    public void updateWorkOrder(WorkOrderDTO dto) {

        WorkOrder workOrder = baseMapper.selectById(dto.getId());
        if (workOrder == null) {
            throw new ServiceException("工单不存在");
        }
        if (workOrder.getOrderStatus() != 1) {
            throw new ServiceException("工单已开始处理，无法修改");
        }
        BeanUtils.copyBeanProp(workOrder, dto);
        baseMapper.updateById(workOrder);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addProcessor(ProcessorAddDTO dto) {

        WorkOrder workOrder = baseMapper.selectById(dto.getOrderId());
        if (workOrder == null) {
            throw new ServiceException("工单不存在");
        }
        if (workOrder.getOrderStatus() == 3) {
            throw new ServiceException("工单已完成");
        }
        OrderProcess processorInfo = baseMapper.getProcessorInfo(dto.getProcessorId());
        if (processorInfo == null) {
            throw new ServiceException("处理人不存在");
        }

        processorInfo.setOrderId(dto.getOrderId());
        SysUser sysUser = getSysUser();
        processorInfo.setConfirmedBy(sysUser.getNickName());

        LambdaQueryWrapper<OrderProcess> queryWrapper = new LambdaQueryWrapper<OrderProcess>()
                .eq(OrderProcess::getOrderId, dto.getOrderId());
        OrderProcess orderProcess = orderProcessMapper.selectOne(queryWrapper);
        if (orderProcess != null) {
            processorInfo.setId(orderProcess.getId());
            orderProcessMapper.updateById(processorInfo);
        } else {
            processorInfo.setConfirmTime(dto.getConfirmTime());

            orderProcessMapper.insert(processorInfo);
            baseMapper.addConfirmPerson(sysUser.getUserId(), 2, dto.getOrderId());
        }

        // todo 发送邮件 通知处理人
        if (processorInfo.getProcessorEmail() != null) {
            String content = StrUtil.format(I18nUtil.get("工单处理通知邮件"), workOrder.getOrderSn(), workOrder.getCommitTime().format(TimeZoneUtil.yyyyMMddHHmmss),
                    workOrder.getStationName(),  workOrder.getDeviceNumber(), workOrder.getDeviceModel(), workOrder.getOrderSn(), workOrder.getProblemDescription(),
                    workOrder.getContact(), workOrder.getEmail(), workOrder.getAddress() == null ? "-" : workOrder.getAddress());
            MailUtil.send(processorInfo.getProcessorEmail(), I18nUtil.get("工单处理通知"), content, true);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOrderProcess(OrderProcessDTO orderProcess) {

        WorkOrder workOrder = baseMapper.selectById(orderProcess.getOrderId());
        if (workOrder == null) {
            throw new ServiceException("工单不存在");
        }
        if (workOrder.getOrderStatus() == 3) {
            throw new ServiceException("工单已完成");
        }

        LambdaQueryWrapper<OrderProcess> queryWrapper = new LambdaQueryWrapper<OrderProcess>()
                .eq(OrderProcess::getOrderId, orderProcess.getOrderId())
                .last("LIMIT 1");
        OrderProcess process = orderProcessMapper.selectOne(queryWrapper);
        if (process == null) {
            throw new ServiceException("工单未分配处理人");
        }

        BeanUtils.copyBeanProp(process, orderProcess);
        orderProcessMapper.updateById(process);

        // 更新工单状态
        if (orderProcess.getCompleted()) {

            if (orderProcess.getSolutionStatus() == 0) {
                throw new ServiceException("工单未解决，无法完成");
            }

            baseMapper.updateOrderStatus(orderProcess.getOrderId(), 3);
        }

    }

    @Override
    public void addOrderEvaluate(OrderEvaluateDTO orderEvaluate) {
        WorkOrder workOrder = baseMapper.selectById(orderEvaluate.getOrderId());
        if (workOrder == null) {
            throw new ServiceException("工单不存在");
        }
        if (workOrder.getOrderStatus() != 3) {
            throw new ServiceException("工单未完成");
        }

        LambdaQueryWrapper<OrderEvaluate> queryWrapper = new LambdaQueryWrapper<OrderEvaluate>()
                .eq(OrderEvaluate::getOrderId, orderEvaluate.getOrderId());
        Long count = orderEvaluateMapper.selectCount(queryWrapper);
        if (count != 0) {
            throw new ServiceException("工单已评价");
        }

        OrderEvaluate evaluate = new OrderEvaluate();
        BeanUtils.copyBeanProp(evaluate, orderEvaluate);
        evaluate.setScore(orderEvaluate.getServiceScore().add(orderEvaluate.getResponseScore())
                .add(orderEvaluate.getHandleScore()).add(orderEvaluate.getNpsScore())
                .divide(BigDecimal.valueOf(4d), 4, RoundingMode.HALF_UP));
        orderEvaluateMapper.insert(evaluate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteWorkOrder(Long orderId) {
        WorkOrder workOrder = baseMapper.selectById(orderId);
        if (workOrder == null) {
            throw new ServiceException("工单不存在");
        }
        if (workOrder.getOrderStatus() != 1) {
            throw new ServiceException("工单已处理，无法撤销");
        }
        baseMapper.deleteById(orderId);

        Map<String, Object> params = new HashMap<>();
        params.put("order_id", orderId);
        orderProcessMapper.deleteByMap(params);
        orderEvaluateMapper.deleteByMap(params);
    }

    @Override
    public Integer getIncompleteOrderCount() {
        Long userId = getUserId();
        return baseMapper.getIncompleteOrderCount(userId);
    }

    @Override
    public Boolean judgeWorkOrderByFaultId(Long faultId) {
        LambdaQueryWrapper<WorkOrder> queryWrapper = new LambdaQueryWrapper<WorkOrder>()
                .eq(WorkOrder::getFaultId, faultId);
        return baseMapper.exists(queryWrapper);
    }
}
