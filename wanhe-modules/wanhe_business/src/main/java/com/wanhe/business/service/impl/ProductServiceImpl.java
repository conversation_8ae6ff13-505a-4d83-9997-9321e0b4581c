package com.wanhe.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.domain.IntStr;
import com.ruoyi.common.core.enums.DeviceTypeEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.Tools;
import com.ruoyi.common.security.service.impl.BaseServiceImpl;
import com.ruoyi.common.security.utils.DictUtils;
import com.wanhe.business.domain.Product;
import com.wanhe.business.domain.StationDevice;
import com.wanhe.business.domain.Vendor;
import com.wanhe.business.mapper.*;
import com.wanhe.business.pojo.product.ProductDTO;
import com.wanhe.business.pojo.product.ProductPageReqDTO;
import com.wanhe.business.pojo.product.ProductUpdateDTO;
import com.wanhe.business.service.IProductService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @date 2024-10-26
*/
@Service
public class ProductServiceImpl extends BaseServiceImpl<ProductMapper, Product> implements IProductService {

    @Resource
    private ProductCategoryMapper productCategoryMapper;
    @Resource
    private VendorMapper vendorMapper;
    @Resource
    private StationDeviceMapper stationDeviceMapper;
    @Resource
    private FirmwareFileMapper firmwareFileMapper;

    @Override
    public boolean add(ProductDTO dto) {
        Product entity = BeanUtil.copyProperties(dto, Product.class);
        return save(entity);
    }

    @Override
    @Transactional
    public boolean update(ProductUpdateDTO dto) {
        Product entity = getByIdThrow(dto.getId());
        String oldDeviceModel = entity.getProductName();
        Integer oldConnectNum = entity.getConnectNum();
        if (!entity.getProductName().equals(dto.getProductName())) {
            stationDeviceMapper.updateDeviceModel(entity.getProductName(), dto.getProductName());
        }
        BeanUtil.copyProperties(dto, entity);
        boolean b = updateById(entity);
        if (dto.getDeviceType() == DeviceTypeEnum.WN.getVal()) {
            LambdaUpdateWrapper<StationDevice> wrapper = Wrappers.<StationDevice>lambdaUpdate()
                    .eq(StationDevice::getDeviceType, DeviceTypeEnum.WN.getVal())
                    .eq(StationDevice::getDeviceModel, oldDeviceModel);
            if (!Objects.equals(oldDeviceModel, dto.getProductName())) {
                stationDeviceMapper.update(wrapper.set(StationDevice::getDeviceModel, dto.getProductName()));
            }
            if (!Objects.equals(oldConnectNum, dto.getConnectNum())) {
                stationDeviceMapper.update(wrapper.set(StationDevice::getConnectNum, dto.getConnectNum()));
            }
        }
        return b;
    }

    @Override
    @Transactional
    public boolean delete(String ids) {
        for (String idStr : ids.split(",")) {
            Integer id = Integer.parseInt(idStr);
            Product product = getByIdThrow(id);
            Integer deviceId = stationDeviceMapper.getIdByDeviceModel(product.getProductName());
            if (deviceId != null) {
                throw new ServiceException("产品已绑定设备，无法删除");
            }
            if (firmwareFileMapper.getCountByProductId(id) > 0) {
                throw new ServiceException("产品已绑定固体文件，无法删除");
            }
            lambdaUpdate().eq(Product::getId, id)
                    .set(Product::getDelete, 1)
                    .set(Product::getDeleted, id)
                    .update();
        }
        return true;
    }

    @Override
    public List<Product> page(ProductPageReqDTO dto) {
        List<Product> list = lambdaQuery()
                .eq(Product::getDeviceType, dto.getDeviceType())
                .eq(dto.getVendorId() != null, Product::getVendorId, dto.getVendorId())
                .like(StrUtil.isNotBlank(dto.getProductName()), Product::getProductName, dto.getProductName())
                .like(StrUtil.isNotBlank(dto.getProductCode()), Product::getProductCode, dto.getProductCode())
                .orderByAsc(Product::getId)
                .list();
        if (!list.isEmpty()) {
            Set<Integer> vid = list.stream().map(Product::getVendorId).filter(Objects::nonNull).collect(Collectors.toSet());
            if (ObjUtil.isNotEmpty(vid)) {
                Map<Integer, String> map = vendorMapper.listByIds(join(vid)).stream().collect(Collectors.toMap(Vendor::getId, Vendor::getVendorName));
                list.forEach(p -> p.setVendorName(map.get(p.getVendorId())));
            }
            if (dto.isExport()) {
                int count = 1;
                for (Product product : list) {
                    product.setNumber(count++);
                }
                switch (dto.getDeviceType()) {
                    case 0 -> {
                        Map<String, String> psmMap = DictUtils.getDictCacheMap("emu_power_supply_mode");
                        Map<String, String> mocMap = DictUtils.getDictCacheMap("emu_means_of_communication");
                        for (Product product : list) {
                            product.setPowerSupplyMode(psmMap.get(product.getPowerSupplyMode()));
                            product.setCommunicateMode(mocMap.get(product.getCommunicateMode()));
                        }
                    }
                    case 2 -> {
                        Map<String, String> psmMap = DictUtils.getDictCacheMap("meter_power_supply_mode");
                        for (Product product : list) {
                            product.setPowerSupplyMode(psmMap.get(product.getPowerSupplyMode()));
                        }
                    }
                }
            }
        }
        return list;
    }

    @Override
    public Product getInfoByCode(String code) {
        if (code.length() > 4) {
            code = code.substring(0, 4);
        }
        return lambdaQuery().eq(Product::getProductCode, code).one();
    }

    @Override
    public Product getProductBySn(String sn) {
        String modelCode = Tools.snModel(sn);
        return lambdaQuery().eq(Product::getProductCode, modelCode).one();
    }

    @Override
    public List<IntStr> listData(Integer vendorId, Integer deviceType) {
        return baseMapper.listData(vendorId, deviceType);
    }

    @Override
    @Transactional
    public boolean importData(List<Product> list, Integer deviceType) {
        Map<String, Integer> map = vendorMapper.listData().stream().collect(Collectors.toMap(IntStr::getV, IntStr::getK));
        List<Vendor> vendorList = list.stream().map(Product::getVendorName).filter(StrUtil::isNotBlank).distinct()
                .filter(name -> !map.containsKey(name)).map(Vendor::new).toList();
        if (!vendorList.isEmpty()) {
            vendorMapper.insert(vendorList);
            vendorList.forEach(v -> map.put(v.getVendorName(), v.getId()));
        }
        switch (deviceType) {
            case 0 -> {
                Map<String, String> psmMap = DictUtils.getDictCacheMapLV("emu_power_supply_mode");
                Map<String, String> mocMap = DictUtils.getDictCacheMapLV("emu_means_of_communication");
                for (Product product : list) {
                    product.setPowerSupplyMode(psmMap.get(product.getPowerSupplyMode()));
                    product.setCommunicateMode(mocMap.get(product.getCommunicateMode()));
                }
            }
            case 2 -> {
                Map<String, String> psmMap = DictUtils.getDictCacheMapLV("meter_power_supply_mode");
                for (Product product : list) {
                    product.setPowerSupplyMode(psmMap.get(product.getPowerSupplyMode()));
                }
            }
        }
        Map<String, Integer> productMap = lambdaQuery().select(Product::getProductName, Product::getId).eq(Product::getDeviceType, deviceType).list()
                .stream().collect(Collectors.toMap(Product::getProductName, Product::getId));
        for (Product product : list) {
            product.setDeviceType(deviceType);
            product.setVendorId(map.get(product.getVendorName()));
            product.setId(productMap.get(product.getProductName()));
        }
        return saveOrUpdateBatch(list);
    }

}
