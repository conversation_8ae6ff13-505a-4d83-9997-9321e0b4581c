package com.wanhe.business.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/8
 */
@Getter
@AllArgsConstructor
public enum WeatherPhenomenaEnum {

    SUNNY("00", "晴", "Sunny"),
    CLOUDY("01", "多云", "Cloudy"),
    OVERCAST("02", "阴", "Overcast"),
    SHOWER("03", "阵雨", "Shower"),
    THUNDERSHOWER("04", "雷阵雨", "Thundershower"),
    THUNDERSHOWER_WITH_HAIL("05", "雷阵雨伴有冰雹", "Thundershower with hail"),
    SLEET("06", "雨夹雪", "Sleet"),
    LIGHT_RAIN("07", "小雨", "Light rain"),
    MODERATE_RAIN("08", "中雨", "Moderate rain"),
    HEAVY_RAIN("09", "大雨", "Heavy rain"),
    STORM("10", "暴雨", "Storm"),
    HEAVY_STORM("11", "大暴雨", "Heavy storm"),
    SEVERE_STORM("12", "特大暴雨", "Severe storm"),
    SNOW_FLURRY("13", "阵雪", "Snow flurry"),
    LIGHT_SNOW("14", "小雪", "Light snow"),
    MODERATE_SNOW("15", "中雪", "Moderate snow"),
    HEAVY_SNOW("16", "大雪", "Heavy snow"),
    SNOWSTORM("17", "暴雪", "Snowstorm"),
    FOG("18", "雾", "Fog"),
    ICE_RAIN("19", "冻雨", "Ice rain"),
    DUSTSTORM("20", "沙尘暴", "Duststorm"),
    LIGHT_TO_MODERATE_RAIN("21", "小到中雨", "Light to moderate rain"),
    MODERATE_TO_HEAVY_RAIN("22", "中到大雨", "Moderate to heavy rain"),
    HEAVY_RAIN_TO_STORM("23", "大到暴雨", "Heavy rain to storm"),
    STORM_TO_HEAVY_STORM("24", "暴雨到大暴雨", "Storm to heavy storm"),
    HEAVY_TO_SEVERE_STORM("25", "大暴雨到特大暴雨", "Heavy to severe storm"),
    LIGHT_TO_MODERATE_SNOW("26", "小到中雪", "Light to moderate snow"),
    MODERATE_TO_HEAVY_SNOW("27", "中到大雪", "Moderate to heavy snow"),
    HEAVY_SNOW_TO_SNOWSTORM("28", "大到暴雪", "Heavy snow to snowstorm"),
    DUST("29", "浮尘", "Dust"),
    SAND("30", "扬沙", "Sand"),
    SANDSTORM("31", "强沙尘暴", "Sandstorm"),
    DENSE_FOG("32", "浓雾", "Dense fog"),
    TORNADO("33", "龙卷风", "Tornado"),
    WEAK_HIGH_BLOW_SNOW("34", "弱高吹雪", "Weak high blow snow"),
    MIST("35", "轻雾", "Mist"),
    HEAVY_DENSE_FOG("49", "强浓雾", "Heavy dense fog"),
    HAZE("53", "霾", "Haze"),
    MODERATE_HAZE("54", "中度霾", "Moderate haze"),
    SEVERE_HAZE("55", "重度霾", "Severe haze"),
    HAZARDOUS_HAZE("56", "严重霾", "Hazardous haze"),
    HEAVY_FOG("57", "大雾", "Heavy fog"),
    EXTRA_HEAVY_DENSE_FOG("58", "特强浓雾", "Extra-heavy dense fog"),
    RAIN("301", "雨", "Rain"),
    SNOW("302", "雪", "Snow");

    private final String code;
    private final String zh;
    private final String en;

    public static final Map<String, String> MAP = Arrays.stream(WeatherPhenomenaEnum.values())
            .collect(Collectors.toMap(WeatherPhenomenaEnum::getCode, WeatherPhenomenaEnum::getEn));

    public static String getEn(String code, String zoneId) {
        if ("00".equals(code)) {
            return "d".equals(checkDayOrNight(code, zoneId)) ? "Sunny" : "clear";
        }
        return MAP.get(code);
    }

    public static String getIcon(String code, String zoneId) {
        return switch (code) {
            case "00", "01", "03" -> code.charAt(1) + checkDayOrNight(code, zoneId);
            case "13" -> code + checkDayOrNight(code, zoneId);
            default -> code;
        };
    }


    // 假设这个方法从某个API获取日出和日落时间
    private static LocalTime getSunriseTime() {
        // 这里返回一个示例日出时间
        return LocalTime.of(6, 0);
    }

    private static LocalTime getSunsetTime() {
        // 这里返回一个示例日落时间
        return LocalTime.of(18, 0);
    }

    private static String checkDayOrNight(String code, String zoneId) {
        LocalTime currentTime = LocalDateTime.now(ZoneId.of(zoneId)).toLocalTime();
        // 获取日出和日落时间
        LocalTime sunriseTime = getSunriseTime();
        LocalTime sunsetTime = getSunsetTime();
        // 判断当前时间是否在日出和日落之间
        if (currentTime.isAfter(sunriseTime) && currentTime.isBefore(sunsetTime)) {
            return "d";
        }
        return "n";
    }

}
