package com.wanhe.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.core.domain.IntStr;
import com.wanhe.business.domain.GridFile;
import com.wanhe.business.pojo.device.GridFileVO;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @date 2024-10-26
*/
public interface GridFileMapper extends BaseMapper<GridFile> {

    List<GridFileVO> all(Integer countryId, String name);

    @Select("select id as k, version_number as v from wh_grid_file where id in(${ids})")
    List<IntStr> getIdName(String ids);

    @Select("select country_id from wh_grid_file where is_delete = 0 group by country_id")
    List<Integer> getCountry();
}
