package com.wanhe.business.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.fasterxml.jackson.annotation.JsonRawValue;
import com.ruoyi.common.core.web.domain.base.ITCUDDEntity;
import com.ruoyi.system.api.domain.PowerStationExtend;
import com.wanhe.business.pojo.station.Module;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-10-28
 */
@Data
@TableName(value = "wh_power_station", autoResultMap = true)
@Schema(name = "PowerStation", description = "电站基础信息表")
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = false)
public class PowerStation extends ITCUDDEntity {

    @Schema(description = "电站名称")
    private String stationName;

    @Schema(description = "电站类型（0:屋顶光伏 1:阳台光伏 2:商业光伏）")
    private String stationType;

    @Schema(description = "电站状态 （0建设中 1在线 2离线 3告警）")
    private Integer stationStatus;

    @Schema(description = "电站封面图片地址")
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private List<String> imageUrl;

    @Schema(description = "装机容量kW")
    private BigDecimal installedCapacity;

    @Schema(description = "国家ID")
    private Integer countryId;

    @Schema(description = "省份ID")
    private Integer provinceId;

    @Schema(description = "邮政编码")
    private String postalCode;

    @Schema(description = "时区")
    private String timeZone;

    @Schema(description = "纬度")
    private String latitude;

    @Schema(description = "经度")
    private String longitude;

    @Schema(description = "详细信息")
    private String stationDetails;

    @Schema(description = "安装图片地址")
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private List<String> installImageUrl;

    @Schema(description = "安装时间")
    private LocalDateTime installTime;

    @Schema(description = "安装人员id")
    private Long installerId;

    @Schema(description = "是否启动逆流")
    @TableField("is_reflux")
    private Boolean reflux;

    @Schema(description = "布局设计json")
    @JsonRawValue
    private String layout;

    @Schema(description = "电网类型 0:单相电网230V 1:三相电网230V/400V 2:裂相电网120V/240V 3:三相电网120V/208V")
    private Integer powerGridType;

    @Schema(description = "是否限制上网功率")
    @TableField("is_network")
    private Boolean network;

    @Schema(description = "上网功率")
    private BigDecimal networkKw;

    @Schema(description = "故障推送方式（0：短信1：邮箱：2：站内消息）")
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private List<Integer> faultNotificationMethod;

    @Schema(description = "货币单位")
    private String monetaryUnit;

    @Schema(description = "电价策略 1统一电价 2分时电价")
    private Integer powerPriceStrategy;

    @Schema(description = "卖出单价")
    private BigDecimal powerPrice;

    @Schema(description = "买入单价")
    private BigDecimal buyPowerPrice;

    @Schema(description = "是否允许业主查看组件布局")
    @TableField("is_allow_owner_view_layout")
    private Boolean allowOwnerViewLayout;

    @Schema(description = "组件布局默认显示(0：功率 1：电量)")
    private Integer defaultLayoutDisplay;

    @Schema(description = "单个组件的最大功率")
    private BigDecimal maxComponentPower;

    @Schema(description = "组件型号")
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private List<Module> module;

    @Schema(description = "绑定的设备数量")
    private Integer num;

    @Schema(description = "是否授权管理员操作")
    private Boolean permission;

    @Schema(description = "最后报告时间")
    private LocalDateTime lastReportTime;

    /** 创建人id */
    @TableField(fill = FieldFill.INSERT)
    private Long createId;

    /** 部门id */
    @TableField(fill = FieldFill.INSERT)
    private Long deptId;

    @Schema(description = "国家")
    @TableField(exist = false)
    private String country;

    @Schema(description = "省份")
    @TableField(exist = false)
    private String province;

    @Schema(description = "是否收藏")
    @TableField(exist = false)
    private boolean collect;

    @Schema(description = "电站扩展信息")
    @TableField(exist = false)
    private PowerStationExtend extend;

    @Schema(description = "业主名称")
    @TableField(exist = false)
    private String proprietor;

}
