package com.wanhe.business.service;


import com.ruoyi.common.security.service.IBaseService;
import com.wanhe.business.domain.Fault;
import com.wanhe.business.pojo.product.FaultDTO;
import com.wanhe.business.pojo.product.FaultPageReqDTO;
import com.wanhe.business.pojo.product.FaultUpdateDTO;
import com.wanhe.business.pojo.product.FaultVO;

import java.util.List;

/**
* <AUTHOR>
* @date 2024-10-26
*/
public interface IFaultService extends IBaseService<Fault> {

    boolean add(FaultDTO dto);

    boolean update(FaultUpdateDTO dto) throws Exception;

    boolean delete(Integer id);

    /**
     * 批量删除
     *
     * @param ids id列表
     */
    void batchDelete(List<Integer> ids);

    List<Fault> page(FaultPageReqDTO dto);

    boolean importData(List<Fault> list);

    List<FaultVO> all();
}
