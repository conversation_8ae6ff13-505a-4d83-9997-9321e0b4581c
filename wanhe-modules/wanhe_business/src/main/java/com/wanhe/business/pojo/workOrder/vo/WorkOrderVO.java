package com.wanhe.business.pojo.workOrder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/6/21
 */
@Data
public class WorkOrderVO {

    @Schema(description = "工单id")
    private Long id;

    /**
     * 工单sn/序列号
     */
    @Schema(description = "工单sn/序列号")
    private String orderSn;

    /**
     * 站点id
     */
    @Schema(description = "站点id")
    private Long stationId;

    /**
     * 站点名称
     */
    @Schema(description = "站点名称")
    private String stationName;

    /**
     * 设备id
     */
    @Schema(description = "设备id")
    private Long deviceId;

    /**
     * 设备编号
     */
    @Schema(description = "设备编号")
    private String deviceNumber;

    /**
     * 设备型号
     */
    @Schema(description = "设备型号")
    private String deviceModel;

    /**
     * 工单类型 1-故障 2-外观 3-资讯 4-其他
     */
    @Schema(description = "工单类型 1-故障 2-外观 3-资讯 4-其他")
    private Integer orderType;

    /**
     * 工单状态 1-未开始 2-进行中 3-已完成
     */
    @Schema(description = "工单状态 1-未开始 2-进行中 3-已完成")
    private Integer orderStatus;

    /**
     * 告警id
     */
    @Schema(description = "告警id")
    private Long faultId;

    /**
     * 故障代码
     */
    @Schema(description = "故障代码")
    private String faultCode;

    /**
     * 故障key
     */
    @Schema(description = "故障key")
    private String faultKey;

    /**
     * 告警描述
     */
    @Schema(description = "告警描述")
    private String faultDescription;

    /**
     * 处理意见
     */
    @Schema(description = "处理意见")
    private String resolutionSuggestion;

    /**
     * 告警开始时间
     */
    @Schema(description = "告警开始时间")
    private LocalDateTime faultStartTime;

    /**
     * 告警结束时间
     */
    @Schema(description = "告警结束时间")
    private LocalDateTime faultEndTime;

    /**
     * 提交时间
     */
    @Schema(description = "提交时间")
    private LocalDateTime commitTime;

    /**
     * 问题描述
     */
    @Schema(description = "问题描述")
    private String problemDescription;

    /**
     * 附件
     */
    @Schema(description = "附件")
    private String attachment;

    /**
     * 用户id
     */
    @Schema(description = "用户id")
    private Long userId;

    /**
     * 联系人
     */
    @Schema(description = "联系人")
    private String contact;

    /**
     * 电话号码
     */
    @Schema(description = "电话号码")
    private String phoneNumber;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    private String email;

    /**
     * 地址
     */
    @Schema(description = "地址")
    private String address;

    @Schema(description = "处理过程id")
    private Long processId;

    /**
     * 严重程度 1-一般 2-中等 3-非常严重
     */
    @Schema(description = "严重程度 1-一般 2-中等 3-非常严重")
    private Integer severity;
    /**
     * 可能原因
     */
    @Schema(description = "可能原因")
    private String possibleCauses;
    /**
     * 处理过程 1-远程调试 2-上门调试 3-更换备件 4-其他
     */
    @Schema(description = "处理过程 1-远程调试 2-上门调试 3-更换备件 4-其他")
    private Integer processSteps;
    /**
     * 处理方法
     */
    @Schema(description = "处理方法")
    private String solution;
    /**
     * 问题解决状态 0-未解决 1-已解决 2-无法解决
     */
    @Schema(description = "问题解决状态 0-未解决 1-已解决 2-无法解决")
    private Integer solutionStatus;
    /**
     * 完成时间
     */
    @Schema(description = "完成时间")
    private LocalDateTime completionTime;
    /**
     * 完成证明
     */
    @Schema(description = "完成证明")
    private String proofCompletion;

    /**
     * 确认人id
     */
    @Schema(description = "确认人id")
    private Long confirmId;

    /**
     * 确认部门id
     */
    @Schema(description = "确认部门id")
    private Long confirmDeptId;
    /**
     * 确认时间
     */
    @Schema(description = "确认时间")
    private LocalDateTime confirmTime;
    /**
     * 确认人
     */
    @Schema(description = "确认人")
    private String confirmedBy;
    /**
     * 处理人id
     */
    @Schema(description = "处理人id")
    private Long processorId;
    /**
     * 处理人
     */
    @Schema(description = "处理人")
    private String processor;
    /**
     * 处理人电话
     */
    @Schema(description = "处理人电话")
    private String processorPhoneNumber;
    /**
     * 处理人邮箱
     */
    @Schema(description = "处理人邮箱")
    private String processorEmail;

    @Schema(description = "评价id")
    private Long evaluateId;

    /**
     * 评价内容
     */
    @Schema(description = "评价内容")
    private String content;
    /**
     * 服务态度评分（默认5分）
     */
    @Schema(description = "服务态度评分（默认5分）")
    private BigDecimal serviceScore;
    /**
     * 响应速度评分（默认5分）
     */
    @Schema(description = "响应速度评分（默认5分）")
    private BigDecimal responseScore;
    /**
     * 处理结果评分（默认5分）
     */
    @Schema(description = "处理结果评分（默认5分）")
    private BigDecimal handleScore;
    /**
     * NPS值（默认5分）
     */
    @Schema(description = "NPS值（默认5分）")
    private BigDecimal npsScore;
    /**
     * 综合评分
     */
    @Schema(description = "综合评分")
    private BigDecimal score;
}
