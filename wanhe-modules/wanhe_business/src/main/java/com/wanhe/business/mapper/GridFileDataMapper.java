package com.wanhe.business.mapper;


import com.github.yulichang.base.MPJBaseMapper;
import com.ruoyi.system.api.domain.GridFileData;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @date 2024-11-15
*/
public interface GridFileDataMapper extends MPJBaseMapper<GridFileData> {

    @Select("select id,addr,is_issue as issue,val from wh_grid_file_data where grid_file_id = #{gridFileId} and addr > 9")
    List<GridFileData> getData(Integer gridFileId);

    @Select("select addr,is_issue as issue,val from wh_grid_data")
    List<GridFileData> getBaseData();

    @Select("""
    select row_number() over (order by d.addr) as num,fd.id,fd.grid_file_id,d.addr,d.name,d.name_en,d.type,ifnull(fd.is_issue,d.is_issue) as issue,ifnull(fd.val,d.val) as val,d.unit
    from wh_grid_data d left join wh_grid_file_data fd on d.addr = fd.addr and fd.grid_file_id = #{gridFileId} where d.addr > 9
    """)
    List<GridFileData> get(Integer gridFileId);

}
