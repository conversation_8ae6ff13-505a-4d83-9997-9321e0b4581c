package com.wanhe.business.pojo.workOrder.dto;

import com.ruoyi.common.core.domain.PageReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/6/21
 */
@Data
public class WorkOrderPageDTO extends PageReqDTO {

    /**
     * 工单id
     */
    @Schema(description = "工单id")
    private Integer id;

    /**
     * 工单编号/序列号
     */
    @Schema(description = "工单编号/序列号")
    private String orderSn;

    /**
     * 站点id
     */
    @Schema(description = "站点id")
    private Long stationId;

    /**
     * 站点名称
     */
    @Schema(description = "站点名称")
    private String stationName;

    /**
     * 设备id
     */
    @Schema(description = "设备id")
    private Long deviceId;

    /**
     * 设备编号
     */
    @Schema(description = "设备编号")
    private String deviceNumber;

    /**
     * 设备型号
     */
    @Schema(description = "设备型号")
    private String deviceModel;

    /**
     * 工单状态 1-未开始 2-进行中 3-已完成
     */
    @Schema(description = "工单状态 1-未开始 2-进行中 3-已完成")
    private Integer orderStatus;

    @Schema(description = "提交时间排序 true: 正序 false: 倒序")
    private Boolean commitTimeAsc;

    @Schema(description = "确认时间排序 true: 正序 false: 倒序")
    private Boolean confirmTimeAsc;

    @Schema(description = "完成时间排序 true: 正序 false: 倒序")
    private Boolean completionTimeAsc;


}
