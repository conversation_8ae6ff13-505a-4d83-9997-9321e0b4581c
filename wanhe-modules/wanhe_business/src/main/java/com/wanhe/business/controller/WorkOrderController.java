package com.wanhe.business.controller;

import com.github.pagehelper.Page;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.wanhe.business.pojo.workOrder.dto.*;
import com.wanhe.business.pojo.workOrder.vo.WorkOrderPageVO;
import com.wanhe.business.pojo.workOrder.vo.WorkOrderVO;
import com.wanhe.business.service.IWorkOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 工单模块
 *
 * <AUTHOR>
 * @date 2025/06/21
 */
@RestController
@AllArgsConstructor
@RequestMapping("/workOrder")
@Tag(name = "工单模块")
public class WorkOrderController extends BaseController {

    private final IWorkOrderService workOrderService;

    /**
     * 获取工单分页数据
     *
     * @param dto 到
     * @return {@link TableDataInfo }<{@link WorkOrderPageVO }>
     */
    @Operation(summary = "获取工单分页数据")
    @GetMapping("/page")
    public TableDataInfo<WorkOrderPageVO> page(WorkOrderPageDTO dto) {
        Page<WorkOrderPageVO> page = startPage(dto);
        workOrderService.page(dto);
        return getDataTable(page);
    }

    /**
     * 获取工单详细信息
     *
     * @param orderId 订单ID
     * @return {@link R }<{@link WorkOrderVO }>
     */
    @GetMapping
    @Operation(summary = "获取工单详细信息")
    public R<WorkOrderVO> getDetail(@RequestParam Long orderId) {
        WorkOrderVO detail = workOrderService.getDetail(orderId);
        return R.ok(detail);
    }

    /**
     * 新增工单
     *
     * @param dto 到
     * @return {@link R }<{@link Void }>
     */
    @PostMapping
    @Log(title = "工单管理", businessType = BusinessType.INSERT)
    @Operation(summary = "新增工单")
    public R<Void> saveWorkOrder(@RequestBody WorkOrderDTO dto){
        workOrderService.saveWorkOrder(dto);
        return R.ok();
    }

    /**
     * 更新工单
     *
     * @param dto 到
     * @return {@link R }<{@link Void }>
     */
    @PutMapping
    @Operation(summary = "更新工单")
    @Log(title = "工单管理", businessType = BusinessType.UPDATE)
    public R<Void> updateWorkOrder(@RequestBody WorkOrderDTO dto) {
        workOrderService.updateWorkOrder(dto);
        return R.ok();
    }

    /**
     * 添加处理人
     *
     * @param dto 到
     * @return {@link R }<{@link Void }>
     */
    @PutMapping("/addProcessor")
    @Operation(summary = "添加处理人")
    public R<Void> addProcessor(@RequestBody ProcessorAddDTO dto){
        workOrderService.addProcessor(dto);
        return R.ok();
    }

    /**
     * 保存/完成工单处理过程
     *
     * @param orderProcess 订单处理
     * @return {@link R }<{@link Void }>
     */
    @PostMapping("/addOrderProcess")
    @Operation(summary = "保存/完成工单处理过程")
    public R<Void> addOrderProcess(@RequestBody OrderProcessDTO orderProcess) {
        workOrderService.addOrderProcess(orderProcess);
        return R.ok();
    }

    /**
     * 工单添加评价
     *
     * @param evaluate 评价
     * @return {@link R }<{@link Void }>
     */
    @PostMapping("/addEvaluate")
    @Operation(summary = "工单添加评价")
    public R<Void> addEvaluate(@RequestBody OrderEvaluateDTO evaluate) {
        workOrderService.addOrderEvaluate(evaluate);
        return R.ok();
    }

    /**
     * 撤回工单
     *
     * @param orderId 订单ID
     * @return {@link R }<{@link Void }>
     */
    @DeleteMapping
    @Operation(summary = "撤回工单")
    @Log(title = "工单管理", businessType = BusinessType.DELETE)
    public R<Void> deleteWorkOrder(@RequestParam("orderId") Long orderId) {
        workOrderService.deleteWorkOrder(orderId);
        return R.ok();
    }

    /**
     * 获取未完成工单数量
     *
     * @return {@link R }<{@link Integer }>
     */
    @GetMapping("/incompleteCount")
    @Operation(summary = "获取未完成工单数量")
    public R<Integer> getIncompleteOrderCount() {
        return R.ok(workOrderService.getIncompleteOrderCount());
    }

    /**
     * 根据故障id判断工单
     *
     * @param faultId 故障id
     * @return {@link R }<{@link Boolean }>
     */
    @GetMapping("/judgeWorkOrder/{faultId}")
    public R<Boolean> judgeWorkOrderByFaultId(@PathVariable("faultId") Long faultId) {
        return R.ok(workOrderService.judgeWorkOrderByFaultId(faultId));
    }
}
