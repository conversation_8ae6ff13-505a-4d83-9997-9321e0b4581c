package com.wanhe.business.pojo.station;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-10-28
 */
@Data
public class MeterDTO {

    @Schema(description = "电站id")
    @NotNull
    @Positive
    private Integer powerStationId;

    @Schema(description = "是否开启逆流")
    @NotNull
    private Boolean reflux;

    @Schema(description = "电网类型 0:单相电网230V 1:三相电网230V/400V 2:裂相电网120V/240V 3:三相电网120V/208V")
    @Range(max = 3)
    private Integer powerGridType;

    @Schema(description = "电表位置")
    @Valid
    @NotNull
    private List<Meter> meter;

    @Schema(description = "是否限制上网功率")
    private Boolean network;

    @Schema(description = "上网功率")
    @PositiveOrZero
    @Digits(integer = 18, fraction = 2)
    private BigDecimal networkKw;

}
