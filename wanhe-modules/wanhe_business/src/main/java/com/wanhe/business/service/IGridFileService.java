package com.wanhe.business.service;


import com.ruoyi.common.core.domain.IntStr;
import com.ruoyi.common.security.service.IBaseService;
import com.wanhe.business.domain.GridFile;
import com.wanhe.business.pojo.device.GridFileVO;
import com.wanhe.business.pojo.product.GridFileDTO;
import com.wanhe.business.pojo.product.GridFilePageReqDTO;
import com.wanhe.business.pojo.product.GridFileUpdateDTO;

import java.util.List;

/**
* <AUTHOR>
* @date 2024-10-26
*/
public interface IGridFileService extends IBaseService<GridFile> {

    IGridFileDataService getGridFileDataService();

    boolean add(GridFileDTO dto);

    boolean update(GridFileUpdateDTO dto);

    void page(GridFilePageReqDTO dto);

    List<GridFileVO> all(Integer countryId, String name);

    boolean delete(Integer id);

    List<IntStr> getCountry();

    GridFile getInfo(Integer id);
}
