package com.wanhe.business.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.domain.base.ICUDEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 工单评价记录
 *
 * <AUTHOR>
 * @date 2025/06/18
 */
@Data
@TableName("wh_order_evaluate")
@Schema(name = "OrderEvaluate", description = "工单评价表")
@EqualsAndHashCode(callSuper = false)
public class OrderEvaluate extends ICUDEntity {

    /**
     * 工单id
     */
    @Schema(description = "工单id")
    private Long orderId;
    /**
     * 评价内容
     */
    @Schema(description = "评价内容")
    private String content;
    /**
     * 服务态度评分（默认5分）
     */
    @Schema(description = "服务态度评分（默认5分）")
    private BigDecimal serviceScore;
    /**
     * 响应速度评分（默认5分）
     */
    @Schema(description = "响应速度评分（默认5分）")
    private BigDecimal responseScore;
    /**
     * 处理结果评分（默认5分）
     */
    @Schema(description = "处理结果评分（默认5分）")
    private BigDecimal handleScore;
    /**
     * NPS值（默认5分）
     */
    @Schema(description = "NPS值（默认5分）")
    private BigDecimal npsScore;
    /**
     * 综合评分
     */
    @Schema(description = "综合评分")
    private BigDecimal score;
}
