package com.wanhe.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.core.domain.IntStr;
import com.wanhe.business.domain.Vendor;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @date 2024-10-26
*/
public interface VendorMapper extends BaseMapper<Vendor> {

    @Select("select id as k,vendor_name as v from wh_vendor where is_delete = 0")
    List<IntStr> listData();

    @Select("select p.vendor_id as k,v.vendor_name as v from wh_product p inner join wh_vendor v on p.vendor_id = v.id and p.device_type = #{deviceType} and p.deleted = 0 group by p.vendor_id")
    List<IntStr> listData2(Integer deviceType);

    @Select("select id,vendor_name from wh_vendor where id in(${ids})")
    List<Vendor> listByIds(String ids);

}
