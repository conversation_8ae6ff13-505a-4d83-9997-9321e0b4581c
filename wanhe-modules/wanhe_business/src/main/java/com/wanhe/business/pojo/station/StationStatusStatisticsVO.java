package com.wanhe.business.pojo.station;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/11/5 0005
 */
@Data
public class StationStatusStatisticsVO {

    @Schema(description = "电站状态 （0建设中 1在线 2离线 3告警）")
    private Integer stationStatus;

    @Schema(description = "电站类型（0:屋顶光伏 1:阳台光伏 2:商业光伏）")
    private String stationType;

    @Schema(description = "数量")
    private Integer stationNum;
}
