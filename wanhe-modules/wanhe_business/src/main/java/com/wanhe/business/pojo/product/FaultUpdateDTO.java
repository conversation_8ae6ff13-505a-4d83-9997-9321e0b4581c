package com.wanhe.business.pojo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 * @date 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(name = "FaultUpdateDTO", description = "故障表")
public class FaultUpdateDTO extends FaultDTO {

    @Schema(description = "故障id")
    @NotNull
    @Positive
    private Integer id;

}
