package com.wanhe.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.domain.IntStr;
import com.ruoyi.common.security.service.impl.BaseServiceImpl;
import com.wanhe.business.domain.Vendor;
import com.wanhe.business.mapper.VendorMapper;
import com.wanhe.business.pojo.product.VendorDTO;
import com.wanhe.business.pojo.product.VendorPageReqDTO;
import com.wanhe.business.pojo.product.VendorUpdateDTO;
import com.wanhe.business.service.IVendorService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @date 2024-10-26
*/
@Service
public class VendorServiceImpl extends BaseServiceImpl<VendorMapper, Vendor> implements IVendorService {

    @Override
    public boolean add(VendorDTO dto) {
        Vendor entity = BeanUtil.copyProperties(dto, Vendor.class);
        return save(entity);
    }

    @Override
    public boolean update(VendorUpdateDTO dto) {
        Vendor entity = getByIdThrow(dto.getId());
        BeanUtil.copyProperties(dto, entity);
        return updateById(entity);
    }

    @Override
    public void page(VendorPageReqDTO dto) {
        lambdaQuery()
                .like(StrUtil.isNotBlank(dto.getVendorName()), Vendor::getVendorName, dto.getVendorName())
                .orderByDesc(Vendor::getId)
                .list();
    }

    @Override
    public List<IntStr> listData(Integer deviceType) {
        if (deviceType == null) {
            return baseMapper.listData();
        }
        return baseMapper.listData2(deviceType);
    }

    @Override
    public boolean delete(Integer id) {
        return lambdaUpdate().eq(Vendor::getId, id)
                .set(Vendor::getDelete, 1)
                .set(Vendor::getDeleted, id)
                .update();
    }

}
