package com.wanhe.business.pojo.station;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/22
 */
@Data
public class PowerStationPriceMonthDTO {

    @Schema(description = "月份")
    @NotEmpty
    private List<Integer> months;

    @Schema(description = "时间集合")
    @Valid
    @NotEmpty
    private List<PowerStationPriceTimeDTO> times;

}
