package com.wanhe.business.pojo.workOrder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/6/21
 */
@Data
public class WorkOrderPageVO {

    /**
     * 工单id
     */
    @Schema(description = "工单id")
    private Long id;

    @Schema(description = "工单编号/序列号")
    private String orderSn;

    /**
     * 工单类型 1-故障 2-外观 3-资讯 4-其他
     */
    @Schema(description = "工单类型 1-故障 2-外观 3-资讯 4-其他")
    private Integer orderType;

    /**
     * 工单状态 1-未开始 2-进行中 3-已完成
     */
    @Schema(description = "工单状态 1-未开始 2-进行中 3-已完成")
    private Integer orderStatus;

    /**
     * 提交时间
     */
    @Schema(description = "提交时间")
    private LocalDateTime commitTime;

    /**
     * 确认时间
     */
    @Schema(description = "确认时间")
    private LocalDateTime confirmTime;

    /**
     * 完成时间
     */
    @Schema(description = "完成时间")
    private LocalDateTime completionTime;

    /**
     * 评分
     */
    @Schema(description = "评分")
    private BigDecimal score;
}
