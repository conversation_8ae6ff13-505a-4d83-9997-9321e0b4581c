package com.wanhe.business.pojo.device;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/11/26
 */
@Data
public class FirmwareVersionsVO {

    @Schema(description = "设备Id")
    private Integer id;

    @Schema(description = "emu设备Id", hidden = true)
    @JsonIgnore
    private Integer pid;

    @Schema(description = "新版本")
    private String versionNumber;

    @Schema(description = "旧版本")
    private String softwareVersion;

    @Schema(description = "设备sn")
    private String number;

    @Schema(description = "操作类型 1 emu固件升级 2 emu微逆固件升级 3wifi微逆固件升级")
    private Integer operateType;

    @Schema(description = "升级文件")
    private Integer fileId;

    @Schema(description = "设备类型 （0：EMU 1:微逆 2：电表）", hidden = true)
    @JsonIgnore
    private Integer deviceType;

    @Schema(description = "设备型号")
    private String deviceModel;

    @Schema(description = "联网模式 0:WIFI 1:有线网 2:4G")
    private Integer commType;

}
