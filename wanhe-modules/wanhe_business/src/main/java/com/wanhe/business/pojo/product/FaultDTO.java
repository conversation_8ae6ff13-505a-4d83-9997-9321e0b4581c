package com.wanhe.business.pojo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2024-10-26
 */
@Data
@Schema(name = "FaultDTO", description = "故障表")
public class FaultDTO {

    @Schema(description = "故障字")
    @NotBlank
    private String faultCode;

    @Schema(description = "bit位")
    @NotNull
    private Integer bit;

    @Schema(description = "故障描述")
    @NotBlank
    private String faultDescription;

    @Schema(description = "处理意见")
    @NotBlank
    private String resolutionSuggestion;

    @Schema(description = "故障等级")
    @NotBlank
    private String faultLevel;

    @Schema(description = "备注信息")
    private String notes;

}
