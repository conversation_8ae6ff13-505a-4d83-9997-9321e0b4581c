package com.wanhe.business.pojo.station;

import com.ruoyi.common.core.domain.DataUnit;
import com.ruoyi.common.core.utils.NumUtil;
import com.ruoyi.common.core.utils.Tools;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/19
 */
@Data
@NoArgsConstructor
public class HomeMapDataVO {

    //电站总数
    private Integer stationNum;

    //电站本月新增
    private Integer stationByNum;

    //电站上月新增
    private Integer stationSyNum;

    //电站本月同比
    private BigDecimal stationRate;

    //总装机容量
    private BigDecimal capacityNum;
    private String capacityNumUnit;

    //装机容量本月新增
    private BigDecimal capacityByNum;
    private String capacityByNumUnit;

    //装机容量上月新增
    private BigDecimal capacitySyNum;

    //装机容量本月同比
    private BigDecimal capacityRate;

    //总微逆
    private Integer wnNum;

    //微逆本月新增
    private Integer wnByNum;

    //微逆上月新增
    private Integer wnSyNum;

    //微逆本月同比
    private BigDecimal wnRate;

    public HomeMapDataVO(List<HomeMapDataVO> stationList, List<Integer> wnList) {
        wnNum = wnList.get(0) + wnList.get(3);
        wnByNum = wnList.get(1) + wnList.get(4);
        wnSyNum = wnList.get(2) + wnList.get(5);
        if (wnByNum.equals(wnSyNum)) {
            wnRate = BigDecimal.ZERO;
        } else if (wnSyNum == 0) {
            wnRate = NumUtil.HUNDRED;
        } else {
            wnRate = new BigDecimal(wnByNum - wnSyNum).multiply(NumUtil.HUNDRED).divide(new BigDecimal(wnSyNum), 2, RoundingMode.HALF_UP);
        }
        stationNum = stationList.get(0).getStationNum();
        stationByNum = stationList.get(1).getStationNum();
        stationSyNum = stationList.get(2).getStationNum();
        if (stationByNum.equals(stationSyNum)) {
            stationRate = BigDecimal.ZERO;
        } else if (stationSyNum == 0) {
            stationRate = NumUtil.HUNDRED;
        } else {
            stationRate = new BigDecimal(stationByNum - stationSyNum).multiply(NumUtil.HUNDRED).divide(new BigDecimal(stationSyNum), 2, RoundingMode.HALF_UP);
        }
        DataUnit all = Tools.covertKwhUnit(stationList.get(0).getCapacityNum());
        capacityNum = all.getData();
        capacityNumUnit = all.getUnit();
        capacityByNum = stationList.get(1).getCapacityNum();
        capacitySyNum = stationList.get(2).getCapacityNum();
        if (capacityByNum.compareTo(capacitySyNum) == 0) {
            capacityRate = BigDecimal.ZERO;
        } else if (capacitySyNum.compareTo(BigDecimal.ZERO) == 0) {
            capacityRate = NumUtil.HUNDRED;
        } else {
            capacityRate = capacityByNum.subtract(capacitySyNum).multiply(NumUtil.HUNDRED).divide(capacitySyNum, 2, RoundingMode.HALF_UP);
        }
        DataUnit month = Tools.covertKwhUnit(stationList.get(1).getCapacityNum());
        capacityByNum = month.getData();
        capacityByNumUnit = month.getUnit();
    }

}
