package com.wanhe.business.pojo.station;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/11/5 0005
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeviceStatisticsVO {

    @Schema(description = "月份")
    private Integer month;

    @Schema(description = "数量")
    private Integer stationNum;

    @Schema(description = "设备类型", hidden = true)
    @JsonIgnore
    private Integer deviceType;

    public DeviceStatisticsVO(Integer month, Integer stationNum) {
        this.month = month;
        this.stationNum = stationNum;
    }
}
