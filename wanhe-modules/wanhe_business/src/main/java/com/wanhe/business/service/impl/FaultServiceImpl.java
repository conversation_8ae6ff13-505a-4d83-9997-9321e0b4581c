package com.wanhe.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.enums.I18nTypeEnum;
import com.ruoyi.common.core.enums.LanguageEnum;
import com.ruoyi.common.core.utils.Tools;
import com.ruoyi.common.redis.RedisUtil;
import com.ruoyi.common.security.service.impl.BaseServiceImpl;
import com.ruoyi.common.security.utils.DictUtils;
import com.wanhe.business.domain.Fault;
import com.wanhe.business.mapper.FaultMapper;
import com.wanhe.business.pojo.product.FaultDTO;
import com.wanhe.business.pojo.product.FaultPageReqDTO;
import com.wanhe.business.pojo.product.FaultUpdateDTO;
import com.wanhe.business.pojo.product.FaultVO;
import com.wanhe.business.service.IFaultService;
import com.wanhe.business.service.ISysI18nService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @date 2024-10-26
*/
@Service
public class FaultServiceImpl extends BaseServiceImpl<FaultMapper, Fault> implements IFaultService {

    @Value("${fault.language}")
    private String faultLanguage;
    @Resource
    private ISysI18nService i18nService;


    private void cache() {
        for (String lang : List.of("zh", "en", "de", "fra")) {
            List<FaultVO> list;
            if (lang.equals("zh")) {
                list = baseMapper.all2();
            } else {
                list = baseMapper.all(lang);
            }
            Map<String, String> map = list.stream().collect(Collectors.toMap(FaultVO::getFaultKey, JSON::toJSONString));
            RedisUtil.transaction(redis -> {
                redis.delete(CacheConstants.FAULT_DATA_KEY + lang);
                redis.opsForHash().putAll(CacheConstants.FAULT_DATA_KEY + lang, map);
            });
        }
    }

    @Override
    @Transactional
    public boolean add(FaultDTO dto) {
        Fault entity = BeanUtil.copyProperties(dto, Fault.class);
        entity.setFaultKey(Tools.getFaultKey(dto.getFaultCode(), dto.getBit()));
        boolean save = save(entity);
        cache();
        CompletableFuture.runAsync(() -> {
            i18nService.add(I18nTypeEnum.FAULT_DESC, entity.getId(), entity.getFaultDescription());
            i18nService.add(I18nTypeEnum.FAULT_OPINION, entity.getId(), entity.getResolutionSuggestion());
        });
        return save;
    }

    @Override
    @Transactional
    public boolean update(FaultUpdateDTO dto) throws Exception {
        Fault entity = getByIdThrow(dto.getId());
        BeanUtil.copyProperties(dto, entity);
        entity.setFaultKey(Tools.getFaultKey(dto.getFaultCode(), dto.getBit()));
        boolean b = updateById(entity);
        cache();
        CompletableFuture.runAsync(() -> {
            i18nService.update(I18nTypeEnum.FAULT_DESC, entity.getId(), entity.getFaultDescription());
            i18nService.update(I18nTypeEnum.FAULT_OPINION, entity.getId(), entity.getResolutionSuggestion());
        });
        return b;
    }

    @Override
    @Transactional
    public boolean delete(Integer id) {
        Fault fault = getByIdThrow(id);
        boolean update = lambdaUpdate().eq(Fault::getId, id)
                .set(Fault::getDelete, 1)
                .set(Fault::getDeleted, id)
                .update();
        cache();
        CompletableFuture.runAsync(() -> {
            i18nService.delete(I18nTypeEnum.FAULT_DESC, id);
            i18nService.delete(I18nTypeEnum.FAULT_OPINION, id);
        });
        return update;
    }

    @Override
    @Transactional
    public void batchDelete(List<Integer> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        lambdaUpdate().in(Fault::getId, ids)
                .set(Fault::getDelete, 1)
                .set(Fault::getDeleted, ids.get(0))
                .update();
        cache();
        CompletableFuture.runAsync(() -> {
            i18nService.batchDelete(I18nTypeEnum.FAULT_DESC, ids);
            i18nService.batchDelete(I18nTypeEnum.FAULT_OPINION, ids);
        });
    }

    @Override
    public List<Fault> page(FaultPageReqDTO dto) {
        String searchCode = StrUtil.isBlank(dto.getFaultCode()) ? "" : " and f.fault_code like concat('%', '"+dto.getFaultCode()+"', '%')";
        List<Fault> list = baseMapper.page(LanguageEnum.getLang(), searchCode);
        if (!list.isEmpty()) {
            Map<String, String> faultGradeMap = DictUtils.getDictCacheMap("fault_grade");
            list.forEach(f -> f.setFaultLevelName(faultGradeMap.get(f.getFaultLevel())));
        }
        return list;
    }

    @Override
    @Transactional
    public boolean importData(List<Fault> list) {

        baseMapper.deleteAll();
        i18nService.batchDelete(I18nTypeEnum.FAULT_DESC, null);
        i18nService.batchDelete(I18nTypeEnum.FAULT_OPINION, null);

        Map<String, String> faultGradeMap = DictUtils.getDictCacheMapLV("fault_grade");
        List<Fault> newList = list.parallelStream().filter(f -> StrUtil.isNotBlank(f.getFaultCode()) && f.getBit() != null)
                .peek(f -> {
                    String faultKey = Tools.getFaultKey(f.getFaultCode(), f.getBit());
                    String level = faultGradeMap.get(f.getFaultLevelName().trim());
                    f.setFaultLevel(level);
                    f.setFaultKey(faultKey);
                }).toList();

        if (CollUtil.isNotEmpty(newList)) {
            saveBatch(newList);
            CompletableFuture.runAsync(() -> {
                for (Fault fault : newList) {
                    i18nService.add(I18nTypeEnum.FAULT_DESC, fault.getId(), fault.getFaultDescription());
                    i18nService.add(I18nTypeEnum.FAULT_OPINION, fault.getId(), fault.getResolutionSuggestion());
                }
                cache();
            });
        }
        return true;
    }

    @Override
    public List<FaultVO> all() {
        return baseMapper.all(LanguageEnum.getLang());
    }

}
