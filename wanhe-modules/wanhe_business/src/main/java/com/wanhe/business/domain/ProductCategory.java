package com.wanhe.business.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.domain.base.ICUDEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024-10-26
 */
@Data
@TableName("wh_product_category")
@Schema(name = "ProductCategory", description = "产品分类表")
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = false)
public class ProductCategory extends ICUDEntity {

    @Schema(description = "产品分类名称")
    private String name;

    @Schema(description = "产品编码")
    private String sn;

    @Schema(description = "描述")
    private String description;

}
