package com.wanhe.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.ruoyi.common.core.enums.I18nTypeEnum;
import com.ruoyi.common.core.enums.LanguageEnum;
import com.ruoyi.common.security.service.impl.BaseServiceImpl;
import com.ruoyi.system.api.domain.SysI18n;
import com.wanhe.business.mapper.SysI18nMapper;
import com.wanhe.business.service.ISysI18nService;
import com.wanhe.common.i18n.util.TransApi;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/19
 */
@Service
public class SysI18nServiceImpl extends BaseServiceImpl<SysI18nMapper, SysI18n> implements ISysI18nService {

    @Override
    public void add(I18nTypeEnum type, Integer dataId, String name) {
        SysI18n i18n = new SysI18n();
        i18n.setType(type.getType());
        i18n.setDataId(dataId.longValue());
        i18n.setNameZh(name);
        i18n.setNameDe(TransApi.get(name, LanguageEnum.DE.getBaidu()));
        i18n.setNameEn(TransApi.get(name, LanguageEnum.EN.getBaidu()));
        i18n.setNameFra(TransApi.get(name, LanguageEnum.FR.getBaidu()));
        save(i18n);
    }

    @Override
    public void update(I18nTypeEnum type, Integer dataId, String name) {
        SysI18n i18n = lambdaQuery().eq(SysI18n::getType, type.getType())
                .eq(SysI18n::getDataId, dataId).one();
        if (i18n == null) {
            add(type, dataId, name);
        } else {
            if (!i18n.getNameZh().equals(name)) {
                i18n.setNameZh(name);
                i18n.setNameDe(TransApi.get(name, LanguageEnum.DE.getBaidu()));
                i18n.setNameEn(TransApi.get(name, LanguageEnum.EN.getBaidu()));
                i18n.setNameFra(TransApi.get(name, LanguageEnum.FR.getBaidu()));
                updateById(i18n);
            }
        }
    }

    @Override
    public void delete(I18nTypeEnum type, Integer dataId) {
        lambdaUpdate().eq(SysI18n::getType, type.getType())
                .eq(SysI18n::getDataId, dataId)
                .remove();
    }

    @Override
    public void batchDelete(I18nTypeEnum type, List<Integer> dataIds) {
        LambdaUpdateChainWrapper<SysI18n> wrapper = lambdaUpdate().eq(SysI18n::getType, type.getType());
        if (CollUtil.isNotEmpty(dataIds)) {
            wrapper.in(SysI18n::getDataId, dataIds);
        }
        wrapper.remove();
    }
}
