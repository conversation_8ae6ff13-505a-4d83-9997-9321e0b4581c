package com.wanhe.business.pojo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024-10-26
 */
@Data
public class ProductDTO {

    @Schema(description = "设备类型")
    @NotNull
    private Integer deviceType;

    @Schema(description = "产品型号")
    @NotBlank
    private String productName;

    @Schema(description = "产品编码")
    private String productCode;

    @Schema(description = "厂家id")
    private Integer vendorId;

    @Schema(description = "通道数量")
    private Integer connectNum;

    @Schema(description = "质保期（月）")
    @NotNull
    @Positive
    private Integer guaranteePeriod;

    @Schema(description = "额定输出功率(W)")
    private BigDecimal power;

    @Schema(description = "额定输出电压(V)")
    private BigDecimal voltage;

    @Schema(description = "通讯方式")
    private String communicateMode;

    @Schema(description = "供电方式")
    private String powerSupplyMode;

    @Schema(description = "尺寸(mm)")
    private String dimension;

    @Schema(description = "最大功率(W)")
    private BigDecimal maxPower;

    @Schema(description = "最大功率点电压(V)")
    private BigDecimal maxVoltage;

    @Schema(description = "最大功率点电流(A)")
    private BigDecimal maxElectricity;

    @Schema(description = "开路电压(V)")
    private BigDecimal openVoltage;

    @Schema(description = "短路电流(A)")
    private BigDecimal shortOutElectricity;

    @Schema(description = "质量(kg)")
    private BigDecimal quality;

}
