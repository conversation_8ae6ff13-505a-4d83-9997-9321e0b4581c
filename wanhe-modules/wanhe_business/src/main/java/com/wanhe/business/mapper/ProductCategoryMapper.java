package com.wanhe.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.core.domain.IntStr;
import com.wanhe.business.domain.ProductCategory;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @date 2024-10-26
*/
public interface ProductCategoryMapper extends BaseMapper<ProductCategory> {

    @Select("select c.id as k,i.name_${language} as v from wh_product_category c left join sys_i18n i on i.type = 3 and c.id = i.data_id where c.is_delete = 0")
    List<IntStr> all(String language);

    @Select("select c.id,i.name_${language} as `name` from wh_product_category c left join sys_i18n i on i.type = 3 and c.id = i.data_id where c.id in(${ids})")
    List<ProductCategory> listByIds(String ids, String language);

}
