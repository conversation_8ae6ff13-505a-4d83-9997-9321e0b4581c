package com.wanhe.business.service;


import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.KV;
import com.ruoyi.common.core.domain.PageReqDTO;
import com.ruoyi.common.security.service.IMPJBaseService;
import com.ruoyi.system.api.dto.PowerStationListDataVO;
import com.wanhe.business.domain.PowerStation;
import com.wanhe.business.pojo.product.PermissionDTO;
import com.wanhe.business.pojo.station.*;

import java.io.OutputStream;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @date 2024-10-26
*/
public interface IPowerStationService extends IMPJBaseService<PowerStation> {

    PowerStation getInfo(Integer id);

    Integer add(PowerStationDTO dto);

    boolean update(PowerStationUpdateDTO dto);

    void page(PowerStationPageReqDTO dto);

    boolean delete(Integer id);

    boolean layout(LayoutDTO dto);

    boolean install(InstallDTO dto);

    boolean fault(MoreDTO dto);

    List<KV> all();

    List<PowerStationSiteVO> allSite(Integer all);

    boolean collect(Integer id);

    StationReportVO report(Integer id);

    PowerStation reportDownload(Integer id, OutputStream outputStream) throws Exception;

    List<String> reportImg(Integer id) throws Exception;

    void reportShare(ReportShareDTO dto) throws Exception;

    PowerStationListDataVO data();

    List<PowerStationPriceMonthVO> powerPrice(Integer id);

    boolean powerPrice(PowerStationPriceDTO dto);

    StationStatusVO stationStatus(Integer id);

    Map<Integer, String> getDistrictName(List<Integer> districtIds);

    /**
     * 统计 电站状态
     */
    PowerStationStatisticsVO queryPowerStationStatus(Integer collect);

    /**
     * 统计 电站类型
     */
    PowerStationStatisticsVO queryPowerStationType();

    /**
     * 统计 电站统计
     */
    List<StationNumStatisticsVO> queryPowerStationStatistics();

    boolean transfer(TransferDTO dto);

    PowerInfoVO owner(Integer id);

    HomeMapDataVO mapData();

    JSONObject weather(Integer id);

    void permissionPage(PageReqDTO dto);

    boolean permission(PermissionDTO dto);
}
