package com.wanhe.business.service.impl;

import com.ruoyi.common.security.service.impl.BaseServiceImpl;
import com.wanhe.business.domain.PowerStationCollect;
import com.wanhe.business.mapper.PowerStationCollectMapper;
import com.wanhe.business.service.IPowerStationCollectService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @date 2024-10-26
*/
@Service
public class PowerStationCollectServiceImpl extends BaseServiceImpl<PowerStationCollectMapper, PowerStationCollect> implements IPowerStationCollectService {

    @Override
    public void delete(Integer powerStationId) {
        baseMapper.deleteBy(powerStationId);
    }

}
