package com.wanhe.business.controller;

import com.github.pagehelper.Page;
import com.ruoyi.common.core.domain.IntStr;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.LanguageEnum;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.GridData;
import com.ruoyi.system.api.domain.GridFileData;
import com.wanhe.business.domain.GridFile;
import com.wanhe.business.pojo.device.GridFileVO;
import com.wanhe.business.pojo.product.*;
import com.wanhe.business.service.IGridFileService;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 并网文件管理
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@RestController
@RequestMapping("/frid/file")
public class GridFileController extends BaseController {

    @Resource
    private IGridFileService gridFileService;

    /**
     * 新增 business:fridFile:add
     */
    @RequiresPermissions("business:fridFile:add")
    @Log(title = "并网文件管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@Validated @RequestBody GridFileDTO dto) {
        return R.ok(gridFileService.add(dto));
    }

    /**
     * 修改 business:fridFile:edit
     */
    @RequiresPermissions("business:fridFile:edit")
    @Log(title = "并网文件管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@Validated @RequestBody GridFileUpdateDTO dto) {
        return R.ok(gridFileService.update(dto));
    }

    /**
     * 修改数据 business:fridFile:editData
     */
    @RequiresPermissions("business:fridFile:editData")
    @Log(title = "并网参数", businessType = BusinessType.UPDATE)
    @PutMapping("/data")
    public R<Boolean> editData(@Validated @NotEmpty @RequestBody List<GridFileDataUpdateDTO> dto) {
        return R.ok(gridFileService.getGridFileDataService().update(dto));
    }

    /**
     * 详情
     */
    @GetMapping(value = "/{id}")
    public R<GridFile> getInfo(@PathVariable Integer id) {
        return R.ok(gridFileService.getInfo(id));
    }

    /**
     * 数据详情
     */
    @GetMapping(value = "/data/{id}")
    public R<List<GridFileData>> getDataInfo(@PathVariable Integer id) {
        return R.ok(gridFileService.getGridFileDataService().get(id));
    }

    /**
     * 查询国家
     */
    @GetMapping(value = "/country")
    public R<List<IntStr>> getCountry() {
        return R.ok(gridFileService.getCountry());
    }

    /**
     * 下拉列表
     */
    @GetMapping("/list")
    public R<List<GridFileVO>> list(@RequestParam(required = false) @Schema(description = "国家编号") Integer countryId,
                                    @RequestParam(required = false) @Schema(description = "文件名称") String name) {
        return R.ok(gridFileService.all(countryId, name));
    }

    /**
     * 导出 business:fridFile:export
     */
    @Log(title = "并网参数", businessType = BusinessType.EXPORT)
    @RequiresPermissions("business:fridFile:export")
    @PostMapping("/data/export")
    public void export(HttpServletResponse response, HttpServletRequest request, @RequestParam Integer gridFileId) {
        List<GridFileData> list = gridFileService.getGridFileDataService().get(gridFileId);
        ExcelUtil<GridFileData> util = new ExcelUtil<>(GridFileData.class);
        util.setLocal(LanguageEnum.getLocal(request));
        util.exportExcel(response, list, "并网参数");
    }

    /**
     * 导入 business:fridFile:import
     */
    @Log(title = "并网参数", businessType = BusinessType.IMPORT)
    @RequiresPermissions("business:fridFile:import")
    @PostMapping("/data/import")
    public R<Boolean> importData(HttpServletRequest request, MultipartFile file, @RequestParam Integer gridFileId) throws Exception {
        ExcelUtil<GridFileDataImport> util = new ExcelUtil<>(GridFileDataImport.class);
        util.setLocal(LanguageEnum.getLocal(request));
        List<GridFileDataImport> list = util.importExcel(file.getInputStream());
        return R.ok(gridFileService.getGridFileDataService().importData(gridFileId, list));
    }

    /**
     * 导入基础数据
     */
    @Log(title = "并网参数", businessType = BusinessType.IMPORT)
    @PostMapping("/base/data/import")
    public R<Boolean> importBaseData(HttpServletRequest request, MultipartFile file) throws Exception {
        ExcelUtil<GridData> util = new ExcelUtil<>(GridData.class);
        util.setLocal(LanguageEnum.getLocal(request));
        List<GridData> list = util.importExcel(file.getInputStream());
        return R.ok(gridFileService.getGridFileDataService().importBaseData(list));
    }

    /**
     * 分页 business:fridFile:page
     */
    @RequiresPermissions("business:fridFile:page")
    @GetMapping("/page")
    public TableDataInfo<GridFile> page(@Validated GridFilePageReqDTO dto) {
        Page<GridFile> page = startPage(dto);
        gridFileService.page(dto);
        return getDataTable(page);
    }

    /**
     * 删除 business:fridFile:remove
     */
    @RequiresPermissions("business:fridFile:remove")
    @Log(title = "并网文件管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public R<Boolean> remove(@PathVariable Integer id) {
        return R.ok(gridFileService.delete(id));
    }
}
