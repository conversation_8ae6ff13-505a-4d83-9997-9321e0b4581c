package com.wanhe.business.service;

import com.ruoyi.common.security.service.IMPJBaseService;
import com.ruoyi.system.api.RemoteBizDataService;
import com.wanhe.business.domain.StationDevice;
import com.wanhe.business.mapper.PowerStationMapper;
import com.wanhe.business.pojo.device.FirmwareVersionsVO;
import com.wanhe.business.pojo.station.*;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @date 2024-10-26
*/
public interface IStationDeviceService extends IMPJBaseService<StationDevice> {

    PowerStationMapper getPowerStationMapper();
    RemoteBizDataService getRemoteBizDataService();

    StationDevice get(Integer id);

    /**
     * 通过sn获取信息
     *
     * @param sn sn
     * @return {@link StationDevice }
     */
    StationDevice getInfoBySn(String sn);

    boolean addEmu(EmuDTO dto);

    boolean addWn(WnDTO dto);

    void addMeter(MeterDTO dto);

    Integer updateEmu(EmuUpdateDTO dto);

    boolean addWnApp(WnAppDTO dto);

    Integer editWn(WnUpdateDTO dto);

    void page(StationDevicePageReqDTO dto);

    void pageEmu(EmuPageReqDTO dto);

    StationDevice delete(Integer id);

    Map<Integer, List<StationDevice>> mapList(Integer powerStationId);

    List<StationDevice> emuList(Integer powerStationId);

    List<StationDevice> wnList(Integer powerStationId);

    MeterDTO meter(Integer powerStationId);

    List<StationDevice> tree(StationDeviceReqDTO dto);

    StationDevice replace(ReplaceDTO dto);

    boolean activate(Integer powerStationId);

    List<FirmwareVersionsVO> versions(Integer powerStationId);

    PowerStationStatisticsVO queryPowerStationDevice();

    List<HomeDeviceStatisticsVO> queryDeviceStatistics();

    void devicePage(DevicePageReqDTO dto);

    List<String> sns(Integer powerStationId, Integer deviceType);

    Integer networkingMode(Integer deviceId);

    List<StationDevice> networkingModeList(Integer powerStationId);

    void checkSn(String sn);

    void checkSn2(String sn, Integer type);

    List<DeviceCountVo> countByType(Integer powerStationId);

}
