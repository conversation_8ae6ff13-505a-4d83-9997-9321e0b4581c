package com.wanhe.business.service;


import com.ruoyi.common.core.domain.IntStr;
import com.ruoyi.common.security.service.IBaseService;
import com.wanhe.business.domain.ProductCategory;
import com.wanhe.business.pojo.product.ProductCategoryDTO;
import com.wanhe.business.pojo.product.ProductCategoryPageReqDTO;
import com.wanhe.business.pojo.product.ProductCategoryUpdateDTO;

import java.util.List;

/**
* <AUTHOR>
* @date 2024-10-26
*/
public interface IProductCategoryService extends IBaseService<ProductCategory> {

    boolean add(ProductCategoryDTO dto);

    boolean update(ProductCategoryUpdateDTO dto);

    boolean delete(Integer id);

    void page(ProductCategoryPageReqDTO dto);

    List<IntStr> all();

}
