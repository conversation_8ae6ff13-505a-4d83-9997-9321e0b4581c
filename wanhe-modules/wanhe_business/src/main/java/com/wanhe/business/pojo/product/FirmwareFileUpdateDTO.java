package com.wanhe.business.pojo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(name = "FirmwareFileUpdateDTO", description = "固件文件表")
public class FirmwareFileUpdateDTO extends FirmwareFileDTO {

    @Schema(description = "固件文件id")
    @NotNull
    @Positive
    private Integer id;

}
