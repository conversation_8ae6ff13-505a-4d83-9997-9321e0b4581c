package com.wanhe.business.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.domain.base.ICUDEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 工单
 *
 * <AUTHOR>
 * @date 2025/06/18
 */
@Data
@TableName("wh_work_order")
@Schema(name = "WorkOrder", description = "工单服务表")
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = false)
public class WorkOrder extends ICUDEntity {

    /**
     * 工单序列号
     */
    @Schema(description = "工单sn/序列号")
    private String orderSn;

    /**
     * 站点id
     */
    @Schema(description = "站点id")
    private Long stationId;

    /**
     * 站点名称
     */
    @Schema(description = "站点名称")
    private String stationName;

    /**
     * 设备id
     */
    @Schema(description = "设备id")
    private Long deviceId;

    /**
     * 设备编号
     */
    @Schema(description = "设备编号")
    private String deviceNumber;

    /**
     * 设备型号
     */
    @Schema(description = "设备型号")
    private String deviceModel;

    /**
     * 工单类型 1-故障 2-外观 3-资讯 4-其他
     */
    @Schema(description = "工单类型 1-故障 2-外观 3-资讯 4-其他")
    private Integer orderType;

    /**
     * 工单状态 1-未开始 2-进行中 3-已完成
     */
    @Schema(description = "工单状态 1-未开始 2-进行中 3-已完成")
    private Integer orderStatus;

    /**
     * 告警id
     */
    @Schema(description = "告警id")
    private Long faultId;

    /**
     * 故障代码
     */
    @Schema(description = "故障代码")
    private String faultCode;

    /**
     * 故障key
     */
    @Schema(description = "故障key")
    private String faultKey;

//    /**
//     * 告警描述
//     */
//    @Schema(description = "告警描述")
//    private String faultDescription;

    /**
     * 告警开始时间
     */
    @Schema(description = "告警开始时间")
    private LocalDateTime faultStartTime;

    /**
     * 告警结束时间
     */
    @Schema(description = "告警结束时间")
    private LocalDateTime faultEndTime;

    /**
     * 提交时间
     */
    @Schema(description = "提交时间")
    private LocalDateTime commitTime;

    /**
     * 问题描述
     */
    @Schema(description = "问题描述")
    private String problemDescription;

    /**
     * 附件
     */
    @Schema(description = "附件")
    private String attachment;

    /**
     * 确认人id
     */
    @Schema(description = "确认人id")
    private Long confirmId;

    /**
     * 确认部门id
     */
    @Schema(description = "确认部门id")
    private Long confirmDeptId;

    /**
     * 用户id
     */
    @Schema(description = "用户id")
    private Long userId;

    /**
     * 联系人
     */
    @Schema(description = "联系人")
    private String contact;

    /**
     * 电话号码
     */
    @Schema(description = "电话号码")
    private String phoneNumber;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    private String email;

    /**
     * 地址
     */
    @Schema(description = "地址")
    private String address;
}
