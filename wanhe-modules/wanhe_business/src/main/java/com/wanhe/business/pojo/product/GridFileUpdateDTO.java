package com.wanhe.business.pojo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(name = "GridFileUpdateDTO", description = "并网文件表")
public class GridFileUpdateDTO extends GridFileDTO {

    @Schema(description = "并网文件id")
    @NotNull
    @Positive
    private Integer id;

}
