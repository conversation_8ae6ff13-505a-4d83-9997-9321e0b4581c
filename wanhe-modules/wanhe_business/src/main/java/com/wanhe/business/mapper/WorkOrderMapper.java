package com.wanhe.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wanhe.business.domain.OrderProcess;
import com.wanhe.business.domain.WorkOrder;
import com.wanhe.business.pojo.workOrder.dto.WorkOrderPageDTO;
import com.wanhe.business.pojo.workOrder.vo.WorkOrderPageVO;
import com.wanhe.business.pojo.workOrder.vo.WorkOrderVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工单映射器
 *
 * <AUTHOR>
 * @date 2025/06/21
 */
public interface WorkOrderMapper extends BaseMapper<WorkOrder> {

    /**
     * 页
     *
     * @param workOrderPage 工作订单页面
     * @param userId        用户id
     * @return {@link List }<{@link WorkOrderPageVO }>
     */
    List<WorkOrderPageVO> page(@Param("dto") WorkOrderPageDTO workOrderPage, @Param("userId") Long userId,
                               @Param("deptId") Long deptId, @Param("orderBy") String orderBy);

    /**
     * 获取详细信息
     *
     * @param orderId 订单ID
     * @return {@link WorkOrderVO }
     */
    WorkOrderVO getDetail(@Param("orderId") Long orderId);

    /**
     * 获取处理人信息
     *
     * @param processorId 处理人
     */
    OrderProcess getProcessorInfo(@Param("processorId") Long processorId);

    /**
     * 更新订单状态
     *
     * @param orderId     订单ID
     * @param orderStatus 订单状态
     */
    void updateOrderStatus(@Param("orderId") Long orderId, @Param("orderStatus") Integer orderStatus);

    /**
     * 添加确认人
     *
     * @param confirmId   确认id
     * @param orderStatus 订单状态
     * @param orderId     订单ID
     */
    void addConfirmPerson(@Param("confirmId") Long confirmId, @Param("orderStatus") Integer orderStatus, @Param("orderId") Long orderId);

    /**
     * 获取不完整工单总数
     *
     * @param userId 用户id
     * @return {@link Integer }
     */
    Integer getIncompleteOrderCount(@Param("userId") Long userId);
}
