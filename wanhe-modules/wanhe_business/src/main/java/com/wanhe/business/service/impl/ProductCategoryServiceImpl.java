package com.wanhe.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.domain.IntStr;
import com.ruoyi.common.core.enums.I18nTypeEnum;
import com.ruoyi.common.core.enums.LanguageEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.security.service.impl.BaseServiceImpl;
import com.wanhe.business.domain.Product;
import com.wanhe.business.domain.ProductCategory;
import com.wanhe.business.mapper.ProductCategoryMapper;
import com.wanhe.business.mapper.ProductMapper;
import com.wanhe.business.pojo.product.ProductCategoryDTO;
import com.wanhe.business.pojo.product.ProductCategoryPageReqDTO;
import com.wanhe.business.pojo.product.ProductCategoryUpdateDTO;
import com.wanhe.business.service.IProductCategoryService;
import com.wanhe.business.service.ISysI18nService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
* <AUTHOR>
* @date 2024-10-26
*/
@Service
public class ProductCategoryServiceImpl extends BaseServiceImpl<ProductCategoryMapper, ProductCategory> implements IProductCategoryService {

    @Resource
    private ProductMapper productMapper;
    @Resource
    private ISysI18nService i18nService;

    @Override
    @Transactional
    public boolean add(ProductCategoryDTO dto) {
        ProductCategory entity = BeanUtil.copyProperties(dto, ProductCategory.class);
        save(entity);
        i18nService.add(I18nTypeEnum.CATEGORY, entity.getId(), entity.getName());
        return true;
    }

    @Override
    @Transactional
    public boolean update(ProductCategoryUpdateDTO dto) {
        ProductCategory entity = getByIdThrow(dto.getId());
        BeanUtil.copyProperties(dto, entity);
        i18nService.update(I18nTypeEnum.CATEGORY, entity.getId(), entity.getName());
        return updateById(entity);
    }

    @Override
    public boolean delete(Integer id) {
        i18nService.delete(I18nTypeEnum.CATEGORY, id);
        return removeById(id);
    }

    @Override
    public void page(ProductCategoryPageReqDTO dto) {
        lambdaQuery()
                .like(StrUtil.isNotBlank(dto.getName()), ProductCategory::getName, dto.getName())
                .list();
    }

    @Override
    public List<IntStr> all() {
        return baseMapper.all(LanguageEnum.getLang());
    }

}
