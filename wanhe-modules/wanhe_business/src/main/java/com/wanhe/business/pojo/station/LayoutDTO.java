package com.wanhe.business.pojo.station;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-10-28
 */
@Data
public class LayoutDTO {

    @Schema(description = "电站id")
    @NotNull
    @Positive
    private Integer id;

    @Schema(description = "布局设计json")
    @NotNull
    private JSONObject layout;

}
