package com.wanhe.business.service;

import com.ruoyi.common.security.service.IBaseService;
import com.wanhe.business.domain.WorkOrder;
import com.wanhe.business.pojo.workOrder.dto.*;
import com.wanhe.business.pojo.workOrder.vo.WorkOrderVO;

/**
 * 工单服务
 *
 * <AUTHOR>
 * @date 2025/06/21
 */
public interface IWorkOrderService extends IBaseService<WorkOrder> {

    /**
     * 获取工单分页数据
     *
     * @param dto 到
     */
    void page(WorkOrderPageDTO dto);


    /**
     * 获取工单详细信息
     *
     * @param orderId 订单ID
     * @return {@link WorkOrderVO }
     */
    WorkOrderVO getDetail(Long orderId);

    /**
     * 保存工单
     *
     * @param dto 到
     */
    void saveWorkOrder(WorkOrderDTO dto);

    /**
     * 更新工单
     *
     * @param dto 到
     */
    void updateWorkOrder(WorkOrderDTO dto);

    /**
     * 添加处理人
     *
     * @param dto 到
     */
    void addProcessor(ProcessorAddDTO dto);

    /**
     * 添加工单流程
     *
     * @param orderProcess 工单流程
     */
    void addOrderProcess(OrderProcessDTO orderProcess);

    /**
     * 添加工单评价
     *
     * @param orderEvaluate 工单评价
     */
    void addOrderEvaluate(OrderEvaluateDTO orderEvaluate);

    /**
     * 删除工单
     *
     * @param orderId 订单ID
     */
    void deleteWorkOrder(Long orderId);

    /**
     * 获取未完成工单数量
     *
     * @return {@link Integer }
     */
    Integer getIncompleteOrderCount();

    /**
     * 根据故障id判断工单
     *
     * @param faultId 故障id
     * @return {@link Boolean }
     */
    Boolean judgeWorkOrderByFaultId(Long faultId);
}
