package com.wanhe.business.pojo.workOrder.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单评估dto
 *
 * <AUTHOR>
 * @date 2025/6/21
 */
@Data
public class OrderEvaluateDTO {

    @Schema(description = "评价id")
    private Long id;

    /**
     * 工单id
     */
    @Schema(description = "工单id")
    private Long orderId;
    /**
     * 评价内容
     */
    @Schema(description = "评价内容")
    private String content;
    /**
     * 服务态度评分（默认5分）
     */
    @Schema(description = "服务态度评分（默认5分）")
    private BigDecimal serviceScore;
    /**
     * 响应速度评分（默认5分）
     */
    @Schema(description = "响应速度评分（默认5分）")
    private BigDecimal responseScore;
    /**
     * 处理结果评分（默认5分）
     */
    @Schema(description = "处理结果评分（默认5分）")
    private BigDecimal handleScore;
    /**
     * NPS值（默认5分）
     */
    @Schema(description = "NPS值（默认5分）")
    private BigDecimal npsScore;
    /**
     * 综合评分
     */
    @Schema(description = "综合评分")
    private BigDecimal score;

}
