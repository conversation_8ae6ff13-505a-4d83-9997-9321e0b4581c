package com.wanhe.business.pojo.station;

import com.ruoyi.common.core.domain.PageReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/10/28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PowerStationPageReqDTO extends PageReqDTO {

    @Schema(description = "查询收藏", hidden = true)
    private boolean collect;

    @Schema(description = "电站ID")
    private Integer id;

    @Schema(description = "电站名称")
    private String stationName;

    @Schema(description = "电站状态 （0建设中 1在线 2离线 3告警）")
    private Integer stationStatus;

    @Schema(description = "国家ID")
    private Integer countryId;

    @Schema(description = "省份ID")
    private Integer provinceId;

    @Schema(description = "电站类型（0:屋顶光伏 1:阳台光伏 2:商业光伏）")
    private String stationType;

    @Schema(description = "开始时间")
    protected LocalDateTime beginTime;

    @Schema(description = "结束时间")
    protected LocalDateTime endTime;

    @Schema(description = "装机容量最小值")
    private BigDecimal installedCapacityMin;

    @Schema(description = "装机容量最大值")
    private BigDecimal installedCapacityMax;

    @Schema(description = "排序字段")
    private String sortField;

    @Schema(description = "排序 0升序 1降序 2不排序")
    private Integer sort;

}
