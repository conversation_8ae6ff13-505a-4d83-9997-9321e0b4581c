package com.wanhe.business.pojo.product;

import com.ruoyi.common.core.domain.PageReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class FirmwareFilePageReqDTO extends PageReqDTO {

    @Schema(description = "固件类型")
    private Integer type;

    @Schema(description = "设备类型")
    private Integer deviceType;

    @Schema(description = "设备型号")
    private String deviceModel;

}
