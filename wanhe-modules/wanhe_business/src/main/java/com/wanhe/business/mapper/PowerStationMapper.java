package com.wanhe.business.mapper;

import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.github.yulichang.base.MPJBaseMapper;
import com.ruoyi.common.core.domain.IntInt;
import com.ruoyi.common.core.domain.IntStr;
import com.ruoyi.common.core.domain.KV;
import com.ruoyi.system.api.domain.SysDept;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.dto.PowerStationListDataVO;
import com.wanhe.business.domain.PowerStation;
import com.wanhe.business.domain.StationDevice;
import com.wanhe.business.pojo.station.HomeMapDataVO;
import com.wanhe.business.pojo.station.PowerStationSiteVO;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
* <AUTHOR>
* @date 2024-10-28
*/
public interface PowerStationMapper extends MPJBaseMapper<PowerStation> {

    @Select("select dept_id,user_id from sys_user where tenant_id = #{tenantId} and del_flag = '0' order by create_time desc limit 1")
    SysUser getUser(Long tenantId);

    @Update("update wh_power_station s inner join sys_user_station u on u.user_id = #{userId} and u.power_station_id = #{id} and s.id = u.power_station_id set s.permission = #{permission}")
    long updatePermission(Long userId, Integer id, Boolean permission);

    @Select("select s.* from wh_power_station s inner join sys_user_station u on u.user_id = #{userId} and s.id = u.power_station_id and s.deleted = 0 order by s.id")
    @Results({
            @Result(column = "image_url", property = "imageUrl", typeHandler = Fastjson2TypeHandler.class),
            @Result(column = "install_image_url", property = "installImageUrl", typeHandler = Fastjson2TypeHandler.class),
            @Result(column = "fault_notification_method", property = "faultNotificationMethod", typeHandler = Fastjson2TypeHandler.class),
            @Result(column = "module", property = "module", typeHandler = Fastjson2TypeHandler.class),
            @Result(column = "is_reflux", property = "reflux"),
            @Result(column = "is_network", property = "network"),
            @Result(column = "is_allow_owner_view_layout", property = "allowOwnerViewLayout")
    })
    List<PowerStation> getPermissionList(Long userId);

    @Delete("delete from sys_user_station where power_station_id = #{id}")
    void deleteUserStation(Integer id);

    @Delete("delete from sys_api_token_station where station_id = #{id}")
    void deleteApiTokenStation(Integer id);

    @Select("update wh_power_station set station_status = if(station_status = 0, 0, #{status}), num = #{num} where id = #{id}")
    Integer updateStatusNum(Integer id, int status, int num);

    @Select("select station_type from wh_power_station where id = #{id}")
    Integer getStationType(Integer id);

    @Select("""
    select s.*,c.user_id is not null as collect,(select name from sys_district where id = s.country_id) as country,
    (select name from sys_district where id = s.province_id) as province,
    (select dept_name from sys_dept where dept_id = s.tenant_id) as tenantName
    from wh_power_station s
    left join wh_power_station_collect c on c.user_id = ${userId} and c.power_station_id = s.id
    where s.id = ${id} and s.deleted = 0
    """)
    @Results({
            @Result(column = "image_url", property = "imageUrl", typeHandler = Fastjson2TypeHandler.class),
            @Result(column = "install_image_url", property = "installImageUrl", typeHandler = Fastjson2TypeHandler.class),
            @Result(column = "fault_notification_method", property = "faultNotificationMethod", typeHandler = Fastjson2TypeHandler.class),
            @Result(column = "module", property = "module", typeHandler = Fastjson2TypeHandler.class),
            @Result(column = "is_reflux", property = "reflux"),
            @Result(column = "is_network", property = "network"),
            @Result(column = "is_allow_owner_view_layout", property = "allowOwnerViewLayout")
    })
    PowerStation getInfo(Integer id, Long userId);

    //迁移设备告警故障
    @Update("update wh_device_alarm set tenant_id = #{tenantId} where power_station_id = #{id}")
    void transferDeviceAlarm(Integer id, Long tenantId);

    //迁移设备替换记录
    @Update("update wh_device_replace_record set tenant_id = #{tenantId} where power_station_id = #{id}")
    void transferDeviceReplaceRecord(Integer id, Long tenantId);

    //迁移电站
    @Update("update wh_power_station set tenant_id = #{tenantId}, dept_id = #{deptId}, create_id = #{createId} where id = #{id}")
    void transferPowerStation(Integer id, Long tenantId, Long deptId, Long createId);

    //迁移设备远程下发记录
    @Update("update wh_station_device set tenant_id = #{tenantId} where power_station_id = #{id}")
    void transferStationDevice(Integer id, Long tenantId);

    //迁移设备远程下发记录
    @Update("update wh_station_device_remote set tenant_id = #{tenantId} where power_station_id = #{id}")
    void transferStationDeviceRemote(Integer id, Long tenantId);

    @Select("select id as k, name as v from sys_district where id in(${pid})")
    List<IntStr> getDistrict(String ids);

    @Select("select date_format(create_time, '%Y%m') as k, count(*) as v from wh_power_station where deleted = 0 and create_time between '${beginYear}-01-01 00:00:00' and '${endYear}-12-31 23:59:59'${dataScopeSql} group by k")
    List<IntInt> queryPowerStationStatistics(int beginYear, int endYear, String dataScopeSql);

    @Select("select longitude,latitude,time_zone from wh_power_station where id = #{id}")
    PowerStation getLonLat(Integer id);

    @Select("select id as k, product_name as v from wh_product where id in(${productIds})")
    List<IntStr> getProducts(String productIds);

    @Select("select id as k, vendor_name as v from wh_vendor where id in(${vendorIds})")
    List<IntStr> getVendors(String vendorIds);

    @Select("select i.name_${language} from sys_dict_data d left join sys_i18n i on i.type = 2 and i.data_id = d.dict_code where d.dict_type = #{type} and d.dict_value = #{value}")
    String getLabel(String language, String type, String value);

    @Select("select id, station_type, tenant_id, create_time, layout from wh_power_station where id = #{id}")
    PowerStation get(Integer id);

    @Select("select u.* from sys_user u inner join sys_user_station us on u.user_id = us.user_id and us.power_station_id = #{powerStationId} and u.del_flag = '0'")
    List<SysUser> getUsers(Integer id);

    @Select("select * from sys_dept where dept_id = #{tenantId}")
    SysDept getTenant(Long tenantId);

    @Select("select dept_name from sys_dept where dept_id = (select tenant_id from wh_power_station where id = #{id})")
    String getTenantNameByStationId(Integer id);

    @Select("select dept_id, dept_name from sys_dept where dept_id in(${tenantIds})")
    List<SysDept> getTenantList(String tenantIds);

    @Select("select us.power_station_id as k,group_concat(u.nick_name) as v from sys_user_station us inner join sys_user u on u.user_id = us.user_id where us.power_station_id in(${ids}) group by us.power_station_id")
    List<IntStr> getUserDataList(String ids);

    @Select("select distinct `status` from wh_station_device where power_station_id = #{id} and deleted = 0 and device_type in(0,1) and pid is null")
    List<Integer> getGatewayStatus(Integer id);

    @Select("select distinct `status` from wh_station_device where power_station_id = #{id} and deleted = 0 and device_type = 1")
    List<Integer> getSysStatus(Integer id);

    @Select("select group_concat(u.nick_name) from sys_user_station us inner join sys_user u on u.user_id = us.user_id where us.power_station_id = #{id} group by us.power_station_id")
    String getUserData(Integer id);

    @Select("""
    select count(*) as count,ifnull(sum(s.installed_capacity),0) as capacity,e.day_kwh,e.month_kwh,e.year_kwh,e.all_kwh
    from wh_power_station s inner join wh_power_station_extend e on s.id = e.id and s.deleted = 0${dataScopeSql}
    """)
    PowerStationListDataVO data(String tenantIds);

    @Select("select device_type,count(*) as wnNum from wh_station_device where device_type in(0,1) and deleted = 0${dataScopeSql} group by device_type")
    List<StationDevice> deviceData(String dataScopeSql);

    @Select("select device_type,count(*) as wnNum from wh_station_device where power_station_id = #{id} and device_type in(0,1) and deleted = 0 group by device_type")
    List<StationDevice> deviceDataById(Integer id);

    @Select("select id as k, station_name as v from wh_power_station where deleted = 0${dataScopeSql} order by id desc")
    List<KV> all(String dataScopeSql);

    @Select("select id,station_name,station_status,latitude,longitude,station_details,installed_capacity,station_type from wh_power_station where deleted = 0${dataScopeSql} order by id desc")
    List<PowerStationSiteVO> allSite(String dataScopeSql);

    @Select("select id,station_name,latitude,longitude from wh_power_station where station_status > 0 and deleted = 0${dataScopeSql} order by id desc")
    List<PowerStationSiteVO> allSite2(String dataScopeSql);

    @Select("select id as k,station_name as v from wh_power_station where id in(${ids})")
    List<IntStr> getIdName(String ids);

    @Select("""
    select count(*) as stationNum, ifnull(sum(installed_capacity),0) as capacityNum from wh_power_station where deleted = 0${dataScopeSql}
    union all
    select count(*) as stationNum, ifnull(sum(installed_capacity),0) as capacityNum from wh_power_station where deleted = 0 and create_time between #{byBegin} and #{byEnd}${dataScopeSql}
    union all
    select count(*) as stationNum, ifnull(sum(installed_capacity),0) as capacityNum from wh_power_station where deleted = 0 and create_time between #{syBegin} and #{syEnd}${dataScopeSql}
    """)
    List<HomeMapDataVO> stationData(String dataScopeSql, LocalDateTime byBegin, LocalDateTime byEnd, LocalDateTime syBegin, LocalDateTime syEnd);

    @Select("""
    select count(*) from wh_station_device where deleted = 0 and device_type = 1${dataScopeSql}
    union all
    select count(*) from wh_station_device where deleted = 0 and device_type = 1 and create_time between #{byBegin} and #{byEnd}${dataScopeSql}
    union all
    select count(*) from wh_station_device where deleted = 0 and device_type = 1 and create_time between #{syBegin} and #{syEnd}${dataScopeSql}
    union all
    select count(*) from wh_device_replace_record where is_delete = 0 and device_type = 1${dataScopeSql}
    union all
    select count(*) from wh_device_replace_record where is_delete = 0 and device_type = 1 and create_time between #{byBegin} and #{byEnd}${dataScopeSql}
    union all
    select count(*) from wh_device_replace_record where is_delete = 0 and device_type = 1 and create_time between #{syBegin} and #{syEnd}${dataScopeSql}
    """)
    List<Integer> wnData(String dataScopeSql, LocalDateTime byBegin, LocalDateTime byEnd, LocalDateTime syBegin, LocalDateTime syEnd);

    @Select("select count(*) from sys_user_station where power_station_id = #{powerStationId}")
    int countOwner(Integer powerStationId);

    @Update("update wh_power_station set layout = #{layout} where id = #{powerStationId}")
    void updateLayout(Integer powerStationId, String layout);
}
