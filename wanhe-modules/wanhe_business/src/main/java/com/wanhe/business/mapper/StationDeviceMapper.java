package com.wanhe.business.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.ruoyi.common.core.domain.IntInt;
import com.ruoyi.common.core.domain.IntStr;
import com.wanhe.business.domain.StationDevice;
import com.wanhe.business.pojo.device.FirmwareVersionsVO;
import com.wanhe.business.pojo.station.DeviceCountVo;
import com.wanhe.business.pojo.station.DeviceStatisticsVO;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
* <AUTHOR>
* @date 2024-10-28
*/
public interface StationDeviceMapper extends MPJBaseMapper<StationDevice> {

    @Update("update wh_device_alarm set is_resolved = 1, resolved_at = now() where device_id = #{deviceId}")
    void resolved(Integer deviceId);

    @Select("select id, number, status, pid from wh_station_device where power_station_id = #{stationId} and device_type in(0,1) and deleted = 0")
    List<StationDevice> getEmuInvList(Integer stationId);

    @Select("select id, number, power_station_id, tenant_id, power_grid_seat, device_type from wh_station_device where power_station_id = #{stationId} and device_type in(0, 2) and deleted = 0")
    List<StationDevice> getEmuMeterList(Integer stationId);

    @Select("select id, number, status, power_station_id, tenant_id from wh_station_device where power_station_id = #{stationId} and device_type = 1 and deleted = 0")
    List<StationDevice> getInvList(Integer stationId);

    @Select("select id as k,number as v from wh_station_device where number in(${numbers}) and power_station_id = #{stationId} and deleted > 0")
    List<IntStr> getDelSn(String numbers, Integer stationId);

    @Update("update wh_station_device set is_delete = 0, deleted = 0 where id in(${ids}) and power_station_id = #{stationId}")
    void recover(String ids, Integer stationId);

    @Select("select id from wh_station_device where device_model = #{deviceModel} limit 1")
    Integer getIdByDeviceModel(String deviceModel);

    @Select("select number from wh_station_device where power_station_id = #{powerStationId} and deleted = 0 and device_type = #{deviceType} order by number")
    List<String> sns(Integer powerStationId, Integer deviceType);

    @Update("update wh_station_device set device_model = #{newDeviceModel} where device_model = #{oldDeviceModel}")
    void updateDeviceModel(String oldDeviceModel, String newDeviceModel);

    @Select("select d.*,s.station_name,s.time_zone from wh_station_device d inner join wh_power_station s on d.power_station_id = s.id and d.id = #{id} and d.deleted = 0")
    StationDevice get(Integer id);

    @Select("select d.*,s.station_name,s.time_zone from wh_station_device d inner join wh_power_station s on d.power_station_id = s.id and d.number = #{sn} and d.deleted = 0")
    StationDevice getInfoBySn(String sn);

    @Select("select device_type,count(*) as id from wh_station_device where deleted=0${dataScopeSql} group by device_type")
    List<StationDevice> queryPowerStationDevice(String dataScopeSql);

    @Select("select date_format(create_time, '%m') as month, device_type, count(*) as stationNum from wh_station_device where deleted = 0 and device_type in(0, 1) and create_time between '${year}-01-01 00:00:00' and '${year}-12-31 23:59:59'${dataScopeSql} group by month, device_type")
    List<DeviceStatisticsVO> queryDeviceStatistics(int year, String dataScopeSql);

    @Select("select date_format(create_time, '%m') as month, device_type, count(*) as stationNum from wh_device_replace_record where is_delete = 0 and device_type in(0, 1) and create_time between '${year}-01-01 00:00:00' and '${year}-12-31 23:59:59'${dataScopeSql} group by month, device_type")
    List<DeviceStatisticsVO> queryDeviceReplaceStatistics(int year, String dataScopeSql);

    @Select("""
    select t.id,t.number,t.device_model,t.software_version,t.pid,t.comm_type,t.device_type,t.version_number,ff.id as file_id from (
        select d.id,d.number,d.device_model,d.software_version,d.pid,d.comm_type,d.device_type,max(f.version_number) as version_number
        from wh_station_device d
        left join wh_firmware_file f on f.type = #{type} and f.device_type = d.device_type and f.device_model = d.device_model and f.version_number >= d.software_version and f.deleted = 0 and f.is_default_enabled = 1
        where d.power_station_id = #{powerStationId} and d.deleted = 0 and d.device_type = #{deviceType}
        group by d.id
    ) t left join wh_firmware_file ff on ff.type = #{type} and ff.device_type = t.device_type and ff.device_model = t.device_model and ff.version_number = t.version_number and ff.deleted = 0
    """)
    List<FirmwareVersionsVO> versions(Integer powerStationId, Integer deviceType, Integer type);

    @Select("select d.status as k, (select status from wh_station_device where id = d.pid) as v from wh_station_device d where d.id = #{id}")
    IntInt getStatus(Integer id);

    @Select("SELECT device_type AS type, COUNT(*) AS deviceCount FROM wh_station_device WHERE power_station_id = #{powerStationId} and deleted=0 GROUP BY device_type ORDER BY device_type;")
    List<DeviceCountVo> countByType(Integer powerStationId);
}
