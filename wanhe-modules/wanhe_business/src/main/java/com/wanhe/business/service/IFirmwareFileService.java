package com.wanhe.business.service;


import com.ruoyi.common.core.domain.IntStr;
import com.ruoyi.common.security.service.IMPJBaseService;
import com.wanhe.business.domain.FirmwareFile;
import com.wanhe.business.pojo.product.FirmwareFileDTO;
import com.wanhe.business.pojo.product.FirmwareFilePageReqDTO;
import com.wanhe.business.pojo.product.FirmwareFileUpdateDTO;

import java.util.List;

/**
* <AUTHOR>
* @date 2024-10-26
*/
public interface IFirmwareFileService extends IMPJBaseService<FirmwareFile> {

    boolean add(FirmwareFileDTO dto);

    boolean update(FirmwareFileUpdateDTO dto);

    void page(FirmwareFilePageReqDTO dto);

    List<IntStr> list(Integer type, Integer deviceType, String deviceModel);

    boolean delete(Integer id);
}
