package com.wanhe.business.pojo.station;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024-10-28
 */
@Data
public class ModuleDTO {

    @Schema(description = "电站id")
    @NotNull
    @Positive
    private Integer id;

    @Schema(description = "是否允许业主查看组件布局")
    @NotNull
    private Boolean allowOwnerViewLayout;

    @Schema(description = "组件布局默认显示(0：功率 1：电量)")
    private Integer defaultLayoutDisplay;

    @Schema(description = "单个组件的最大功率")
    @Positive
    @Digits(integer = 18, fraction = 2)
    private BigDecimal maxComponentPower;

}
