package com.wanhe.business.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 用户收藏的电站关系表
 * <AUTHOR>
 * @date 2024/11/6
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@TableName("wh_power_station_collect")
public class PowerStationCollect {

    @Schema(description = "用户id")
    @TableId
    private Long userId;

    @Schema(description = "电站id")
    private Integer powerStationId;

    @Schema(description = "创建时间")
    protected LocalDateTime createTime;

    public PowerStationCollect(Long userId, Integer powerStationId) {
        this.userId = userId;
        this.powerStationId = powerStationId;
        this.createTime = LocalDateTime.now();
    }
}
