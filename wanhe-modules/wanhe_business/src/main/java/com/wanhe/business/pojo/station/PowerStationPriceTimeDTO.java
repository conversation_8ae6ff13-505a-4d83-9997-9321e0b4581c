package com.wanhe.business.pojo.station;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @date 2024/11/22
 */
@Data
public class PowerStationPriceTimeDTO {

    @Schema(description = "时间段 1尖时刻 2峰时刻 3平时刻 4谷时刻")
    @NotNull
    private Integer timeBucket;

    @Schema(description = "开始时间")
    @NotNull
    @JsonFormat(pattern = "HH:mm")
    private LocalTime beginTime;

    @Schema(description = "结束时间")
    @NotNull
    @JsonFormat(pattern = "HH:mm")
    private LocalTime endTime;

    @Schema(description = "价格")
    @NotNull
    private BigDecimal price;

}
