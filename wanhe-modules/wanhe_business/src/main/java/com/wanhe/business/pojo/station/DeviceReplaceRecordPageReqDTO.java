package com.wanhe.business.pojo.station;

import com.ruoyi.common.core.domain.PageReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/10/28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DeviceReplaceRecordPageReqDTO extends PageReqDTO {

    @Schema(description = "电站id")
    private Integer powerStationId;

    @Schema(description = "设备类型 （0：EMU 1:微逆 2：电表）有pid是emu微逆 没有就是wifi微逆")
    private Integer deviceType;

    @Schema(description = "序列号")
    private String number;

}
