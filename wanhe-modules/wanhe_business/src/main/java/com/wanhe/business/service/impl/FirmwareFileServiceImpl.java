package com.wanhe.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.ruoyi.common.core.domain.IntStr;
import com.ruoyi.common.core.enums.LanguageEnum;
import org.apache.http.client.methods.HttpGet;
import com.ruoyi.common.core.utils.NumUtil;
import com.ruoyi.common.security.service.impl.MPJBaseServiceImpl;
import com.wanhe.business.domain.FirmwareFile;
import com.wanhe.business.domain.Product;
import com.wanhe.business.domain.ProductCategory;
import com.wanhe.business.mapper.FirmwareFileMapper;
import com.wanhe.business.mapper.ProductCategoryMapper;
import com.wanhe.business.mapper.ProductMapper;
import com.wanhe.business.pojo.product.FirmwareFileDTO;
import com.wanhe.business.pojo.product.FirmwareFilePageReqDTO;
import com.wanhe.business.pojo.product.FirmwareFileUpdateDTO;
import com.wanhe.business.service.IFirmwareFileService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @date 2024-10-26
*/
@Slf4j
@Service
public class FirmwareFileServiceImpl extends MPJBaseServiceImpl<FirmwareFileMapper, FirmwareFile> implements IFirmwareFileService {

    @Resource
    private ProductCategoryMapper productCategoryMapper;
    @Resource
    private ProductMapper productMapper;

    @Override
    public boolean add(FirmwareFileDTO dto) {
        FirmwareFile entity = BeanUtil.copyProperties(dto, FirmwareFile.class);
        verifyUniquenessFirmwareFile(entity);
        md5(entity);
        entity.setVersions(Integer.valueOf(dto.getVersionNumber().replace(".", "")));
        return save(entity);
    }

    private void verifyUniquenessFirmwareFile(FirmwareFile entity) {
        LambdaQueryChainWrapper<FirmwareFile> chainWrapper = lambdaQuery();
        if (entity.getId() != null) {
            chainWrapper.ne(FirmwareFile::getId, entity.getId());
        }
        FirmwareFile old = chainWrapper
                .eq(FirmwareFile::getProductId, entity.getProductId())
                .eq(FirmwareFile::getType, entity.getType())
                .eq(FirmwareFile::getDeviceType, entity.getDeviceType())
                .eq(FirmwareFile::getVersionNumber, entity.getVersionNumber())
                .last("limit 1")
                .one();
        if (old != null) {
            throw new RuntimeException("该固件已存在");
        }
    }

    private void md5(FirmwareFile file) {
        byte[] bytes = null;
        int count = 3;
        while  (bytes == null && count-- > 0) {
            try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                HttpGet httpGet = new HttpGet(file.getFilePath());
                try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
                    HttpEntity entity = response.getEntity();
                    if (entity != null) {
                        bytes = EntityUtils.toByteArray(entity);
                    }
                } catch (IOException e) {
                    log.error("文件下载错误: {}", e.getMessage());
                }
            } catch (IOException e) {
                log.error("文件下载错误: {}", e.getMessage());
            }
        }
        BigDecimal len = new BigDecimal(bytes.length);
        if (len.compareTo(NumUtil.MB) > 0) {
            file.setFileSize(len.divide(NumUtil.MB, 2, RoundingMode.UP).stripTrailingZeros().toPlainString() + "MB");
        } else if (len.compareTo(NumUtil.B) > 0) {
            file.setFileSize(len.divide(NumUtil.B, 2, RoundingMode.UP).stripTrailingZeros().toPlainString() + "KB");
        } else {
            file.setFileSize(len.stripTrailingZeros().toPlainString() + "B");
        }
        file.setFileMd5(DigestUtil.md5Hex(bytes));
    }

    @Override
    public boolean update(FirmwareFileUpdateDTO dto) {
        FirmwareFile entity = getByIdThrow(dto.getId());
        String oldFilePath = entity.getFilePath();
        BeanUtil.copyProperties(dto, entity);
        verifyUniquenessFirmwareFile(entity);
        if (!dto.getFilePath().equals(oldFilePath)) {
            md5(entity);
        }
        entity.setVersions(Integer.valueOf(dto.getVersionNumber().replace(".", "")));
        return updateById(entity);
    }

    @Override
    public void page(FirmwareFilePageReqDTO dto) {
        List<FirmwareFile> list = lambdaJoinQuery()
                .selectAll(FirmwareFile.class)
                .selectAs(Product::getProductName, FirmwareFile::getDeviceModel)
                .leftJoin(Product.class, Product::getId, FirmwareFile::getProductId)
                .eq(dto.getType() != null, FirmwareFile::getType, dto.getType())
                .eq(dto.getDeviceType() != null, FirmwareFile::getDeviceType, dto.getDeviceType())
                .like(StrUtil.isNotBlank(dto.getDeviceModel()), Product::getProductName, dto.getDeviceModel())
                .orderByDesc(FirmwareFile::getId)
                .list();
        if (!list.isEmpty()) {
            Set<Integer> tid = list.stream().map(FirmwareFile::getDeviceType).collect(Collectors.toSet());
            Map<Integer, String> tmap = productCategoryMapper.listByIds(join(tid), LanguageEnum.getLang()).stream().collect(Collectors.toMap(ProductCategory::getId, ProductCategory::getName));
            list.forEach(p -> p.setDeviceTypeName(tmap.get(p.getDeviceType())));
        }
    }

    @Override
    public List<IntStr> list(Integer type, Integer deviceType, String deviceModel) {
        return baseMapper.all(type, deviceType, deviceModel);
    }

    @Override
    public boolean delete(Integer id) {
        return lambdaUpdate().eq(FirmwareFile::getId, id)
                .set(FirmwareFile::getDelete, 1)
                .set(FirmwareFile::getDeleted, id)
                .update();
    }

}
