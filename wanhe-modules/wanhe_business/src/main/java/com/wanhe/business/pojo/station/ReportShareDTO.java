package com.wanhe.business.pojo.station;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/11/23
 */
@Data
public class ReportShareDTO {

    @Schema(description = "电站id")
    @NotNull
    private Integer id;

    @Schema(description = "邮箱")
    @NotBlank
    @Email
    private String email;

}
