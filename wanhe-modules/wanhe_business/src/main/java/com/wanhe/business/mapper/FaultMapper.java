package com.wanhe.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wanhe.business.domain.Fault;
import com.wanhe.business.pojo.product.FaultVO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @date 2024-10-26
*/
public interface FaultMapper extends BaseMapper<Fault> {

    @Select("select f.fault_key, i.name_${language} as fault_description from wh_fault f left join sys_i18n i on i.type = 4 and i.data_id = f.id where f.fault_key in(${keys})")
    List<FaultVO> getByKeys(String keys, String language);

    @Select("select f.fault_key, i.name_${language} as fault_description, n.name_${language} as resolutionSuggestion from wh_fault f left join sys_i18n i on i.type = 4 and i.data_id = f.id left join sys_i18n n on n.type = 5 and n.data_id = f.id where f.fault_key = #{key}")
    FaultVO getByKey(String key, String language);

    @Select("select fault_code,fault_key,bit,fault_description,resolution_suggestion from wh_fault where deleted = 0")
    List<FaultVO> all2();

    @Select("""
    select f.id,f.fault_code,f.bit,i.name_${language} as faultDescription,n.name_${language} as resolutionSuggestion,
    f.fault_level,f.notes from wh_fault f
    left join sys_i18n i on i.type = 4 and i.data_id = f.id
    left join sys_i18n n on n.type = 5 and n.data_id = f.id
    where f.deleted = 0${searchCode} order by f.id
    """)
    List<Fault> page(String language, String searchCode);

    @Select("""
    select f.fault_code,fault_key,f.bit,i.name_${language} as faultDescription,n.name_${language} as resolutionSuggestion,f.fault_level
    from wh_fault f
    left join sys_i18n i on i.type = 4 and i.data_id = f.id
    left join sys_i18n n on n.type = 5 and n.data_id = f.id
    where f.deleted = 0
    """)
    List<FaultVO> all(String language);

    @Delete("truncate table wh_fault")
    void deleteAll();
}
