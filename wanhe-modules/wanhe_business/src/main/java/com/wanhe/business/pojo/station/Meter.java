package com.wanhe.business.pojo.station;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Range;


/**
 * <AUTHOR>
 * @date 2024/10/29
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class Meter {

    @Schema(description = "电表位置 0负载侧电表 1光伏测电表 2并网点电表")
    @NotNull
    @Range(max = 2)
    private Integer seat;

    @Schema(description = "电表SN序列号")
    private String number;

}
