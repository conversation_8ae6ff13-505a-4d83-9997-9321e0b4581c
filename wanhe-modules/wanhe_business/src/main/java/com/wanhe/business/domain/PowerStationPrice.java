package com.wanhe.business.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/22
 */
@Data
@Accessors(chain = true)
@TableName(value = "wh_power_station_price", autoResultMap = true)
@Schema(name = "PowerStationPrice", description = "电站价格表")
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class PowerStationPrice {

    @Schema(description = "主键id")
    @EqualsAndHashCode.Include
    @TableId(type = IdType.AUTO)
    private Integer id;

    @Schema(description = "电站id")
    private Integer powerStationId;

    @Schema(description = "月份")
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private List<Integer> months;

    @Schema(description = "时间段 1尖时刻 2峰时刻 3平时刻 4谷时刻")
    private Integer timeBucket;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "HH:mm")
    private LocalTime beginTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "HH:mm")
    private LocalTime endTime;

    @Schema(description = "价格")
    private BigDecimal price;
}
