package com.wanhe.business.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.domain.base.ITCUDEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024-10-26
 */
@Data
@TableName("wh_device_replace_record")
@Schema(name = "DeviceReplaceRecord", description = "设备替换记录")
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = false)
public class DeviceReplaceRecord extends ITCUDEntity {

    @Schema(description = "电站id")
    private Integer powerStationId;

    @Schema(description = "设备类型 （0：EMU 1:微逆 2：电表）有pid是emu微逆 没有就是wifi微逆")
    private Integer deviceType;

    @Schema(description = "设备id")
    private Integer deviceId;

    @Schema(description = "新设备序列号")
    private String newNumber;

    @Schema(description = "旧设备序列号")
    private String oldNumber;

    @Schema(description = "替换原因")
    private String reason;

    @Schema(description = "质保期（月）")
    private Integer guaranteePeriod;

    @Schema(description = "使用日期")
    private LocalDate useDate;

    @Schema(description = "电站名称")
    @TableField(exist = false)
    private String stationName;

    @Schema(description = "设备类型名称")
    @TableField(exist = false)
    private String deviceTypeName;

    @Schema(description = "时区")
    @TableField(exist = false)
    private String timeZone;

}
