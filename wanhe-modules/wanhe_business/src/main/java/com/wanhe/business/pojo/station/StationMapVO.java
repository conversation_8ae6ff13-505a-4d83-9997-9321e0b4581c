package com.wanhe.business.pojo.station;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/11/5 0005
 */
@Data
public class StationMapVO {


    @Schema(description = "数量")
    private Object totalNum = 0;

    @Schema(description = "0 减少 1增加 2 不变")
    private Integer type ;

    @Schema(description = "当前数据")
    private Object currentData ;

    @Schema(description = "百分比 %")
    private BigDecimal percentage =BigDecimal.ZERO;
}
