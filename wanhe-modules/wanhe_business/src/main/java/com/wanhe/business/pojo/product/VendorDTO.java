package com.wanhe.business.pojo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-10-26
 */
@Data
@Schema(name = "VendorDTO", description = "厂商表")
public class VendorDTO {

    @Schema(description = "厂商名称")
    @NotBlank
    private String vendorName;

    @Schema(description = "厂商负责人")
    private String vendorContactPerson;

    @Schema(description = "厂商联系人")
    private String contactPerson;

    @Schema(description = "区号")
    private String areaCode;

    @Schema(description = "联系人电话")
    private String contactPhone;

    @Schema(description = "联系人地址")
    private String contactAddress;

    @Schema(description = "备注")
    private String remark;

}
