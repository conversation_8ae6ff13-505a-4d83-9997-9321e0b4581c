package com.wanhe.business.pojo.station;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-10-28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "StationDeviceUpdateDTO", description = "设备表")
public class WnUpdateDTO {

    @Schema(description = "设备id")
    private Integer id;

    @Schema(description = "微逆序列号")
    @NotBlank
    private String number;

}
