package com.wanhe.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.core.web.domain.base.ITCUDEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024-11-04
 */
@Data
@TableName("wh_device_alarm")
@Schema(name = "DeviceAlarm", description = "设备告警故障表")
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = false)
public class DeviceAlarm extends ITCUDEntity {

    @Schema(description = "设备id")
    private Integer deviceId;

    @Schema(description = "电站id")
    private Integer powerStationId;

    @Schema(description = "序列号")
    private String snNumber;

    @Schema(description = "告警code")
    private String alarmCode;

    @Schema(description = "告警key")
    private String alarmKey;

    @Schema(description = "告警数据")
    private Integer alarmData;

    @Schema(description = "告警发生时间")
    private LocalDateTime occurredAt;

    @Schema(description = "告警解决时间")
    private LocalDateTime resolvedAt;

    @Schema(description = "告警是否已解决 （0：未解决，1：已解决）")
    @TableField("is_resolved")
    private Integer resolved;

    @Schema(description = "设备类型 （0：EMU 1:微逆 2：电表）")
    private Integer deviceType;

    @Schema(description = "电站名称")
    @TableField(exist = false)
    private String stationName;

    @Schema(description = "告警名称")
    @TableField(exist = false)
    private String alarmName;

    @Schema(description = "操作")
    @TableField(exist = false)
    private String operationMethod;

}
