package com.wanhe.business.pojo.station;

import com.ruoyi.common.core.domain.PageReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/10/28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class EmuPageReqDTO extends PageReqDTO {

    @Schema(description = "电站id")
    @NotNull
    private Integer powerStationId;

    @Schema(description = "emu序列号")
    private String emuSn;

    @Schema(description = "设备类型 （0：EMU 1:微逆 2：电表）")
    private Integer stationType;

}
