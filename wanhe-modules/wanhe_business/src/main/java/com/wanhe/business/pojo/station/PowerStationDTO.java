package com.wanhe.business.pojo.station;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-10-28
 */
@Data
@Schema(name = "PowerStationDTO", description = "电站基础信息表")
public class PowerStationDTO {

    @Schema(description = "电站名称")
    @NotBlank
    private String stationName;

    @Schema(description = "电站类型（0:屋顶光伏 1:阳台光伏 2:商业光伏）")
    @NotBlank
    private String stationType;

    @Schema(description = "装机容量")
    @NotNull
    @PositiveOrZero
    @Digits(integer = 18, fraction = 2)
    private BigDecimal installedCapacity;

    @Schema(description = "国家ID")
    @NotNull
    @Positive
    private Integer countryId;

    @Schema(description = "省份ID")
    @NotNull
    @Positive
    private Integer provinceId;

    @Schema(description = "邮政编码")
    private String postalCode;

    @Schema(description = "时区")
    private String timeZone;

    @Schema(description = "电站封面图片地址")
    @NotNull
    @Size(max = 6)
    private List<String> imageUrl;

    @Schema(description = "纬度")
    @NotBlank
    private String latitude;

    @Schema(description = "经度")
    @NotBlank
    private String longitude;

    @Schema(description = "详细信息")
    private String stationDetails;

    @Schema(description = "组件型号")
    @NotNull
    private List<Module> module;

}
