package com.wanhe.business.pojo.workOrder.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/6/21
 */
@Data
public class OrderProcessDTO {

    @Schema(description = "处理过程id")
    private Long id;

    /**
     * 工单id
     */
    @Schema(description = "工单id")
    private Long orderId;
    /**
     * 严重程度 1-一般 2-中等 3-非常严重
     */
    @Schema(description = "严重程度 1-一般 2-中等 3-非常严重")
    private Integer severity;
    /**
     * 可能原因
     */
    @Schema(description = "可能原因")
    private String possibleCauses;
    /**
     * 处理过程 1-远程调试 2-上门调试 3-更换备件 4-其他
     */
    @Schema(description = "处理过程 1-远程调试 2-上门调试 3-更换备件 4-其他")
    private Integer processSteps;
    /**
     * 处理方法
     */
    @Schema(description = "处理方法")
    private String solution;
    /**
     * 问题解决状态 0-未解决 1-已解决 2-无法解决
     */
    @Schema(description = "问题解决状态 0-未解决 1-已解决 2-无法解决")
    private Integer solutionStatus;
    /**
     * 完成时间
     */
    @Schema(description = "完成时间")
    private LocalDateTime completionTime;
    /**
     * 完成证明
     */
    @Schema(description = "完成证明")
    private String proofCompletion;

    @Schema(description = "工单完成状态  true：完成工单 false:仅保存")
    private Boolean completed;
}
