package com.wanhe.business.service.impl;

import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.ruoyi.common.security.service.impl.MPJBaseServiceImpl;
import com.ruoyi.system.api.domain.GridData;
import com.ruoyi.system.api.domain.GridFileData;
import com.wanhe.business.mapper.GridDataMapper;
import com.wanhe.business.mapper.GridFileDataMapper;
import com.wanhe.business.pojo.product.GridFileDataImport;
import com.wanhe.business.pojo.product.GridFileDataUpdateDTO;
import com.wanhe.business.service.IGridFileDataService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/15
 */
@Service
public class GridFileDataServiceImpl extends MPJBaseServiceImpl<GridFileDataMapper, GridFileData> implements IGridFileDataService {

    @Resource
    private GridDataMapper gridDataMapper;

    @Override
    @Transactional
    public boolean update(List<GridFileDataUpdateDTO> dto) {
        Map<Integer, GridFileDataUpdateDTO> map = dto.stream().collect(Collectors.toMap(GridFileDataUpdateDTO::getId, Function.identity()));
        List<GridFileData> data = baseMapper.getData(dto.get(0).getGridFileId());
        List<GridFileData> list = new ArrayList<>();
        for (GridFileData oldData : data) {
            GridFileDataUpdateDTO nowData = map.get(oldData.getId());
            if (!Objects.equals(nowData.getIssue(), oldData.getIssue()) || !Objects.equals(nowData.getVal(), oldData.getVal())) {
                oldData.setIssue(nowData.getIssue());
                oldData.setVal(nowData.getVal());
                list.add(oldData);
            }
        }
        if (!list.isEmpty()) {
            return updateBatchById(list);
        }
        return true;
    }

    @Override
    @Transactional
    public boolean importData(Integer gridFileId, List<GridFileDataImport> dto) {
        Map<Integer, GridFileDataImport> map = dto.stream().collect(Collectors.toMap(GridFileDataImport::getAddr, Function.identity(), (a, b) -> b));
        List<GridFileData> data = baseMapper.getData(gridFileId);
        List<GridFileData> list = new ArrayList<>();
        for (GridFileData oldData : data) {
            GridFileDataImport nowData = map.get(oldData.getAddr());
            if (!Objects.equals(nowData.getIssue(), oldData.getIssue()) || !Objects.equals(nowData.getVal(), oldData.getVal())) {
                oldData.setIssue(nowData.getIssue());
                oldData.setVal(nowData.getVal());
                list.add(oldData);
            }
        }
        if (!list.isEmpty()) {
            return updateBatchById(list);
        }
        return true;
    }

    @Override
    @Transactional
    public boolean importBaseData(List<GridData> list) {
        gridDataMapper.deletedAll();
        list.add(0, new GridData(9, "并网文件编号", "ID_GRID_FILE", "int", true, null, "G"));
        return SqlHelper.retBool(gridDataMapper.insert(list));
    }

    @Override
    public List<GridFileData> get(Integer gridFileId) {
        return baseMapper.get(gridFileId);
    }

    @Override
    @Transactional
    public void save(Integer fileId) {
        List<GridFileData> baseData = baseMapper.getBaseData();
        baseData.get(0).setVal(new BigDecimal(fileId));
        baseData.forEach(d -> d.setGridFileId(fileId));
        saveBatch(baseData);
    }

}
