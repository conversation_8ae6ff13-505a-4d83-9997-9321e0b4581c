package com.wanhe.business.controller;

import com.github.pagehelper.Page;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.LanguageEnum;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.wanhe.business.domain.DeviceAlarm;
import com.wanhe.business.pojo.product.DeviceAlarmPageReqDTO;
import com.wanhe.business.service.IDeviceAlarmService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 设备故障
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@RestController
@RequestMapping("/device/alarm")
public class DeviceAlarmController extends BaseController {

    @Resource
    private IDeviceAlarmService deviceAlarmService;

    /**
     * 分页 business:deviceAlarm:page
     */
    @RequiresPermissions("business:deviceAlarm:page")
    @GetMapping("/page")
    public TableDataInfo<DeviceAlarm> page(@Validated DeviceAlarmPageReqDTO dto, HttpServletRequest request) {
        dto.setLanguage(LanguageEnum.getLang(request));
        Page<DeviceAlarm> page = startPage(dto);
        deviceAlarmService.page(dto);
        return getDataTable(page);
    }

    /**
     * 告警记录
     */
    @GetMapping("/record")
    public R<List<DeviceAlarm>> record(HttpServletRequest request) {
        return R.ok(deviceAlarmService.record(LanguageEnum.getLang(request)));
    }

}
