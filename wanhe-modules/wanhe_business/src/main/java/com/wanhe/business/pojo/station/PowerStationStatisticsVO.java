package com.wanhe.business.pojo.station;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/5 0005
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PowerStationStatisticsVO {

    @Schema(description = "总量")
    private Integer total;

    @Schema(description = "数据")
    private Map<Integer, Integer> data;

    @Schema(description = "装机容量kW")
    private BigDecimal installedCapacity;

    public PowerStationStatisticsVO(Integer total, Map<Integer, Integer> data) {
        this.total = total;
        this.data = data;
    }
}





