package com.wanhe.business.pojo.station;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/22
 */
@Data
public class PowerStationPriceDTO {

    @Schema(description = "电站id")
    @NotNull
    private Integer powerStationId;

    @Schema(description = "货币单位")
    @NotBlank
    private String monetaryUnit;

    @Schema(description = "电价策略 1统一电价 2分时电价")
    @NotNull
    private Integer powerPriceStrategy;

    @Schema(description = "卖出单价")
    private BigDecimal powerPrice;

    @Schema(description = "买入单价")
    private BigDecimal buyPowerPrice;

    @Schema(description = "分时策略")
    @Valid
    private List<PowerStationPriceMonthDTO> timeStrategy;

}
