package com.wanhe.business.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.domain.base.ICUDDEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-10-26
 */
@Data
@TableName("wh_vendor")
@NoArgsConstructor
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = false)
public class Vendor extends ICUDDEntity {

    @Schema(description = "厂商名称")
    private String vendorName;

    @Schema(description = "厂商负责人")
    private String vendorContactPerson;

    @Schema(description = "厂商联系人")
    private String contactPerson;

    @Schema(description = "区号")
    private String areaCode;

    @Schema(description = "联系人电话")
    private String contactPhone;

    @Schema(description = "联系人地址")
    private String contactAddress;

    @Schema(description = "备注")
    private String remark;

    public Vendor(String vendorName) {
        this.vendorName = vendorName;
    }

}
