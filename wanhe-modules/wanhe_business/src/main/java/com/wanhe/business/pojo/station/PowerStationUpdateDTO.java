package com.wanhe.business.pojo.station;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024-10-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(name = "PowerStationUpdateDTO", description = "电站基础信息表")
public class PowerStationUpdateDTO extends PowerStationDTO {

    @Schema(description = "电站id")
    @NotNull
    @Positive
    private Integer id;

}
