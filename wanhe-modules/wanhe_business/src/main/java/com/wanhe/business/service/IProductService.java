package com.wanhe.business.service;


import com.ruoyi.common.core.domain.IntStr;
import com.ruoyi.common.security.service.IBaseService;
import com.wanhe.business.domain.Product;
import com.wanhe.business.pojo.product.ProductDTO;
import com.wanhe.business.pojo.product.ProductPageReqDTO;
import com.wanhe.business.pojo.product.ProductUpdateDTO;

import java.util.List;

/**
* <AUTHOR>
* @date 2024-10-26
*/
public interface IProductService extends IBaseService<Product> {

    boolean add(ProductDTO dto);

    boolean update(ProductUpdateDTO dto);

    boolean delete(String id);

    List<Product> page(ProductPageReqDTO dto);

    Product getInfoByCode(String code);

    /**
     * 通过sn获取产品
     *
     * @param sn sn
     * @return {@link Product }
     */
    Product getProductBySn(String sn);

    List<IntStr> listData(Integer vendorId, Integer deviceType);

    boolean importData(List<Product> list, Integer deviceType);
}
