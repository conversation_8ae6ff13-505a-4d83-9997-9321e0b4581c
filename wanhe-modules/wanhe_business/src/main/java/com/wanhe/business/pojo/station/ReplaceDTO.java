package com.wanhe.business.pojo.station;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-10-28
 */
@Data
public class ReplaceDTO {

    @Schema(description = "设备id")
    @NotNull
    @Positive
    private Integer deviceId;

    @Schema(description = "新设备号")
    @NotBlank
    private String newNumber;

    @Schema(description = "替换原因")
    private String reason;

}
