package com.wanhe.business.pojo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024-11-15
 */
@Data
public class GridFileDataUpdateDTO {

    @Schema(description = "主键")
    @NotNull
    private Integer id;

    @Schema(description = "并网文件id")
    @NotNull
    private Integer gridFileId;

    @Schema(description = "是否下发")
    @NotNull
    private Boolean issue;

    @Schema(description = "值")
    @Digits(integer = 18, fraction = 2)
    private BigDecimal val;

}
