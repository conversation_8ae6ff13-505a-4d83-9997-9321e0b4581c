package com.wanhe.business.service.impl;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.enums.LanguageEnum;
import com.ruoyi.common.core.utils.TimeZoneUtil;
import com.wanhe.business.domain.DeviceReplaceRecord;
import com.wanhe.business.domain.PowerStation;
import com.wanhe.business.domain.ProductCategory;
import com.wanhe.business.mapper.DeviceReplaceRecordMapper;
import com.wanhe.business.mapper.PowerStationMapper;
import com.wanhe.business.mapper.ProductCategoryMapper;
import com.wanhe.business.pojo.station.DeviceReplaceRecordPageReqDTO;
import com.wanhe.business.service.IDeviceReplaceRecordService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @date 2024-10-26
*/
@Service
public class DeviceReplaceRecordImpl extends DataScopeServiceImpl<DeviceReplaceRecordMapper, DeviceReplaceRecord> implements IDeviceReplaceRecordService {

    @Resource
    private PowerStationMapper powerStationMapper;
    @Resource
    private ProductCategoryMapper productCategoryMapper;

    @Override
    public void page(DeviceReplaceRecordPageReqDTO dto) {
        List<DeviceReplaceRecord> list = lambdaQueryAll(DeviceReplaceRecord::getPowerStationId, dto.getPowerStationId())
                .selectAll(DeviceReplaceRecord.class)
                .select(PowerStation::getStationName, PowerStation::getTimeZone)
                .eq(dto.getDeviceType() != null, DeviceReplaceRecord::getDeviceType, dto.getDeviceType())
                .and(StrUtil.isNotBlank(dto.getNumber()), c -> c.like(DeviceReplaceRecord::getOldNumber, dto.getNumber())
                        .or().like(DeviceReplaceRecord::getNewNumber, dto.getNumber()))
                .orderByDesc(DeviceReplaceRecord::getId)
                .list();
        if (!list.isEmpty()) {
            Set<Integer> tid = list.stream().map(DeviceReplaceRecord::getDeviceType).collect(Collectors.toSet());
            Map<Integer, String> tmap = productCategoryMapper.listByIds(join(tid), LanguageEnum.getLang()).stream().collect(Collectors.toMap(ProductCategory::getId, ProductCategory::getName));
            list.forEach(p -> {
                p.setDeviceTypeName(tmap.get(p.getDeviceType()));
                p.setCreateTime(TimeZoneUtil.getZonedTime(p.getCreateTime(), p.getTimeZone()));
            });
        }
    }
}
