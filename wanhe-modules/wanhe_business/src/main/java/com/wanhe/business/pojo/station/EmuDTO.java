package com.wanhe.business.pojo.station;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-10-28
 */
@Data
@Schema(name = "AddEmuDTO", description = "设备表")
public class EmuDTO {

    @Schema(description = "电站id")
    @NotNull
    @Positive
    private Integer powerStationId;

    @Schema(description = "EMU序列号")
    @NotBlank
    private String emuNumber;

    @Schema(description = "设备序列号")
    @NotEmpty
    private List<String> number;

}
