package com.wanhe.business.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.Page;
import com.ruoyi.common.core.domain.KV;
import com.ruoyi.common.core.domain.PageReqDTO;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.ClientTypeEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.TimeZoneUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.redis.RedisUtil;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.dto.PowerStationListDataVO;
import com.wanhe.business.domain.PowerStation;
import com.wanhe.business.pojo.product.PermissionDTO;
import com.wanhe.business.pojo.station.*;
import com.wanhe.business.service.IPowerStationService;
import com.wanhe.business.service.IStationDeviceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.Duration;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 电站
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@Slf4j
@RestController
@RequestMapping("/power/station")
public class PowerStationController extends BaseController {

    @Resource
    private IPowerStationService powerStationService;
    @Resource
    private IStationDeviceService stationDeviceService;

    /**
     * 新增 business:powerStation:add
     */
    @RequiresPermissions("business:powerStation:add")
    @Log(title = "电站", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Integer> add(@Validated @RequestBody PowerStationDTO dto) {
        return R.ok(powerStationService.add(dto));
    }

    /**
     * 修改 business:powerStation:edit
     */
    @RequiresPermissions("business:powerStation:edit")
    @Log(title = "电站", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@Validated @RequestBody PowerStationUpdateDTO dto) {
        return R.ok(powerStationService.update(dto));
    }

    /**
     * 详情
     */
    @GetMapping(value = "/{id}")
    public R<PowerStation> getInfo(@PathVariable Integer id) {
        return R.ok(powerStationService.getInfo(id));
    }

    /**
     * 电站信息：业主、EMU、微逆数量
     */
    @GetMapping(value = "/owner")
    public R<PowerInfoVO> owner(@RequestParam Integer id) {
        return R.ok(powerStationService.owner(id));
    }

    /**
     * 分页 business:powerStation:page
     */
    @RequiresPermissions("business:powerStation:page")
    @GetMapping("/page")
    public TableDataInfo<PowerStation> page(@Validated PowerStationPageReqDTO dto) {
        Page<PowerStation> page = startPage(dto);
        powerStationService.page(dto);
        return getDataTable(page);
    }

    /**
     * 业主可以授权电站分页
     */
    @GetMapping("/permission/page")
    public TableDataInfo<PowerStation> permissionPage(PageReqDTO dto) {
        Page<PowerStation> page = startPage(dto);
        powerStationService.permissionPage(dto);
        return getDataTable(page);
    }

    /**
     * 业主电站授权
     */
    @PutMapping("/permission")
    public R<Boolean> permission(@RequestBody PermissionDTO dto) {
        return R.ok(powerStationService.permission(dto));
    }

    /**
     * 电站列表电站信息统计
     */
    @GetMapping("/data")
    public R<PowerStationListDataVO> data() {
        return R.ok(powerStationService.data());
    }

    /**
     * 收藏分页 business:powerStation:page
     */
    @RequiresPermissions("business:powerStation:page")
    @GetMapping("/collect/page")
    public TableDataInfo<PowerStation> collectPage(@Validated PowerStationPageReqDTO dto) {
        dto.setCollect(true);
        Page<PowerStation> page = startPage(dto);
        powerStationService.page(dto);
        return getDataTable(page);
    }

    /**
     * 布局设计保存 business:stationDevice:layout
     */
    @RequiresPermissions("business:powerStation:layout")
    @Log(title = "电站", businessType = BusinessType.UPDATE)
    @PutMapping("/layout")
    public R<Boolean> layout(@Validated @RequestBody LayoutDTO dto) {
        return R.ok(powerStationService.layout(dto));
    }

    /**
     * 列表
     */
    @GetMapping("/all/list")
    public R<List<KV>> all() {
        return R.ok(powerStationService.all());
    }

    /**
     * 电站位置列表 默认查询全部， all=1是查询不包含建设者中的电站
     */
    @GetMapping("/all/list/site")
    public R<List<PowerStationSiteVO>> allSite(@RequestParam(required = false) Integer all) {
        return R.ok(powerStationService.allSite(all));
    }

    /**
     * 删除 business:powerStation:remove
     */
    @RequiresPermissions("business:powerStation:remove")
    @Log(title = "电站", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public R<Boolean> remove(@PathVariable Integer id) {
        return R.ok(powerStationService.delete(id));
    }

    /**
     * 上传安装图片 business:powerStation:install
     */
    @RequiresPermissions("business:powerStation:install")
    @Log(title = "电站", businessType = BusinessType.UPDATE)
    @PutMapping("/install")
    public R<Boolean> install(@Validated @RequestBody InstallDTO dto) {
        return R.ok(powerStationService.install(dto));
    }

    /**
     * 更多设置 business:powerStation:fault
     */
    @RequiresPermissions("business:powerStation:fault")
    @Log(title = "电站", businessType = BusinessType.UPDATE)
    @PutMapping("/fault")
    public R<Void> fault(@Validated @RequestBody MoreDTO dto) {
        powerStationService.fault(dto);
        if (Boolean.FALSE.equals(dto.getReflux())) {
            MeterDTO meter = new MeterDTO();
            meter.setPowerStationId(dto.getId());
            meter.setReflux(false);
            meter.setMeter(Collections.emptyList());
            stationDeviceService.addMeter(meter);
            stationDeviceService.getRemoteBizDataService().configMeter(dto.getId());
        }
        return R.ok();
    }

    /**
     * 电价设置 business:powerStation:powerPrice
     */
    @RequiresPermissions("business:powerStation:powerPrice")
    @Log(title = "电站", businessType = BusinessType.UPDATE)
    @PutMapping("/power/price")
    public R<Boolean> powerPrice(@Validated @RequestBody PowerStationPriceDTO dto) {
        if (dto.getPowerPriceStrategy() == 1) {
            if (dto.getBuyPowerPrice() == null) throw new RuntimeException("买入电价不能为空");
        } else {
            if (CollUtil.isEmpty(dto.getTimeStrategy())) throw new ServiceException("分时策略不能为空");
            List<Integer> monthList = dto.getTimeStrategy().stream().flatMap(d -> d.getMonths().stream()).sorted().toList();
            if (monthList.size() != 12) throw new ServiceException("月份配置不足12个月或重复配置月份");
            for (int i = 0; i < monthList.size(); i++) {
                if (monthList.get(i) != i + 1) throw new ServiceException("月份配置错误，配置了不存在的月份");
            }
            for (PowerStationPriceMonthDTO strategy : dto.getTimeStrategy()) {
                List<Integer> months = strategy.getMonths();
                for (PowerStationPriceTimeDTO time : strategy.getTimes()) {
                    if (!time.getBeginTime().isBefore(time.getEndTime())) {
                        throw new ServiceException("{0}月({1}-{2})配置错误，开始时间必须小于结束时间", months, time.getBeginTime(), time.getEndTime());
                    }
                }
                Set<LocalTime> beginTimes = strategy.getTimes().stream().map(PowerStationPriceTimeDTO::getBeginTime).collect(Collectors.toSet());
                if (beginTimes.size() != strategy.getTimes().size()) throw new ServiceException("{0}月开始时间配置重复", months.toString());
                Set<LocalTime> endTimes = strategy.getTimes().stream().map(PowerStationPriceTimeDTO::getEndTime).collect(Collectors.toSet());
                if (endTimes.size() != strategy.getTimes().size()) throw new ServiceException("{0}月结束时间配置重复", months.toString());
                List<PowerStationPriceTimeDTO> times = strategy.getTimes().stream().sorted(Comparator.comparing(PowerStationPriceTimeDTO::getBeginTime)).toList();
                PowerStationPriceTimeDTO firstTime = times.get(0);
                if (firstTime.getBeginTime().getHour() != 0) throw new ServiceException("{0}月开始时间未从0点0分开始", months.toString());
                if (times.size() > 1) {
                    for (int i = 1; i < times.size(); i++) {
                        PowerStationPriceTimeDTO lastTime = times.get(i);
                        long seconds = Duration.between(firstTime.getEndTime(), lastTime.getBeginTime()).getSeconds();
                        if (seconds == 0) {
                            firstTime = lastTime;
                        } else if (seconds > 0) {
                            throw new ServiceException("{0}月{1}-{2}时间段未配置", months.toString(), firstTime.getEndTime(), lastTime.getBeginTime());
                        } else {
                            throw new ServiceException("{0}月{1}-{2}时间段重叠", months.toString(), lastTime.getBeginTime(), firstTime.getEndTime());
                        }
                    }
                }
                if (!firstTime.getEndTime().equals(TimeZoneUtil.TIME_23_59)) {
                    throw new ServiceException("{0}月未覆盖全时段", months.toString());
                }
            }
        }
        List<String> list = Arrays.stream(ClientTypeEnum.values())
                .map(clientType -> "cache:earnings_" + dto.getPowerStationId() + "_" + clientType.getVal())
                .toList();
        RedisUtil.delete(list);
        return R.ok(powerStationService.powerPrice(dto));
    }

    /**
     * 电价数据
     */
    @GetMapping("/power/price")
    public R<List<PowerStationPriceMonthVO>> powerPrice(@Schema(description = "电站id") @RequestParam Integer id) {
        return R.ok(powerStationService.powerPrice(id));
    }

    /**
     * 电站收藏 business:powerStation:collect
     */
    @RequiresPermissions("business:powerStation:collect")
    @Log(title = "电站", businessType = BusinessType.UPDATE)
    @PutMapping("/collect")
    public R<Boolean> collect(@Schema(description = "电站id") @RequestParam Integer id) {
        return R.ok(powerStationService.collect(id));
    }

    /**
     * 转移电站
     */
    @PutMapping("/transfer")
    public R<Boolean> transfer(@Validated @RequestBody TransferDTO dto) {
        return R.ok(powerStationService.transfer(dto));
    }

    /**
     * 开站报告
     */
    @GetMapping("/report")
    public R<StationReportVO> report(@Schema(description = "电站id") @RequestParam Integer id) {
        return R.ok(powerStationService.report(id));
    }

    /**
     * 开站报告下载
     */
    @PostMapping("/report/download")
    public void reportDownload(@Schema(description = "电站id") @RequestParam Integer id, HttpServletResponse response) throws Exception {
        response.setContentType("application/pdf");
        response.setCharacterEncoding("utf-8");
        powerStationService.reportDownload(id, response.getOutputStream());
    }

    /**
     * 开站报告图片路径
     */
    @GetMapping("/report/img/path")
    public R<List<String>> reportImg(@Schema(description = "电站id") @RequestParam Integer id) throws Exception {
        return R.ok(powerStationService.reportImg(id));
    }

    /**
     * 开站报告分享
     */
    @PostMapping("/report/share")
    public R<Void> reportShare(@Validated @RequestBody ReportShareDTO dto) throws Exception {
        powerStationService.reportShare(dto);
        return R.ok();
    }

    /**
     * 电站概览 电站状态、电站缺失信息
     */
    @GetMapping("/station/status")
    public R<StationStatusVO> stationStatus(@Schema(description = "电站id") @RequestParam Integer id) {
        return R.ok(powerStationService.stationStatus(id));
    }

    @Operation(summary = "天气预报", description = "<a href=\"https://www.apispace.com/eolink/api/456456/apiDocument\">参数路径</a>")
    @GetMapping("/weather")
    public R<JSONObject> weather(@Schema(description = "电站id") @RequestParam Integer id) {
        return R.ok(powerStationService.weather(id));
    }

}
