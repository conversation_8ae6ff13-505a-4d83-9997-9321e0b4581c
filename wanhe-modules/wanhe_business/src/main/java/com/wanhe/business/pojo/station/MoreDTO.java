package com.wanhe.business.pojo.station;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-10-28
 */
@Data
public class MoreDTO {

    @Schema(description = "电站id")
    @NotNull
    @Positive
    private Integer id;

    @Schema(description = "故障推送方式（0：短信1：邮箱：2：站内消息）")
    @NotNull
    private List<Integer> faultNotificationMethod;

    @Schema(description = "是否允许业主查看组件布局")
    @NotNull
    private Boolean allowOwnerViewLayout;

    @Schema(description = "组件布局默认显示(0：功率 1：电量)")
    private Integer defaultLayoutDisplay;

    @Schema(description = "单个组件的最大功率")
    @Positive
    @Digits(integer = 18, fraction = 2)
    private BigDecimal maxComponentPower;

    @Schema(description = "是否启动逆流")
    private Boolean reflux;

}
