package com.wanhe.business.pojo.station;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/11/1
 */
@Data
public class StationStatusVO {

    @Schema(description = "网关或wifi 运行状态，1：正常")
    private Integer runningStatus;

    @Schema(description = "设备状态 0建设中 1正常 2离线 3故障")
    private Integer sysStatus;

    @Schema(description = "业主名称")
    private String proprietor;

}
