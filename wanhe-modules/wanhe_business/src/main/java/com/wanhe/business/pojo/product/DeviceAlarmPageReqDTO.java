package com.wanhe.business.pojo.product;

import com.ruoyi.common.core.domain.PageReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DeviceAlarmPageReqDTO extends PageReqDTO {

    @Schema(description = "电站id")
    private Integer powerStationId;

    @Schema(description = "告警名称")
    private String alarmName;

    @Schema(description = "故障码")
    private String alarmCode;

    @Schema(description = "故障字")
    private String alarmKey;

    @Schema(description = "序列号")
    private String snNumber;

    @Schema(description = "告警是否已解决 （0：未解决，1：已解决）")
    @NotNull
    private Integer resolved;

    @Schema(description = "权限", hidden = true)
    private String dataScopeSql;

    @Schema(description = "国际化", hidden = true)
    private String language;

}
