package com.wanhe.business.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.ip.IpUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.wanhe.business.pojo.station.HomeDeviceStatisticsVO;
import com.wanhe.business.pojo.station.HomeMapDataVO;
import com.wanhe.business.pojo.station.PowerStationStatisticsVO;
import com.wanhe.business.pojo.station.StationNumStatisticsVO;
import com.wanhe.business.service.IPowerStationService;
import com.wanhe.business.service.IStationDeviceService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 首页图表
 */
@RestController
@RequestMapping("/home")
public class HomeController extends BaseController {

    @Resource
    private IPowerStationService powerStationService;
    @Resource
    private IStationDeviceService stationDeviceService;

    /**
     * 电站状态统计
     */
    @GetMapping(value = "/queryPowerStationStatus")
    public R<PowerStationStatisticsVO> queryPowerStationStatus() {
        return R.ok(powerStationService.queryPowerStationStatus(null));
    }

    /**
     * 电站状态统计,collect=0 统计所有电站,collect=1 统计收藏电站
     */
    @GetMapping(value = "/queryPowerStationStatus2")
    public R<PowerStationStatisticsVO> queryPowerStationStatus2(@RequestParam(required = false, defaultValue = "0") Integer collect) {
        return R.ok(powerStationService.queryPowerStationStatus(collect));
    }

    /**
     * 电站类型统计
     */
    @GetMapping(value = "/queryPowerStationType")
    public R<PowerStationStatisticsVO> queryPowerStationType() {
        return R.ok(powerStationService.queryPowerStationType());
    }

    /**
     * 设备统计饼图
     */
    @GetMapping(value = "/queryPowerStationDevice")
    public R<PowerStationStatisticsVO> queryPowerStationDevice() {
        return R.ok(stationDeviceService.queryPowerStationDevice());
    }

    /**
     * 电站新增趋势
     */
    @GetMapping(value = "/queryPowerStationStatistics")
    public R<List<StationNumStatisticsVO>> queryPowerStationStatistics() {
        return R.ok(powerStationService.queryPowerStationStatistics());
    }

    /**
     * 设备统计柱状图
     */
    @GetMapping(value = "/queryDeviceStatistics")
    public R<List<HomeDeviceStatisticsVO>> queryDeviceStatistics() {
        return R.ok(stationDeviceService.queryDeviceStatistics());
    }

    /**
     * 首页地图 电站总数 装机容量 微逆累积销售
     */
    @GetMapping(value = "/map/data")
    public R<HomeMapDataVO> mapData() {
        return R.ok(powerStationService.mapData());
    }

    /**
     * 获取ip地址所在区域 1-国内 2-国外
     *
     * @param ip 知识产权
     * @return {@link R }<{@link Integer }>
     */
    @Operation(summary = "获取ip地址所在区域 1-国内 2-国外")
    @GetMapping(value = "/ipArea")
    public R<Integer> getIpArea(@RequestParam(value = "ip") String ip) {
        return R.ok(IpUtils.getIpLocation(ip));
    }

}
