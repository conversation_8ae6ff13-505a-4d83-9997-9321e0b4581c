package com.wanhe.business.pojo.station;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024-10-28
 */
@Data
public class PowerStationSiteVO {

    @Schema(description = "电站编号")
    private Integer id;

    @Schema(description = "电站名称")
    private String stationName;

    @Schema(description = "电站类型（0:屋顶光伏 1:阳台光伏 2:商业光伏）")
    private String stationType;

    @Schema(description = "电站状态 （0建设中 1在线 2离线 3告警）")
    private Integer stationStatus;

    @Schema(description = "纬度")
    private String latitude;

    @Schema(description = "经度")
    private String longitude;

    @Schema(description = "详细信息")
    private String stationDetails;

    @Schema(description = "装机容量kW")
    private BigDecimal installedCapacity;

}
