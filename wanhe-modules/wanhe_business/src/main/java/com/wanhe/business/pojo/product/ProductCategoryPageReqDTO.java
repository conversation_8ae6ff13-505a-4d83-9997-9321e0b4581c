package com.wanhe.business.pojo.product;

import com.ruoyi.common.core.domain.PageReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ProductCategoryPageReqDTO extends PageReqDTO {

    @Schema(description = "产品分类名称")
    private String name;

}
