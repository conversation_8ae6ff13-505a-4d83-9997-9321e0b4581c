package com.wanhe.business.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.base.ICUDDEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024-10-26
 */
@Data
@TableName("wh_product")
@Schema(name = "Product", description = "产品表")
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = false)
public class Product extends ICUDDEntity {

    public static final Product DEFAULT = new Product();

    @Schema(description = "设备类型")
    private Integer deviceType;

    @Excel(name = "产品型号", sort = 10)
    @Schema(description = "产品型号")
    private String productName;

    @Excel(name = "型号编码", sort = 20)
    @Schema(description = "型号编码")
    private String productCode;

    @Schema(description = "厂家id")
    private Integer vendorId;

    @Excel(name = "通道数", sort = 50)
    @Schema(description = "通道数量")
    private Integer connectNum;

    @Excel(name = "质保时长(月)", sort = 70)
    @Schema(description = "质保时长(月)")
    private Integer guaranteePeriod;

    @Excel(name = "额定输出功率(W)", sort = 30)
    @Schema(description = "额定输出功率(W)")
    private BigDecimal power;

    @Excel(name = "额定输出电压(V)", sort = 31)
    @Schema(description = "额定输出电压(V)")
    private BigDecimal voltage;

    @Excel(name = "通讯方式", sort = 40)
    @Schema(description = "通讯方式")
    private String communicateMode;

    @Excel(name = "供电方式", sort = 41)
    @Schema(description = "供电方式")
    private String powerSupplyMode;

    @Excel(name = "尺寸(mm)", sort = 21)
    @Schema(description = "尺寸(mm)")
    private String dimension;

    @Excel(name = "最大功率(W)", sort = 22)
    @Schema(description = "最大功率(W)")
    private BigDecimal maxPower;

    @Excel(name = "最大功率点电压(V)", sort = 23)
    @Schema(description = "最大功率点电压(V)")
    private BigDecimal maxVoltage;

    @Excel(name = "最大功率点电流(A)", sort = 24)
    @Schema(description = "最大功率点电流(A)")
    private BigDecimal maxElectricity;

    @Excel(name = "开路电压(V)", sort = 25)
    @Schema(description = "开路电压(V)")
    private BigDecimal openVoltage;

    @Excel(name = "短路电流(A)", sort = 26)
    @Schema(description = "短路电流(A)")
    private BigDecimal shortOutElectricity;

    @Excel(name = "质量(kg)", sort = 27)
    @Schema(description = "质量(kg)")
    private BigDecimal quality;

    @Excel(name = "生产厂商", sort = 60)
    @Schema(description = "厂家名称")
    @TableField(exist = false)
    private String vendorName;

    @Excel(name = "序号", sort = 1, type = Excel.Type.EXPORT)
    @Schema(description = "序号", hidden = true)
    @TableField(exist = false)
    @JsonIgnore
    private int number;

}
