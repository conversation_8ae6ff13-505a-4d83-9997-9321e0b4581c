package com.wanhe.business.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.domain.base.ICUDEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 工单处理记录
 *
 * <AUTHOR>
 * @date 2025/06/18
 */
@Data
@TableName("wh_order_process")
@Schema(name = "OrderProcess", description = "工单处理表")
@EqualsAndHashCode(callSuper = false)
public class OrderProcess extends ICUDEntity {
    /**
     * 工单id
     */
    @Schema(description = "工单id")
    private Long orderId;
    /**
     * 严重程度 1-一般 2-中等 3-非常严重
     */
    @Schema(description = "严重程度 1-一般 2-中等 3-非常严重")
    private Integer severity;
    /**
     * 可能原因
     */
    @Schema(description = "可能原因")
    private String possibleCauses;
    /**
     * 处理过程 1-远程调试 2-上门调试 3-更换备件 4-其他
     */
    @Schema(description = "处理过程 1-远程调试 2-上门调试 3-更换备件 4-其他")
    private Integer processSteps;
    /**
     * 处理方法
     */
    @Schema(description = "处理方法")
    private String solution;
    /**
     * 问题解决状态 0-未解决 1-已解决 2-无法解决
     */
    @Schema(description = "问题解决状态 0-未解决 1-已解决 2-无法解决")
    private Integer solutionStatus;
    /**
     * 完成时间
     */
    @Schema(description = "完成时间")
    private LocalDateTime completionTime;
    /**
     * 完成证明
     */
    @Schema(description = "完成证明")
    private String proofCompletion;

    /**
     * 确认时间
     */
    @Schema(description = "确认时间")
    private LocalDateTime confirmTime;
    /**
     * 确认人
     */
    @Schema(description = "确认人")
    private String confirmedBy;
    /**
     * 处理人id
     */
    @Schema(description = "处理人id")
    private Long processorId;
    /**
     * 处理人
     */
    @Schema(description = "处理人")
    private String processor;
    /**
     * 处理人电话
     */
    @Schema(description = "处理人电话")
    private String processorPhoneNumber;
    /**
     * 处理人邮箱
     */
    @Schema(description = "处理人邮箱")
    private String processorEmail;
}
