package com.wanhe.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.domain.IntStr;
import com.ruoyi.common.security.service.impl.BaseServiceImpl;
import com.ruoyi.system.api.domain.GridFileData;
import com.wanhe.business.domain.GridFile;
import com.wanhe.business.mapper.GridFileMapper;
import com.wanhe.business.pojo.device.GridFileVO;
import com.wanhe.business.pojo.product.GridFileDTO;
import com.wanhe.business.pojo.product.GridFilePageReqDTO;
import com.wanhe.business.pojo.product.GridFileUpdateDTO;
import com.wanhe.business.service.IGridFileDataService;
import com.wanhe.business.service.IGridFileService;
import com.wanhe.business.service.IPowerStationService;
import jakarta.annotation.Resource;
import lombok.Getter;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

/**
* <AUTHOR>
* @date 2024-10-26
*/
@Service
public class GridFileServiceImpl extends BaseServiceImpl<GridFileMapper, GridFile> implements IGridFileService {

    @Resource
    private IPowerStationService powerStationService;
    @Getter
    @Resource
    private IGridFileDataService gridFileDataService;

//    public void

    @Override
    @Transactional
    public boolean add(GridFileDTO dto) {
        GridFile entity = BeanUtil.copyProperties(dto, GridFile.class);
        boolean save = save(entity);
        gridFileDataService.save(entity.getId());
        return save;
    }

    @Override
    public boolean update(GridFileUpdateDTO dto) {
        GridFile entity = getByIdThrow(dto.getId());
        BeanUtil.copyProperties(dto, entity);
        return updateById(entity);
    }

    @Override
    public void page(GridFilePageReqDTO dto) {
        List<GridFile> list = lambdaQuery()
                .eq(dto.getCountryId() != null, GridFile::getCountryId, dto.getCountryId())
                .like(StrUtil.isNotBlank(dto.getFileName()), GridFile::getFileName, dto.getFileName())
                .orderByAsc(GridFile::getId)
                .list();
        if (!list.isEmpty()) {
            List<Integer> ids = list.stream().flatMap(p -> Stream.of(p.getCountryId(), p.getProvinceId())).distinct().toList();
            Map<Integer, String> map = powerStationService.getDistrictName(ids);
            list.forEach(p -> {
                p.setCountry(map.get(p.getCountryId()));
                p.setProvince(map.get(p.getProvinceId()));
            });
        }
    }

    @Override
    public List<GridFileVO> all(Integer countryId, String name) {
        return baseMapper.all(countryId, name);
    }

    @Override
    @Transactional
    public boolean delete(Integer id) {
        removeById(id);
        gridFileDataService.remove(Wrappers.lambdaQuery(GridFileData.class).eq(GridFileData::getGridFileId, id));
        return true;
    }

    @Override
    public List<IntStr> getCountry() {
        List<Integer> country = baseMapper.getCountry();
        if (country.isEmpty()) {
            return Collections.emptyList();
        }
        Map<Integer, String> map = powerStationService.getDistrictName(country);
        return country.stream().map(p -> new IntStr(p, map.get(p))).toList();
    }

    @Override
    public GridFile getInfo(Integer id) {
        GridFile file = getByIdThrow(id);
        Map<Integer, String> map = powerStationService.getDistrictName(List.of(file.getProvinceId(), file.getCountryId()));
        file.setProvince(map.get(file.getProvinceId()));
        file.setCountry(map.get(file.getCountryId()));
        return file;
    }

}
