package com.wanhe.business.pojo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-10-26
 */
@Data
@Schema(name = "ProductCategoryDTO", description = "产品分类表")
public class ProductCategoryDTO {

    @Schema(description = "产品分类名称")
    @NotBlank
    private String name;

    @Schema(description = "产品编码")
    @NotBlank
    private String sn;

    @Schema(description = "描述")
    private String description;

}
