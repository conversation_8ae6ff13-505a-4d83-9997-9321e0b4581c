package com.wanhe.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wanhe.business.domain.PowerStationCollect;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @date 2024/11/6
 */
public interface PowerStationCollectMapper extends BaseMapper<PowerStationCollect> {

    @Delete("delete from wh_power_station_collect where power_station_id = #{powerStationId}")
    void deleteBy(Integer powerStationId);

}
