package com.wanhe.business.pojo.workOrder.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/6/21
 */
@Data
public class ProcessorAddDTO {

    @Schema(description = "工单id")
    private Long orderId;

    @Schema(description = "处理人id")
    private Long processorId;

    @Schema(description = "确认时间")
    private LocalDateTime confirmTime;

}
