package com.wanhe.business.controller;

import com.github.pagehelper.Page;
import com.ruoyi.common.core.domain.IntStr;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.LanguageEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.wanhe.business.domain.Product;
import com.wanhe.business.pojo.product.ProductDTO;
import com.wanhe.business.pojo.product.ProductPageReqDTO;
import com.wanhe.business.pojo.product.ProductUpdateDTO;
import com.wanhe.business.service.IProductService;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 产品型号
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@RestController
@RequestMapping("/product")
public class ProductController extends BaseController {

    @Resource
    private IProductService productService;

    /**
     * 新增 business:product:add
     */
    @RequiresPermissions("business:product:add")
    @Log(title = "产品", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@Validated @RequestBody ProductDTO dto) {
        return R.ok(productService.add(dto));
    }

    /**
     * 修改 business:product:edit
     */
    @RequiresPermissions("business:product:edit")
    @Log(title = "产品", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@Validated @RequestBody ProductUpdateDTO dto) {
        return R.ok(productService.update(dto));
    }

    /**
     * 详情
     */
    @GetMapping(value = "/{id}")
    public R<Product> getInfo(@PathVariable Integer id) {
        return R.ok(productService.getById(id));
    }

    /**
     * 通过设备号查询产品
     */
    @GetMapping(value = "/code")
    public R<Product> getInfoByCode(@RequestParam String code) {
        return R.ok(productService.getInfoByCode(code));
    }

    /**
     * 通过sn获取产品
     *
     * @param sn sn
     * @return {@link R }<{@link Product }>
     */
    @GetMapping(value = "/productBySn")
    public R<Product> getProductBySn(@RequestParam String sn) {
        return R.ok(productService.getProductBySn(sn));
    }

    /**
     * 分页 business:product:page
     */
    @RequiresPermissions("business:product:page")
    @GetMapping("/page")
    public TableDataInfo<Product> page(@Validated ProductPageReqDTO dto) {
        Page<Product> page = startPage(dto);
        productService.page(dto);
        return getDataTable(page);
    }

    /**
     * 导出
     */
    @Log(title = "产品", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void exportData(HttpServletResponse response, HttpServletRequest request, @Validated ProductPageReqDTO dto) {
        ExcelUtil<Product> util = new ExcelUtil<>(Product.class);
        util.setLocal(LanguageEnum.getLocal(request));
        switch (dto.getDeviceType()) {
            case 0 -> util.hideColumn("connectNum", "power", "voltage", "dimension", "maxPower", "maxVoltage", "maxElectricity", "openVoltage", "shortOutElectricity", "quality");
            case 1 -> util.hideColumn("powerSupplyMode", "dimension", "maxPower", "maxVoltage", "maxElectricity", "openVoltage", "shortOutElectricity", "quality");
            case 2 -> util.hideColumn("productCode", "connectNum", "power", "voltage", "communicateMode", "dimension", "maxPower", "maxVoltage", "maxElectricity", "openVoltage", "shortOutElectricity", "quality");
            case 3 -> util.hideColumn("productCode", "connectNum", "power", "voltage", "communicateMode", "powerSupplyMode");
            default -> throw new ServiceException("参数错误");
        }
        dto.setExport(true);
        List<Product> list = productService.page(dto);
        util.exportExcel(response, list, "产品型号");
    }

    /**
     * 导入
     */
    @Log(title = "产品", businessType = BusinessType.IMPORT)
    @PostMapping("/import")
    public R<Boolean> importData(HttpServletRequest request, MultipartFile file,
                                 @RequestParam @Schema(description = "设备类型") Integer deviceType) throws Exception {
        ExcelUtil<Product> util = new ExcelUtil<>(Product.class);
        util.setLocal(LanguageEnum.getLocal(request));
        List<Product> list = util.importExcel(file.getInputStream());
        return R.ok(productService.importData(list, deviceType));
    }

    /**
     * 删除 business:product:remove
     */
    @RequiresPermissions("business:product:remove")
    @Log(title = "产品", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public R<Boolean> remove(@PathVariable String id) {
        return R.ok(productService.delete(id));
    }

    /**
     * 下拉选项
     */
    @GetMapping("/list")
    public R<List<IntStr>> list(@RequestParam(required = false) Integer vendorId, @RequestParam(required = false) Integer deviceType) {
        return R.ok(productService.listData(vendorId, deviceType));
    }

}
