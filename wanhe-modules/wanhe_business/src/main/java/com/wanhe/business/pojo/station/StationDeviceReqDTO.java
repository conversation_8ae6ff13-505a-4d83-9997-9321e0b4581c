package com.wanhe.business.pojo.station;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/10/28
 */
@Data
public class StationDeviceReqDTO {

    @Schema(description = "电站id")
    @NotNull
    private Integer powerStationId;

    @Schema(description = "设备类型 （0：EMU 1:微逆 2：电表）")
    private Integer deviceType;

    @Schema(description = "序列号")
    private String number;

    @Schema(description = "设备状态 0离线 1在线 2告警 3故障 4停机 5自检 6预启动")
    private Integer status;

    @Schema(description = "emuId")
    private Integer emuId;

}
