package com.wanhe.business.controller;

import com.github.pagehelper.Page;
import com.ruoyi.common.core.domain.IntStr;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.wanhe.business.domain.Vendor;
import com.wanhe.business.pojo.product.VendorDTO;
import com.wanhe.business.pojo.product.VendorPageReqDTO;
import com.wanhe.business.pojo.product.VendorUpdateDTO;
import com.wanhe.business.service.IVendorService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 厂商
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@RestController
@RequestMapping("/vendor")
public class VendorController extends BaseController {

    @Resource
    private IVendorService vendorService;

    /**
     * 新增 business:vendor:add
     */
    @RequiresPermissions("business:vendor:add")
    @Log(title = "厂商", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@Validated @RequestBody VendorDTO dto) {
        return R.ok(vendorService.add(dto));
    }

    /**
     * 修改 business:vendor:edit
     */
    @RequiresPermissions("business:vendor:edit")
    @Log(title = "厂商", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@Validated @RequestBody VendorUpdateDTO dto) {
        return R.ok(vendorService.update(dto));
    }

    /**
     * 详情
     */
    @GetMapping(value = "/{id}")
    public R<Vendor> getInfo(@PathVariable Integer id) {
        return R.ok(vendorService.getById(id));
    }

    /**
     * 分页 business:vendor:page
     */
    @RequiresPermissions("business:vendor:page")
    @GetMapping("/page")
    public TableDataInfo<Vendor> page(@Validated VendorPageReqDTO dto) {
        Page<Vendor> page = startPage(dto);
        vendorService.page(dto);
        return getDataTable(page);
    }

    /**
     * 下拉选项
     */
    @GetMapping("/list")
    public R<List<IntStr>> list(@RequestParam(required = false) Integer deviceType) {
        return R.ok(vendorService.listData(deviceType));
    }

    /**
     * 删除 business:vendor:remove
     */
    @RequiresPermissions("business:vendor:remove")
    @Log(title = "厂商", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public R<Boolean> remove(@PathVariable Integer id) {
        return R.ok(vendorService.delete(id));
    }
}
