package com.wanhe.business.controller;

import com.github.pagehelper.Page;
import com.ruoyi.common.core.domain.IntStr;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.wanhe.business.domain.FirmwareFile;
import com.wanhe.business.pojo.product.FirmwareFileDTO;
import com.wanhe.business.pojo.product.FirmwareFilePageReqDTO;
import com.wanhe.business.pojo.product.FirmwareFileUpdateDTO;
import com.wanhe.business.service.IFirmwareFileService;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Resource;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 固件文件管理
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@RestController
@RequestMapping("/firmware/file")
public class FirmwareFileController extends BaseController {

    @Resource
    private IFirmwareFileService firmwareFileService;

    /**
     * 新增 business:firmwareFile:add
     */
    @RequiresPermissions("business:firmwareFile:add")
    @Log(title = "固件文件管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@Validated @RequestBody FirmwareFileDTO dto) {
        return R.ok(firmwareFileService.add(dto));
    }

    /**
     * 修改 business:firmwareFile:edit
     */
    @RequiresPermissions("business:firmwareFile:edit")
    @Log(title = "固件文件管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@Validated @RequestBody FirmwareFileUpdateDTO dto) {
        return R.ok(firmwareFileService.update(dto));
    }

    /**
     * 详情
     */
    @GetMapping(value = "/{id}")
    public R<FirmwareFile> getInfo(@PathVariable Integer id) {
        return R.ok(firmwareFileService.getById(id));
    }

    /**
     * 分页 business:firmwareFile:page
     */
    @RequiresPermissions("business:firmwareFile:page")
    @GetMapping("/page")
    public TableDataInfo<FirmwareFile> page(@Validated FirmwareFilePageReqDTO dto) {
        Page<FirmwareFile> page = startPage(dto);
        firmwareFileService.page(dto);
        return getDataTable(page);
    }

    /**
     * 下拉列表
     */
    @GetMapping("/list")
    public R<List<IntStr>> list(@Schema(description = "固件类型") @RequestParam Integer type,
                                @Schema(description = "设备类型") @RequestParam Integer deviceType,
                                @Schema(description = "设备类型") @RequestParam String deviceModel) {
        return R.ok(firmwareFileService.list(type, deviceType, deviceModel));
    }

    /**
     * 删除 business:firmwareFile:remove
     */
    @RequiresPermissions("business:firmwareFile:remove")
    @Log(title = "固件文件管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public R<Boolean> remove(@PathVariable Integer id) {
        return R.ok(firmwareFileService.delete(id));
    }
}
