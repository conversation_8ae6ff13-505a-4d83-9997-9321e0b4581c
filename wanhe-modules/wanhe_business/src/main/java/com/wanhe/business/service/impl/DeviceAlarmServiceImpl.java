package com.wanhe.business.service.impl;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.utils.Tools;
import com.ruoyi.system.api.domain.SysI18n;
import com.wanhe.business.domain.DeviceAlarm;
import com.wanhe.business.domain.Fault;
import com.wanhe.business.domain.PowerStation;
import com.wanhe.business.domain.StationDevice;
import com.wanhe.business.mapper.DeviceAlarmMapper;
import com.wanhe.business.mapper.FaultMapper;
import com.wanhe.business.pojo.product.DeviceAlarmPageReqDTO;
import com.wanhe.business.pojo.product.FaultVO;
import com.wanhe.business.service.IDeviceAlarmService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-10-26
 */
@Service
public class DeviceAlarmServiceImpl extends DataScopeServiceImpl<DeviceAlarmMapper, DeviceAlarm> implements IDeviceAlarmService {

    @Resource
    private FaultMapper faultMapper;

    @Override
    public void page(DeviceAlarmPageReqDTO dto) {
        lambdaQueryAll(DeviceAlarm::getPowerStationId, dto.getPowerStationId())
                .select(PowerStation::getStationName)
                .select(DeviceAlarm::getId, DeviceAlarm::getAlarmData, DeviceAlarm::getOccurredAt, DeviceAlarm::getResolvedAt, DeviceAlarm::getSnNumber,
                        DeviceAlarm::getAlarmCode,DeviceAlarm::getAlarmKey, DeviceAlarm::getDeviceId, DeviceAlarm::getDeviceType, DeviceAlarm::getPowerStationId)
                .select("n.name_{lang} as alarmName,i.name_{lang} as operationMethod".replace("{lang}", dto.getLanguage()))
                .innerJoin(Fault.class, i -> i.eq(Fault::getFaultKey, DeviceAlarm::getAlarmKey)
                        .eq(DeviceAlarm::getResolved, dto.getResolved())
                        .like(StrUtil.isNotBlank(dto.getSnNumber()), DeviceAlarm::getSnNumber, dto.getSnNumber())
                )
                .innerJoin(StationDevice.class, i -> i.eq(StationDevice::getId, DeviceAlarm::getDeviceId).eq(StationDevice::getDeleted, 0))
                .leftJoin(SysI18n.class, "n", i -> i.eq(SysI18n::getType, 4).eq(SysI18n::getDataId, Fault::getId))
                .leftJoin(SysI18n.class, "i", i -> i.eq(SysI18n::getType, 5).eq(SysI18n::getDataId, Fault::getId))
                .like(StrUtil.isNotBlank(dto.getAlarmName()), "n.name_" + dto.getLanguage(), dto.getAlarmName())
                .orderByDesc(DeviceAlarm::getOccurredAt)
                .list();
    }

    @Override
    public List<DeviceAlarm> record(String language) {
        List<DeviceAlarm> list = lambdaQueryAll(DeviceAlarm::getPowerStationId, null)
                .select(DeviceAlarm::getAlarmKey, DeviceAlarm::getOccurredAt, DeviceAlarm::getPowerStationId, DeviceAlarm::getAlarmCode,DeviceAlarm::getAlarmKey)
                .select(PowerStation::getStationName)
                .innerJoin(StationDevice.class, i -> i.eq(StationDevice::getId, DeviceAlarm::getDeviceId).eq(StationDevice::getDeleted, 0))
                .eq(DeviceAlarm::getResolved, 0)
                .orderByDesc(DeviceAlarm::getId)
                .last("limit 50")
                .list();
        if (!list.isEmpty()) {
            String keys = list.stream().map(a -> Tools.joinKey(a.getAlarmKey())).distinct().collect(Collectors.joining(","));
            Map<String, String> fMap = faultMapper.getByKeys(keys, language).stream().collect(Collectors.toMap(FaultVO::getFaultKey, FaultVO::getFaultDescription));
            list.forEach(p -> p.setAlarmName(fMap.get(p.getAlarmKey())));
        }
        return list;
    }

}
