package com.wanhe.business.service;

import com.ruoyi.common.core.enums.I18nTypeEnum;
import com.ruoyi.common.security.service.IBaseService;
import com.ruoyi.system.api.domain.SysI18n;

import java.util.List;

/**
 * 国际化配置Service接口
 *
 * <AUTHOR>
 * @date 2023-08-24
 */
public interface ISysI18nService extends IBaseService<SysI18n> {

    void add(I18nTypeEnum type, Integer dataId, String name);

    void update(I18nTypeEnum type, Integer dataId, String name);

    void delete(I18nTypeEnum type, Integer dataId);

    void batchDelete(I18nTypeEnum type, List<Integer> dataIds);

}
