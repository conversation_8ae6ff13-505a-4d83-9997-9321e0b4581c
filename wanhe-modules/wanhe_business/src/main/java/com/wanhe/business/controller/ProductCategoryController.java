package com.wanhe.business.controller;

import com.github.pagehelper.Page;
import com.ruoyi.common.core.domain.IntStr;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.wanhe.business.domain.ProductCategory;
import com.wanhe.business.pojo.product.ProductCategoryDTO;
import com.wanhe.business.pojo.product.ProductCategoryPageReqDTO;
import com.wanhe.business.pojo.product.ProductCategoryUpdateDTO;
import com.wanhe.business.service.IProductCategoryService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品分类
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@RestController
@RequestMapping("/product/category")
public class ProductCategoryController extends BaseController {

    @Resource
    private IProductCategoryService productCategoryService;

    /**
     * 新增 business:product:category:add
     */
    @RequiresPermissions("business:product:category:add")
    @Log(title = "产品分类", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@Validated @RequestBody ProductCategoryDTO dto) {
        return R.ok(productCategoryService.add(dto));
    }

    /**
     * 修改 business:product:category:edit
     */
    @RequiresPermissions("business:product:category:edit")
    @Log(title = "产品分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@Validated @RequestBody ProductCategoryUpdateDTO dto) {
        return R.ok(productCategoryService.update(dto));
    }

    /**
     * 详情
     */
    @GetMapping(value = "/{id}")
    public R<ProductCategory> getInfo(@PathVariable Integer id) {
        return R.ok(productCategoryService.getById(id));
    }

    /**
     * 分页 business:product:category:page
     */
    @RequiresPermissions("business:product:category:page")
    @GetMapping("/page")
    public TableDataInfo<ProductCategory> page(@Validated ProductCategoryPageReqDTO dto) {
        Page<ProductCategory> page = startPage(dto);
        productCategoryService.page(dto);
        return getDataTable(page);
    }

    /**
     * 删除 business:product:category:remove
     */
    @RequiresPermissions("business:product:category:remove")
    @Log(title = "产品分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public R<Boolean> remove(@PathVariable Integer id) {
        return R.ok(productCategoryService.delete(id));
    }

    /**
     * 全部列表
     */
    @GetMapping("/all/list")
    public R<List<IntStr>> all() {
        return R.ok(productCategoryService.all());
    }
}
