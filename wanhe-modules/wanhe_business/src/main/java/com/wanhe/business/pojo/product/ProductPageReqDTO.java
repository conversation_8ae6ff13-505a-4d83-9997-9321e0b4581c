package com.wanhe.business.pojo.product;

import com.ruoyi.common.core.domain.PageReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ProductPageReqDTO extends PageReqDTO {

    @Schema(description = "设备类型")
    @NotNull
    private Integer deviceType;

    @Schema(description = "产品型号")
    private String productName;

    @Schema(description = "型号编码")
    private String productCode;

    @Schema(description = "厂家id")
    private Integer vendorId;

    @Schema(description = "是否导出", hidden = true)
    private boolean export;

}
