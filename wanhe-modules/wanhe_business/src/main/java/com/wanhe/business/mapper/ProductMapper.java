package com.wanhe.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.core.domain.IntStr;
import com.wanhe.business.domain.Product;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @date 2024-10-26
*/
public interface ProductMapper extends BaseMapper<Product> {

    @Select("select id as k, product_name as v from wh_product where id in(${ids})")
    List<IntStr> getData(String ids);

    @Select("select * from wh_product where device_type = #{deviceType} and product_code = #{productCodes} and deleted = 0")
    Product getByTypeCode(Integer deviceType, String productCodes);

    @Select("select * from wh_product where device_type = #{deviceType} and product_code in(${productCodes}) and deleted = 0")
    List<Product> getByTypeCodes(int deviceType, String productCodes);

    List<IntStr> listData(Integer vendorId, Integer deviceType);

}
