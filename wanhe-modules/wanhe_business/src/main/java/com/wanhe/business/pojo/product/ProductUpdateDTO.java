package com.wanhe.business.pojo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(name = "ProductUpdateDTO", description = "产品表")
public class ProductUpdateDTO extends ProductDTO {

    @Schema(description = "产品id")
    @NotNull
    @Positive
    private Integer id;

}
