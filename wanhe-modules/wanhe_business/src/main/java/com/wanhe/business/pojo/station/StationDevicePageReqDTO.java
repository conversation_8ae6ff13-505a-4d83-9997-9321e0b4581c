package com.wanhe.business.pojo.station;

import com.ruoyi.common.core.domain.PageReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/10/28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StationDevicePageReqDTO extends PageReqDTO {

    @Schema(description = "电站id")
    private Integer powerStationId;

    @Schema(description = "序列号")
    private String number;

    @Schema(description = "设备状态 0离线 1在线 2告警 3故障 4停机 5自检 6预启动")
    private Integer status;

    @Schema(description = "设备类型 （0：EMU 1:微逆 2：电表）有pid是EMU微逆 没有就是WIFI微逆")
    private Integer deviceType;

    @Schema(description = "微逆类型 0EMU微逆 1WIFI微逆")
    private Integer wnType;

}
