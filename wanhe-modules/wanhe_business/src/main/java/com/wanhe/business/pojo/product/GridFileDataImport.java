package com.wanhe.business.pojo.product;

import com.ruoyi.common.core.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024-11-15
 */
@Data
public class GridFileDataImport {

    @Excel(name = "地址")
    private Integer addr;

    @Excel(name = "是否下发", readConverterExp = "1=true,0=false")
    private Boolean issue;

    @Excel(name = "值")
    private BigDecimal val;

}
