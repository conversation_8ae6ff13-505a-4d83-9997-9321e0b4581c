package com.wanhe.business.pojo.station;

import com.ruoyi.system.api.domain.SysDept;
import com.ruoyi.system.api.domain.SysUser;
import com.wanhe.business.domain.PowerStation;
import com.wanhe.business.domain.StationDevice;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/23
 */
@Data
public class StationReportVO {

    @Schema(description = "电站数据")
    private PowerStation station;

    @Schema(description = "业主信息")
    private List<SysUser> users;

    @Schema(description = "EMU")
    private List<StationDevice> emus;

    @Schema(description = "微逆")
    private List<StationDevice> wns;

    @Schema(description = "电表")
    private List<StationDevice> dbs;

    @Schema(description = "分时电价")
    private List<PowerStationPriceMonthVO> prices;

    @Schema(description = "机构")
    private SysDept dept;

}
