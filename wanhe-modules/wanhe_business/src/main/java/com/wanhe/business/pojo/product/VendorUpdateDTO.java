package com.wanhe.business.pojo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(name = "VendorUpdateDTO", description = "厂商表")
public class VendorUpdateDTO extends VendorDTO {

    @Schema(description = "厂商id")
    @NotNull
    @Positive
    private Integer id;

}
