package com.wanhe.business.pojo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-10-26
 */
@Data
@Schema(name = "GridFileDTO", description = "并网文件表")
public class GridFileDTO {

    @Schema(description = "文件名称")
    @NotBlank
    private String fileName;

    @Schema(description = "国家ID")
    private Integer countryId;

    @Schema(description = "省份ID")
    private Integer provinceId;

    @Schema(description = "版本号")
    private String versionNumber;

    @Schema(description = "描述")
    private String remark;

}
