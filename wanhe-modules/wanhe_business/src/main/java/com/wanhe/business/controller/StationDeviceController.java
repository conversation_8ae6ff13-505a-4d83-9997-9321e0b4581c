package com.wanhe.business.controller;

import com.github.pagehelper.Page;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.wanhe.business.domain.DeviceReplaceRecord;
import com.wanhe.business.domain.StationDevice;
import com.wanhe.business.pojo.device.FirmwareVersionsVO;
import com.wanhe.business.pojo.station.*;
import com.wanhe.business.service.IDeviceReplaceRecordService;
import com.wanhe.business.service.IStationDeviceService;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 设备
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@RestController
@RequestMapping("/station/device")
public class StationDeviceController extends BaseController {

    @Resource
    private IStationDeviceService stationDeviceService;
    @Resource
    private IDeviceReplaceRecordService deviceReplaceRecordService;

    /**
     * 检查序列号是否正确
     */
    @GetMapping("/check/sn")
    public R<Void> checkSn(@RequestParam String sn) {
        stationDeviceService.checkSn(sn);
        return R.ok();
    }

    /**
     * 校验序列号是否正确
     * type: 0-EMU 1-微逆 2-电表 3-组件 4-电池 5-充电桩
     */
    @GetMapping("/check/sn2")
    public R<Void> checkSn2(@RequestParam String sn, @RequestParam Integer type) {
        stationDeviceService.checkSn2(sn,type);
        return R.ok();
    }

    /**
     * 新增EMU business:stationDevice:addEmu
     */
    @RequiresPermissions("business:stationDevice:addEmu")
    @Log(title = "设备", businessType = BusinessType.INSERT)
    @PostMapping("/emu")
    public R<Boolean> addEmu(@Validated @RequestBody EmuDTO dto) {
        return R.ok(stationDeviceService.addEmu(dto));
    }

    /**
     * 修改EMU business:stationDevice:editEmu
     */
    @RequiresPermissions("business:stationDevice:editEmu")
    @Log(title = "设备", businessType = BusinessType.UPDATE)
    @PutMapping("/emu")
    public R<Void> editEmu(@Validated @RequestBody EmuUpdateDTO dto) {
        Integer id = stationDeviceService.updateEmu(dto);
        if (id != null) {
            stationDeviceService.getRemoteBizDataService().networking(id);
        }
        return R.ok();
    }

    /**
     * 新增微逆 business:stationDevice:addWn
     */
    @RequiresPermissions("business:stationDevice:addWn")
    @Log(title = "设备", businessType = BusinessType.INSERT)
    @PostMapping("/wn")
    public R<Boolean> addWn(@Validated @RequestBody WnDTO dto) {
        return R.ok(stationDeviceService.addWn(dto));
    }

    /**
     * app新增修改微逆 business:stationDevice:addWn
     */
    @RequiresPermissions("business:stationDevice:addWn")
    @Log(title = "设备", businessType = BusinessType.INSERT)
    @PostMapping("/wn/app")
    public R<Void> addWnApp(@Validated @RequestBody WnAppDTO dto) {
        if (stationDeviceService.addWnApp(dto)) {
            stationDeviceService.getRemoteBizDataService().networking(dto.getPowerStationId());
        }
        return R.ok();
    }

    /**
     * 修改微逆 business:stationDevice:editEmu
     */
    @RequiresPermissions("business:stationDevice:editEmu")
    @Log(title = "设备", businessType = BusinessType.UPDATE)
    @PutMapping("/wn")
    public R<Void> editWn(@Validated @RequestBody WnUpdateDTO dto) {
        if (dto.getId() == null) {
            throw new ServiceException("参数错误");
        }
        Integer id = stationDeviceService.editWn(dto);
        if (id != null) {
            stationDeviceService.getRemoteBizDataService().networking(id);
        }
        return R.ok();
    }

    /**
     * 新增电表或编辑电表 business:stationDevice:meter
     */
    @RequiresPermissions("business:stationDevice:meter")
    @Log(title = "设备", businessType = BusinessType.INSERT)
    @PostMapping("/meter")
    public R<Boolean> addMeter(@Validated @RequestBody MeterDTO dto) {
        if (dto.getReflux()) {
            if (dto.getMeter().stream().map(Meter::getNumber).collect(Collectors.toSet()).size() != dto.getMeter().size()) {
                throw new ServiceException("序列号重复");
            }
            if (dto.getMeter().stream().map(Meter::getSeat).collect(Collectors.toSet()).size() != dto.getMeter().size()) {
                throw new ServiceException("电表位置重复");
            }
        }
        stationDeviceService.addMeter(dto);
        return stationDeviceService.getRemoteBizDataService().configMeter(dto.getPowerStationId());
    }

    /**
     * 替换设备 business:stationDevice:replace
     */
    @RequiresPermissions("business:powerStation:replace")
    @Log(title = "设备", businessType = BusinessType.UPDATE)
    @PutMapping("/replace")
    public R<Integer> replace(@Validated @RequestBody ReplaceDTO dto) {
        StationDevice device = stationDeviceService.replace(dto);
        if (device.getDeviceType() == 2) {
            stationDeviceService.getRemoteBizDataService().configMeter(device.getPowerStationId());
        }
        if (device.getDelete()) {
            stationDeviceService.getRemoteBizDataService().networking(device.getPowerStationId());
        }
        return R.ok(device.getId());
    }

    /**
     * 分页
     */
    @GetMapping("/page")
    public TableDataInfo<StationDevice> page(@Validated StationDevicePageReqDTO dto) {
        Page<StationDevice> page = startPage(dto);
        stationDeviceService.page(dto);
        return getDataTable(page);
    }

    /**
     * app组件联网方式
     */
    @GetMapping("/networking/mode")
    public R<Integer> networkingMode(@RequestParam Integer deviceId) {
        return R.ok(stationDeviceService.networkingMode(deviceId));
    }

    /**
     * app联网方式列表
     */
    @GetMapping("/networking/mode/list")
    public R<List<StationDevice>> networkingModeList(@RequestParam Integer powerStationId) {
        return R.ok(stationDeviceService.networkingModeList(powerStationId));
    }

    /**
     * 分页EMU
     */
    @GetMapping("/emu/page")
    public TableDataInfo<StationDevice> page(@Validated EmuPageReqDTO dto) {
        Integer stationType = stationDeviceService.getPowerStationMapper().getStationType(dto.getPowerStationId());
        if (stationType == null) {
            return getDataTable(null);
        }
        dto.setStationType(stationType);
        Page<StationDevice> page = startPage(dto);
        stationDeviceService.pageEmu(dto);
        return getDataTable(page);
    }

    /**
     * 设备列表树
     */
    @RequiresPermissions("business:stationDevice:tree")
    @GetMapping("/all/tree")
    public R<List<StationDevice>> tree(@Validated StationDeviceReqDTO dto) {
        return R.ok(stationDeviceService.tree(dto));
    }

    /**
     * EMU设备列表
     */
    @GetMapping("/emu/list/{powerStationId}")
    public R<List<StationDevice>> emuList(@PathVariable Integer powerStationId) {
        return R.ok(stationDeviceService.emuList(powerStationId));
    }

    /**
     * 微逆设备列表
     */
    @GetMapping("/wn/list/{powerStationId}")
    public R<List<StationDevice>> wnList(@PathVariable Integer powerStationId) {
        return R.ok(stationDeviceService.wnList(powerStationId));
    }
    /**
     * app电站所属设备分页
     */
    @GetMapping("/device/page")
    public TableDataInfo<StationDevice> devicePage(@Validated DevicePageReqDTO dto) {
        Page<StationDevice> page = startPage(dto);
        stationDeviceService.devicePage(dto);
        return getDataTable(page);
    }

    /**
     * 统计各类型设备数量
     */
    @GetMapping("/device/countByType")
    public R<List<DeviceCountVo>> count(@RequestParam Integer powerStationId) {
        List<DeviceCountVo> list = stationDeviceService.countByType(powerStationId);
        return R.ok(list);
    }

    /**
     * 查询电表信息
     */
    @GetMapping("/meter/{powerStationId}")
    public R<MeterDTO> meter(@PathVariable Integer powerStationId) {
        return R.ok(stationDeviceService.meter(powerStationId));
    }

    /**
     * 详情
     */
    @GetMapping(value = "/{id}")
    public R<StationDevice> getInfo(@PathVariable Integer id) {
        return R.ok(stationDeviceService.get(id));
    }

    /**
     * 通过sn获取设备信息
     *
     * @param sn sn
     * @return {@link R }<{@link StationDevice }>
     */
    @GetMapping("/sn/{sn}")
    public R<StationDevice> getInfoBySn(@PathVariable String sn) {
        return R.ok(stationDeviceService.getInfoBySn(sn));
    }

    /**
     * 删除 business:stationDevice:remove
     */
    @RequiresPermissions("business:stationDevice:remove")
    @Log(title = "设备", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public R<Void> remove(@PathVariable Integer id) {
        StationDevice device = stationDeviceService.delete(id);
        if (device.getDeviceType() == 2) {
            stationDeviceService.getRemoteBizDataService().configMeter(device.getPowerStationId());
        }
        if (device.getDelete()) {
            stationDeviceService.getRemoteBizDataService().networking(device.getPowerStationId());
        }
        return R.ok();
    }

    /**
     * 设备更替记录分页 business:stationDevice:pageEmu
     */
    @RequiresPermissions("business:stationDevice:pageRecord")
    @GetMapping("/record/page")
    public TableDataInfo<DeviceReplaceRecord> recordPage(@Validated DeviceReplaceRecordPageReqDTO dto) {
        Page<DeviceReplaceRecord> page = startPage(dto);
        deviceReplaceRecordService.page(dto);
        return getDataTable(page);
    }

    /**
     * 固件版本检查
     */
    @GetMapping("/firmware/versions")
    public R<List<FirmwareVersionsVO>> versions(@Schema(description = "电站id") @RequestParam Integer powerStationId) {
        return R.ok(stationDeviceService.versions(powerStationId));
    }

    /**
     * 激活
     */
    @GetMapping("/activate")
    public R<Boolean> activate(@Schema(description = "电站id") @RequestParam Integer powerStationId) {
        return R.ok(stationDeviceService.activate(powerStationId));
    }

    /**
     * 通过电站查询EMU或微逆序列号列表
     */
    @GetMapping("/sns")
    public R<List<String>> sns(@Schema(description = "电站id") @RequestParam Integer powerStationId,
                               @Schema(description = "设备类型 0emu 1微逆") @RequestParam Integer deviceType) {
        return R.ok(stationDeviceService.sns(powerStationId, deviceType));
    }

}
