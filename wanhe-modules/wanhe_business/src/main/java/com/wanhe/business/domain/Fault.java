package com.wanhe.business.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.base.ICUDDEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024-10-26
 */
@Data
@TableName("wh_fault")
@Schema(name = "Fault", description = "故障表")
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = false)
public class Fault extends ICUDDEntity {

    @Excel(name = "故障字")
    @Schema(description = "故障字")
    private String faultCode;

    @Excel(name = "bit位")
    @Schema(description = "bit位")
    private Integer bit;

    @Excel(name = "故障描述")
    @Schema(description = "故障描述")
    private String faultDescription;

    @Excel(name = "处理意见")
    @Schema(description = "处理意见")
    private String resolutionSuggestion;

    @Schema(description = "故障等级")
    private String faultLevel;

    @Excel(name = "故障等级")
    @Schema(description = "故障等级")
    @TableField(exist = false)
    private String faultLevelName;

    @Excel(name = "备注信息")
    @Schema(description = "备注信息")
    private String notes;

    @Schema(description = "故障key")
    private String faultKey;

}
