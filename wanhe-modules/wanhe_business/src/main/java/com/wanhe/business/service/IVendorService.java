package com.wanhe.business.service;


import com.ruoyi.common.core.domain.IntStr;
import com.ruoyi.common.security.service.IBaseService;
import com.wanhe.business.domain.Vendor;
import com.wanhe.business.pojo.product.VendorDTO;
import com.wanhe.business.pojo.product.VendorPageReqDTO;
import com.wanhe.business.pojo.product.VendorUpdateDTO;

import java.util.List;

/**
* <AUTHOR>
* @date 2024-10-26
*/
public interface IVendorService extends IBaseService<Vendor> {

    boolean add(VendorDTO dto);

    boolean update(VendorUpdateDTO dto);

    void page(VendorPageReqDTO dto);

    List<IntStr> listData(Integer deviceType);

    boolean delete(Integer id);

}
