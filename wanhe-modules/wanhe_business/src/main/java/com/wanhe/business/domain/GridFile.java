package com.wanhe.business.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.domain.base.ICUDEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024-10-26
 */
@Data
@TableName("wh_grid_file")
@Schema(name = "GridFile", description = "并网文件表")
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = false)
public class GridFile extends ICUDEntity {

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "国家ID")
    private Integer countryId;

    @Schema(description = "省份ID")
    private Integer provinceId;

    @Schema(description = "版本号")
    private String versionNumber;

    @Schema(description = "描述")
    private String remark;

    @Schema(description = "国家")
    @TableField(exist = false)
    private String country;

    @Schema(description = "省份")
    @TableField(exist = false)
    private String province;

}
