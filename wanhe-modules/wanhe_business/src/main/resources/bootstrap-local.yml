spring:
  cloud:
    nacos:
      username: nacos
      password: wa<PERSON><PERSON>@123
      discovery:
        # 服务注册地址
        server-addr: 192.168.10.249:8848
        # 命名空间
        namespace: 0d8f014d-b030-4467-be18-f86b1c11a27b
      config:
        # 配置中心地址
        server-addr: 192.168.10.249:8848
        # 命名空间
        namespace: 0d8f014d-b030-4467-be18-f86b1c11a27b
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
