<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanhe.business.mapper.ProductMapper">
    <select id="listData" resultType="com.ruoyi.common.core.domain.IntStr">
        select id as k, product_name as v from wh_product where deleted = 0
        <if test="vendorId != null">and vendor_id = #{vendorId}</if>
        <if test="deviceType != null">and device_type = #{deviceType}</if>
        order by id desc
    </select>
</mapper>