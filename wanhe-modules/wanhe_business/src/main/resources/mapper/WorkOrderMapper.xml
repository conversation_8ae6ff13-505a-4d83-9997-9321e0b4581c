<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wanhe.business.mapper.WorkOrderMapper">

    <update id="addConfirmPerson">
        UPDATE wh_work_order
        SET order_status = #{orderStatus},
        confirm_id = #{confirmId}
        WHERE id = #{orderId} AND is_delete = 0
          AND order_status = 1

    </update>

    <update id="updateOrderStatus">
        UPDATE wh_work_order
        SET order_status = #{orderStatus}
        WHERE id = #{orderId} AND is_delete = 0
        <if test="orderStatus == 2">
            AND order_status = 1
        </if>
        <if test="orderStatus == 3">
            AND order_status = 2
        </if>

    </update>


    <select id="page" resultType="com.wanhe.business.pojo.workOrder.vo.WorkOrderPageVO">

        SELECT wo.id, wo.order_sn, wo.order_type, wo.order_status, wo.commit_time, process.confirm_time,
               process.completion_time, evaluate.score
        FROM wh_work_order AS wo
        LEFT JOIN wh_order_evaluate AS evaluate ON wo.id = evaluate.order_id AND evaluate.is_delete = 0
        LEFT JOIN wh_order_process AS process ON wo.id = process.order_id AND process.is_delete = 0
        LEFT JOIN wh_power_station AS station ON wo.station_id = station.id AND station.is_delete = 0
        WHERE wo.is_delete = 0 AND
            (wo.user_id = #{userId} OR process.processor_id = #{userId}
                <if test="dto.orderStatus != null and dto.orderStatus == 1">
                    OR station.installer_id = #{userId} OR wo.confirm_dept_id = #{deptId} OR wo.confirm_id = #{userId}
                </if>
            )
        <if test="dto.orderSn != null">
            AND wo.order_sn LIKE CONCAT('%', #{dto.orderSn}, '%')
        </if>
        <if test="dto.orderStatus != null">
            AND wo.order_status = #{dto.orderStatus}
        </if>
        ORDER BY ${orderBy}
    </select>

    <select id="getDetail" resultType="com.wanhe.business.pojo.workOrder.vo.WorkOrderVO">
        SELECT wo.*, process.id AS processId, process.severity AS severity,
               process.possible_causes AS possibleCauses, process.process_steps,
               process.solution, process.solution_status, process.completion_time,
               process.proof_completion, process.confirm_time, process.confirmed_by,
               process.processor_id, process.processor, process.processor_phone_number,
               process.processor_email, evaluate.id AS evaluateId, evaluate.content,
               evaluate.service_score, evaluate.response_score, evaluate.handle_score,
               evaluate.nps_score, evaluate.score
        FROM wh_work_order AS wo
        LEFT JOIN wh_order_evaluate AS evaluate ON wo.id = evaluate.order_id AND evaluate.is_delete = 0
        LEFT JOIN wh_order_process AS process ON wo.id = process.order_id AND process.is_delete = 0
        LEFT JOIN wh_power_station AS station ON wo.station_id = station.id AND station.is_delete = 0
        WHERE wo.is_delete = 0 AND wo.id = #{orderId}
    </select>

    <select id="getProcessorInfo" resultType="com.wanhe.business.domain.OrderProcess">
        SELECT email AS processorEmail, phonenumber AS processorPhoneNumber, user_id AS processorId, nick_name AS processor
        FROM sys_user
        WHERE user_id = #{processorId}
    </select>

    <select id="getIncompleteOrderCount" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM wh_work_order AS wo
        LEFT JOIN wh_order_process AS process ON wo.id = process.order_id AND process.is_delete = 0
        WHERE wo.is_delete = 0 AND wo.order_status != 3
        AND (wo.user_id = #{userId} OR process.processor_id = #{userId})
    </select>
</mapper>