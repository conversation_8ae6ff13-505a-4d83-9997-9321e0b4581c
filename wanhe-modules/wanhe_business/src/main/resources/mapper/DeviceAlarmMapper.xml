<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanhe.business.mapper.DeviceAlarmMapper">

	<select id="page" resultType="com.wanhe.business.domain.DeviceAlarm">
		select s.station_name,a.alarm_data,a.occurred_at,a.resolved_at,a.sn_number,n.name_${dto.language} as alarmName,i.name_${dto.language} as operationMethod
		from wh_device_alarm a
		inner join wh_fault f on f.fault_code = a.alarm_code and f.bit = a.alarm_data and a.is_resolved = #{dto.resolved}${dto.dataScopeSql}
		<if test="dto.snNumber != null and dto.snNumber != ''">and a.sn_number = #{dto.snNumber}</if>
		left join wh_power_station s on s.id = a.power_station_id
		left join sys_i18n n on n.type = 4 and n.data_id = f.id
		left join sys_i18n i on i.type = 5 and i.data_id = f.id
		<where>
			<if test="dto.alarmName != null and dto.alarmName != ''">and n.name_${dto.language} like concat('%',#{dto.alarmName},'%')</if>
		</where>
		order by a.id
	</select>

</mapper>