<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanhe.business.mapper.GridFileMapper">

	<select id="all" resultType="com.wanhe.business.pojo.device.GridFileVO">
		select f.id as k, f.file_name as v, d.name as c
		from wh_grid_file f left join sys_district d on d.id = f.country_id
	   	where f.is_delete = 0
	   	<if test="countryId != null">
			and f.country_id = #{countryId}
		</if>
	   	<if test="name != null and name != ''">
			and f.file_name like concat('%', #{name}, '%')
		</if>
	   	order by f.id
	</select>

</mapper>