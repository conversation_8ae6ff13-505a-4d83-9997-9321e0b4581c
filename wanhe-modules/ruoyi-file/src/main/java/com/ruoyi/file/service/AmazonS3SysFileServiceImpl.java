package com.ruoyi.file.service;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.PutObjectResult;
import com.ruoyi.file.config.AmazonS3Config;
import com.ruoyi.file.utils.FileUploadUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

/**
 * FastDFS 文件存储
 *
 * <AUTHOR>
 */
@Slf4j
@Component("amazonS3")
public class AmazonS3SysFileServiceImpl implements ISysFileService {

    @Resource
    private AmazonS3 s3Client;

    @Resource
    private AmazonS3Config amazonS3Config;

    /**
     * FastDfs文件上传接口
     *
     * @param file 上传的文件
     * @return 访问地址
     */
    @Override
    public String uploadFile(MultipartFile file, Float compression) throws Exception {
        try {
            String fileName = FileUploadUtils.extractFilename(file);
            String fileKey = amazonS3Config.getDefaultFile().concat("/").concat(fileName);
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(file.getSize());
            metadata.setContentType(file.getContentType());
            PutObjectRequest request = new PutObjectRequest(amazonS3Config.getBucketName(), fileKey, file.getInputStream(), metadata);
            s3Client.putObject(request);
            return amazonS3Config.getHost() + fileKey;
        } catch (Exception e) {
            log.error("S3文件上传失败", e);
        }
        return "";
    }

}
