package com.ruoyi.file.service;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.PutObjectRequest;
import com.ruoyi.common.core.utils.file.FileTypeUtils;
import com.ruoyi.common.core.utils.file.MimeTypeUtils;
import com.ruoyi.file.config.AliOssConfig;
import com.ruoyi.file.utils.FileUploadUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;

/**
 * FastDFS 文件存储
 *
 * <AUTHOR>
 */
@Slf4j
@Component("aliOss")
public class AliOssSysFileServiceImpl implements ISysFileService {

    @Resource
    private OSS ossClient;

    @Resource
    private AliOssConfig aliOssConfig;

    /**
     * FastDfs文件上传接口
     *
     * @param file 上传的文件
     * @return 访问地址
     */
    @Override
    public String uploadFile(MultipartFile file,Float compression) throws Exception {
        String filePath = "";
        String fileName = FileUploadUtils.extractFilename(file);
        // 创建ossClient实例。
        String folder = aliOssConfig.getDefaultFile().concat("/").concat(fileName);
        //获取文件类型
        String extension = FileTypeUtils.getExtension(file);

        try(InputStream inputStream = file.getInputStream()) {
            uploadFileOss(folder, inputStream);
            filePath = aliOssConfig.getHost() + "/" + folder;

            //如果不是图片，直接返回路径
            if(!FileUploadUtils.isAllowedExtension(extension, MimeTypeUtils.IMAGE_EXTENSION)){
                return filePath;
            }
        } catch (IOException oe) {
            log.error("OSSException 文件上传失败", oe);
        }
        //将图片转换为输入流进入上传
        folder =  folder.replace(".".concat(extension),"m.".concat(extension));

        try (InputStream inputStream2 = file.getInputStream();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            Thumbnails.of(inputStream2)
                    .scale(1.0) // 保持原始尺寸
                    .outputQuality(compression)
                    .toOutputStream(outputStream);
            byte[] byteArray = outputStream.toByteArray();
            try(ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(byteArray)){
                uploadFileOss(folder,byteArrayInputStream);
            }catch (IOException oe) {
                log.error("OSSException 压缩文件上传失败", oe);
            }
        } catch (IOException e) {
            log.error("OSSException 压缩文件失败", e);
        }
        return filePath;
    }

    public void uploadFileOss(String folder,InputStream inputStream)  {
        // 创建PutObjectRequest对象。
        PutObjectRequest putObjectRequest = new PutObjectRequest(aliOssConfig.getBucketName(), folder,inputStream);
        // 创建PutObject请求。
        ossClient.putObject(putObjectRequest);
        log.info("oss文件上传成功:{}",folder);
    }
}
