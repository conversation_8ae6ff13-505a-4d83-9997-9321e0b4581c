package com.ruoyi.file.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Minio 配置信息
 *
 * <AUTHOR>
 */
@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "ali.oss")
public class AliOssConfig {
    //应用key
    private String accessKeyId;
    //应用密钥
    private String accessKeySecret;
    //访问域名
    private String host;
    //地域
    private String endpoint;
    //存储桶名称
    private String bucketName;
    //默认路径
    private String defaultFile;
    //过期时间
    private Long expireTime;
    //最大长度
    private Long maxLength;

    @Bean
    public OSS getOssClient(){
        return new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
    }
}
