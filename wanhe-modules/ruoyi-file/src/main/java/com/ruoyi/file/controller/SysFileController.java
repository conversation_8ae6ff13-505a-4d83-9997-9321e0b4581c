package com.ruoyi.file.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.file.config.SysFileConfig;
import com.ruoyi.file.service.ISysFileService;
import com.ruoyi.system.api.domain.SysFile;
import com.wanhe.common.ocr.OcrService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 文件请求处理
 *
 * <AUTHOR>
 */
@RestController
public class SysFileController {

    @Resource
    public SysFileConfig sysFileConfig;
    @Resource
    public OcrService ocrService;
    @Resource
    private Map<String, ISysFileService> sysFileServiceMap;

    /**
     * 文件上传请求
     */
    @PostMapping("/upload")
    public R<SysFile> upload(MultipartFile file) throws Exception {
        // 上传并返回访问地址
        ISysFileService sysFileService = sysFileServiceMap.get(sysFileConfig.getSysFileType());
        String url = sysFileService.uploadFile(file, sysFileConfig.getCompression());
        SysFile sysFile = new SysFile();
        sysFile.setName(file.getOriginalFilename());
        sysFile.setUrl(url);
        return R.ok(sysFile);
    }

    /**
     * 识别条形码
     */
    @PostMapping("/upload/barcode")
    public R<List<String>> barcode(MultipartFile file) throws IOException {
        return R.ok(ocrService.barCode(file));
    }

}