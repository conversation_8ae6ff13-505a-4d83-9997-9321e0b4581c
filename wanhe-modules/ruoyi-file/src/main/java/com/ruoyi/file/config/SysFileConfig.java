package com.ruoyi.file.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * Minio 配置信息
 *
 * <AUTHOR>
 */
@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "sys.file")
public class SysFileConfig {
    //应用保存类型
    public String sysFileType;
    //压缩倍率
    public Float compression;

}
