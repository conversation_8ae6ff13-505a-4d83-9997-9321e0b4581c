# Spring
spring:
  cloud:
    nacos:
      username: nacos
      password: wa<PERSON><PERSON>@123
      discovery:
        # 服务注册地址
        server-addr: 127.0.0.1:8848
        # 命名空间
        namespace: public
      config:
        # 配置中心地址
        server-addr: 127.0.0.1:8848
        # 命名空间
        namespace: public
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
