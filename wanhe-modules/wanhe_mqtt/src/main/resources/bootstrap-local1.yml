spring:
  cloud:
    nacos:
      username: nacos
      password: wa<PERSON><PERSON>@123
      discovery:
        # 服务注册地址
        server-addr: 152.136.150.248:8848
        # 命名空间
        namespace: f201b8d5-2edd-4645-ae26-ff14b13093da
      config:
        # 配置中心地址
        server-addr: 152.136.150.248:8848
        # 命名空间
        namespace: f201b8d5-2edd-4645-ae26-ff14b13093da
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
