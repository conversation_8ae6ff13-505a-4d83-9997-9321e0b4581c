package com.wh.mqtt.mqtt;

import com.ruoyi.system.api.domain.PublishDTO;
import com.wh.mqtt.mqtt.exception.MqttServiceException;
import com.wh.mqtt.mqtt.util.Aes;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;


/**
 * 发布服务mqtt客户端
 */
@Slf4j
@Component
@RefreshScope
public class PubMqttClient {

    @Resource
    private MqttClientConfig mqttConfig;

    @Value("${spring.profiles.active}")
    private String active;

    @Value("${dataAes}")
    private boolean dataAes;

    @Resource(name = "pubMqttCallBack")
    private PubMqttCallBack mqttCallBack;
    /**
     * 连接配置
     */
    private MqttConnectOptions options;
    /**
     * MQTT异步客户端
     */
    private MqttAsyncClient client;
    /**
     * 是否连接标记
     */
    private boolean isConnected = false;

    @PostConstruct
    public void initClient() {
        initialize();
    }

    /**
     * 启动MQTT客户端
     */
    public synchronized void initialize() {
        try {
            setOptions();
            createClient();
            while (!client.isConnected()) {
                IMqttToken token = client.connect(options);
                if (token != null && token.isComplete()) {
                    log.debug("=>内部MQTT客户端启动成功");
                    this.isConnected = true;
                    break;
                }
                log.debug("=>内部mqtt客户端连接中...");
                Thread.sleep(20000);
            }
        } catch (MqttException ex) {
            log.error("=>MQTT客户端初始化异常", ex);
        } catch (Exception e) {
            log.error("=>连接MQTT服务器异常", e);
            this.isConnected = false;
        }

    }

    public boolean isConnected() {
        return this.isConnected;
    }

    private void createClient() {
        try {
            if (client == null) {
                /*host为主机名，clientId是连接MQTT的客户端ID*/
                client = new MqttAsyncClient(mqttConfig.getHostUrl(), getClientId(), new MemoryPersistence());
                //设置回调函数
                client.setCallback(mqttCallBack);
                mqttCallBack.setClient(client);
                mqttCallBack.setOptions(this.options);
                mqttCallBack.setEnabled(mqttConfig.getEnabled());
            }
        } catch (Exception e) {
            log.error("=>mqtt客户端创建错误");
        }
    }

    /**
     * 设置连接属性
     */
    private void setOptions() {
        options = new MqttConnectOptions();
        options.setConnectionTimeout(mqttConfig.getTimeout());
        options.setKeepAliveInterval(mqttConfig.getKeepalive());
        options.setUserName(mqttConfig.getUsername());
        options.setPassword(mqttConfig.getPassword().toCharArray());
        //设置自动重新连接
        options.setAutomaticReconnect(true);
            /*设置为false，断开连接，不清除session，重连后还是原来的session
              保留订阅的主题，能接收离线期间的消息*/
        options.setCleanSession(true);
    }

    /**
     * 断开与mqtt的连接
     */
    public synchronized void disconnect() {
        //判断客户端是否null 是否连接
        if (client != null && client.isConnected()) {
            try {
                IMqttToken token = client.disconnect();
                token.waitForCompletion();
            } catch (MqttException e) {
                log.error("=>断开mqtt连接发生错误 message={}", e.getMessage());
                throw new MqttServiceException("断开mqtt连接发生错误" + e.getMessage());
            }
        }
        client = null;
    }

    /**
     * 重新连接MQTT
     */
    public synchronized void refresh() {
        disconnect();
        initialize();
    }

    /**
     * 拼接客户端id
     */
    public final String getClientId() {
        return "server-" + active + "-" + System.currentTimeMillis();
    }

    /**
     * 发布
     */
    public Boolean publish(PublishDTO dto) {
        log.info("发布主题{},发布消息{}", dto.getTopic(), dto.getPushMessage());
        MqttMessage message = new MqttMessage();
        message.setQos(dto.getQos());
        message.setRetained(dto.getRetained());
        message.setPayload(Aes.encrypt(dataAes, dto.getPushMessage()).getBytes());
        try {
            IMqttDeliveryToken token = client.publish(dto.getTopic(), message);
            token.waitForCompletion();
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("发布主题失败", e);
            return Boolean.FALSE;
        }
    }

}
