package com.wh.mqtt.mqtt.util;

import cn.hutool.core.util.StrUtil;
import com.wh.mqtt.mqtt.MqttClientConfig;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * topic工具类
 *
 * <AUTHOR>
 * @date 2022/9/15 16:49
 */
@Slf4j
@Component
public class TopicsUtils {

    @Resource
    private MqttClientConfig config;

    /**
     * 获取所有可订阅的主题
     *
     * @return 订阅主题列表
     */
    public TopicsPost getAllPost() {
        List<Integer> qos = new ArrayList<>();
        List<String> topics = new ArrayList<>();
        TopicsPost post = new TopicsPost();
        String topicGroup = StrUtil.isNotBlank(config.getTopicGroup()) ? config.getTopicGroup() : "";
        for (TopicType topicType : TopicType.values()) {
            if (topicType.getType() == 0) {
                //String topic = this.buildTopic(0L, null, topicType);
                topics.add(topicGroup + topicType.getTopicSuffix());
                qos.add(1);
            }
        }
        post.setTopics(topics.toArray(new String[0]));
        int[] ints = Arrays.stream(qos.toArray(new Integer[0])).mapToInt(Integer::valueOf).toArray();
        post.setQos(ints);
        return post;
    }

    @Getter
    @AllArgsConstructor
    public enum TopicType {

        /*
          @param     type  0:标记是订阅主题  1:标记是发布属性
         * @param     order 排序
         * @param     topicSuffix topic后缀
         * @param     msg  描述信息
         */

        /*** 通用设备上报主题（平台订阅） ***/
        INV_TOPIC(0, 1, "+/Inv/+/+", "微逆直连主题"),
        EMU_TOPIC(0, 1, "+/Emu/+/+", "EMU直连主题");
        private final Integer type;
        private final Integer order;
        private final String topicSuffix;
        private final String msg;

    }
}
