package com.wh.mqtt.mqtt;

import com.wh.mqtt.mqtt.util.Aes;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RefreshScope
public class MessageService {

    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;
    @Value("${kafka.topic}")
    private String kafkaTopic;
    @Value("${dataAes}")
    private boolean dataAes;

    /**
     * mqtt 消费消息
     *
     * @param topicName   主题
     * @param mqttMessage 消息正文
     */
    @Async("handMqttMessageThreadPoolTaskExecutor")
    public void handMessageData(String topicName, MqttMessage mqttMessage) {
        String message = new String(mqttMessage.getPayload());
        log.info("接收到数据: topic:{},Qos:{},消息内容:{}", topicName, mqttMessage.getQos(), message);
        try {
            kafkaTemplate.send(kafkaTopic, topicName, Aes.decrypt(dataAes, message));
        } catch (Exception e) {
            log.error("mqtt接收到数据发送kafka失败", e);
        }
    }

}
