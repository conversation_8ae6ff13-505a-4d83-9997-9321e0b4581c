package com.wh.mqtt.mqtt;


import com.wh.mqtt.mqtt.util.TopicsPost;
import com.wh.mqtt.mqtt.util.TopicsUtils;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * mqtt客户端回调
 */
@Slf4j
@Component
@Data
@NoArgsConstructor
public class PubMqttCallBack implements MqttCallbackExtended {


    private Boolean enabled;
    /**
     * mqtt客户端
     */
    private MqttAsyncClient client;
    /**
     * 创建客户端参数
     */
    private MqttConnectOptions options;

    @Resource
    private TopicsUtils topicsUtils;
    @Resource
    private MessageService messageService;

    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;
    @Value("${kafka.topic}")
    private String kafkaTopic;

    public PubMqttCallBack(MqttAsyncClient client, MqttConnectOptions options, Boolean enabled) {
        this.client = client;
        this.options = options;
        this.enabled = enabled;
    }

    /**
     * mqtt客户端连接
     *
     * @param cause 错误
     */
    @Override
    public void connectionLost(Throwable cause) {

        // 连接丢失后，一般在这里面进行重连
        log.debug("=>mqtt 连接丢失", cause);
        int count = 1;
        // int sleepTime = 0;
        boolean willConnect = true;
        while (willConnect) {
            try {
                Thread.sleep(1000);
                log.debug("=>连接[{}]断开，尝试重连第{}次", this.client.getServerURI(), count++);
                this.client.connect(this.options);
                log.debug("=>重连成功");
                willConnect = false;
            } catch (Exception e) {
                log.error("=>重连异常", e);
            }
        }
    }

    /**
     * 客户端消费消息
     *
     * @param topic   主题
     * @param message 消息
     */
    @Override
    public void messageArrived(String topic, MqttMessage message) {
        // subscribe后得到的消息会执行到这里面
        if (message.getPayload().length == 0) {
            return;
        }
        messageService.handMessageData(topic, message);
    }

    @Override
    public void deliveryComplete(IMqttDeliveryToken token) {
        log.info("deliveryComplete=>消息投递成功");
    }


    /**
     * 监听mqtt连接消息
     */
    @Override
    public void connectComplete(boolean reconnect, String serverURI) {
        log.info("MQTT客户端已经连接!");
        //连接后订阅, enable为false表示使用emq
        try {
            TopicsPost allPost = topicsUtils.getAllPost();
            client.subscribe(allPost.getTopics(), allPost.getQos());
            log.info("mqtt监控主题,{}", Arrays.asList(allPost.getTopics()));
        } catch (MqttException e) {
            log.error("=>订阅主题失败 error={}", e.getMessage());
        }
    }
}
