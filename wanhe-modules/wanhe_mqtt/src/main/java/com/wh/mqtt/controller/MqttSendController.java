package com.wh.mqtt.controller;

import com.ruoyi.system.api.domain.PublishDTO;
import com.wh.mqtt.mqtt.PubMqttClient;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

/**
 * mqtt消息发送
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@RestController
@RequestMapping("/mqtt/send")
public class MqttSendController {
    
    @Resource
    private PubMqttClient pubMqttClient;

    /**
     * 发布
     */
    @PostMapping(value = "/publish")
    public Boolean postPublish(@RequestBody PublishDTO publishDto) {
        return pubMqttClient.publish(publishDto);
    }

}
