package com.wh.mqtt.mqtt.util;

import com.ruoyi.common.core.exception.ServiceException;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * <AUTHOR>
 * @since 2025/5/7
 */
public class Aes {

    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/ECB/PKCS5Padding";
    private static final byte[] KEY = "whyn2025@!123456".getBytes(StandardCharsets.UTF_8);

    /**
     * 加密
     *
     * @param plaintext 明文
     * @return {@link String }
     */
    public static String encrypt(boolean aes, String plaintext) {
        if (aes) {
            try {
                SecretKeySpec secretKey = new SecretKeySpec(KEY, ALGORITHM);
                Cipher cipher = Cipher.getInstance(TRANSFORMATION);
                cipher.init(Cipher.ENCRYPT_MODE, secretKey);
                byte[] encryptedBytes = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));
                return Base64.getEncoder().encodeToString(encryptedBytes);
            } catch (Exception e) {
                throw new ServiceException("加密失败");
            }
        }
        return plaintext;
    }

    /**
     * 解密
     *
     * @param ciphertext 密文
     * @return null 解密失败
     */
    public static String decrypt(boolean aes, String ciphertext) {
        if (aes) {
            try {
                SecretKeySpec secretKey = new SecretKeySpec(KEY, ALGORITHM);
                Cipher cipher = Cipher.getInstance(TRANSFORMATION);
                cipher.init(Cipher.DECRYPT_MODE, secretKey);
                byte[] decodedBytes = Base64.getDecoder().decode(ciphertext);
                byte[] decryptedBytes = cipher.doFinal(decodedBytes);
                return new String(decryptedBytes, StandardCharsets.UTF_8);
            } catch (Exception e) {
                return ciphertext;
            }
        }
        return ciphertext;
    }

}
