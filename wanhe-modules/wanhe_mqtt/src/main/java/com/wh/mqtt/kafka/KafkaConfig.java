package com.wh.mqtt.kafka;//package config;


import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.ListTopicsResult;
import org.apache.kafka.clients.admin.NewTopic;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.UniformStickyPartitioner;
import org.apache.kafka.common.config.TopicConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.*;

/**
 * <AUTHOR>
 * 2024-08-13 15:21
 * 配置kafka的生产消费java config
 */
@EnableKafka
@Configuration
public class KafkaConfig implements InitializingBean {

    private static final Logger log = LoggerFactory.getLogger(KafkaConfig.class);
    @Value("#{'${kafka.bootstrap.servers}'.split(',')}")
    private List<String> bootstrapServers;
    @Value("${kafka.group.id}")
    private String groupId;
    @Value("#{'${kafka.topic}'.split(',')}")
    private List<String> topicList;
    @Value("#{'${kafka.partition-number}'.split(',')}")
    private int partitionNumber;

    @Bean
    public KafkaTemplate<String, String> kafkaTemplate() {
        return new KafkaTemplate<>(producerFactory());
    }

    public ProducerFactory<String, String> producerFactory() {
        return new DefaultKafkaProducerFactory<>(producerConfigs());
    }

    public Map<String, Object> producerConfigs() {
        Map<String, Object> props = new HashMap<>();
        //设置接入点，请通过控制台获取对应Topic的接入点
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        //设置客户端内部重试次数
        props.put(ProducerConfig.RETRIES_CONFIG, 0);
        //请求的最长等待时间
        props.put(ProducerConfig.MAX_BLOCK_MS_CONFIG, 30 * 1000);
        //Kafka消息的序列化方式
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        //设置客户端内部重试间隔
        props.put(ProducerConfig.RECONNECT_BACKOFF_MS_CONFIG, 3000);
        // 开启幂等
        props.put(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG, false);
        //粘性分配策略
        props.put(ProducerConfig.PARTITIONER_CLASS_CONFIG, UniformStickyPartitioner.class);
        props.put(ProducerConfig.ACKS_CONFIG, "1");
        return props;
    }

    @Override
    public void afterPropertiesSet() {
        try (AdminClient admin = AdminClient.create(producerConfigs())) {
            ListTopicsResult listTopicsResult = admin.listTopics();
            Set<String> names = listTopicsResult.names().get();
            topicList.forEach(topicName -> {
                //如果kafka中没有指定的主题,则手动创建,默认创建只有一个分区
                if (!names.contains(topicName)) {
                    NewTopic newTopic = new NewTopic(topicName, partitionNumber, (short) 1);
                    //主题清除策略
                    newTopic.configs(Collections.singletonMap(TopicConfig.CLEANUP_POLICY_CONFIG, "delete"));
                    admin.createTopics(Collections.singletonList(newTopic));
                }
            });
        } catch (Exception e) {
            log.error("创建kafka主题失败", e);
        }
    }

}