package com.wh.mqtt.mqtt.util;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池配置
 *
 * <AUTHOR>
 **/
@Configuration
public class ThreadPoolConfig {

    /**
     * 处理mqtt消息线程池
     */
    @Bean(name = "handMqttMessageThreadPoolTaskExecutor")
    public ThreadPoolTaskExecutor handMqttMessageThreadPoolTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        int cpuNum = Runtime.getRuntime().availableProcessors();
        // 核心线程池大小
        executor.setCorePoolSize(cpuNum);
        // 最大可创建的线程数
        executor.setMaxPoolSize(cpuNum * 2);
        // 队列最大长度
        executor.setQueueCapacity(5000);
        // 线程池维护线程所允许的空闲时间
        executor.setKeepAliveSeconds(3000);
        // 线程池对拒绝任务(无线程可用)的处理策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }


}
