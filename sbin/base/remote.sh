#!/bin/sh
case $1 in
  gateway)
    PROJECT_PATH="/www/wwwroot/jar/wanhe-gateway.jar"
  ;;
  auth)
    PROJECT_PATH="/www/wwwroot/jar/wanhe-auth.jar"
  ;;
  file)
    PROJECT_PATH="/www/wwwroot/jar/wanhe-modules-file.jar"
  ;;
  system)
    PROJECT_PATH="/www/wwwroot/jar/wanhe-modules-system.jar"
  ;;
  job)
    PROJECT_PATH="/www/wwwroot/wanhe/wanhe-modules-job.jar"
  ;;
  mqtt)
    PROJECT_PATH="/www/wwwroot/wanhe/wanhe-mqtt.jar"
  ;;
  business)
    PROJECT_PATH="/www/wwwroot/wanhe/wanhe_business.jar"
  ;;
  data)
    PROJECT_PATH="/www/wwwroot/wanhe/wanhe_biz_data.jar"
  ;;
esac
echo $PROJECT_PATH
start()
{
  PROJECT_PID=$(pgrep -f "$PROJECT_PATH")
  if [ -z "$PROJECT_PID" ]; then
    JAVA="/www/server/java/jdk-17.0.8/bin/java"
    JAVA_OPT="$JAVA_OPT -jar -Dfile.encoding=utf-8 -Xms$2 -Xmx$2 $PROJECT_PATH"
    JAVA_OPT="$JAVA_OPT --server.port=$1"
    JAVA_OPT="$JAVA_OPT --spring.profiles.active=dev"
    echo "nohup $JAVA $JAVA_OPT > /dev/null 2>&1 &"
    nohup "$JAVA" $JAVA_OPT > /dev/null 2>&1 &
  else
    echo "$PROJECT_NAME 已经启动"
  fi
}
stop()
{
  PROJECT_PID=$(pgrep -f "$PROJECT_PATH")
  if [ -z "$PROJECT_PID" ]; then
    echo "$PROJECT_PATH 没有启动"
    return
  fi
  echo "$PROJECT_PATH 运行中  pid: $PROJECT_PID"
  echo "kill -15 $PROJECT_PID"
  kill -15 "$PROJECT_PID"
  #给30秒自动关闭时间
  for ((i=1; i<=10; i++)); do
    sleep 1
    PROJECT_PID=$(pgrep -f "$PROJECT_PATH")
    if [ -n "$PROJECT_PID" ]; then
      echo "暂停中...$i"
    else
      break;
    fi
  done
  #30秒还未关闭就强制结束进程
  if [ -n "$PROJECT_PID" ]; then
    echo "$PROJECT_PATH 运行中  pid: $PROJECT_PID"
    echo "kill -9 $PROJECT_PID"
    kill -9 "$PROJECT_PID"
  fi
  #最后输出是否关闭
  PROJECT_PID=$(pgrep -f "$PROJECT_PATH")
  if [ -n "$PROJECT_PID" ]; then
    echo "$PROJECT_PATH 运行中"
  else
    echo "$PROJECT_PATH 已关闭"
  fi
}
stop
# start $2 $3