{"group": ["系统参数", "环路控制1", "环路控制2", "环路控制3", "故障保护", "示波器", "故障录波", "PV采样校准", "AC采样校准", "温度采样校准", "时间版本类型", "存储参数", "PLC", "故障保护门限", "故障记录", "IV曲线", "降额控制P", "降额控制Q", "电量功率计算", "mppt参数", "burst参数", "装备测试", "测试", "自定义故障保护门限", "中国故障保护门限", "欧洲故障保护门限"], "AC采样校准": [{"paraId": "380", "paraNameCn": "AC电压实际值", "paraNameEn": "Ac Vol Real", "paraVarName": "AdPara.acVol.real", "paraMacroName": "ID_AD_AC_VOL_REAL", "paraDescription": "AC电压实际值", "paraUnit": "V", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "300", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "381", "paraNameCn": "AC电压滤波值", "paraNameEn": "Ac Vol Filter", "paraVarName": "AdPara.acVol.filter", "paraMacroName": "ID_AD_AC_VOL_FILTER", "paraDescription": "AC电压滤波值", "paraUnit": "V", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "300", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "382", "paraNameCn": "AC电压采样值", "paraNameEn": "Ac Vol reg", "paraVarName": "AdPara.acVol.reg", "paraMacroName": "ID_AD_AC_VOL_REG", "paraDescription": "AC电压采样值", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "4096", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I16", "saveAttr": "N"}, {"paraId": "383", "paraNameCn": "AC电压系数k", "paraNameEn": "Ac Vol k", "paraVarName": "AdPara.acVol.k", "paraMacroName": "ID_AD_AC_VOL_K", "paraDescription": "AC电压系数k", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "30000", "minValue": "-30000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "384", "paraNameCn": "AC电压偏置b", "paraNameEn": "Ac Vol b", "paraVarName": "AdPara.acVol.b", "paraMacroName": "ID_AD_AC_VOL_B", "paraDescription": "AC电压偏置b", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "30000", "minValue": "-30000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "385", "paraNameCn": "AC电压校准系数k", "paraNameEn": "Ac Vol adj k", "paraVarName": "AdPara.acVol.adj_k", "paraMacroName": "ID_AD_AC_VOL_ADJ_K", "paraDescription": "AC电压校准系数k", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "30000", "minValue": "-30000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "386", "paraNameCn": "AC电压校准偏置b", "paraNameEn": "Ac Vol adj b", "paraVarName": "AdPara.acVol.adj_b", "paraMacroName": "ID_AD_AC_VOL_ADJ_B", "paraDescription": "AC电压校准偏置b", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "30000", "minValue": "-30000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "387", "paraNameCn": "AC电流实际值", "paraNameEn": "Ac Curr Real", "paraVarName": "AdPara.acCurr.real", "paraMacroName": "ID_AD_AC_CURR_REAL", "paraDescription": "AC电流实际值", "paraUnit": "A", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "10", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "388", "paraNameCn": "AC电流滤波值", "paraNameEn": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "paraVarName": "AdPara.acCurr.filter", "paraMacroName": "ID_AD_AC_CURR_FILTER", "paraDescription": "AC电流滤波值", "paraUnit": "A", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "10", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "389", "paraNameCn": "AC电流采样值", "paraNameEn": "<PERSON><PERSON><PERSON> reg", "paraVarName": "AdPara.acCurr.reg", "paraMacroName": "ID_AD_AC_CURR_REG", "paraDescription": "AC电流采样值", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "4096", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I16", "saveAttr": "N"}, {"paraId": "390", "paraNameCn": "AC电流系数k", "paraNameEn": "Ac Curr k", "paraVarName": "AdPara.acCurr.k", "paraMacroName": "ID_AD_AC_CURR_K", "paraDescription": "AC电流系数k", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "30000", "minValue": "-30000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "391", "paraNameCn": "AC电流偏置b", "paraNameEn": "<PERSON><PERSON> b", "paraVarName": "AdPara.acCurr.b", "paraMacroName": "ID_AD_AC_CURR_B", "paraDescription": "AC电流偏置b", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "30000", "minValue": "-30000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "392", "paraNameCn": "AC电流校准系数k", "paraNameEn": "<PERSON>c Curr adj k", "paraVarName": "AdPara.acCurr.adj_k", "paraMacroName": "ID_AD_AC_CURR_ADJ_K", "paraDescription": "AC电流校准系数k", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "30000", "minValue": "-30000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "393", "paraNameCn": "AC电流校准偏置b", "paraNameEn": "<PERSON><PERSON> adj b", "paraVarName": "AdPara.acCurr.adj_b", "paraMacroName": "ID_AD_AC_CURR_ADJ_B", "paraDescription": "AC电流校准偏置b", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "30000", "minValue": "-30000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "394", "paraNameCn": "AC电流(CT)实际值", "paraNameEn": "Ac CT Curr Real", "paraVarName": "AdPara.ctCurr.real", "paraMacroName": "ID_AD_CT_CURR_REAL", "paraDescription": "AC电流(CT)实际值", "paraUnit": "A", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "395", "paraNameCn": "AC电流(CT)滤波值", "paraNameEn": "Ac CT Curr Filter", "paraVarName": "AdPara.ctCurr.filter", "paraMacroName": "ID_AD_CT_CURR_FILTER", "paraDescription": "AC电流(CT)滤波值", "paraUnit": "A", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "396", "paraNameCn": "AC电流(CT)采样值", "paraNameEn": "Ac CT Curr reg", "paraVarName": "AdPara.ctCurr.reg", "paraMacroName": "ID_AD_CT_CURR_REG", "paraDescription": "AC电流(CT)采样值", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "4096", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I16", "saveAttr": "N"}, {"paraId": "397", "paraNameCn": "AC电流(CT)系数k", "paraNameEn": "Ac CT Curr k", "paraVarName": "AdPara.ctCurr.k", "paraMacroName": "ID_AD_CT_CURR_K", "paraDescription": "AC电流(CT)系数k", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "30000", "minValue": "-30000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "398", "paraNameCn": "AC电流(CT)偏置b", "paraNameEn": "Ac CT Curr b", "paraVarName": "AdPara.ctCurr.b", "paraMacroName": "ID_AD_CT_CURR_B", "paraDescription": "AC电流(CT)偏置b", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "30000", "minValue": "-30000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "399", "paraNameCn": "AC电流(CT)校准系数k", "paraNameEn": "Ac CT Curr adj k", "paraVarName": "AdPara.ctCurr.adj_k", "paraMacroName": "ID_AD_CT_CURR_ADJ_K", "paraDescription": "AC电流(CT)校准系数k", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "30000", "minValue": "-30000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "400", "paraNameCn": "AC电流(CT)校准偏置b", "paraNameEn": "Ac CT Curr adj b", "paraVarName": "AdPara.ctCurr.adj_b", "paraMacroName": "ID_AD_CT_CURR_ADJ_B", "paraDescription": "AC电流(CT)校准偏置b", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "30000", "minValue": "-30000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "401", "paraNameCn": "AC电压有效值", "paraNameEn": "Ac Vol Rms", "paraVarName": "AcVolRmsPara.rms", "paraMacroName": "ID_AD_AC_VOL_RMS", "paraDescription": "AC电压有效值", "paraUnit": "V", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "300", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "402", "paraNameCn": "AC电流有效值", "paraNameEn": "Ac Curr Rms", "paraVarName": "AcCurrRmsPara.rms", "paraMacroName": "ID_AD_AC_CURR_RMS", "paraDescription": "AC电流有效值", "paraUnit": "A", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "300", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}], "IV曲线": [{"paraId": "740", "paraNameCn": "运行状态", "paraNameEn": "IV Curve State", "paraVarName": "IvCurve.state", "paraMacroName": "ID_IVCURVE_STATUS", "paraDescription": "0:停止 1:采样 2:等待", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "2", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "741", "paraNameCn": "运行命令", "paraNameEn": "IV Curve Cmd", "paraVarName": "IvCurve.cmd", "paraMacroName": "ID_IVCURVE_CMD", "paraDescription": "0/停止  1/运行", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "742", "paraNameCn": "PV端口", "paraNameEn": "IV Curve PvPort", "paraVarName": "IvCurve.pvPort", "paraMacroName": "ID_IVCURVE_PV_PORT", "paraDescription": "PV 端口  0为第一端口", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "743", "paraNameCn": "步进", "paraNameEn": "IV Curve Stepping", "paraVarName": "IvCurve.stepping", "paraMacroName": "ID_IVCURVE_STEPPING", "paraDescription": "采样步进值", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "5", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}], "PLC": [{"paraId": "520", "paraNameCn": "PLC 操作命令", "paraNameEn": "PlcCtrlWord", "paraVarName": "PlcCtrlWord.all", "paraMacroName": "ID_PLC_COMMAND", "paraDescription": "B0:读MAC B1:写MAC B2:读功率等级 B3:写功率等级 \nB4:读PA B5:写PA B6:读版本 B7:保留\nB8:保留 B9:保留 B10:保留 B11:保留 \nB12:保留 B13:保留 B14:保留 B15:保留", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_BIT16", "saveAttr": "N"}, {"paraId": "521", "paraNameCn": "PLC 通信错误码", "paraNameEn": "PlcCommErrWord", "paraVarName": "PlcCommErrWord.all", "paraMacroName": "ID_PLC_COMM_ERROR", "paraDescription": "B0:读MAC B1:写MAC B2:读功率等级 B3:写功率等级 \nB4:读PA B5:写PA B6:读版本 B7:保留\nB8:保留 B9:保留 B10:保留 B11:保留 \nB12:保留 B13:保留 B14:保留 B15:保留", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_BIT16", "saveAttr": "N"}, {"paraId": "522", "paraNameCn": "PLC 写MAC状态", "paraNameEn": "Plc WriteMacState", "paraVarName": "PlcMasgPara.writeMacState", "paraMacroName": "ID_PLC_WRITE_MAC_STATE", "paraDescription": "写MAC状态 1 失败 0成功", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "2", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "523", "paraNameCn": "PLC 读功率等级值", "paraNameEn": "Plc ReadPowerLevel", "paraVarName": "PlcMasgPara.readPowerLevel", "paraMacroName": "ID_PLC_READ_POWER", "paraDescription": "读取 PLC功率等级值  1(min)-16(max)", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "16", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "524", "paraNameCn": "PLC 写功率等级值", "paraNameEn": "Plc WritePowerLevel", "paraVarName": "PlcMasgPara.writePowerLevel", "paraMacroName": "ID_PLC_WRITE_POWER", "paraDescription": "设置 PLC功率等级值  1(min)-16(max)", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "16", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "525", "paraNameCn": "PLC 读PA模式值", "paraNameEn": "Plc ReadPaMode", "paraVarName": "PlcMasgPara.readPaMode", "paraMacroName": "ID_PLC_READ_PA_MODE", "paraDescription": "1:禁止  2：使能", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "2", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "526", "paraNameCn": "PLC 写PA模式值", "paraNameEn": "Plc WritePaMode", "paraVarName": "PlcMasgPara.writePaMode", "paraMacroName": "ID_PLC_WRITE_PA_MODE", "paraDescription": "1:禁止  2：使能", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "2", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "527", "paraNameCn": "PLC 版本", "paraNameEn": "PlcMasgPara PlcVer", "paraVarName": "PlcMasgPara.plcVer", "paraMacroName": "ID_PLC_READ_PLC_VER", "paraDescription": "PLC版本:年（bit31-24）月（bit23-20）日（bit19-16）版本（bit15-0）", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "65536", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U32", "saveAttr": "N"}], "PV采样校准": [{"paraId": "250", "paraNameCn": "PV电压1实际值", "paraNameEn": "PV Vol Real", "paraVarName": "AdPara.pvVol_1.real", "paraMacroName": "ID_AD_PV_VOL_1_REAL", "paraDescription": "PV电压1实际值", "paraUnit": "V", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "251", "paraNameCn": "PV电压1滤波值", "paraNameEn": "PV Vol Filter", "paraVarName": "AdPara.pvVol_1.filter", "paraMacroName": "ID_AD_PV_VOL_1_FILTER", "paraDescription": "PV电压1滤波值", "paraUnit": "V", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "252", "paraNameCn": "PV电压1采样值", "paraNameEn": "PV Vol reg", "paraVarName": "AdPara.pvVol_1.reg", "paraMacroName": "ID_AD_PV_VOL_1_REG", "paraDescription": "PV电压1采样值", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "4096", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I16", "saveAttr": "N"}, {"paraId": "253", "paraNameCn": "PV电压1系数k", "paraNameEn": "PV Vol k", "paraVarName": "AdPara.pvVol_1.k", "paraMacroName": "ID_AD_PV_VOL_1_K", "paraDescription": "PV电压1系数k", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "30000", "minValue": "-30000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "254", "paraNameCn": "PV电压1偏置b", "paraNameEn": "PV Vol b", "paraVarName": "AdPara.pvVol_1.b", "paraMacroName": "ID_AD_PV_VOL_1_B", "paraDescription": "PV电压1偏置b", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "30000", "minValue": "-30000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "255", "paraNameCn": "PV电压1校准系数k", "paraNameEn": "PV Vol adj k", "paraVarName": "AdPara.pvVol_1.adj_k", "paraMacroName": "ID_AD_PV_VOL_1_ADJ_K", "paraDescription": "PV电压1校准系数k", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "30000", "minValue": "-30000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "256", "paraNameCn": "PV电压1校准偏置b", "paraNameEn": "PV Vol adj b", "paraVarName": "AdPara.pvVol_1.adj_b", "paraMacroName": "ID_AD_PV_VOL_1_ADJ_B", "paraDescription": "PV电压1校准偏置b", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "30000", "minValue": "-30000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "257", "paraNameCn": "PV电流1实际值", "paraNameEn": "PV Curr Real", "paraVarName": "AdPara.pvCurr_1.real", "paraMacroName": "ID_AD_PV_CURR_1_REAL", "paraDescription": "PV电流1实际值", "paraUnit": "A", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "50", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "258", "paraNameCn": "PV电流1滤波值", "paraNameEn": "<PERSON><PERSON>urr <PERSON>lter", "paraVarName": "AdPara.pvCurr_1.filter", "paraMacroName": "ID_AD_PV_CURR_1_FILTER", "paraDescription": "PV电流1滤波值", "paraUnit": "A", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "50", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "259", "paraNameCn": "PV电流1采样值", "paraNameEn": "PV <PERSON>urr reg", "paraVarName": "AdPara.pvCurr_1.reg", "paraMacroName": "ID_AD_PV_CURR_1_REG", "paraDescription": "PV电流1采样值", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "4096", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I16", "saveAttr": "N"}, {"paraId": "260", "paraNameCn": "PV电流1系数k", "paraNameEn": "PV Curr k", "paraVarName": "AdPara.pvCurr_1.k", "paraMacroName": "ID_AD_PV_CURR_1_K", "paraDescription": "PV电流1系数k", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "30000", "minValue": "-30000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "261", "paraNameCn": "PV电流1偏置b", "paraNameEn": "<PERSON><PERSON> b", "paraVarName": "AdPara.pvCurr_1.b", "paraMacroName": "ID_AD_PV_CURR_1_B", "paraDescription": "PV电流1偏置b", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "30000", "minValue": "-30000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "262", "paraNameCn": "PV电流1校准系数k", "paraNameEn": "PV Curr adj k", "paraVarName": "AdPara.pvCurr_1.adj_k", "paraMacroName": "ID_AD_PV_CURR_1_ADJ_K", "paraDescription": "PV电流1校准系数k", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "30000", "minValue": "-30000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "263", "paraNameCn": "PV电流1校准偏置b", "paraNameEn": "<PERSON><PERSON> adj b", "paraVarName": "AdPara.pvCurr_1.adj_b", "paraMacroName": "ID_AD_PV_CURR_1_ADJ_B", "paraDescription": "PV电流1校准偏置b", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "30000", "minValue": "-30000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}], "burst参数": [{"paraId": "900", "paraNameCn": "Burst使能命令", "paraNameEn": "BurstPara cmd", "paraVarName": "BurstPara.cmd", "paraMacroName": "ID_BURST_CMD", "paraDescription": "Burst使能命令", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "Y"}, {"paraId": "901", "paraNameCn": "Burst功率门限", "paraNameEn": "BurstPara powerLimit", "paraVarName": "BurstPara.powerLimit", "paraMacroName": "ID_BURST_POWER_LIMIT", "paraDescription": "Burst功率门限", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "500", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "902", "paraNameCn": "Burst电流给定", "paraNameEn": "BurstPara iacSet", "paraVarName": "BurstPara.iacSet", "paraMacroName": "ID_BURST_I_SET", "paraDescription": "Burst电流给定", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "903", "paraNameCn": "Burst最大周期值", "paraNameEn": "BurstPara maxCnt", "paraVarName": "BurstPara.maxCnt", "paraMacroName": "ID_BURST_MAX_CND", "paraDescription": "Burst最大周期值", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "30000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I16", "saveAttr": "N"}, {"paraId": "904", "paraNameCn": "Burst当前周期计数", "paraNameEn": "BurstPara periodCnt", "paraVarName": "BurstPara.periodCnt", "paraMacroName": "ID_BURST_PERIOD_CNT", "paraDescription": "Burst当前周期计数", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "30000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I16", "saveAttr": "N"}, {"paraId": "905", "paraNameCn": "电流环Kp", "paraNameEn": "AcCurrPiPara Kp  ", "paraVarName": "AcCurrPiPara.Kp", "paraMacroName": "ID_PI_AC_CURR_KP", "paraDescription": "电流环Kp", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "906", "paraNameCn": "电流环Ki", "paraNameEn": "AcCurrPiPara Ki  ", "paraVarName": "AcCurrPiPara.Ki", "paraMacroName": "ID_PI_AC_CURR_KI", "paraDescription": "电流环Ki", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "907", "paraNameCn": "电流环Kd", "paraNameEn": "AcCurrPiPara Kd  ", "paraVarName": "AcCurrPiPara.Kd", "paraMacroName": "ID_PI_AC_CURR_KD", "paraDescription": "电流环Kd", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "908", "paraNameCn": "电流环OutMax", "paraNameEn": "AcCurrPiPara OutMax ", "paraVarName": "AcCurrPiPara.OutMax", "paraMacroName": "ID_PI_AC_CURR_MAX", "paraDescription": "电流环OutMax", "paraUnit": "A", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "909", "paraNameCn": "电流环OutMin", "paraNameEn": "AcCurrPiPara OutMin ", "paraVarName": "AcCurrPiPara.OutMin", "paraMacroName": "ID_PI_AC_CURR_MIN", "paraDescription": "电流环OutMin", "paraUnit": "A", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "910", "paraNameCn": "电流环Ref", "paraNameEn": "AcCurrPiPara Ref ", "paraVarName": "AcCurrPiPara.Ref", "paraMacroName": "ID_PI_AC_CURR_REF", "paraDescription": "电流环Ref", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "911", "paraNameCn": "电流环Fdb", "paraNameEn": "AcCurrPiPara Fdb ", "paraVarName": "AcCurrPiPara.Fdb", "paraMacroName": "ID_PI_AC_CURR_FDB", "paraDescription": "电流环Fdb", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "912", "paraNameCn": "电流环Err", "paraNameEn": "AcCurrPiPara Err ", "paraVarName": "AcCurrPiPara.Err", "paraMacroName": "ID_PI_AC_CURR_ERR", "paraDescription": "电流环Err", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "913", "paraNameCn": "电流环Up", "paraNameEn": "AcCurrPiPara Up  ", "paraVarName": "AcCurrPiPara.Up", "paraMacroName": "ID_PI_AC_CURR_UP", "paraDescription": "电流环Up", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "914", "paraNameCn": "电流环Ui", "paraNameEn": "AcCurrPiPara Ui  ", "paraVarName": "AcCurrPiPara.Ui", "paraMacroName": "ID_PI_AC_CURR_UI", "paraDescription": "电流环Ui", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "915", "paraNameCn": "电流环Ud", "paraNameEn": "AcCurrPiPara Ud  ", "paraVarName": "AcCurrPiPara.Ud", "paraMacroName": "ID_PI_AC_CURR_UD", "paraDescription": "电流环Ud", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "916", "paraNameCn": "电流环OutPreSat", "paraNameEn": "AcCurrPiPara OutPreSat ", "paraVarName": "AcCurrPiPara.OutPreSat", "paraMacroName": "ID_PI_AC_CURR_OUT_PRE", "paraDescription": "电流环OutPreSat", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "917", "paraNameCn": "电流环Out", "paraNameEn": "AcCurrPiPara Out    ", "paraVarName": "AcCurrPiPara.Out", "paraMacroName": "ID_PI_AC_CURR_OUT", "paraDescription": "电流环Out", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "918", "paraNameCn": "电流环SatErr", "paraNameEn": "AcCurrPiPara SatErr ", "paraVarName": "AcCurrPiPara.SatErr", "paraMacroName": "ID_PI_AC_CURR_SATERR", "paraDescription": "电流环SatErr", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "919", "paraNameCn": "电流环Up1", "paraNameEn": "AcCurrPiPara Up1    ", "paraVarName": "AcCurrPiPara.Up1", "paraMacroName": "ID_PI_AC_CURR_UP1", "paraDescription": "电流环Up1", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "920", "paraNameCn": "功率环Kp", "paraNameEn": "PowerPiPara Kp  ", "paraVarName": "PowerPiPara.Kp", "paraMacroName": "ID_PI_POWER_KP", "paraDescription": "功率环Kp", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "921", "paraNameCn": "功率环Ki", "paraNameEn": "PowerPiPara Ki  ", "paraVarName": "PowerPiPara.Ki", "paraMacroName": "ID_PI_POWER_KI", "paraDescription": "功率环Ki", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "922", "paraNameCn": "功率环Kd", "paraNameEn": "PowerPiPara Kd  ", "paraVarName": "PowerPiPara.Kd", "paraMacroName": "ID_PI_POWER_KD", "paraDescription": "功率环Kd", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "923", "paraNameCn": "功率环OutMax", "paraNameEn": "PowerPiPara OutMax ", "paraVarName": "PowerPiPara.OutMax", "paraMacroName": "ID_PI_POWER_MAX", "paraDescription": "功率环OutMax", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "60", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "924", "paraNameCn": "功率环OutMin", "paraNameEn": "PowerPiPara OutMin ", "paraVarName": "PowerPiPara.OutMin", "paraMacroName": "ID_PI_POWER_MIN", "paraDescription": "功率环OutMin", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "2", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "925", "paraNameCn": "功率环Ref", "paraNameEn": "PowerPiPara Ref ", "paraVarName": "PowerPiPara.Ref", "paraMacroName": "ID_PI_POWER_REF", "paraDescription": "功率环Ref", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "926", "paraNameCn": "功率环Fdb", "paraNameEn": "PowerPiPara Fdb ", "paraVarName": "PowerPiPara.Fdb", "paraMacroName": "ID_PI_POWER_FDB", "paraDescription": "功率环Fdb", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "927", "paraNameCn": "功率环Err", "paraNameEn": "PowerPiPara Err ", "paraVarName": "PowerPiPara.Err", "paraMacroName": "ID_PI_POWER_ERR", "paraDescription": "功率环Err", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "928", "paraNameCn": "功率环Up", "paraNameEn": "PowerPiPara Up  ", "paraVarName": "PowerPiPara.Up", "paraMacroName": "ID_PI_POWER_UP", "paraDescription": "功率环Up", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "929", "paraNameCn": "功率环Ui", "paraNameEn": "PowerPiPara Ui  ", "paraVarName": "PowerPiPara.Ui", "paraMacroName": "ID_PI_POWER_UI", "paraDescription": "功率环Ui", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "930", "paraNameCn": "功率环Ud", "paraNameEn": "PowerPiPara Ud  ", "paraVarName": "PowerPiPara.Ud", "paraMacroName": "ID_PI_POWER_UD", "paraDescription": "功率环Ud", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "931", "paraNameCn": "功率环OutPreSat", "paraNameEn": "PowerPiPara OutPreSat ", "paraVarName": "PowerPiPara.OutPreSat", "paraMacroName": "ID_PI_POWER_OUT_PRE", "paraDescription": "功率环OutPreSat", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "932", "paraNameCn": "功率环Out", "paraNameEn": "PowerPiPara Out    ", "paraVarName": "PowerPiPara.Out", "paraMacroName": "ID_PI_POWER_OUT", "paraDescription": "功率环Out", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "933", "paraNameCn": "功率环SatErr", "paraNameEn": "PowerPiPara SatErr ", "paraVarName": "PowerPiPara.SatErr", "paraMacroName": "ID_PI_POWER_SATERR", "paraDescription": "功率环SatErr", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "934", "paraNameCn": "功率环Up1", "paraNameEn": "PowerPiPara Up1    ", "paraVarName": "PowerPiPara.Up1", "paraMacroName": "ID_PI_POWER_UP1", "paraDescription": "功率环Up1", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}], "mppt参数": [{"paraId": "880", "paraNameCn": "mppt使能命令", "paraNameEn": "MpptPara cmd", "paraVarName": "MpptPara.cmd", "paraMacroName": "ID_MPPT_CMD", "paraDescription": "使能命令", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I8", "saveAttr": "Y"}, {"paraId": "881", "paraNameCn": "mppt状态", "paraNameEn": "MpptPara state", "paraVarName": "MpptPara.state", "paraMacroName": "ID_MPPT_STATE", "paraDescription": "0:爬升 1:mppt运行 2:降额", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I8", "saveAttr": "N"}, {"paraId": "882", "paraNameCn": "mppt方向", "paraNameEn": "<PERSON><PERSON>t<PERSON><PERSON> dir", "paraVarName": "MpptPara.dir", "paraMacroName": "ID_MPPT_DIR", "paraDescription": "1:左侧 -1:右侧", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "-1", "defaultValue": "0", "dataType": "TYPE_I8", "saveAttr": "N"}, {"paraId": "883", "paraNameCn": "mppt方向旧值", "paraNameEn": "MpptPara dirOld", "paraVarName": "MpptPara.dirOld", "paraMacroName": "ID_MPPT_DIR_OLD", "paraDescription": "1:左侧 -1:右侧", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "-1", "defaultValue": "0", "dataType": "TYPE_I8", "saveAttr": "N"}, {"paraId": "884", "paraNameCn": "mppt计时器", "paraNameEn": "MpptPara timer", "paraVarName": "MpptPara.timer", "paraMacroName": "ID_MPPT_TIMER", "paraDescription": "计时器", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "-1", "defaultValue": "0", "dataType": "TYPE_I16", "saveAttr": "N"}, {"paraId": "885", "paraNameCn": "mppt周期计数", "paraNameEn": "MpptPara periodCnt", "paraVarName": "MpptPara.periodCnt", "paraMacroName": "ID_MPPT_P_CNT", "paraDescription": "周期计数", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "-1", "defaultValue": "0", "dataType": "TYPE_I16", "saveAttr": "N"}, {"paraId": "886", "paraNameCn": "mppt周期计数最大值", "paraNameEn": "MpptPara maxPeriodCnt", "paraVarName": "MpptPara.maxPeriodCnt", "paraMacroName": "ID_MPPT_P_CNT_MAX", "paraDescription": "周期计数最大值", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "10", "minValue": "-1", "defaultValue": "0", "dataType": "TYPE_I16", "saveAttr": "N"}, {"paraId": "887", "paraNameCn": "vPv开路电压", "paraNameEn": "MpptPara vPvOc", "paraVarName": "MpptPara.vPvOc", "paraMacroName": "ID_MPPT_VPV_OC", "paraDescription": "vPv开路电压", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "60", "minValue": "-1", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "888", "paraNameCn": "mppt采样开始标志", "paraNameEn": "MpptPara startFlag", "paraVarName": "MpptPara.startFlag", "paraMacroName": "ID_MPPT_START_FLAG", "paraDescription": "采样开始标志", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "-1", "defaultValue": "0", "dataType": "TYPE_I16", "saveAttr": "N"}, {"paraId": "889", "paraNameCn": "mppt采样结束标志", "paraNameEn": "MpptPara endFlag", "paraVarName": "MpptPara.endFlag", "paraMacroName": "ID_MPPT_END_FLAG", "paraDescription": "采样结束标志", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "-1", "defaultValue": "0", "dataType": "TYPE_I16", "saveAttr": "N"}, {"paraId": "890", "paraNameCn": "ac过零标志", "paraNameEn": "MpptPara acZeroFlag", "paraVarName": "MpptPara.acZeroFlag", "paraMacroName": "ID_MPPT_AC_ZERO_FLAG", "paraDescription": "ac过零标志", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "-1", "defaultValue": "0", "dataType": "TYPE_I16", "saveAttr": "N"}, {"paraId": "891", "paraNameCn": "步长", "paraNameEn": "MpptPara k", "paraVarName": "MpptPara.k", "paraMacroName": "ID_MPPT_STEP", "paraDescription": "步长", "paraUnit": "v", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "892", "paraNameCn": "参考给定", "paraNameEn": "MpptPara voutSet", "paraVarName": "MpptPara.voutSet", "paraMacroName": "ID_MPPT_VOUT", "paraDescription": "参考给定", "paraUnit": "v", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "893", "paraNameCn": "电流加减速使能", "paraNameEn": "AcCurrRampPara enable   ", "paraVarName": "AcCurrRampPara.enable", "paraMacroName": "ID_RAMP_ENABLE", "paraDescription": "电流加减速使能", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I8", "saveAttr": "Y"}, {"paraId": "894", "paraNameCn": "电流加减速上限", "paraNameEn": "AcCurrRampPara upLimit  ", "paraVarName": "AcCurrRampPara.upLimit", "paraMacroName": "ID_RAMP_UP_LIMIT", "paraDescription": "电流加减速上限", "paraUnit": "A", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "895", "paraNameCn": "电流加减速下限", "paraNameEn": "AcCurrRampPara dnLimit  ", "paraVarName": "AcCurrRampPara.dnLimit", "paraMacroName": "ID_RAMP_DN_LIMIT", "paraDescription": "电流加减速下限", "paraUnit": "A", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "896", "paraNameCn": "电流加减速加速步长", "paraNameEn": "AcCurrRampPara accStep  ", "paraVarName": "AcCurrRampPara.accStep", "paraMacroName": "ID_RAMP_ACC_STEP", "paraDescription": "电流加减速加速步长", "paraUnit": "A/0.1ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "897", "paraNameCn": "电流加减速减速步长", "paraNameEn": "AcCurrRampPara decStep  ", "paraVarName": "AcCurrRampPara.decStep", "paraMacroName": "ID_RAMP_DEC_STEP", "paraDescription": "电流加减速减速步长", "paraUnit": "A/0.1ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "898", "paraNameCn": "电流加减速输出", "paraNameEn": "AcCurrRampPara realValue", "paraVarName": "AcCurrRampPara.realValue", "paraMacroName": "ID_RAMP_REAL_VALUE", "paraDescription": "电流加减速输出", "paraUnit": "A", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}], "测试": [{"paraId": "1020", "paraNameCn": "测试参数0", "paraNameEn": "Test0", "paraVarName": "Test[0]", "paraMacroName": "ID_TEST_0", "paraDescription": "测试参数0", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "30000", "minValue": "-30000", "defaultValue": "0", "dataType": "TYPE_I16", "saveAttr": "N"}, {"paraId": "1021", "paraNameCn": "测试参数1", "paraNameEn": "Test1", "paraVarName": "Test[1]", "paraMacroName": "ID_TEST_1", "paraDescription": "测试参数1", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "30000", "minValue": "-30000", "defaultValue": "0", "dataType": "TYPE_I16", "saveAttr": "N"}, {"paraId": "1022", "paraNameCn": "测试参数2", "paraNameEn": "Test2", "paraVarName": "TestInt32_0", "paraMacroName": "ID_TEST_INT32_0", "paraDescription": "测试参数2", "paraUnit": "0.1ms", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "2000000000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "Y"}, {"paraId": "1023", "paraNameCn": "测试参数F32_0", "paraNameEn": "null", "paraVarName": "TestF32[0]", "paraMacroName": "ID_PWM_TEST_F32_0", "paraDescription": "测试参数F32_0", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "3", "minValue": "-3", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1024", "paraNameCn": "测试参数F32_1", "paraNameEn": "null", "paraVarName": "TestF32[1]", "paraMacroName": "ID_PWM_TEST_F32_1", "paraDescription": "测试参数F32_1", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000000", "minValue": "-1000000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1025", "paraNameCn": "测试参数F32_2", "paraNameEn": "null", "paraVarName": "TestF32[2]", "paraMacroName": "ID_PWM_TEST_F32_2", "paraDescription": "测试参数F32_2", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000000", "minValue": "-1000000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}], "存储参数": [{"paraId": "500", "paraNameCn": "EEPROM错误码", "paraNameEn": "EepromErrWord", "paraVarName": "EepromErrWord.all", "paraMacroName": "ID_EEPROM_ERROR", "paraDescription": "B0:读SN B1:写SN B2:读校准参数 B3:写校准参数 \nB4:擦校准参数 B5:读所有参数 B6:写所有参数 B7:擦所有参数 \nB8:读故障记录 B9:写故障记录 B10:擦故障记录 B11:读故障录波 \nB12:写故障录波 B13:保留 B14:写老化参数 B15:擦老化参数", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_BIT16", "saveAttr": "N"}, {"paraId": "501", "paraNameCn": "EEPROM操作命令", "paraNameEn": "EepromCtrlWord", "paraVarName": "EepromCtrlWord.all", "paraMacroName": "ID_EEPROM_COMMAND", "paraDescription": "B0:保留 B1:写SN B2:保留 B3:写校准参数 \nB4:擦校准参数 B5:保留 B6:写所有参数 B7:擦所有参数 \nB8:读故障记录 B9:保留 B10:擦故障记录 B11:读故障录波 \nB12:保留 B13:保留 B14:写老化参数 B15:擦老化参数", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_BIT16", "saveAttr": "N"}, {"paraId": "502", "paraNameCn": "EEPROM操作标志", "paraNameEn": "EepromUsingWord", "paraVarName": "EepromUsingWord.all", "paraMacroName": "ID_EEPROM_OPERATION_FLAG", "paraDescription": "B0:保留 B1:写SN B2:保留 B3:写校准参数 \nB4:擦校准参数 B5:读所有参数 B6:写所有参数 B7:擦所有参数 \nB8:读故障记录 B9:写故障记录 B10:擦故障记录 B11:读故障录波 \nB12:写故障录波 B13:保留 B14:写老化参数 B15:擦老化参数", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_BIT16", "saveAttr": "N"}, {"paraId": "503", "paraNameCn": "擦所有参数命令(装备使用)", "paraNameEn": "EepromPara", "paraVarName": "EepromPara.eraseAllChipCmd", "paraMacroName": "ID_EEPROM_ERASE_ALL", "paraDescription": "擦所有参数、擦故障记录 ", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "504", "paraNameCn": "自检状态", "paraNameEn": "EepromPara", "paraVarName": "EepromPara.eepromCheckState", "paraMacroName": "ID_EEPROM_CHECK_STATE", "paraDescription": "0:失败,1:通过", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "505", "paraNameCn": "单参数写状态", "paraNameEn": "EepromPara", "paraVarName": "SingleParaCtrl.writeState", "paraMacroName": "ID_EEPROM_SINGLE_WRITE_STATE", "paraDescription": "单参数写状态", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}], "电量功率计算": [{"paraId": "850", "paraNameCn": "功率累计值", "paraNameEn": "MpptPara powerSum", "paraVarName": "MpptPara.powerSum", "paraMacroName": "ID_MPPT_POWER_SUM", "paraDescription": "功率累计值", "paraUnit": "W", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "851", "paraNameCn": "功率平均值", "paraNameEn": "MpptPara powerAvg", "paraVarName": "MpptPara.powerAvg", "paraMacroName": "ID_MPPT_POWER_AVG", "paraDescription": "功率平均值", "paraUnit": "W", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "852", "paraNameCn": "功率瞬时值", "paraNameEn": "MpptPara power", "paraVarName": "MpptPara.power", "paraMacroName": "ID_MPPT_POWER_TEMP", "paraDescription": "功率瞬时值", "paraUnit": "W", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "853", "paraNameCn": "功率差值", "paraNameEn": "MpptPara powerDelta", "paraVarName": "MpptPara.powerDelta", "paraMacroName": "ID_MPPT_POWER_DELTA", "paraDescription": "功率差值", "paraUnit": "W", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "854", "paraNameCn": "功率旧值", "paraNameEn": "MpptPara powerOld", "paraVarName": "MpptPara.powerOld", "paraMacroName": "ID_MPPT_POWER_OLD", "paraDescription": "功率旧值", "paraUnit": "W", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "855", "paraNameCn": "vPv累计值", "paraNameEn": "MpptPara vPvSum", "paraVarName": "MpptPara.vPvSum", "paraMacroName": "ID_MPPT_VPV_SUM", "paraDescription": "vPv累计值", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "856", "paraNameCn": "vPv平均值", "paraNameEn": "MpptPara vPvAvg", "paraVarName": "MpptPara.vPvAvg", "paraMacroName": "ID_MPPT_VPV_AVG", "paraDescription": "vPv平均值", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "857", "paraNameCn": "vPv瞬时值", "paraNameEn": "MpptPara vPv", "paraVarName": "MpptPara.vPv", "paraMacroName": "ID_MPPT_VPV_TEMP", "paraDescription": "vPv瞬时值", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "858", "paraNameCn": "vPv差值", "paraNameEn": "MpptPara vPvDelta", "paraVarName": "MpptPara.vPvDelta", "paraMacroName": "ID_MPPT_VPV_DELTA", "paraDescription": "vPv差值", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "859", "paraNameCn": "vPv旧值", "paraNameEn": "MpptPara vPvOld", "paraVarName": "MpptPara.vPvOld", "paraMacroName": "ID_MPPT_VPV_OLD", "paraDescription": "vPv旧值", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "860", "paraNameCn": "iPv累计值", "paraNameEn": "MpptPara iPvSum", "paraVarName": "MpptPara.iPvSum", "paraMacroName": "ID_MPPT_IPV_SUM", "paraDescription": "iPv累计值", "paraUnit": "A", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "861", "paraNameCn": "iPv平均值", "paraNameEn": "MpptPara iPvAvg", "paraVarName": "MpptPara.iPvAvg", "paraMacroName": "ID_MPPT_IPV_AVG", "paraDescription": "iPv平均值", "paraUnit": "A", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "862", "paraNameCn": "iPv平均滤波值", "paraNameEn": "MpptPara iPvFilter", "paraVarName": "MpptPara.iPvFilter", "paraMacroName": "ID_MPPT_IPV_FILTER", "paraDescription": "iPv平均滤波值", "paraUnit": "A", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "863", "paraNameCn": "输入电量(ws)", "paraNameEn": "MpptPara wsIn", "paraVarName": "MpptPara.wsIn  ", "paraMacroName": "ID_MPPT_WS_IN", "paraDescription": "输入电量(ws)", "paraUnit": "ws", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "864", "paraNameCn": "输入电量(kwh)", "paraNameEn": "MpptPara kwhIn", "paraVarName": "MpptPara.kwhIn ", "paraMacroName": "ID_MPPT_KWH_IN", "paraDescription": "输入电量(kwh)", "paraUnit": "kwh", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "865", "paraNameCn": "输出电量(ws)", "paraNameEn": "MpptPara wsOut", "paraVarName": "MpptPara.wsOut ", "paraMacroName": "ID_MPPT_WS_OUT", "paraDescription": "输出电量(ws)", "paraUnit": "ws", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "866", "paraNameCn": "输出电量(kwh)", "paraNameEn": "MpptPara kwhOut", "paraVarName": "MpptPara.kwhOut", "paraMacroName": "ID_MPPT_KWH_OUT", "paraDescription": "输出电量(kwh)", "paraUnit": "kwh", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "867", "paraNameCn": "输出功率平均值", "paraNameEn": "MpptPara powerAvgOut", "paraVarName": "MpptPara.powerAvgOut", "paraMacroName": "ID_MPPT_POWER_AVG_OUT", "paraDescription": "输出功率平均值", "paraUnit": "w", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "868", "paraNameCn": "效率", "paraNameEn": "MpptPara yita", "paraVarName": "MpptPara.yita", "paraMacroName": "ID_MPPT_YITA", "paraDescription": "效率", "paraUnit": "%", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}], "故障保护": [{"paraId": "170", "paraNameCn": "故障复位", "paraNameEn": "FaultRestCmd", "paraVarName": "FaultResetCmd", "paraMacroName": "ID_FAULT_RESET_CMD", "paraDescription": "故障复位", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "171", "paraNameCn": "故障自复位使能", "paraNameEn": "FaultSelfResetEnable", "paraVarName": "FaultSelfResetEnable", "paraMacroName": "ID_FAULT_SELF_RESET_EN", "paraDescription": "0：禁止自复位 1：使能自复位", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "Y"}, {"paraId": "172", "paraNameCn": "故障字1", "paraNameEn": "FaultWord1", "paraVarName": "FaultWord1.all", "paraMacroName": "ID_FAULT_WORD_1", "paraDescription": "B0:PV过压  B1:PV欠压  B2:PV过流   B3:PV短路 \nB4:PV接反   B5:PV错接  B6:PV接地  B7:保留 \nB8:保留 B9:保留 B10:保留 B11:保留 \nB12:保留 B13:保留 B14:保留 B15:保留", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_BIT16", "saveAttr": "N"}, {"paraId": "173", "paraNameCn": "故障源1", "paraNameEn": "FaultSrcWord1", "paraVarName": "ProStatusWord1.all", "paraMacroName": "ID_FAULT_SRC_WORD_1", "paraDescription": "B0:PV过压  B1:PV欠压  B2:PV过流   B3:PV短路 \nB4:PV接反   B5:PV错接  B6:PV接地  B7:保留 \nB8:保留 B9:保留 B10:保留 B11:保留 \nB12:保留 B13:保留 B14:保留 B15:保留", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_BIT16", "saveAttr": "N"}, {"paraId": "174", "paraNameCn": "外部屏蔽字1", "paraNameEn": "InnerMaskWord1", "paraVarName": "OuterMaskWord1.all", "paraMacroName": "ID_FAULT_OUTER_MASK_WORD_1", "paraDescription": "B0:PV过压  B1:PV欠压  B2:PV过流   B3:PV短路 \nB4:PV接反   B5:PV错接  B6:PV接地  B7:保留 \nB8:保留 B9:保留 B10:保留 B11:保留 \nB12:保留 B13:保留 B14:保留 B15:保留", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_BIT16", "saveAttr": "Y"}, {"paraId": "175", "paraNameCn": "内部部屏蔽字1", "paraNameEn": "OuterMaskWord1", "paraVarName": "InnerMaskWord1.all", "paraMacroName": "ID_FAULT_INNER_MASK_WORD_1", "paraDescription": "B0:PV过压  B1:PV欠压  B2:PV过流   B3:PV短路 \nB4:PV接反   B5:PV错接  B6:PV接地  B7:保留 \nB8:保留 B9:保留 B10:保留 B11:保留 \nB12:保留 B13:保留 B14:保留 B15:保留", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_BIT16", "saveAttr": "N"}, {"paraId": "176", "paraNameCn": "故障字2", "paraNameEn": "FaultWord2", "paraVarName": "FaultWord2.all", "paraMacroName": "ID_FAULT_WORD_2", "paraDescription": "B0:AC过压等级1  B1:AC过压等级2  B2:AC过压等级3  B3:10分钟平均过压 \nB4:AC欠压等级1  B5:AC欠压等级2  B6:AC欠压等级3  B7:AC过频等级1 \nB8:AC过频等级2  B9:AC过频等级3  B10:AC欠频等级1 B11:AC欠频等级2 \nB12:AC欠频等级3 B13:谐振腔过流  B14:AC短路      B15:AC电压异常 ", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_BIT16", "saveAttr": "N"}, {"paraId": "177", "paraNameCn": "故障源2", "paraNameEn": "FaultSrcWord2", "paraVarName": "ProStatusWord2.all", "paraMacroName": "ID_FAULT_SRC_WORD_2", "paraDescription": "B0:AC过压等级1  B1:AC过压等级2  B2:AC过压等级3  B3:10分钟平均过压 \nB4:AC欠压等级1  B5:AC欠压等级2  B6:AC欠压等级3  B7:AC过频等级1 \nB8:AC过频等级2  B9:AC过频等级3  B10:AC欠频等级1 B11:AC欠频等级2 \nB12:AC欠频等级3 B13:谐振腔过流  B14:AC短路      B15:AC电压异常 ", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_BIT16", "saveAttr": "N"}, {"paraId": "178", "paraNameCn": "外部屏蔽字2", "paraNameEn": "InnerMaskWord2", "paraVarName": "OuterMaskWord2.all", "paraMacroName": "ID_FAULT_OUTER_MASK_WORD_2", "paraDescription": "B0:AC过压等级1  B1:AC过压等级2  B2:AC过压等级3  B3:10分钟平均过压 \nB4:AC欠压等级1  B5:AC欠压等级2  B6:AC欠压等级3  B7:AC过频等级1 \nB8:AC过频等级2  B9:AC过频等级3  B10:AC欠频等级1 B11:AC欠频等级2 \nB12:AC欠频等级3 B13:谐振腔过流  B14:AC短路      B15:AC电压异常 ", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_BIT16", "saveAttr": "Y"}, {"paraId": "179", "paraNameCn": "内部部屏蔽字2", "paraNameEn": "OuterMaskWord2", "paraVarName": "InnerMaskWord2.all", "paraMacroName": "ID_FAULT_INNER_MASK_WORD_2", "paraDescription": "B0:AC过压等级1  B1:AC过压等级2  B2:AC过压等级3  B3:10分钟平均过压 \nB4:AC欠压等级1  B5:AC欠压等级2  B6:AC欠压等级3  B7:AC过频等级1 \nB8:AC过频等级2  B9:AC过频等级3  B10:AC欠频等级1 B11:AC欠频等级2 \nB12:AC欠频等级3 B13:谐振腔过流  B14:AC短路      B15:AC电压异常 ", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_BIT16", "saveAttr": "N"}, {"paraId": "180", "paraNameCn": "故障字3", "paraNameEn": "FaultWord3", "paraVarName": "FaultWord3.all", "paraMacroName": "ID_FAULT_WORD_3", "paraDescription": "B0:PLC串口通信丢失 B1:Wifi串口通信丢失 B2:EMU通信丢失 B3:存储异常 \nB4:PV侧Mos过温 B5:AC侧Mos过温   B6:绝缘阻抗  B7:保留 B8:保留 \nB9:保留 B10:保留 B11:保留 B12:保留 B13:保留 B14:保留 B15:保留", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_BIT16", "saveAttr": "N"}, {"paraId": "181", "paraNameCn": "故障源3", "paraNameEn": "FaultSrcWord3", "paraVarName": "ProStatusWord3.all", "paraMacroName": "ID_FAULT_SRC_WORD_3", "paraDescription": "B0:PLC串口通信丢失 B1:Wifi串口通信丢失 B2:EMU通信丢失 B3:存储异常 \nB4:PV侧Mos过温 B5:AC侧Mos过温   B6:绝缘阻抗  B7:保留 B8:保留 \nB9:保留 B10:保留 B11:保留 B12:保留 B13:保留 B14:保留 B15:保留", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_BIT16", "saveAttr": "N"}, {"paraId": "182", "paraNameCn": "外部屏蔽字3", "paraNameEn": "InnerMaskWord3", "paraVarName": "OuterMaskWord3.all", "paraMacroName": "ID_FAULT_OUTER_MASK_WORD_3", "paraDescription": "B0:PLC串口通信丢失 B1:Wifi串口通信丢失 B2:EMU通信丢失 B3:存储异常 \nB4:PV侧Mos过温 B5:AC侧Mos过温   B6:绝缘阻抗  B7:保留 B8:保留 \nB9:保留 B10:保留 B11:保留 B12:保留 B13:保留 B14:保留 B15:保留", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_BIT16", "saveAttr": "Y"}, {"paraId": "183", "paraNameCn": "内部部屏蔽字3", "paraNameEn": "OuterMaskWord3", "paraVarName": "InnerMaskWord3.all", "paraMacroName": "ID_FAULT_INNER_MASK_WORD_3", "paraDescription": "B0:PLC串口通信丢失 B1:Wifi串口通信丢失 B2:EMU通信丢失 B3:存储异常 \nB4:PV侧Mos过温 B5:AC侧Mos过温   B6:绝缘阻抗  B7:保留 B8:保留 \nB9:保留 B10:保留 B11:保留 B12:保留 B13:保留 B14:保留 B15:保留", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_BIT16", "saveAttr": "N"}, {"paraId": "184", "paraNameCn": "锁死故障", "paraNameEn": "<PERSON><PERSON><PERSON>", "paraVarName": "<PERSON><PERSON><PERSON>", "paraMacroName": "ID_LOCK_FAULT", "paraDescription": "锁死故障", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "185", "paraNameCn": "故障反复发生次数", "paraNameEn": "CntOfRecurrFault", "paraVarName": "CntOfRecurrFault", "paraMacroName": "ID_FAULT_RECURR_CNT", "paraDescription": "故障反复发生次数", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "186", "paraNameCn": "故障反复发生最大次数", "paraNameEn": "MaxCntOfRecurrFault", "paraVarName": "MaxCntOfRecurrFault", "paraMacroName": "ID_FAULT_RECURR_MAX_CNT", "paraDescription": "故障反复发生最大次数", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "187", "paraNameCn": "故障重连时间", "paraNameEn": "MaxCntOfRecurrFault", "paraVarName": "ReconnectTime", "paraMacroName": "ID_FAULT_RECONNECT_TIME", "paraDescription": "故障重连时间", "paraUnit": "s", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "1000", "minValue": "10", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}], "故障保护门限": [{"paraId": "550", "paraNameCn": "PV过压上限", "paraNameEn": "PvVolUp upLimit", "paraVarName": "FaultProLimit.pvVolUp.upLimit", "paraMacroName": "ID_PV_VOL_UP_UP_LIMIT", "paraDescription": "PV过压上限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "551", "paraNameCn": "PV过压下限", "paraNameEn": "PvVolUp dnLimit", "paraVarName": "FaultProLimit.pvVolUp.dnLimit", "paraMacroName": "ID_PV_VOL_UP_DN_LIMIT", "paraDescription": "PV过压下限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "552", "paraNameCn": "PV过压滤波时间", "paraNameEn": "PvVolUp filterTime", "paraVarName": "FaultProLimit.pvVolUp.filter", "paraMacroName": "ID_PV_VOL_UP_FILTER", "paraDescription": "PV过压滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "Y"}, {"paraId": "553", "paraNameCn": "PV欠压上限", "paraNameEn": "PvVolDn upLimit", "paraVarName": "FaultProLimit.pvVolDn.upLimit", "paraMacroName": "ID_PV_VOL_DN_UP_LIMIT", "paraDescription": "PV欠压上限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "554", "paraNameCn": "PV欠压下限", "paraNameEn": "PvVolDn dnLimit", "paraVarName": "FaultProLimit.pvVolDn.dnLimit", "paraMacroName": "ID_PV_VOL_DN_DN_LIMIT", "paraDescription": "PV欠压下限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "555", "paraNameCn": "PV欠压滤波时间", "paraNameEn": "PvVolDn filterTime", "paraVarName": "FaultProLimit.pvVolDn.filter", "paraMacroName": "ID_PV_VOL_DN_FILTER", "paraDescription": "PV欠压滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "Y"}, {"paraId": "556", "paraNameCn": "PV过流上限", "paraNameEn": "PvCurrUp upLimit", "paraVarName": "FaultProLimit.pvCurrUp.upLimit", "paraMacroName": "ID_PV_CURR_UP_UP_LIMIT", "paraDescription": "PV过流上限", "paraUnit": "A", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "557", "paraNameCn": "PV过流下限", "paraNameEn": "PvCurrUp dnLimit", "paraVarName": "FaultProLimit.pvCurrUp.dnLimit", "paraMacroName": "ID_PV_CURR_UP_DN_LIMIT", "paraDescription": "PV过流下限", "paraUnit": "A", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "558", "paraNameCn": "PV过流滤波时间", "paraNameEn": "PvCurrUp filterTime", "paraVarName": "FaultProLimit.pvCurrUp.filter", "paraMacroName": "ID_PV_CURR_UP_FILTER", "paraDescription": "PV过流滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "Y"}, {"paraId": "559", "paraNameCn": "PV短路上限", "paraNameEn": "PvShort upLimit", "paraVarName": "FaultProLimit.pvShort.upLimit", "paraMacroName": "ID_PV_SHORT_UP_LIMIT", "paraDescription": "PV短路上限", "paraUnit": "A", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "560", "paraNameCn": "PV短路下限", "paraNameEn": "PvShort dnLimit", "paraVarName": "FaultProLimit.pvShort.dnLimit", "paraMacroName": "ID_PV_SHORT_DN_LIMIT", "paraDescription": "PV短路下限", "paraUnit": "A", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "561", "paraNameCn": "PV短路滤波时间", "paraNameEn": "PvShort filterTime", "paraVarName": "FaultProLimit.pvShort.filter", "paraMacroName": "ID_PV_SHORT_FILTER", "paraDescription": "PV短路滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "Y"}, {"paraId": "562", "paraNameCn": "PV接反上限", "paraNameEn": "PvReverse upLimit", "paraVarName": "FaultProLimit.pvReverse.upLimit", "paraMacroName": "ID_PV_REVERSE_UP_LIMIT", "paraDescription": "PV接反上限", "paraUnit": "A", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "563", "paraNameCn": "PV接反下限", "paraNameEn": "PvReverse dnLimit", "paraVarName": "FaultProLimit.pvReverse.dnLimit", "paraMacroName": "ID_PV_REVERSE_DN_LIMIT", "paraDescription": "PV接反下限", "paraUnit": "A", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "564", "paraNameCn": "PV接反滤波时间", "paraNameEn": "PvReverse filterTime", "paraVarName": "FaultProLimit.pvReverse.filter", "paraMacroName": "ID_PV_REVERSE_FILTER", "paraDescription": "PV接反滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "Y"}, {"paraId": "565", "paraNameCn": "PV错接上限", "paraNameEn": "PvConnErr upLimit", "paraVarName": "FaultProLimit.pvConnErr.upLimit", "paraMacroName": "ID_PV_ERR_UP_LIMIT", "paraDescription": "PV错接上限", "paraUnit": "A", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "566", "paraNameCn": "PV错接下限", "paraNameEn": "PvConnErr dnLimit", "paraVarName": "FaultProLimit.pvConnErr.dnLimit", "paraMacroName": "ID_PV_ERR_DN_LIMIT", "paraDescription": "PV错接下限", "paraUnit": "A", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "567", "paraNameCn": "PV错接滤波时间", "paraNameEn": "PvConnErr filterTime", "paraVarName": "FaultProLimit.pvConnErr.filter", "paraMacroName": "ID_PV_ERR_FILTER", "paraDescription": "PV错接滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "Y"}, {"paraId": "568", "paraNameCn": "PV接地上限", "paraNameEn": "PvEarth upLimit", "paraVarName": "FaultProLimit.pvEarth.upLimit", "paraMacroName": "ID_PV_EARTH_UP_LIMIT", "paraDescription": "PV接地上限", "paraUnit": "A", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "569", "paraNameCn": "PV接地下限", "paraNameEn": "PvEarth dnLimit", "paraVarName": "FaultProLimit.pvEarth.dnLimit", "paraMacroName": "ID_PV_EARTH_DN_LIMIT", "paraDescription": "PV接地下限", "paraUnit": "A", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "570", "paraNameCn": "PV接地滤波时间", "paraNameEn": "PvEarth filterTime", "paraVarName": "FaultProLimit.pvEarth.filter", "paraMacroName": "ID_PV_EARTH_FILTER", "paraDescription": "PV接地滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "Y"}, {"paraId": "571", "paraNameCn": "AC过压1上限", "paraNameEn": "AcVolUp1 upLimit", "paraVarName": "FaultProLimit.acVolUp1.upLimit", "paraMacroName": "ID_AC_VOL_UP1_UP_LIMIT", "paraDescription": "AC过压1上限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "572", "paraNameCn": "AC过压1下限", "paraNameEn": "AcVolUp1 dnLimit", "paraVarName": "FaultProLimit.acVolUp1.dnLimit", "paraMacroName": "ID_AC_VOL_UP1_DN_LIMIT", "paraDescription": "AC过压1下限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "573", "paraNameCn": "AC过压1滤波时间", "paraNameEn": "AcVolUp1 filterTime", "paraVarName": "FaultProLimit.acVolUp1.filter", "paraMacroName": "ID_AC_VOL_UP1_FILTER", "paraDescription": "AC过压1滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "574", "paraNameCn": "AC过压2上限", "paraNameEn": "AcVolUp2 upLimit", "paraVarName": "FaultProLimit.acVolUp2.upLimit", "paraMacroName": "ID_AC_VOL_UP2_UP_LIMIT", "paraDescription": "AC过压2上限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "575", "paraNameCn": "AC过压2下限", "paraNameEn": "AcVolUp2 dnLimit", "paraVarName": "FaultProLimit.acVolUp2.dnLimit", "paraMacroName": "ID_AC_VOL_UP2_DN_LIMIT", "paraDescription": "AC过压2下限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "576", "paraNameCn": "AC过压2滤波时间", "paraNameEn": "AcVolUp2 filterTime", "paraVarName": "FaultProLimit.acVolUp2.filter", "paraMacroName": "ID_AC_VOL_UP2_FILTER", "paraDescription": "AC过压2滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "577", "paraNameCn": "AC过压3上限", "paraNameEn": "AcVolUp3 upLimit", "paraVarName": "FaultProLimit.acVolUp3.upLimit", "paraMacroName": "ID_AC_VOL_UP3_UP_LIMIT", "paraDescription": "AC过压3上限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "578", "paraNameCn": "AC过压3下限", "paraNameEn": "AcVolUp3 dnLimit", "paraVarName": "FaultProLimit.acVolUp3.dnLimit", "paraMacroName": "ID_AC_VOL_UP3_DN_LIMIT", "paraDescription": "AC过压3下限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "579", "paraNameCn": "AC过压3滤波时间", "paraNameEn": "AcVolUp3 filterTime", "paraVarName": "FaultProLimit.acVolUp3.filter", "paraMacroName": "ID_AC_VOL_UP3_FILTER", "paraDescription": "AC过压3滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "580", "paraNameCn": "AC10分钟平均过压上限", "paraNameEn": "TenMinAvgVolUp upLimit", "paraVarName": "FaultProLimit.tenMinAvgVolUp.upLimit", "paraMacroName": "ID_TEN_MIN_VOL_UP_UP_LIMIT", "paraDescription": "AC10分钟过压上限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "581", "paraNameCn": "AC10分钟平均过压下限", "paraNameEn": "TenMinAvgVolUp dnLimit", "paraVarName": "FaultProLimit.tenMinAvgVolUp.dnLimit", "paraMacroName": "ID_TEN_MIN_VOL_UP_DN_LIMIT", "paraDescription": "AC10分钟过压下限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "582", "paraNameCn": "AC10分钟平均过压滤波时间", "paraNameEn": "TenMinAvgVolUp filterTime", "paraVarName": "FaultProLimit.tenMinAvgVolUp.filter", "paraMacroName": "ID_TEN_MIN_VOL_UP_FILTER", "paraDescription": "AC10分钟过压滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "583", "paraNameCn": "AC欠压1上限", "paraNameEn": "AcVolDn1 upLimit", "paraVarName": "FaultProLimit.acVolDn1.upLimit", "paraMacroName": "ID_AC_VOL_DN1_UP_LIMIT", "paraDescription": "AC欠压1上限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "584", "paraNameCn": "AC欠压1下限", "paraNameEn": "AcVolDn1 dnLimit", "paraVarName": "FaultProLimit.acVolDn1.dnLimit", "paraMacroName": "ID_AC_VOL_DN1_DN_LIMIT", "paraDescription": "AC欠压1下限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "585", "paraNameCn": "AC欠压1滤波时间", "paraNameEn": "AcVolDn1 filterTime", "paraVarName": "FaultProLimit.acVolDn1.filter", "paraMacroName": "ID_AC_VOL_DN1_FILTER", "paraDescription": "AC欠压1滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "586", "paraNameCn": "AC欠压2上限", "paraNameEn": "AcVolDn2 upLimit", "paraVarName": "FaultProLimit.acVolDn2.upLimit", "paraMacroName": "ID_AC_VOL_DN2_UP_LIMIT", "paraDescription": "AC欠压2上限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "587", "paraNameCn": "AC欠压2下限", "paraNameEn": "AcVolDn2 dnLimit", "paraVarName": "FaultProLimit.acVolDn2.dnLimit", "paraMacroName": "ID_AC_VOL_DN2_DN_LIMIT", "paraDescription": "AC欠压2下限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "588", "paraNameCn": "AC欠压2滤波时间", "paraNameEn": "AcVolDn2 filterTime", "paraVarName": "FaultProLimit.acVolDn2.filter", "paraMacroName": "ID_AC_VOL_DN2_FILTER", "paraDescription": "AC欠压2滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "589", "paraNameCn": "AC欠压3上限", "paraNameEn": "AcVolDn3 upLimit", "paraVarName": "FaultProLimit.acVolDn3.upLimit", "paraMacroName": "ID_AC_VOL_DN3_UP_LIMIT", "paraDescription": "AC欠压3上限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "590", "paraNameCn": "AC欠压3下限", "paraNameEn": "AcVolDn3 dnLimit", "paraVarName": "FaultProLimit.acVolDn3.dnLimit", "paraMacroName": "ID_AC_VOL_DN3_DN_LIMIT", "paraDescription": "AC欠压3下限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "591", "paraNameCn": "AC欠压3滤波时间", "paraNameEn": "AcVolDn3 filterTime", "paraVarName": "FaultProLimit.acVolDn3.filter", "paraMacroName": "ID_AC_VOL_DN3_FILTER", "paraDescription": "AC欠压3滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "592", "paraNameCn": "AC过频1上限", "paraNameEn": "AcFreqUp1 upLimit", "paraVarName": "FaultProLimit.acFreqUp1.upLimit", "paraMacroName": "ID_AC_FREQ_UP1_UP_LIMIT", "paraDescription": "AC过频1上限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "593", "paraNameCn": "AC过频1下限", "paraNameEn": "AcFreqUp1 dnLimit", "paraVarName": "FaultProLimit.acFreqUp1.dnLimit", "paraMacroName": "ID_AC_FREQ_UP1_DN_LIMIT", "paraDescription": "AC过频1下限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "594", "paraNameCn": "AC过频1滤波时间", "paraNameEn": "AcFreqUp1 filterTime", "paraVarName": "FaultProLimit.acFreqUp1.filter", "paraMacroName": "ID_AC_FREQ_UP1_FILTER", "paraDescription": "AC过频1滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "595", "paraNameCn": "AC过频2上限", "paraNameEn": "AcFreqUp2 upLimit", "paraVarName": "FaultProLimit.acFreqUp2.upLimit", "paraMacroName": "ID_AC_FREQ_UP2_UP_LIMIT", "paraDescription": "AC过频2上限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "596", "paraNameCn": "AC过频2下限", "paraNameEn": "AcFreqUp2 dnLimit", "paraVarName": "FaultProLimit.acFreqUp2.dnLimit", "paraMacroName": "ID_AC_FREQ_UP2_DN_LIMIT", "paraDescription": "AC过频2下限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "597", "paraNameCn": "AC过频2滤波时间", "paraNameEn": "AcFreqUp2 filterTime", "paraVarName": "FaultProLimit.acFreqUp2.filter", "paraMacroName": "ID_AC_FREQ_UP2_FILTER", "paraDescription": "AC过频2滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "598", "paraNameCn": "AC过频3上限", "paraNameEn": "AcFreqUp3 upLimit", "paraVarName": "FaultProLimit.acFreqUp3.upLimit", "paraMacroName": "ID_AC_FREQ_UP3_UP_LIMIT", "paraDescription": "AC过频3上限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "599", "paraNameCn": "AC过频3下限", "paraNameEn": "AcFreqUp3 dnLimit", "paraVarName": "FaultProLimit.acFreqUp3.dnLimit", "paraMacroName": "ID_AC_FREQ_UP3_DN_LIMIT", "paraDescription": "AC过频3下限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "600", "paraNameCn": "AC过频3滤波时间", "paraNameEn": "AcFreqUp3 filterTime", "paraVarName": "FaultProLimit.acFreqUp3.filter", "paraMacroName": "ID_AC_FREQ_UP3_FILTER", "paraDescription": "AC过频3滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "601", "paraNameCn": "AC欠频1上限", "paraNameEn": "AcFreqDn1 upLimit", "paraVarName": "FaultProLimit.acFreqDn1.upLimit", "paraMacroName": "ID_AC_FREQ_DN1_UP_LIMIT", "paraDescription": "AC欠频1上限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "602", "paraNameCn": "AC欠频1下限", "paraNameEn": "AcFreqDn1 dnLimit", "paraVarName": "FaultProLimit.acFreqDn1.dnLimit", "paraMacroName": "ID_AC_FREQ_DN1_DN_LIMIT", "paraDescription": "AC欠频1下限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "603", "paraNameCn": "AC欠频1滤波时间", "paraNameEn": "AcFreqDn1 filterTime", "paraVarName": "FaultProLimit.acFreqDn1.filter", "paraMacroName": "ID_AC_FREQ_DN1_FILTER", "paraDescription": "AC欠频1滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "604", "paraNameCn": "AC欠频2上限", "paraNameEn": "AcFreqDn2 upLimit", "paraVarName": "FaultProLimit.acFreqDn2.upLimit", "paraMacroName": "ID_AC_FREQ_DN2_UP_LIMIT", "paraDescription": "AC欠频2上限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "605", "paraNameCn": "AC欠频2下限", "paraNameEn": "AcFreqDn2 dnLimit", "paraVarName": "FaultProLimit.acFreqDn2.dnLimit", "paraMacroName": "ID_AC_FREQ_DN2_DN_LIMIT", "paraDescription": "AC欠频2下限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "606", "paraNameCn": "AC欠频2滤波时间", "paraNameEn": "AcFreqDn2 filterTime", "paraVarName": "FaultProLimit.acFreqDn2.filter", "paraMacroName": "ID_AC_FREQ_DN2_FILTER", "paraDescription": "AC欠频2滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "607", "paraNameCn": "AC欠频3上限", "paraNameEn": "AcFreqDn3 upLimit", "paraVarName": "FaultProLimit.acFreqDn3.upLimit", "paraMacroName": "ID_AC_FREQ_DN3_UP_LIMIT", "paraDescription": "AC欠频3上限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "608", "paraNameCn": "AC欠频3下限", "paraNameEn": "AcFreqDn3 dnLimit", "paraVarName": "FaultProLimit.acFreqDn3.dnLimit", "paraMacroName": "ID_AC_FREQ_DN3_DN_LIMIT", "paraDescription": "AC欠频3下限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "609", "paraNameCn": "AC欠频3滤波时间", "paraNameEn": "AcFreqDn3 filterTime", "paraVarName": "FaultProLimit.acFreqDn3.filter", "paraMacroName": "ID_AC_FREQ_DN3_FILTER", "paraDescription": "AC欠频3滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "610", "paraNameCn": "谐振腔过流上限", "paraNameEn": "ResonatorCurr upLimit", "paraVarName": "FaultProLimit.resonatorCurr.upLimit", "paraMacroName": "ID_RESON_CURR_UP_LIMIT", "paraDescription": "谐振腔过流上限", "paraUnit": "A", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "611", "paraNameCn": "谐振腔过流下限", "paraNameEn": "ResonatorCurr dnLimit", "paraVarName": "FaultProLimit.resonatorCurr.dnLimit", "paraMacroName": "ID_RESON_CURR_DN_LIMIT", "paraDescription": "谐振腔过流下限", "paraUnit": "A", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "612", "paraNameCn": "谐振腔过流滤波时间", "paraNameEn": "ResonatorCurr filterTime", "paraVarName": "FaultProLimit.resonatorCurr.filter", "paraMacroName": "ID_RESON_CURR_UP_LIMIT_FILTER", "paraDescription": "谐振腔过流滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "Y"}, {"paraId": "613", "paraNameCn": "AC短路上限", "paraNameEn": "AcShort upLimit", "paraVarName": "FaultProLimit.acShort.upLimit", "paraMacroName": "ID_AC_SHORT_UP_LIMIT", "paraDescription": "AC短路上限", "paraUnit": "A", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "614", "paraNameCn": "AC短路下限", "paraNameEn": "AcShort dnLimit", "paraVarName": "FaultProLimit.acShort.dnLimit", "paraMacroName": "ID_AC_SHORT_DN_LIMIT", "paraDescription": "AC短路下限", "paraUnit": "A", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "615", "paraNameCn": "AC短路滤波时间", "paraNameEn": "AcShort filterTime", "paraVarName": "FaultProLimit.acShort.filter", "paraMacroName": "ID_AC_SHORT_FILTER", "paraDescription": "AC短路滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "Y"}, {"paraId": "616", "paraNameCn": "AC电压异常上限", "paraNameEn": "AcVolErr upLimit", "paraVarName": "FaultProLimit.acVolErr.upLimit", "paraMacroName": "ID_AC_VOL_ERR_UP_LIMIT", "paraDescription": "AC电压异常上限", "paraUnit": "A", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "617", "paraNameCn": "AC电压异常下限", "paraNameEn": "AcVolErr dnLimit", "paraVarName": "FaultProLimit.acVolErr.dnLimit", "paraMacroName": "ID_AC_VOL_ERR_DN_LIMIT", "paraDescription": "AC电压异常下限", "paraUnit": "A", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "618", "paraNameCn": "AC电压异常滤波时间", "paraNameEn": "AcVolErr filterTime", "paraVarName": "FaultProLimit.acVolErr.filter", "paraMacroName": "ID_AC_VOL_ERR_FILTER", "paraDescription": "AC电压异常滤波时间", "paraUnit": "A", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "Y"}, {"paraId": "619", "paraNameCn": "PVMos过温上限", "paraNameEn": "PvMosTempUp upLimit", "paraVarName": "FaultProLimit.pvMosTempUp.upLimit", "paraMacroName": "ID_PV_MOS_TEMP_UP_UP_LIMIT", "paraDescription": "PVMos过温上限", "paraUnit": "度", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "620", "paraNameCn": "PVMos过温下限", "paraNameEn": "PvMosTempUp dnLimit", "paraVarName": "FaultProLimit.pvMosTempUp.dnLimit", "paraMacroName": "ID_PV_MOS_TEMP_UP_DN_LIMIT", "paraDescription": "PVMos过温下限", "paraUnit": "度", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "621", "paraNameCn": "PVMos过温滤波时间", "paraNameEn": "PvMosTempUp filterTime", "paraVarName": "FaultProLimit.pvMosTempUp.filter", "paraMacroName": "ID_PV_MOS_TEMP_UP_FILTER", "paraDescription": "PVMos过温滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "Y"}, {"paraId": "622", "paraNameCn": "ACMos过温上限", "paraNameEn": "AcMosTempUp upLimit", "paraVarName": "FaultProLimit.acMosTempUp.upLimit", "paraMacroName": "ID_AC_MOS_TEMP_UP_UP_LIMIT", "paraDescription": "ACMos过温上限", "paraUnit": "度", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "623", "paraNameCn": "ACMos过温下限", "paraNameEn": "AcMosTempUp dnLimit", "paraVarName": "FaultProLimit.acMosTempUp.dnLimit", "paraMacroName": "ID_AC_MOS_TEMP_UP_DN_LIMIT", "paraDescription": "ACMos过温下限", "paraUnit": "度", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "624", "paraNameCn": "ACMos滤波时间", "paraNameEn": "AcMosTempUp filterTime", "paraVarName": "FaultProLimit.acMosTempUp.filter", "paraMacroName": "ID_AC_MOS_TEMP_UP_FILTER", "paraDescription": "ACMos过温滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "Y"}, {"paraId": "625", "paraNameCn": "绝缘阻抗检测上限", "paraNameEn": "iSO upLimit", "paraVarName": "FaultProLimit.iSO.upLimit", "paraMacroName": "ID_ISO_UP_LIMIT", "paraDescription": "绝缘阻抗上限", "paraUnit": "oHm", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "626", "paraNameCn": "绝缘阻抗检测下限", "paraNameEn": "iSO dnLimit", "paraVarName": "FaultProLimit.iSO.dnLimit", "paraMacroName": "ID_ISO_DN_LIMIT", "paraDescription": "绝缘阻抗下限", "paraUnit": "oHm", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "627", "paraNameCn": "绝缘阻抗检测时间", "paraNameEn": "iSO filterTime", "paraVarName": "FaultProLimit.iSO.filter", "paraMacroName": "ID_ISO_FILTER", "paraDescription": "绝缘阻抗滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "Y"}], "故障记录": [{"paraId": "700", "paraNameCn": "运行状态", "paraNameEn": "Fault Record State", "paraVarName": "FaultRecord.status", "paraMacroName": "ID_FAULT_RECORD_STATUS", "paraDescription": "0:停止 1:预存 2:等待 3:存储 4:恢复 5:读取 6:退出", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "4", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "701", "paraNameCn": "运行命令", "paraNameEn": "Fault Record  Cmd", "paraVarName": "FaultRecord.cmd", "paraMacroName": "ID_FAULT_RECORD_CMD", "paraDescription": "0/停止  1/启动", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "702", "paraNameCn": "复位命令", "paraNameEn": "Fault Record  Reset", "paraVarName": "FaultRecord.resetCmd", "paraMacroName": "ID_FAULT_RECORD_RESET_CMD", "paraDescription": "0/停止  1/启动", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "703", "paraNameCn": "读取命令", "paraNameEn": "Fault Record  SampleFreq", "paraVarName": "FaultRecord.readCmd", "paraMacroName": "ID_FAULT_RECORD_READ_CMD", "paraDescription": "0/停止  1/读取", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "704", "paraNameCn": "读索引", "paraNameEn": "Fault Record  ReadIndex", "paraVarName": "FaultRecord.readIndex", "paraMacroName": "ID_FAULT_RECORD_READ_INDEX", "paraDescription": "读故障索引", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "705", "paraNameCn": "写索引", "paraNameEn": "Fault Record  WriteIndex", "paraVarName": "FaultRecord.writeIndex  ", "paraMacroName": "ID_FAULT_RECORD_WRITE_INDEX", "paraDescription": "写故障索引", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "160", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "Y"}, {"paraId": "706", "paraNameCn": "故障数量", "paraNameEn": "Fault Record  FaultNum", "paraVarName": "FaultRecord.faultNum ", "paraMacroName": "ID_FAULT_RECORD_NUM_INDEX", "paraDescription": "故障数量", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "160", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "Y"}, {"paraId": "707", "paraNameCn": "写状态", "paraNameEn": "Fault Record  Writestate", "paraVarName": "FaultRecord.writeState", "paraMacroName": "ID_FAULT_RECORD_WRITE_STATE", "paraDescription": "0:空闲 1:写数据 2:写控制 3:写结束", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "708", "paraNameCn": "读状态", "paraNameEn": "Fault Record  Readstate", "paraVarName": "FaultRecord.readState", "paraMacroName": "ID_FAULT_RECORD_READ_STATE", "paraDescription": "读状态", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "709", "paraNameCn": "故障时间 年", "paraNameEn": "Fault Record  Year", "paraVarName": "FaultRecordPara.year", "paraMacroName": "ID_FAULT_RECORD_YEAR", "paraDescription": "故障时间 年", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "710", "paraNameCn": "故障时间 月", "paraNameEn": "Fault Record  Month", "paraVarName": "FaultRecordPara.month", "paraMacroName": "ID_FAULT_RECORD_MONTH", "paraDescription": "故障时间 月", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "711", "paraNameCn": "故障时间 日", "paraNameEn": "Fault Record  Day", "paraVarName": "FaultRecordPara.day", "paraMacroName": "ID_FAULT_RECORD_DAY", "paraDescription": "故障时间 日", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "712", "paraNameCn": "故障时间 时", "paraNameEn": "Fault Record  Hour", "paraVarName": "FaultRecordPara.hour", "paraMacroName": "ID_FAULT_RECORD_HOUR", "paraDescription": "故障时间 时", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "713", "paraNameCn": "故障时间 分", "paraNameEn": "Fault Record  Min", "paraVarName": "FaultRecordPara.min", "paraMacroName": "ID_FAULT_RECORD_MIN", "paraDescription": "故障时间 分", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "714", "paraNameCn": "故障时间 秒", "paraNameEn": "Fault Record  Sec", "paraVarName": "FaultRecordPara.sec", "paraMacroName": "ID_FAULT_RECORD_SEC", "paraDescription": "故障时间 秒", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "715", "paraNameCn": "故障码 1", "paraNameEn": "Fault Record Faultnum01", "paraVarName": "FaultRecordPara.faultCode[0]", "paraMacroName": "ID_FAULT_RECORD_CODE_01", "paraDescription": "故障码 1", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "716", "paraNameCn": "故障码 2", "paraNameEn": "Fault Record Faultnum02", "paraVarName": "FaultRecordPara.faultCode[1]", "paraMacroName": "ID_FAULT_RECORD_CODE_02", "paraDescription": "故障码 2", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "717", "paraNameCn": "故障码 3", "paraNameEn": "Fault Record Faultnum03", "paraVarName": "FaultRecordPara.faultCode[2]", "paraMacroName": "ID_FAULT_RECORD_CODE_03", "paraDescription": "故障码 3", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "718", "paraNameCn": "故障码 4", "paraNameEn": "Fault Record Faultnum04", "paraVarName": "FaultRecordPara.faultCode[3]", "paraMacroName": "ID_FAULT_RECORD_CODE_04", "paraDescription": "故障码 4", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "719", "paraNameCn": "故障码 5", "paraNameEn": "Fault Record Faultnum05", "paraVarName": "FaultRecordPara.faultCode[4]", "paraMacroName": "ID_FAULT_RECORD_CODE_05", "paraDescription": "故障码 5", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "720", "paraNameCn": "AC MOS温度", "paraNameEn": "Fault Record AcMosTempreal", "paraVarName": "FaultRecordPara.acMosTempreal", "paraMacroName": "ID_FAULT_RECORD_AC_TEMP", "paraDescription": "AC MOS温度实际值", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "721", "paraNameCn": "PV MOS温度", "paraNameEn": "Fault Record PvMosTempreal", "paraVarName": "FaultRecordPara.pvMosTempreal", "paraMacroName": "ID_FAULT_RECORD_PV_TEMP", "paraDescription": "PV MOS温度实际值", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "722", "paraNameCn": "PV电压", "paraNameEn": "Fault Record PvVol_1_real", "paraVarName": "FaultRecordPara.pvVol_1_real", "paraMacroName": "ID_FAULT_RECORD_PV_V", "paraDescription": "PV电压实际值", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "723", "paraNameCn": "PV电流", "paraNameEn": "Fault Record PvCurr_1_real", "paraVarName": "FaultRecordPara.pvCurr_1_real", "paraMacroName": "ID_FAULT_RECORD_PV_I", "paraDescription": "PV电流实际值", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "724", "paraNameCn": "AC电压有效值", "paraNameEn": "Fault Record AcVolRmsPara", "paraVarName": "FaultRecordPara.acVolRmsPara", "paraMacroName": "ID_FAULT_RECORD_AC_VOLRMS", "paraDescription": "AC电压有效值", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "725", "paraNameCn": "AC电压实际值", "paraNameEn": "Fault Record AcVol_real", "paraVarName": "FaultRecordPara.acVol_real", "paraMacroName": "ID_FAULT_RECORD_AC_VREAL", "paraDescription": "AC电压实际值", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "726", "paraNameCn": "AC电流有效值", "paraNameEn": "Fault Record AcCurrRmsPara", "paraVarName": "FaultRecordPara.acCurrRmsPara", "paraMacroName": "ID_FAULT_RECORD_AC_IRMS", "paraDescription": "AC电流有效值", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "727", "paraNameCn": "AC电流实际值", "paraNameEn": "Fault Record AcCur_real", "paraVarName": "FaultRecordPara.acCur_real", "paraMacroName": "ID_FAULT_RECORD_AC_IREAL", "paraDescription": "AC电流实际值", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}], "故障录波": [{"paraId": "210", "paraNameCn": "运行状态", "paraNameEn": "DataLogger State", "paraVarName": "DataLogger.status", "paraMacroName": "ID_DATALOGGER_STATUS", "paraDescription": "0:停止 1:预存 2:等待 3:触发 4:存储", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "4", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "211", "paraNameCn": "运行命令", "paraNameEn": "DataLogger Cmd", "paraVarName": "DataLogger.cmd", "paraMacroName": "ID_DATALOGGER_CMD", "paraDescription": "0/停止  1/启动", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "212", "paraNameCn": "读取命令", "paraNameEn": "DataLogger ReadCmd", "paraVarName": "DataLogger.readCmd", "paraMacroName": "ID_DATALOGGER_READ_CMD", "paraDescription": "0/停止  1/读取", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "213", "paraNameCn": "读取索引", "paraNameEn": "DataLogger ReadIndex", "paraVarName": "DataLogger.readIndex", "paraMacroName": "ID_DATALOGGER_READ_INDEX", "paraDescription": "null", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "214", "paraNameCn": "复位命令", "paraNameEn": "DataLogger resetCmd", "paraVarName": "DataLogger.resetCmd", "paraMacroName": "ID_DATALOGGER_RESET_CMD", "paraDescription": "null", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "215", "paraNameCn": "写索引", "paraNameEn": "DataLogger WriteIndex", "paraVarName": "DataLogger.writeIndex", "paraMacroName": "ID_DATALOGGER_WRITE_INDEX", "paraDescription": "null", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "10", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "216", "paraNameCn": "录波数量", "paraNameEn": "DataLogger TotalNum", "paraVarName": "DataLogger.totalNum", "paraMacroName": "ID_DATALOGGER_NUM", "paraDescription": "null", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "217", "paraNameCn": "写状态", "paraNameEn": "DataLogger WriteState", "paraVarName": "DataLogger.writeState", "paraMacroName": "ID_DATALOGGER_WRITE_STATE", "paraDescription": "0:空闲 1:写配置 2:写波形 3:写管理数据  4:写结束", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "4", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "218", "paraNameCn": "读状态", "paraNameEn": "DataLogger ReadState", "paraVarName": "DataLogger.readState", "paraMacroName": "ID_DATALOGGER_READ_STATE", "paraDescription": "5:空闲 6:读配置 7:读波形 8:读结束", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "8", "minValue": "5", "defaultValue": "5", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "219", "paraNameCn": "采样频率", "paraNameEn": "DataLogger sampleFreq", "paraVarName": "DataLogger.sampleFreq", "paraMacroName": "ID_DATALOGGER_SAMPL_FREQ", "paraDescription": "null", "paraUnit": "Hz", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "30000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "220", "paraNameCn": "采样间隔", "paraNameEn": "DataLogger sampleRate", "paraVarName": "DataLogger.sampleRate", "paraMacroName": "ID_DATALOGGER_SAMPL_RATE", "paraDescription": "null", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "10000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "221", "paraNameCn": "触发前百分比", "paraNameEn": "DataLogger trigPointPercent", "paraVarName": "DataLogger.trigPointPercent", "paraMacroName": "ID_DATALOGGER_TRIG_POINT_PER", "paraDescription": "null", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "222", "paraNameCn": "触发点数", "paraNameEn": "DataLogger trigPoint", "paraVarName": "DataLogger.trigPoint", "paraMacroName": "ID_DATALOGGER_TRIG_POINT", "paraDescription": "null", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "223", "paraNameCn": "强制触发", "paraNameEn": "DataLogger forceTripCmd", "paraVarName": "DataLogger.forceTrigCmd", "paraMacroName": "ID_DATALOGGER_FORCE_CMD", "paraDescription": "0/停止  1/强制触发", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "224", "paraNameCn": "触发类型", "paraNameEn": "DataLogger trigType", "paraVarName": "DataLogger.trigType", "paraMacroName": "ID_DATALOGGER_TRIP_TYPE", "paraDescription": "0:模拟量上升沿 1:模拟量下降沿 2:数字量上升沿 3:数字量下降沿", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "225", "paraNameCn": "触发ID ", "paraNameEn": "DataLogger trigId", "paraVarName": "DataLogger.trigId", "paraMacroName": "ID_DATALOGGER_TRIP_ID", "paraDescription": "触发ID ", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "226", "paraNameCn": "触发位", "paraNameEn": "DataLogger trigBit", "paraVarName": "DataLogger.trigBit", "paraMacroName": "ID_DATALOGGER_TRIP_BIT", "paraDescription": "触发位", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "15", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "227", "paraNameCn": "触发门限", "paraNameEn": "DataLogger trigLimit", "paraVarName": "DataLogger.trigLimit", "paraMacroName": "ID_DATALOGGER_TRIP_LIMIT", "paraDescription": "null", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "228", "paraNameCn": "通道0", "paraNameEn": "DataLogger Channel0", "paraVarName": "DataLogger.channelId[0]", "paraMacroName": "ID_DATALOGGER_CHANNLE_0", "paraDescription": "通道0", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "229", "paraNameCn": "通道1", "paraNameEn": "DataLogger Channel1", "paraVarName": "DataLogger.channelId[1]", "paraMacroName": "ID_DATALOGGER_CHANNLE_1", "paraDescription": "通道1", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "230", "paraNameCn": "通道2", "paraNameEn": "DataLogger Channel2", "paraVarName": "DataLogger.channelId[2]", "paraMacroName": "ID_DATALOGGER_CHANNLE_2", "paraDescription": "通道2", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "231", "paraNameCn": "通道3", "paraNameEn": "DataLogger Channel3", "paraVarName": "DataLogger.channelId[3]", "paraMacroName": "ID_DATALOGGER_CHANNLE_3", "paraDescription": "通道3", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "232", "paraNameCn": "通道4", "paraNameEn": "DataLogger Channel4", "paraVarName": "DataLogger.channelId[4]", "paraMacroName": "ID_DATALOGGER_CHANNLE_4", "paraDescription": "通道4", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "233", "paraNameCn": "通道5", "paraNameEn": "DataLogger Channel5", "paraVarName": "DataLogger.channelId[5]", "paraMacroName": "ID_DATALOGGER_CHANNLE_5", "paraDescription": "通道5", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}], "环路控制1": [{"paraId": "30", "paraNameCn": "发波使能", "paraNameEn": "PwmEnable", "paraVarName": "PwmEnable", "paraMacroName": "ID_PWM_ENABLE", "paraDescription": "发波使能", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "31", "paraNameCn": "关机标志", "paraNameEn": "PwmShutFlag", "paraVarName": "PwmShutFlag", "paraMacroName": "ID_PWM_SHUT_FLAG", "paraDescription": "关机标志", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "32", "paraNameCn": "模式", "paraNameEn": "PwmMode", "paraVarName": "PwmMode", "paraMacroName": "ID_PWM_MODE", "paraDescription": "0：封波模式 1：开环正弦 2：开环馒头波 3：开环馒头波定角度 4：独立正半周测试 5：独立负半周测试 6：充电测试 7：开环并网 8：闭环并网(电流) 9：闭环并网(电流+频率)", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "10", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "33", "paraNameCn": "发波状态", "paraNameEn": "PwmAcState", "paraVarName": "PwmAcState", "paraMacroName": "ID_PWM_AC_STATE", "paraDescription": "0:初始 1:首次N2P 2:N2P 3:P 4:P2N 5:N 6:默认", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "10", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "34", "paraNameCn": "频率给定", "paraNameEn": "PwmFreq", "paraVarName": "PwmFreq", "paraMacroName": "ID_PWM_FREQ", "paraDescription": "频率给定", "paraUnit": "kHz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "2500", "minValue": "50", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "35", "paraNameCn": "周期值", "paraNameEn": "PwmPrd", "paraVarName": "PwmPrd", "paraMacroName": "ID_PWM_PRD", "paraDescription": "周期值", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "36", "paraNameCn": "充电频率给定", "paraNameEn": "PwmChargeFreq", "paraVarName": "PwmChargeFreq", "paraMacroName": "ID_PWM_CHG_FREQ", "paraDescription": "充电频率给定", "paraUnit": "kHz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "2500", "minValue": "50", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "37", "paraNameCn": "充电发波计数设置", "paraNameEn": "PwmCntSet1", "paraVarName": "PwmCntSet1", "paraMacroName": "ID_PWM_CNT_SET_1", "paraDescription": "充电发波计数设置", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "20000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "38", "paraNameCn": "放电发波计数设置", "paraNameEn": "PwmCntSet2", "paraVarName": "PwmCntSet2", "paraMacroName": "ID_PWM_CNT_SET_2", "paraDescription": "放电发波计数设置", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "20000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "39", "paraNameCn": "占空比设置", "paraNameEn": "PwmDuty", "paraVarName": "PwmDuty", "paraMacroName": "ID_PWM_DUTY", "paraDescription": "占空比设置", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "40", "paraNameCn": "移相值1", "paraNameEn": "PwmPhase1", "paraVarName": "PwmPhase1", "paraMacroName": "ID_PWM_PHASE_1", "paraDescription": "移相值1内移", "paraUnit": "内移", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "60000", "minValue": "48", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "41", "paraNameCn": "移相值2", "paraNameEn": "PwmPhase2", "paraVarName": "PwmPhase2", "paraMacroName": "ID_PWM_PHASE_2", "paraDescription": "移相值2外移", "paraUnit": "外移", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "60000", "minValue": "48", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "42", "paraNameCn": "死区值1", "paraNameEn": "PwmDed1", "paraVarName": "PwmDeadTime1", "paraMacroName": "ID_PWM_DEAD_1", "paraDescription": "死区值", "paraUnit": "1000/2720ns", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "60000", "minValue": "100", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "43", "paraNameCn": "死区值2", "paraNameEn": "PwmDed2", "paraVarName": "PwmDeadTime2", "paraMacroName": "ID_PWM_DEAD_2", "paraDescription": "死区值", "paraUnit": "1000/2720ns", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "60000", "minValue": "100", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "44", "paraNameCn": "并网电流给定", "paraNameEn": "iacSet", "paraVarName": "CtrlPara.iacSet", "paraMacroName": "ID_PWM_CTRL_IAC_SET", "paraDescription": "并网电流给定(有效值)", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "3", "minValue": "-3", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "45", "paraNameCn": "并网电流给定实际", "paraNameEn": "iacSetRef", "paraVarName": "CtrlPara.iacSetRef", "paraMacroName": "ID_PWM_CTRL_IAC_SET_REF", "paraDescription": "并网电流给定实际", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "3", "minValue": "-3", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "46", "paraNameCn": "电流最大值", "paraNameEn": "iacMax", "paraVarName": "CtrlPara.iacMax", "paraMacroName": "ID_PWM_CTRL_IAC_MAX", "paraDescription": "电流最大值", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "5", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "47", "paraNameCn": "电流最小值", "paraNameEn": "iacMin", "paraVarName": "CtrlPara.iacMin", "paraMacroName": "ID_PWM_CTRL_IAC_MIN", "paraDescription": "电流最小值", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "5", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "48", "paraNameCn": "电压最大值", "paraNameEn": "vacMax", "paraVarName": "CtrlPara.vacMax", "paraMacroName": "ID_PWM_CTRL_VAC_MAX", "paraDescription": "电压最大值", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "400", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "49", "paraNameCn": "电压最小值", "paraNameEn": "vacMin", "paraVarName": "CtrlPara.vacMin", "paraMacroName": "ID_PWM_CTRL_VAC_MIN", "paraDescription": "电压最小值", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "400", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "50", "paraNameCn": "pv电压输入", "paraNameEn": "vPv in", "paraVarName": "CtrlPara.vPv.in", "paraMacroName": "ID_PWM_CTRL_V_PV_IN", "paraDescription": "pv电压输入", "paraUnit": "V", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "3", "minValue": "-3", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "51", "paraNameCn": "pv电压滤波系数", "paraNameEn": "vPv coeff", "paraVarName": "CtrlPara.vPv.filterCoeff", "paraMacroName": "ID_PWM_CTRL_V_PV_COEFF", "paraDescription": "pv电压滤波系数", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "52", "paraNameCn": "pv电压滤波输出", "paraNameEn": "vPv out", "paraVarName": "CtrlPara.vPv.filterOut", "paraMacroName": "ID_PWM_CTRL_V_PV_OUT", "paraDescription": "pv电压滤波输出", "paraUnit": "V", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "3", "minValue": "-3", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "53", "paraNameCn": "pv电流输入", "paraNameEn": "iPv in", "paraVarName": "CtrlPara.iPv.in", "paraMacroName": "ID_PWM_CTRL_I_PV_IN", "paraDescription": "pv电流输入", "paraUnit": "A", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "3", "minValue": "-3", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "54", "paraNameCn": "pv电流滤波系数", "paraNameEn": "iPv coeff", "paraVarName": "CtrlPara.iPv.filterCoeff", "paraMacroName": "ID_PWM_CTRL_I_PV_COEFF", "paraDescription": "pv电流滤波系数", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "55", "paraNameCn": "pv电流滤波输出", "paraNameEn": "iPv out", "paraVarName": "CtrlPara.iPv.filterOut", "paraMacroName": "ID_PWM_CTRL_I_PV_OUT", "paraDescription": "pv电流滤波输出", "paraUnit": "A", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "3", "minValue": "-3", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "56", "paraNameCn": "ac电压输入", "paraNameEn": "vAc in", "paraVarName": "CtrlPara.vAc.in", "paraMacroName": "ID_PWM_CTRL_V_AC_IN", "paraDescription": "ac电压输入", "paraUnit": "V", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "3", "minValue": "-3", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "57", "paraNameCn": "ac电压滤波系数", "paraNameEn": "vAc coeff", "paraVarName": "CtrlPara.vAc.filterCoeff", "paraMacroName": "ID_PWM_CTRL_V_AC_COEFF", "paraDescription": "ac电压滤波系数", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "58", "paraNameCn": "ac电压滤波输出", "paraNameEn": "vAc out", "paraVarName": "CtrlPara.vAc.filterOut", "paraMacroName": "ID_PWM_CTRL_V_AC_OUT", "paraDescription": "ac电压滤波输出", "paraUnit": "V", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "3", "minValue": "-3", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "59", "paraNameCn": "ac电流输入", "paraNameEn": "iAc in", "paraVarName": "CtrlPara.iAc.in", "paraMacroName": "ID_PWM_CTRL_I_AC_IN", "paraDescription": "ac电流输入", "paraUnit": "A", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "3", "minValue": "-3", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "60", "paraNameCn": "ac电流滤波系数", "paraNameEn": "iAc coeff", "paraVarName": "CtrlPara.iAc.filterCoeff", "paraMacroName": "ID_PWM_CTRL_I_AC_COEFF", "paraDescription": "ac电流滤波系数", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "61", "paraNameCn": "ac电流滤波输出", "paraNameEn": "iAc out", "paraVarName": "CtrlPara.iAc.filterOut", "paraMacroName": "ID_PWM_CTRL_I_AC_OUT", "paraDescription": "ac电流滤波输出", "paraUnit": "A", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "3", "minValue": "-3", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "62", "paraNameCn": "谐振电流输入", "paraNameEn": "iLr in", "paraVarName": "CtrlPara.iLr.in", "paraMacroName": "ID_PWM_CTRL_I_LR_IN", "paraDescription": "谐振电流输入", "paraUnit": "A", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "3", "minValue": "-3", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "63", "paraNameCn": "谐振电流滤波系数", "paraNameEn": "iLr coeff", "paraVarName": "CtrlPara.iLr.filter<PERSON>oeff", "paraMacroName": "ID_PWM_CTRL_I_LR_COEFF", "paraDescription": "谐振电流滤波系数", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "64", "paraNameCn": "谐振电流滤波输出", "paraNameEn": "iLr out", "paraVarName": "CtrlPara.iLr.filterOut", "paraMacroName": "ID_PWM_CTRL_I_LR_OUT", "paraDescription": "谐振电流滤波输出", "paraUnit": "A", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "3", "minValue": "-3", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "65", "paraNameCn": "ac角度设置(角度)", "paraNameEn": "actheta_set", "paraVarName": "CtrlPara.acThetaSet", "paraMacroName": "ID_PWM_CTRL_AC_THETA_SET", "paraDescription": "ac角度设置(角度)", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "180", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "66", "paraNameCn": "ac电压角度", "paraNameEn": "CtrlPara acVolTheta", "paraVarName": "CtrlPara.acVolTheta", "paraMacroName": "ID_PWM_CTRL_AC_VOL_THETA", "paraDescription": "ac电压角度", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "7", "minValue": "-7", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "67", "paraNameCn": "sin ac电压", "paraNameEn": "CtrlPara sinAcVolTheta", "paraVarName": "CtrlPara.sinAcVolTheta", "paraMacroName": "ID_PWM_CTRL_AC_VOL_SIN", "paraDescription": "sin ac电压", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "-1", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "68", "paraNameCn": "sin ac电压 prev", "paraNameEn": "CtrlPara sinAcVolThetaPrev", "paraVarName": "CtrlPara.sinAcVolThetaPrev", "paraMacroName": "ID_PWM_CTRL_AC_VOL_SIN_PREV", "paraDescription": "sin ac电压 prev", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "-1", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "69", "paraNameCn": "ac电流角度", "paraNameEn": "CtrlPara acCurrTheta", "paraVarName": "CtrlPara.acCurrTheta", "paraMacroName": "ID_PWM_CTRL_AC_CURR_THETA", "paraDescription": "ac电流角度", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "7", "minValue": "-7", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "70", "paraNameCn": "sin ac电流", "paraNameEn": "CtrlPara sinAcCurrTheta", "paraVarName": "CtrlPara.sinAcCurrTheta", "paraMacroName": "ID_PWM_CTRL_AC_CURR_SIN", "paraDescription": "sin ac电流", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "-1", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "71", "paraNameCn": "sin ac电流 prev", "paraNameEn": "CtrlPara sinAcCurrThetaPrev", "paraVarName": "CtrlPara.sinAcCurrThetaPrev", "paraMacroName": "ID_PWM_CTRL_AC_CURR_SIN_PREV", "paraDescription": "sin ac电流 prev", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "-1", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "72", "paraNameCn": "X补偿", "paraNameEn": "CtrlPara xComp", "paraVarName": "CtrlPara.xComp", "paraMacroName": "ID_PWM_CTRL_X_COMP", "paraDescription": "X补偿(弧度)", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "-1", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "73", "paraNameCn": "虚拟vAc", "paraNameEn": "CtrlPara vAcVirtual", "paraVarName": "CtrlPara.vAcVirtual", "paraMacroName": "ID_PWM_CTRL_V_AC_VR", "paraDescription": "虚拟vAc", "paraUnit": "V", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "3", "minValue": "-3", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "74", "paraNameCn": "AC电压差值", "paraNameEn": "CtrlPara vAc Diff", "paraVarName": "CtrlPara.vAcDiff", "paraMacroName": "ID_PWM_CTRL_V_AC_DIFF", "paraDescription": "AC电压差值", "paraUnit": "V", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "3", "minValue": "-3", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "75", "paraNameCn": "外移补偿", "paraNameEn": "alphaAdjust", "paraVarName": "CtrlPara.alphaAdjust", "paraMacroName": "ID_PWM_CTRL_ALPHA_ADJUST", "paraDescription": "外移补偿", "paraUnit": "ns", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3000", "minValue": "-3000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "76", "paraNameCn": "内移补偿", "paraNameEn": "thetaAdjust", "paraVarName": "CtrlPara.thetaAdjust", "paraMacroName": "ID_PWM_CTRL_THETA_ADJUST", "paraDescription": "内移补偿", "paraUnit": "ns", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3000", "minValue": "-3000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "77", "paraNameCn": "外移", "paraNameEn": "alpha", "paraVarName": "CtrlPara.alpha", "paraMacroName": "ID_PWM_CTRL_ALPHA", "paraDescription": "外移", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "3", "minValue": "-3", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "78", "paraNameCn": "外移百分比", "paraNameEn": "alphaPercnt", "paraVarName": "CtrlPara.alphaPercnt", "paraMacroName": "ID_PWM_CTRL_ALPHA_PER", "paraDescription": "外移百分比", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "3", "minValue": "-3", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "79", "paraNameCn": "内移", "paraNameEn": "theta", "paraVarName": "CtrlPara.theta", "paraMacroName": "ID_PWM_CTRL_THETA", "paraDescription": "内移", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "3", "minValue": "-3", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "80", "paraNameCn": "内移百分比", "paraNameEn": "thetaPercnt", "paraVarName": "CtrlPara.thetaPercnt", "paraMacroName": "ID_PWM_CTRL_THETA_PER", "paraDescription": "内移百分比", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "3", "minValue": "-3", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "81", "paraNameCn": "fai值", "paraNameEn": "fai", "paraVarName": "CtrlPara.fai", "paraMacroName": "ID_PWM_CTRL_FAI", "paraDescription": "fai值", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "3", "minValue": "-3", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "82", "paraNameCn": "功率因数PF", "paraNameEn": "Test4", "paraVarName": "CtrlPara.acPF", "paraMacroName": "ID_PWM_CTRL_AC_PF", "paraDescription": "功率因数PF", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "-1", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "83", "paraNameCn": "相位差Phi", "paraNameEn": "Test5", "paraVarName": "CtrlPara.acPhi", "paraMacroName": "ID_PWM_CTRL_AC_FAI", "paraDescription": "相位差Phi", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "10", "minValue": "-10", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "84", "paraNameCn": "过零校准", "paraNameEn": "Test11", "paraVarName": "CtrlPara.acVolThetaCzeroAdj", "paraMacroName": "ID_PWM_CTRL_AC_ZERO_ADJ", "paraDescription": "测试参数AdjVz", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "-1", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "85", "paraNameCn": "输出频率最大值", "paraNameEn": "fs_max", "paraVarName": "CtrlPara.fs_max", "paraMacroName": "ID_PWM_CTRL_FS_MAX", "paraDescription": "输出频率最大值", "paraUnit": "Hz", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "3", "minValue": "-3", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "86", "paraNameCn": "输出频率最小值", "paraNameEn": "fs_min", "paraVarName": "CtrlPara.fs_min", "paraMacroName": "ID_PWM_CTRL_FS_MIN", "paraDescription": "输出频率最小值", "paraUnit": "Hz", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "3", "minValue": "-3", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "87", "paraNameCn": "输出频率", "paraNameEn": "fs", "paraVarName": "CtrlPara.fs", "paraMacroName": "ID_PWM_CTRL_FS", "paraDescription": "输出频率", "paraUnit": "Hz", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "3", "minValue": "-3", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "88", "paraNameCn": "锁相theta", "paraNameEn": "theta", "paraVarName": "AcCurrSpll.theta", "paraMacroName": "ID_PWM_PLL_THETA", "paraDescription": "ac角度(弧度)", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "3", "minValue": "-3", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "89", "paraNameCn": "锁相u_d", "paraNameEn": "u_d", "paraVarName": "AcCurrSpll.u_D[0]", "paraMacroName": "ID_PWM_PLL_UD", "paraDescription": "锁相u_d", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "3", "minValue": "-3", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "90", "paraNameCn": "锁相u_q", "paraNameEn": "u_q", "paraVarName": "AcCurrSpll.u_Q[0]", "paraMacroName": "ID_PWM_PLL_UQ", "paraDescription": "锁相u_q", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "3", "minValue": "-3", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "91", "paraNameCn": "锁相频率", "paraNameEn": "fs", "paraVarName": "AcCurrSpll.fo", "paraMacroName": "ID_PWM_PLL_FREQ", "paraDescription": "锁相频率", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "3", "minValue": "-3", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "92", "paraNameCn": "锁相有效值", "paraNameEn": "rms", "paraVarName": "AcVolSpllRms", "paraMacroName": "ID_AC_VOL_PLL_RMS", "paraDescription": "锁相有效值", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "3", "minValue": "-3", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}], "环路控制2": [{"paraId": "100", "paraNameCn": "lib版<PERSON>", "paraNameEn": "lib <PERSON>", "paraVarName": "LibVersion", "paraMacroName": "ID_LIB_VER", "paraDescription": "lib版<PERSON>", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "255", "minValue": "1", "defaultValue": "1", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "101", "paraNameCn": "死区时间(控制)", "paraNameEn": "tds", "paraVarName": "CtrlPara.tds", "paraMacroName": "ID_CTRL_TDS", "paraDescription": "死区时间(控制)", "paraUnit": "ns", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "5000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "102", "paraNameCn": "死区时间(PV)", "paraNameEn": "PV tds", "paraVarName": "CtrlPara.pvTds", "paraMacroName": "ID_CTRL_PV_TDS", "paraDescription": "死区时间(PV)", "paraUnit": "ns", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "5000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "103", "paraNameCn": "死区时间(PV 正负半周)", "paraNameEn": "PV tds0", "paraVarName": "CtrlPara.pvTds0", "paraMacroName": "ID_CTRL_PV_TDS_0", "paraDescription": "死区时间(PV 正负半周)", "paraUnit": "ns", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "5000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "104", "paraNameCn": "死区时间(PV 同开同关)", "paraNameEn": "PV tds1", "paraVarName": "CtrlPara.pvTds1", "paraMacroName": "ID_CTRL_PV_TDS_1", "paraDescription": "死区时间(PV 同开同关)", "paraUnit": "ns", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "5000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "105", "paraNameCn": "死区时间(AC)", "paraNameEn": "AC tds", "paraVarName": "CtrlPara.acTds", "paraMacroName": "ID_CTRL_AC_TDS", "paraDescription": "死区时间(AC)", "paraUnit": "ns", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "5000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "106", "paraNameCn": "并网电流给定", "paraNameEn": "AcCurrSet", "paraVarName": "AcCurrSet", "paraMacroName": "ID_AC_CURR_SET", "paraDescription": "并网电流给定   有效值", "paraUnit": "A", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "107", "paraNameCn": "并网电压给定", "paraNameEn": "AcVolSet", "paraVarName": "AcVolSet", "paraMacroName": "ID_AC_VOL_SET", "paraDescription": "并网电压给定   有效值", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "240", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "108", "paraNameCn": "PV电压给定", "paraNameEn": "PvVolSet", "paraVarName": "PvVolSet", "paraMacroName": "ID_PV_VOL_SET", "paraDescription": "PV电压给定", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "55", "minValue": "12", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "109", "paraNameCn": "实际增益", "paraNameEn": "m", "paraVarName": "CtrlPara.m", "paraMacroName": "ID_PWM_CTRL_M", "paraDescription": "实际增益", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "55", "minValue": "30", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "110", "paraNameCn": "k alpha 1", "paraNameEn": "k alpha 1", "paraVarName": "CtrlPara.k_alpha_1", "paraMacroName": "ID_PWM_CTRL_K_A1", "paraDescription": "k alpha 1", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000", "minValue": "-1000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "111", "paraNameCn": "k beta 2", "paraNameEn": "k beta 2", "paraVarName": "CtrlPara.k_beta_2", "paraMacroName": "ID_PWM_CTRL_K_B2", "paraDescription": "k beta 2", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000", "minValue": "-1000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "112", "paraNameCn": "m_alpha 0", "paraNameEn": "m_alpha 0", "paraVarName": "CtrlPara.m_alpha_0", "paraMacroName": "ID_PWM_CTRL_M_A0", "paraDescription": "m_alpha 0", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000", "minValue": "-1000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "113", "paraNameCn": "m_beta_s", "paraNameEn": "m_beta_s", "paraVarName": "CtrlPara.m_beta_s", "paraMacroName": "ID_PWM_CTRL_M_BS", "paraDescription": "m_beta_s", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000", "minValue": "-1000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "114", "paraNameCn": "angle_alpha_s", "paraNameEn": "angle_alpha_s", "paraVarName": "CtrlPara.angle_alpha_s", "paraMacroName": "ID_PWM_CTRL_ANGLE_AS", "paraDescription": "angle_alpha_s", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000", "minValue": "-1000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "115", "paraNameCn": "vol beta s", "paraNameEn": "vol beta s", "paraVarName": "CtrlPara.vol_beta_s", "paraMacroName": "ID_PWM_CTRL_V_BS", "paraDescription": "vol beta s", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000", "minValue": "-1000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "116", "paraNameCn": "angle beta s", "paraNameEn": "angle beta s", "paraVarName": "CtrlPara.angle_beta_s", "paraMacroName": "ID_PWM_CTRL_ANGLE_BS", "paraDescription": "angle beta s", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000", "minValue": "-1000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "117", "paraNameCn": "acTheta_zero", "paraNameEn": "acTheta zero", "paraVarName": "CtrlPara.acTheta_zero", "paraMacroName": "ID_PWM_CTRL_AC_THETA_ZERO", "paraDescription": "过零保持角度(弧度)", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "118", "paraNameCn": "VolBeta v0", "paraNameEn": "VolBeta v0", "paraVarName": "CtrlVolBetaPara.v0", "paraMacroName": "ID_PWM_CTRL_VOL_BETA_V0", "paraDescription": "VolBeta v0", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "500", "minValue": "-500", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "119", "paraNameCn": "VolBeta v1", "paraNameEn": "VolBeta v1", "paraVarName": "CtrlVolBetaPara.v1", "paraMacroName": "ID_PWM_CTRL_VOL_BETA_V1", "paraDescription": "VolBeta v1", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "500", "minValue": "-500", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "120", "paraNameCn": "VolBeta v2", "paraNameEn": "VolBeta v2", "paraVarName": "CtrlVolBetaPara.v2", "paraMacroName": "ID_PWM_CTRL_VOL_BETA_V2", "paraDescription": "VolBeta v2", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "500", "minValue": "-500", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "121", "paraNameCn": "VolBeta v3", "paraNameEn": "VolBeta v3", "paraVarName": "CtrlVolBetaPara.v3", "paraMacroName": "ID_PWM_CTRL_VOL_BETA_V3", "paraDescription": "VolBeta v3", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "500", "minValue": "-500", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "122", "paraNameCn": "VolBeta v4", "paraNameEn": "VolBeta v4", "paraVarName": "CtrlVolBetaPara.v4", "paraMacroName": "ID_PWM_CTRL_VOL_BETA_V4", "paraDescription": "VolBeta v4", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "500", "minValue": "-500", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "123", "paraNameCn": "AngleBeta v0", "paraNameEn": "AngleBeta v0", "paraVarName": "CtrlAngleBetaPara.v0", "paraMacroName": "ID_PWM_CTRL_ANGLE_BETA_V0", "paraDescription": "AngleBeta v0", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "500", "minValue": "-500", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "124", "paraNameCn": "AngleBeta v1", "paraNameEn": "AngleBeta v1", "paraVarName": "CtrlAngleBetaPara.v1", "paraMacroName": "ID_PWM_CTRL_ANGLE_BETA_V1", "paraDescription": "AngleBeta v1", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "500", "minValue": "-500", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "125", "paraNameCn": "AngleBeta v2", "paraNameEn": "AngleBeta v2", "paraVarName": "CtrlAngleBetaPara.v2", "paraMacroName": "ID_PWM_CTRL_ANGLE_BETA_V2", "paraDescription": "AngleBeta v2", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "500", "minValue": "-500", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "126", "paraNameCn": "AngleBeta v3", "paraNameEn": "AngleBeta v3", "paraVarName": "CtrlAngleBetaPara.v3", "paraMacroName": "ID_PWM_CTRL_ANGLE_BETA_V3", "paraDescription": "AngleBeta v3", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "500", "minValue": "-500", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "127", "paraNameCn": "AngleBeta v4", "paraNameEn": "AngleBeta v4", "paraVarName": "CtrlAngleBetaPara.v4", "paraMacroName": "ID_PWM_CTRL_ANGLE_BETA_V4", "paraDescription": "AngleBeta v4", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "500", "minValue": "-500", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "128", "paraNameCn": "AngleBeta v5", "paraNameEn": "AngleBeta v5", "paraVarName": "CtrlAngleBetaPara.v5", "paraMacroName": "ID_PWM_CTRL_ANGLE_BETA_V5", "paraDescription": "AngleBeta v5", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "500", "minValue": "-500", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "129", "paraNameCn": "AngleBeta v1a", "paraNameEn": "AngleBeta v1a", "paraVarName": "CtrlAngleBetaPara.v1_a", "paraMacroName": "ID_PWM_CTRL_ANGLE_BETA_V1_A", "paraDescription": "AngleBeta v1a", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "500", "minValue": "-500", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "130", "paraNameCn": "AngleBeta v1b", "paraNameEn": "AngleBeta v1b", "paraVarName": "CtrlAngleBetaPara.v1_b", "paraMacroName": "ID_PWM_CTRL_ANGLE_BETA_V1_B", "paraDescription": "AngleBeta v1b", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "500", "minValue": "-500", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "131", "paraNameCn": "AngleBeta v2a", "paraNameEn": "AngleBeta v2a", "paraVarName": "CtrlAngleBetaPara.v2_a", "paraMacroName": "ID_PWM_CTRL_ANGLE_BETA_V2_A", "paraDescription": "AngleBeta v2a", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "500", "minValue": "-500", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "132", "paraNameCn": "AngleBeta v2b", "paraNameEn": "AngleBeta v2b", "paraVarName": "CtrlAngleBetaPara.v2_b", "paraMacroName": "ID_PWM_CTRL_ANGLE_BETA_V2_B", "paraDescription": "AngleBeta v2b", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "500", "minValue": "-500", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "133", "paraNameCn": "AngleBeta v3a", "paraNameEn": "AngleBeta v3a", "paraVarName": "CtrlAngleBetaPara.v3_a", "paraMacroName": "ID_PWM_CTRL_ANGLE_BETA_V3_A", "paraDescription": "AngleBeta v3a", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "500", "minValue": "-500", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "134", "paraNameCn": "AngleBeta v3b", "paraNameEn": "AngleBeta v3b", "paraVarName": "CtrlAngleBetaPara.v3_b", "paraMacroName": "ID_PWM_CTRL_ANGLE_BETA_V3_B", "paraDescription": "AngleBeta v3b", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "500", "minValue": "-500", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "135", "paraNameCn": "AngleBeta v4a", "paraNameEn": "AngleBeta v4a", "paraVarName": "CtrlAngleBetaPara.v4_a", "paraMacroName": "ID_PWM_CTRL_ANGLE_BETA_V4_A", "paraDescription": "AngleBeta v4a", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "500", "minValue": "-500", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "136", "paraNameCn": "AngleBeta v4b", "paraNameEn": "AngleBeta v4b", "paraVarName": "CtrlAngleBetaPara.v4_b", "paraMacroName": "ID_PWM_CTRL_ANGLE_BETA_V4_B", "paraDescription": "AngleBeta v4b", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "500", "minValue": "-500", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "137", "paraNameCn": "AngleBeta v5a", "paraNameEn": "AngleBeta v5a", "paraVarName": "CtrlAngleBetaPara.v5_a", "paraMacroName": "ID_PWM_CTRL_ANGLE_BETA_V5_A", "paraDescription": "AngleBeta v5a", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "500", "minValue": "-500", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "138", "paraNameCn": "AngleBeta v5b", "paraNameEn": "AngleBeta v5b", "paraVarName": "CtrlAngleBetaPara.v5_b", "paraMacroName": "ID_PWM_CTRL_ANGLE_BETA_V5_B", "paraDescription": "AngleBeta v5b", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "500", "minValue": "-500", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "139", "paraNameCn": "Kbeta v0", "paraNameEn": "Kbeta v0", "paraVarName": "CtrlKbetaPara.v0", "paraMacroName": "ID_PWM_CTRL_K_BETA_V0", "paraDescription": "Kbeta v0", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "500", "minValue": "-500", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "140", "paraNameCn": "Kbeta v1", "paraNameEn": "Kbeta v1", "paraVarName": "CtrlKbetaPara.v1", "paraMacroName": "ID_PWM_CTRL_K_BETA_V1", "paraDescription": "Kbeta v1", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "500", "minValue": "-500", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "141", "paraNameCn": "Kbeta v1a", "paraNameEn": "Kbeta v1a", "paraVarName": "CtrlKbetaPara.v1_a", "paraMacroName": "ID_PWM_CTRL_K_BETA_V1_A", "paraDescription": "Kbeta v1a", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "500", "minValue": "-500", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "142", "paraNameCn": "Kbeta v1b", "paraNameEn": "Kbeta v1b", "paraVarName": "CtrlKbetaPara.v1_b", "paraMacroName": "ID_PWM_CTRL_K_BETA_V1_B", "paraDescription": "Kbeta v1b", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "500", "minValue": "-500", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "143", "paraNameCn": "ThetaZero v0", "paraNameEn": "ThetaZero v0", "paraVarName": "CtrlThetaZeroPara.v0", "paraMacroName": "ID_PWM_CTRL_THETA_ZERO_V0", "paraDescription": "ThetaZero v0", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "500", "minValue": "-500", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "144", "paraNameCn": "ThetaZero v1", "paraNameEn": "ThetaZero v1", "paraVarName": "CtrlThetaZeroPara.v1", "paraMacroName": "ID_PWM_CTRL_THETA_ZERO_V1", "paraDescription": "ThetaZero v1", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "500", "minValue": "-500", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "145", "paraNameCn": "kbetaM", "paraNameEn": "kbetaM", "paraVarName": "CtrlPara.kbetaM", "paraMacroName": "ID_PWM_TEST_F32_13", "paraDescription": "kbetaM", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "10", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "146", "paraNameCn": "kbetaI", "paraNameEn": "kbetaI", "paraVarName": "CtrlPara.kbetaI", "paraMacroName": "ID_PWM_TEST_F32_14", "paraDescription": "kbetaI", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "10", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}], "环路控制3": [{"paraId": "150", "paraNameCn": "TIM A周期", "paraNameEn": "<PERSON>", "paraVarName": "TimA.period", "paraMacroName": "ID_PWM_PERIOD_A", "paraDescription": "TIM A周期", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "151", "paraNameCn": "TIM A比较器1", "paraNameEn": "Tim A Cmp0", "paraVarName": "TimA.cmp[0]", "paraMacroName": "ID_PWM_CMP_0_A", "paraDescription": "TIM A比较器1", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "152", "paraNameCn": "TIM A比较器2", "paraNameEn": "Tim A Cmp1", "paraVarName": "TimA.cmp[1]", "paraMacroName": "ID_PWM_CMP_1_A", "paraDescription": "TIM A比较器2", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "153", "paraNameCn": "TIM A比较器3", "paraNameEn": "Tim A Cmp2", "paraVarName": "TimA.cmp[2]", "paraMacroName": "ID_PWM_CMP_2_A", "paraDescription": "TIM A比较器3", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "154", "paraNameCn": "TIM A比较器4", "paraNameEn": "Tim A Cmp3", "paraVarName": "TimA.cmp[3]", "paraMacroName": "ID_PWM_CMP_3_A", "paraDescription": "TIM A比较器4", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "155", "paraNameCn": "TIM B比较器1", "paraNameEn": "Tim B Cmp0", "paraVarName": "TimB.cmp[0]", "paraMacroName": "ID_PWM_CMP_0_B", "paraDescription": "TIM B比较器1", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "156", "paraNameCn": "TIM B比较器2", "paraNameEn": "<PERSON> B Cmp1", "paraVarName": "TimB.cmp[1]", "paraMacroName": "ID_PWM_CMP_1_B", "paraDescription": "TIM B比较器2", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "157", "paraNameCn": "TIM B比较器3", "paraNameEn": "Tim B Cmp2", "paraVarName": "TimB.cmp[2]", "paraMacroName": "ID_PWM_CMP_2_B", "paraDescription": "TIM B比较器3", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "158", "paraNameCn": "TIM B比较器4", "paraNameEn": "Tim B Cmp3", "paraVarName": "Tim<PERSON><PERSON>cmp[3]", "paraMacroName": "ID_PWM_CMP_3_B", "paraDescription": "TIM B比较器4", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "159", "paraNameCn": "TIM E比较器1", "paraNameEn": "Tim <PERSON> Cmp0", "paraVarName": "TimE.cmp[0]", "paraMacroName": "ID_PWM_CMP_0_E", "paraDescription": "TIM E比较器1", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "160", "paraNameCn": "TIM E比较器2", "paraNameEn": "<PERSON> Cmp1", "paraVarName": "TimE.cmp[1]", "paraMacroName": "ID_PWM_CMP_1_E", "paraDescription": "TIM E比较器2", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "161", "paraNameCn": "TIM E比较器3", "paraNameEn": "Tim <PERSON> Cmp2", "paraVarName": "TimE.cmp[2]", "paraMacroName": "ID_PWM_CMP_2_E", "paraDescription": "TIM E比较器3", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "162", "paraNameCn": "TIM E比较器4", "paraNameEn": "Tim <PERSON> Cmp3", "paraVarName": "TimE.cmp[3]", "paraMacroName": "ID_PWM_CMP_3_E", "paraDescription": "TIM E比较器4", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "163", "paraNameCn": "TIM F比较器1", "paraNameEn": "Tim F Cmp0", "paraVarName": "TimF.cmp[0]", "paraMacroName": "ID_PWM_CMP_0_F", "paraDescription": "TIM F比较器1", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "164", "paraNameCn": "TIM F比较器2", "paraNameEn": "Tim F Cmp1", "paraVarName": "TimF.cmp[1]", "paraMacroName": "ID_PWM_CMP_1_F", "paraDescription": "TIM F比较器2", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "165", "paraNameCn": "TIM F比较器3", "paraNameEn": "Tim F Cmp2", "paraVarName": "Tim<PERSON>.cmp[2]", "paraMacroName": "ID_PWM_CMP_2_F", "paraDescription": "TIM F比较器3", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "166", "paraNameCn": "TIM F比较器4", "paraNameEn": "Tim F Cmp3", "paraVarName": "Tim<PERSON>.cmp[3]", "paraMacroName": "ID_PWM_CMP_3_F", "paraDescription": "TIM F比较器4", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}], "降额控制P": [{"paraId": "750", "paraNameCn": "过频有功命令", "paraNameEn": "UpFreqCtrlPower Cmd", "paraVarName": "UpFreqCtrlPower.cmd", "paraMacroName": "ID_UP_FREQ_POWER_CMD", "paraDescription": "运行命令 0：不使能 1：使能", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "Y"}, {"paraId": "751", "paraNameCn": "过频有功状态", "paraNameEn": "UpFreqCtrlPower State", "paraVarName": "UpFreqCtrlPower.state", "paraMacroName": "ID_UP_FREQ_POWER_STATE", "paraDescription": "运行状态 0：未开始调整 1：开始调整", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "752", "paraNameCn": "过频有功计时器", "paraNameEn": "UpFreqCtrlPower Timer", "paraVarName": "UpFreqCtrlPower.timer", "paraMacroName": "ID_UP_FREQ_POWER_TIMER", "paraDescription": "null", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U32", "saveAttr": "N"}, {"paraId": "753", "paraNameCn": "过频有功停用使能", "paraNameEn": "UpFreqCtrlPower StopEnable", "paraVarName": "UpFreqCtrlPower.stopEnable", "paraMacroName": "ID_UP_FREQ_POWER_STOP_ENABLE", "paraDescription": "null", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "754", "paraNameCn": "过频有功启动门限", "paraNameEn": "UpFreqCtrlPower StartLimit", "paraVarName": "UpFreqCtrlPower.startLimit", "paraMacroName": "ID_UP_FREQ_POWER_START_LIMIT", "paraDescription": "过频限制", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "755", "paraNameCn": "过频有功启动门限滤波时间", "paraNameEn": "UpFreqCtrlPower StartLimitTime", "paraVarName": "UpFreqCtrlPower.startLimitTime", "paraMacroName": "ID_UP_FREQ_POWER_START_FILTER", "paraDescription": "过频限制时间", "paraUnit": "ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "600000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U32", "saveAttr": "Y"}, {"paraId": "756", "paraNameCn": "过频有功停止门限", "paraNameEn": "UpFreqCtrlPower StopLimit", "paraVarName": "UpFreqCtrlPower.stopLimit", "paraMacroName": "ID_UP_FREQ_POWER_STOP_LIMIT", "paraDescription": "过频限制回差", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "757", "paraNameCn": "过频有功停止门限滤波时间", "paraNameEn": "UpFreqCtrlPower StopLimitTime", "paraVarName": "UpFreqCtrlPower.stopLimitTime", "paraMacroName": "ID_UP_FREQ_POWER_STOP_FILTER", "paraDescription": "过频限制回差时间", "paraUnit": "ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "600000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U32", "saveAttr": "Y"}, {"paraId": "758", "paraNameCn": "过频有功下垂值", "paraNameEn": "UpFreqCtrlPower Droop", "paraVarName": "UpFreqCtrlPower.droop", "paraMacroName": "ID_UP_FREQ_POWER_DROOP", "paraDescription": "过频下垂", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "759", "paraNameCn": "过频有功旧频率", "paraNameEn": "UpFreqCtrlPower FreqOld", "paraVarName": "UpFreqCtrlPower.freqOld", "paraMacroName": "ID_UP_FREQ_POWER_FREQ_OLD", "paraDescription": "过频旧频率", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "760", "paraNameCn": "过频有功旧功率", "paraNameEn": "UpFreqCtrlPower PowerOld", "paraVarName": "UpFreqCtrlPower.powerOld", "paraMacroName": "ID_UP_FREQ_POWER_POWER_OLD", "paraDescription": "过频旧功率", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "761", "paraNameCn": "过频有功进入点功率", "paraNameEn": "UpFreqCtrlPower entryLockPower", "paraVarName": "UpFreqCtrlPower.entryLockPower", "paraMacroName": "ID_UP_FREQ_POWER_ENTRY_POWER", "paraDescription": "过频有功进入点功率", "paraUnit": "w", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "762", "paraNameCn": "过频有功输出", "paraNameEn": "UpFreqCtrlPower OutPower", "paraVarName": "UpFreqCtrlPower.outPower", "paraMacroName": "ID_UP_FREQ_POWER_OUT_POWER", "paraDescription": "输出有功功率", "paraUnit": "w", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "763", "paraNameCn": "欠频有功命令", "paraNameEn": "DnFreqCtrlPower Cmd", "paraVarName": "DnFreqCtrlPower.cmd", "paraMacroName": "ID_DN_FREQ_POWER_CMD", "paraDescription": "运行命令 0：不使能 1：使能", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "Y"}, {"paraId": "764", "paraNameCn": "欠频有功状态", "paraNameEn": "DnFreqCtrlPower State", "paraVarName": "DnFreqCtrlPower.state", "paraMacroName": "ID_DN_FREQ_POWER_STATE", "paraDescription": "运行状态 0：未开始调整 1：开始调整", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "765", "paraNameCn": "欠频有功计时器", "paraNameEn": "DnFreqCtrlPower Timer", "paraVarName": "DnFreqCtrlPower.timer", "paraMacroName": "ID_DN_FREQ_POWER_TIMER", "paraDescription": "null", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U32", "saveAttr": "N"}, {"paraId": "766", "paraNameCn": "欠频有功启动门限", "paraNameEn": "DnFreqCtrlPower StartLimit", "paraVarName": "DnFreqCtrlPower.startLimit", "paraMacroName": "ID_DN_FREQ_POWER_START_LIMIT", "paraDescription": "欠频限制", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "767", "paraNameCn": "欠频有功启动门限滤波时间", "paraNameEn": "DnFreqCtrlPower StartLimitTime", "paraVarName": "DnFreqCtrlPower.startLimitTime", "paraMacroName": "ID_DN_FREQ_POWER_START_FILTER", "paraDescription": "欠频限制时间", "paraUnit": "ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "600000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U32", "saveAttr": "Y"}, {"paraId": "768", "paraNameCn": "欠频有功下垂值", "paraNameEn": "DnFreqCtrlPower Droop", "paraVarName": "DnFreqCtrlPower.droop", "paraMacroName": "ID_DN_FREQ_POWER_DROOP", "paraDescription": "欠频下垂", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "769", "paraNameCn": "欠频有功进入点功率", "paraNameEn": "DnFreqCtrlPower entryLockPower", "paraVarName": "DnFreqCtrlPower.entryLockPower", "paraMacroName": "ID_DN_FREQ_POWER_ENTRY_POWER", "paraDescription": "欠频有功进入点功率", "paraUnit": "w", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "770", "paraNameCn": "欠频有功输出", "paraNameEn": "DnFreqCtrlPower OutPower", "paraVarName": "DnFreqCtrlPower.outPower", "paraMacroName": "ID_DN_FREQ_POWER_OUT_POWER", "paraDescription": "输出有功功率", "paraUnit": "w", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "771", "paraNameCn": "电压有功命令", "paraNameEn": "VolCtrlPower Cmd", "paraVarName": "VolCtrlPower.cmd", "paraMacroName": "ID_VOL_POWER_CMD", "paraDescription": "运行命令 0：不使能 1：使能", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "Y"}, {"paraId": "772", "paraNameCn": "电压有功状态", "paraNameEn": "VolCtrlPower State", "paraVarName": "VolCtrlPower.state", "paraMacroName": "ID_VOL_POWER_STATE", "paraDescription": "运行状态 0：未开始调整 1：开始调整", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "773", "paraNameCn": "电压有功计时器", "paraNameEn": "VolCtrlPower Timer", "paraVarName": "VolCtrlPower.timer", "paraMacroName": "ID_VOL_POWER_TIMER", "paraDescription": "null", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U32", "saveAttr": "N"}, {"paraId": "774", "paraNameCn": "电压有功滤波时间", "paraNameEn": "VolCtrlPower StartLimitTime", "paraVarName": "VolCtrlPower.limitTime", "paraMacroName": "ID_VOL_POWER_FILTER", "paraDescription": "过频限制时间", "paraUnit": "ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "600000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U32", "saveAttr": "Y"}, {"paraId": "775", "paraNameCn": "电压有功给定", "paraNameEn": "VolCtrlPower OutPower", "paraVarName": "VolCtrlPower.powerSet", "paraMacroName": "ID_VOL_POWER_POWER_SET", "paraDescription": "有功功率给定", "paraUnit": "w", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "776", "paraNameCn": "电压有功给定参考", "paraNameEn": "VolCtrlPower OutPower", "paraVarName": "VolCtrlPower.powerSetRef", "paraMacroName": "ID_VOL_POWER_POWER_SET_REF", "paraDescription": "有功功率给定参考", "paraUnit": "w", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "777", "paraNameCn": "电压有功进入点功率", "paraNameEn": "VolCtrlPower entryLockPower", "paraVarName": "VolCtrlPower.entryLockPower", "paraMacroName": "ID_VOL_POWER_POWER_ENTRY_POWER", "paraDescription": "电压有功进入点功率", "paraUnit": "w", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "778", "paraNameCn": "电压有功低通滤波系数", "paraNameEn": "VolCtrlPower K", "paraVarName": "VolCtrlPower.k", "paraMacroName": "ID_VOL_POWER_POWER_K", "paraDescription": "电压有功低通滤波系数", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "779", "paraNameCn": "电压有功响应时间", "paraNameEn": "VolCtrlPower Tau", "paraVarName": "VolCtrlPower.tau", "paraMacroName": "ID_VOL_POWER_POWER_TAU", "paraDescription": "电压有功响应时间", "paraUnit": "ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "600000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U32", "saveAttr": "Y"}, {"paraId": "780", "paraNameCn": "定点有功命令", "paraNameEn": "ManualCtrlPower Cmd", "paraVarName": "ManualCtrlPower.cmd", "paraMacroName": "ID_MANUAL_POWER_CMD", "paraDescription": "运行命令 0：不使能 1：使能", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "Y"}, {"paraId": "781", "paraNameCn": "定点有功给定", "paraNameEn": "ManualCtrlPower PowerSet", "paraVarName": "ManualCtrlPower.powerSet", "paraMacroName": "ID_MANUAL_POWER_POWER_SET", "paraDescription": "有功功率给定", "paraUnit": "w", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "782", "paraNameCn": "定点有功给定参考", "paraNameEn": "ManualCtrlPower PowerSetRef", "paraVarName": "ManualCtrlPower.powerSetRef", "paraMacroName": "ID_MANUAL_POWER_POWER_SET_REF", "paraDescription": "有功功率给定参考", "paraUnit": "w", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "783", "paraNameCn": "定点有功斜坡使能", "paraNameEn": "ManualCtrlPower Ramp Enable", "paraVarName": "ManualCtrlPower.ramp.enable", "paraMacroName": "ID_MANUAL_POWER_RAMP_CMD", "paraDescription": "定点有功斜坡使能", "paraUnit": "w", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "784", "paraNameCn": "定点有功向上斜率", "paraNameEn": "ManualCtrlPower Ramp AccStep", "paraVarName": "ManualCtrlPower.ramp.accStep", "paraMacroName": "ID_MANUAL_POWER_RAMP_UP_RATE", "paraDescription": "定点有功向上斜率", "paraUnit": "w", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "785", "paraNameCn": "定点有功向下斜率", "paraNameEn": "ManualCtrlPower Ramp DecStep", "paraVarName": "ManualCtrlPower.ramp.decStep", "paraMacroName": "ID_MANUAL_POWER_RAMP_DN_RATE", "paraDescription": "定点有功向下斜率", "paraUnit": "w", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "786", "paraNameCn": "定点有功实际值", "paraNameEn": "ManualCtrlPower Ramp RealValue", "paraVarName": "ManualCtrlPower.ramp.realValue", "paraMacroName": "ID_MANUAL_POWER_RAMP_RATE", "paraDescription": "定点有功实际值", "paraUnit": "w", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}], "降额控制Q": [{"paraId": "790", "paraNameCn": "无功功率控制模式", "paraNameEn": "QPowerPara Mode", "paraVarName": "QPowerPara.mode", "paraMacroName": "ID_Q_POWER_CMD", "paraDescription": "0:关闭无功 1：设定点Q   2：设定点PF  3：电压控制无功  4：有功控制无功", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "4", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "Y"}, {"paraId": "791", "paraNameCn": "定点Q", "paraNameEn": "QPowerPara setPoint_Q", "paraVarName": "QPowerPara.setPoint_Q", "paraMacroName": "ID_Q_POWER_SET_POINT_Q", "paraDescription": "设定点Q  ", "paraUnit": "VA", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000", "minValue": "-1000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "792", "paraNameCn": "定点cosFai", "paraNameEn": "QPowerPara setPoint_CosFai", "paraVarName": "QPowerPara.setPoint_CosFai", "paraMacroName": "ID_Q_POWER_SET_POINT_COS", "paraDescription": "设定点CosFai", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "-1", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "793", "paraNameCn": "回差状态", "paraNameEn": "VolCtrlQ state", "paraVarName": "QPowerPara.v_vs_q.state", "paraMacroName": "ID_Q_POWER_V_Q_STATE", "paraDescription": "0:低于退出点不发无功 1：发无功", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "794", "paraNameCn": "电压曲线模式", "paraNameEn": "VolCtrlQ Mode", "paraVarName": "QPowerPara.v_vs_q.mode", "paraMacroName": "ID_Q_POWER_V_Q_MODE", "paraDescription": "0:曲线A 1：曲线B", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "2", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "795", "paraNameCn": "A曲线电压设置1", "paraNameEn": "VolCtrlQ aSet1", "paraVarName": "QPowerPara.v_vs_q.aSet1", "paraMacroName": "ID_Q_POWER_V_Q_ASET_1", "paraDescription": "电压控制无功   A曲线电压设置1 ", "paraUnit": "v", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "796", "paraNameCn": "A曲线电压设置2", "paraNameEn": "VolCtrlQ aSet2", "paraVarName": "QPowerPara.v_vs_q.aSet2", "paraMacroName": "ID_Q_POWER_V_Q_ASET_2", "paraDescription": "电压控制无功   A曲线电压设置2 ", "paraUnit": "v", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "797", "paraNameCn": "A曲线电压设置3", "paraNameEn": "VolCtrlQ aSet3", "paraVarName": "QPowerPara.v_vs_q.aSet3", "paraMacroName": "ID_Q_POWER_V_Q_ASET_3", "paraDescription": "电压控制无功   A曲线电压设置3", "paraUnit": "v", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "798", "paraNameCn": "A曲线电压设置4", "paraNameEn": "VolCtrlQ aSet4", "paraVarName": "QPowerPara.v_vs_q.aSet4", "paraMacroName": "ID_Q_POWER_V_Q_ASET_4", "paraDescription": "电压控制无功   A曲线电压设置4", "paraUnit": "v", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "799", "paraNameCn": "B曲线电压设置1", "paraNameEn": "VolCtrlQ bSet1", "paraVarName": "QPowerPara.v_vs_q.bSet1", "paraMacroName": "ID_Q_POWER_V_Q_BSET_1", "paraDescription": "电压控制无功   B曲线电压设置1 ", "paraUnit": "v", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "800", "paraNameCn": "B曲线电压设置2", "paraNameEn": "VolCtrlQ bSet2", "paraVarName": "QPowerPara.v_vs_q.bSet2", "paraMacroName": "ID_Q_POWER_V_Q_BSET_2", "paraDescription": "电压控制无功   B曲线电压设置2 ", "paraUnit": "v", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "801", "paraNameCn": "锁定进入点", "paraNameEn": "VolCtrlQ pLockIn", "paraVarName": "QPowerPara.v_vs_q.pLockIn", "paraMacroName": "ID_Q_POWER_V_Q_LOCK_IN", "paraDescription": "电压控制无功   锁定进入点", "paraUnit": "w", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "802", "paraNameCn": "锁定退出点", "paraNameEn": "VolCtrlQ pLockOut", "paraVarName": "QPowerPara.v_vs_q.pLockOut", "paraMacroName": "ID_Q_POWER_V_Q_LOCK_OUT", "paraDescription": "电压控制无功   锁定退出点", "paraUnit": "w", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "803", "paraNameCn": "有功曲线模式", "paraNameEn": "PowerCtrlQ Mode", "paraVarName": "QPowerPara.p_vs_q.mode", "paraMacroName": "ID_Q_POWER_P_Q_MODE", "paraDescription": "0:曲线A 1：曲线B 2;曲线C", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "2", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "804", "paraNameCn": "A曲线有功设置1", "paraNameEn": "PowerCtrlQ aSet1", "paraVarName": "QPowerPara.p_vs_q.aSet1", "paraMacroName": "ID_Q_POWER_P_Q_ASET_1", "paraDescription": "有功控制无功   A曲线有功设置1", "paraUnit": "w", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "805", "paraNameCn": "A曲线有功设置2", "paraNameEn": "PowerCtrlQ aSet2", "paraVarName": "QPowerPara.p_vs_q.aSet2", "paraMacroName": "ID_Q_POWER_P_Q_ASET_2", "paraDescription": "有功控制无功   A曲线有功设置2", "paraUnit": "w", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "806", "paraNameCn": "A曲线有功设置3", "paraNameEn": "PowerCtrlQ aSet3", "paraVarName": "QPowerPara.p_vs_q.aSet3", "paraMacroName": "ID_Q_POWER_P_Q_ASET_3", "paraDescription": "有功控制无功   A曲线有功设置3", "paraUnit": "w", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "807", "paraNameCn": "A曲线有功设置4", "paraNameEn": "PowerCtrlQ aSet4", "paraVarName": "QPowerPara.p_vs_q.aSet4", "paraMacroName": "ID_Q_POWER_P_Q_ASET_4", "paraDescription": "有功控制无功   A曲线有功设置4", "paraUnit": "w", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "808", "paraNameCn": "B曲线有功设置1", "paraNameEn": "PowerCtrlQ bSet1", "paraVarName": "QPowerPara.p_vs_q.bSet1", "paraMacroName": "ID_Q_POWER_P_Q_BSET_1", "paraDescription": "有功控制无功   B曲线有功设置1", "paraUnit": "w", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "809", "paraNameCn": "B曲线有功设置2", "paraNameEn": "PowerCtrlQ bSet2", "paraVarName": "QPowerPara.p_vs_q.bSet2", "paraMacroName": "ID_Q_POWER_P_Q_BSET_2", "paraDescription": "有功控制无功   B曲线有功设置2", "paraUnit": "w", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "810", "paraNameCn": "C曲线有功设置1", "paraNameEn": "PowerCtrlQ cset1", "paraVarName": "QPowerPara.p_vs_q.cSet1", "paraMacroName": "ID_Q_POWER_P_Q_CSET_1", "paraDescription": "有功控制无功   C曲线有功设置1", "paraUnit": "w", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "811", "paraNameCn": "C曲线有功设置2", "paraNameEn": "PowerCtrlQ cset2", "paraVarName": "QPowerPara.p_vs_q.cSet2", "paraMacroName": "ID_Q_POWER_P_Q_CSET_2", "paraDescription": "有功控制无功   C曲线有功设置2", "paraUnit": "w", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "812", "paraNameCn": "有功计算值", "paraNameEn": "QPowerPara pPower", "paraVarName": "QPowerPara.pPower", "paraMacroName": "ID_Q_POWER_P_POWER", "paraDescription": "有功计算值", "paraUnit": "w", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000", "minValue": "-1000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "813", "paraNameCn": "无功计算值", "paraNameEn": "QPowerPara qPower", "paraVarName": "QPowerPara.qPower", "paraMacroName": "ID_Q_POWER_Q_POWER", "paraDescription": "无功计算值", "paraUnit": "va", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "-1", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "814", "paraNameCn": "视在计算值", "paraNameEn": "QPowerPara sPower", "paraVarName": "QPowerPara.sPower", "paraMacroName": "ID_Q_POWER_S_POWER", "paraDescription": "视在计算值", "paraUnit": "va", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000", "minValue": "-1000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "815", "paraNameCn": "fai计算值", "paraNameEn": "QPowerPara fai", "paraVarName": "QPowerPara.fai", "paraMacroName": "ID_Q_POWER_FAI", "paraDescription": "fai计算值", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "10", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "816", "paraNameCn": "fai参考值", "paraNameEn": "QPowerPara faiRef", "paraVarName": "QPowerPara.faiRef", "paraMacroName": "ID_Q_POWER_FAIREF", "paraDescription": "fai参考值", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "10", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "817", "paraNameCn": "无功响应时间", "paraNameEn": "QPowerPara tau", "paraVarName": "QPowerPara.tau", "paraMacroName": "ID_Q_POWER_TAU", "paraDescription": "无功响应时间", "paraUnit": "ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "600000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U32", "saveAttr": "Y"}, {"paraId": "818", "paraNameCn": "低通滤波系数", "paraNameEn": "QPowerPara K", "paraVarName": "QPowerPara.k", "paraMacroName": "ID_DERATE_POWER_K", "paraDescription": "低通滤波系数", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "819", "paraNameCn": "温度降额功率给定", "paraNameEn": "DeratePowerS_Temp", "paraVarName": "DeratePowerS_Temp", "paraMacroName": "ID_DERATE_POWER_S_TEMP", "paraDescription": "温度降额功率给定", "paraUnit": "VA", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "820", "paraNameCn": "pv降额功率给定", "paraNameEn": "DeratePowerS_Vpv", "paraVarName": "DeratePowerS_Vpv", "paraMacroName": "ID_DERATE_POWER_S_PV", "paraDescription": "pv降额功率给定", "paraUnit": "VA", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "821", "paraNameCn": "ac降额功率给定", "paraNameEn": "DeratePowerS_Vac", "paraVarName": "DeratePowerS_Vac", "paraMacroName": "ID_DERATE_POWER_S_AC", "paraDescription": "ac降额功率给定", "paraUnit": "VA", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "822", "paraNameCn": "降额功率给定P(最小值)", "paraNameEn": "DeratePowerP", "paraVarName": "DeratePowerP", "paraMacroName": "ID_DERATE_POWER_P", "paraDescription": "降额功率给定P(最小值)", "paraUnit": "VA", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "823", "paraNameCn": "降额功率给定Q(最小值)", "paraNameEn": "DeratePowerQ", "paraVarName": "DeratePowerQ", "paraMacroName": "ID_DERATE_POWER_Q", "paraDescription": "降额功率给定Q(最小值)", "paraUnit": "VA", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "824", "paraNameCn": "降额功率给定S(最小值)", "paraNameEn": "DeratePowerS", "paraVarName": "DeratePowerS", "paraMacroName": "ID_DERATE_POWER_S", "paraDescription": "降额功率给定S(最小值)", "paraUnit": "VA", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "825", "paraNameCn": "防逆流开关", "paraNameEn": "AntiPowerPara cmd       ", "paraVarName": "AntiPowerPara.cmd       ", "paraMacroName": "ID_ANTI_POWER_CMD", "paraDescription": "防逆流开关", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "Y"}, {"paraId": "826", "paraNameCn": "防逆流功率百分比", "paraNameEn": "AntiPowerPara percentSet", "paraVarName": "AntiPowerPara.percentSet", "paraMacroName": "ID_ANTI_POWER_PERCENT", "paraDescription": "防逆流功率百分比", "paraUnit": "%", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "200", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I16", "saveAttr": "N"}, {"paraId": "827", "paraNameCn": "防逆流功率给定", "paraNameEn": "AntiPowerPara powerSet  ", "paraVarName": "AntiPowerPara.powerSet  ", "paraMacroName": "ID_ANTI_POWER_SET", "paraDescription": "防逆流功率给定", "paraUnit": "w", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "828", "paraNameCn": "今日发电量", "paraNameEn": "DailyEnergy", "paraVarName": "DailyEnergy", "paraMacroName": "ID_DAILY_ENERGY", "paraDescription": "今日发电量", "paraUnit": "w", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "12000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "829", "paraNameCn": "转换效率", "paraNameEn": "Efficiency", "paraVarName": "Efficiency", "paraMacroName": "ID_EFFIENCY", "paraDescription": "转换效率", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "830", "paraNameCn": "降额功率给定", "paraNameEn": "DeratePowerSet", "paraVarName": "DeratePowerSet", "paraMacroName": "ID_DERATE_POWER_SET", "paraDescription": "降额功率给定", "paraUnit": "W", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "831", "paraNameCn": "降额角度给定", "paraNameEn": "DerateFaiSet", "paraVarName": "DerateFaiSet", "paraMacroName": "ID_DERATE_FAI_SET", "paraDescription": "降额角度给定", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "10", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}], "欧洲故障保护门限": [{"paraId": "1300", "paraNameCn": "AC过压1上限(欧洲区)", "paraNameEn": "AcVolUp1 upLimit[EU]", "paraVarName": "ProLimitInfo[E_EU].acVolUp1.upLimit", "paraMacroName": "ID_EU_AC_VOL_UP1_UP_LIMIT", "paraDescription": "AC过压1上限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1301", "paraNameCn": "AC过压1下限(欧洲区)", "paraNameEn": "AcVolUp1 dnLimit[EU]", "paraVarName": "ProLimitInfo[E_EU].acVolUp1.dnLimit", "paraMacroName": "ID_EU_AC_VOL_UP1_DN_LIMIT", "paraDescription": "AC过压1下限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1302", "paraNameCn": "AC过压1滤波时间(欧洲区)", "paraNameEn": "AcVolUp1 filterTime[EU]", "paraVarName": "ProLimitInfo[E_EU].acVolUp1.filter", "paraMacroName": "ID_EU_AC_VOL_UP1_FILTER", "paraDescription": "AC过压1滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "1303", "paraNameCn": "AC过压2上限(欧洲区)", "paraNameEn": "AcVolUp2 upLimit[EU]", "paraVarName": "ProLimitInfo[E_EU].acVolUp2.upLimit", "paraMacroName": "ID_EU_AC_VOL_UP2_UP_LIMIT", "paraDescription": "AC过压2上限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1304", "paraNameCn": "AC过压2下限(欧洲区)", "paraNameEn": "AcVolUp2 dnLimit[EU]", "paraVarName": "ProLimitInfo[E_EU].acVolUp2.dnLimit", "paraMacroName": "ID_EU_AC_VOL_UP2_DN_LIMIT", "paraDescription": "AC过压2下限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1305", "paraNameCn": "AC过压2滤波时间(欧洲区)", "paraNameEn": "AcVolUp2 filterTime[EU]", "paraVarName": "ProLimitInfo[E_EU].acVolUp2.filter", "paraMacroName": "ID_EU_AC_VOL_UP2_FILTER", "paraDescription": "AC过压2滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "1306", "paraNameCn": "AC过压3上限(欧洲区)", "paraNameEn": "AcVolUp3 upLimit[EU]", "paraVarName": "ProLimitInfo[E_EU].acVolUp3.upLimit", "paraMacroName": "ID_EU_AC_VOL_UP3_UP_LIMIT", "paraDescription": "AC过压3上限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1307", "paraNameCn": "AC过压3下限(欧洲区)", "paraNameEn": "AcVolUp3 dnLimit[EU]", "paraVarName": "ProLimitInfo[E_EU].acVolUp3.dnLimit", "paraMacroName": "ID_EU_AC_VOL_UP3_DN_LIMIT", "paraDescription": "AC过压3下限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1308", "paraNameCn": "AC过压3滤波时间(欧洲区)", "paraNameEn": "AcVolUp3 filterTime[EU]", "paraVarName": "ProLimitInfo[E_EU].acVolUp3.filter", "paraMacroName": "ID_EU_AC_VOL_UP3_FILTER", "paraDescription": "AC过压3滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "1309", "paraNameCn": "AC10分钟平均过压上限(欧洲区)", "paraNameEn": "tenMinAvgVolUp upLimit[EU]", "paraVarName": "ProLimitInfo[E_EU].tenMinAvgVolUp.upLimit", "paraMacroName": "ID_EU_TEN_MIN_VOL_UP_UP_LIMIT", "paraDescription": "AC10分钟过压上限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1310", "paraNameCn": "AC10分钟平均过压下限(欧洲区)", "paraNameEn": "tenMinAvgVolUp dnLimit[EU]", "paraVarName": "ProLimitInfo[E_EU].tenMinAvgVolUp.dnLimit", "paraMacroName": "ID_EU_TEN_MIN_VOL_UP_DN_LIMIT", "paraDescription": "AC10分钟过压下限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1311", "paraNameCn": "AC10分钟平均过压滤波时间(欧洲区)", "paraNameEn": "tenMinAvgVolUp filterTime[EU]", "paraVarName": "ProLimitInfo[E_EU].tenMinAvgVolUp.filter", "paraMacroName": "ID_EU_TEN_MIN_VOL_UP_FILTER", "paraDescription": "AC10分钟过压滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "1312", "paraNameCn": "AC欠压1上限(欧洲区)", "paraNameEn": "AcVolDn1 upLimit[EU]", "paraVarName": "ProLimitInfo[E_EU].acVolDn1.upLimit", "paraMacroName": "ID_EU_AC_VOL_DN1_UP_LIMIT", "paraDescription": "AC欠压1上限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1313", "paraNameCn": "AC欠压1下限(欧洲区)", "paraNameEn": "AcVolDn1 dnLimit[EU]", "paraVarName": "ProLimitInfo[E_EU].acVolDn1.dnLimit", "paraMacroName": "ID_EU_AC_VOL_DN1_DN_LIMIT", "paraDescription": "AC欠压1下限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1314", "paraNameCn": "AC欠压1滤波时间(欧洲区)", "paraNameEn": "AcVolDn1 filterTime[EU]", "paraVarName": "ProLimitInfo[E_EU].acVolDn1.filter", "paraMacroName": "ID_EU_AC_VOL_DN1_FILTER", "paraDescription": "AC欠压1滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "1315", "paraNameCn": "AC欠压2上限(欧洲区)", "paraNameEn": "AcVolDn2 upLimit[EU]", "paraVarName": "ProLimitInfo[E_EU].acVolDn2.upLimit", "paraMacroName": "ID_EU_AC_VOL_DN2_UP_LIMIT", "paraDescription": "AC欠压2上限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1316", "paraNameCn": "AC欠压2下限(欧洲区)", "paraNameEn": "AcVolDn2 dnLimit[EU]", "paraVarName": "ProLimitInfo[E_EU].acVolDn2.dnLimit", "paraMacroName": "ID_EU_AC_VOL_DN2_DN_LIMIT", "paraDescription": "AC欠压2下限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1317", "paraNameCn": "AC欠压2滤波时间(欧洲区)", "paraNameEn": "AcVolDn2 filterTime[EU]", "paraVarName": "ProLimitInfo[E_EU].acVolDn2.filter", "paraMacroName": "ID_EU_AC_VOL_DN2_FILTER", "paraDescription": "AC欠压2滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "1318", "paraNameCn": "AC欠压3上限(欧洲区)", "paraNameEn": "AcVolDn3 upLimit[EU]", "paraVarName": "ProLimitInfo[E_EU].acVolDn3.upLimit", "paraMacroName": "ID_EU_AC_VOL_DN3_UP_LIMIT", "paraDescription": "AC欠压3上限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1319", "paraNameCn": "AC欠压3下限(欧洲区)", "paraNameEn": "AcVolDn3 dnLimit[EU]", "paraVarName": "ProLimitInfo[E_EU].acVolDn3.dnLimit", "paraMacroName": "ID_EU_AC_VOL_DN3_DN_LIMIT", "paraDescription": "AC欠压3下限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1320", "paraNameCn": "AC欠压3滤波时间(欧洲区)", "paraNameEn": "AcVolDn3 filterTime[EU]", "paraVarName": "ProLimitInfo[E_EU].acVolDn3.filter", "paraMacroName": "ID_EU_AC_VOL_DN3_FILTER", "paraDescription": "AC欠压3滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "1321", "paraNameCn": "AC过频1上限(欧洲区)", "paraNameEn": "AcFreqUp1 upLimit[EU]", "paraVarName": "ProLimitInfo[E_EU].acFreqUp1.upLimit", "paraMacroName": "ID_EU_AC_FREQ_UP1_UP_LIMIT", "paraDescription": "AC过频1上限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1322", "paraNameCn": "AC过频1下限(欧洲区)", "paraNameEn": "AcFreqUp1 dnLimit[EU]", "paraVarName": "ProLimitInfo[E_EU].acFreqUp1.dnLimit", "paraMacroName": "ID_EU_AC_FREQ_UP1_DN_LIMIT", "paraDescription": "AC过频1下限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1323", "paraNameCn": "AC过频1滤波时间(欧洲区)", "paraNameEn": "AcFreqUp1 filterTime[EU]", "paraVarName": "ProLimitInfo[E_EU].acFreqUp1.filter", "paraMacroName": "ID_EU_AC_FREQ_UP1_FILTER", "paraDescription": "AC过频1滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "1324", "paraNameCn": "AC过频2上限(欧洲区)", "paraNameEn": "AcFreqUp2 upLimit[EU]", "paraVarName": "ProLimitInfo[E_EU].acFreqUp2.upLimit", "paraMacroName": "ID_EU_AC_FREQ_UP2_UP_LIMIT", "paraDescription": "AC过频2上限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1325", "paraNameCn": "AC过频2下限(欧洲区)", "paraNameEn": "AcFreqUp2 dnLimit[EU]", "paraVarName": "ProLimitInfo[E_EU].acFreqUp2.dnLimit", "paraMacroName": "ID_EU_AC_FREQ_UP2_DN_LIMIT", "paraDescription": "AC过频2下限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1326", "paraNameCn": "AC过频2滤波时间(欧洲区)", "paraNameEn": "AcFreqUp2 filterTime[EU]", "paraVarName": "ProLimitInfo[E_EU].acFreqUp2.filter", "paraMacroName": "ID_EU_AC_FREQ_UP2_FILTER", "paraDescription": "AC过频2滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "1327", "paraNameCn": "AC过频3上限(欧洲区)", "paraNameEn": "AcFreqUp3 upLimit[EU]", "paraVarName": "ProLimitInfo[E_EU].acFreqUp3.upLimit", "paraMacroName": "ID_EU_AC_FREQ_UP3_UP_LIMIT", "paraDescription": "AC过频3上限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1328", "paraNameCn": "AC过频3下限(欧洲区)", "paraNameEn": "AcFreqUp3 dnLimit[EU]", "paraVarName": "ProLimitInfo[E_EU].acFreqUp3.dnLimit", "paraMacroName": "ID_EU_AC_FREQ_UP3_DN_LIMIT", "paraDescription": "AC过频3下限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1329", "paraNameCn": "AC过频3滤波时间(欧洲区)", "paraNameEn": "AcFreqUp3 filterTime[EU]", "paraVarName": "ProLimitInfo[E_EU].acFreqUp3.filter", "paraMacroName": "ID_EU_AC_FREQ_UP3_FILTER", "paraDescription": "AC过频3滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "1330", "paraNameCn": "AC欠频1上限(欧洲区)", "paraNameEn": "AcFreqDn1 upLimit[EU]", "paraVarName": "ProLimitInfo[E_EU].acFreqDn1.upLimit", "paraMacroName": "ID_EU_AC_FREQ_DN1_UP_LIMIT", "paraDescription": "AC欠频1上限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1331", "paraNameCn": "AC欠频1下限(欧洲区)", "paraNameEn": "AcFreqDn1 dnLimit[EU]", "paraVarName": "ProLimitInfo[E_EU].acFreqDn1.dnLimit", "paraMacroName": "ID_EU_AC_FREQ_DN1_DN_LIMIT", "paraDescription": "AC欠频1下限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1332", "paraNameCn": "AC欠频1滤波时间(欧洲区)", "paraNameEn": "AcFreqDn1 filterTime[EU]", "paraVarName": "ProLimitInfo[E_EU].acFreqDn1.filter", "paraMacroName": "ID_EU_AC_FREQ_DN1_FILTER", "paraDescription": "AC欠频1滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "1333", "paraNameCn": "AC欠频2上限(欧洲区)", "paraNameEn": "AcFreqDn2 upLimit[EU]", "paraVarName": "ProLimitInfo[E_EU].acFreqDn2.upLimit", "paraMacroName": "ID_EU_AC_FREQ_DN2_UP_LIMIT", "paraDescription": "AC欠频2上限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1334", "paraNameCn": "AC欠频2下限(欧洲区)", "paraNameEn": "AcFreqDn2 dnLimit[EU]", "paraVarName": "ProLimitInfo[E_EU].acFreqDn2.dnLimit", "paraMacroName": "ID_EU_AC_FREQ_DN2_DN_LIMIT", "paraDescription": "AC欠频2下限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1335", "paraNameCn": "AC欠频2滤波时间(欧洲区)", "paraNameEn": "AcFreqDn2 filterTime[EU]", "paraVarName": "ProLimitInfo[E_EU].acFreqDn2.filter", "paraMacroName": "ID_EU_AC_FREQ_DN2_FILTER", "paraDescription": "AC欠频2滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "1336", "paraNameCn": "AC欠频3上限(欧洲区)", "paraNameEn": "AcFreqDn3 upLimit[EU]", "paraVarName": "ProLimitInfo[E_EU].acFreqDn3.upLimit", "paraMacroName": "ID_EU_AC_FREQ_DN3_UP_LIMIT", "paraDescription": "AC欠频3上限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1337", "paraNameCn": "AC欠频3下限(欧洲区)", "paraNameEn": "AcFreqDn3 dnLimit[EU]", "paraVarName": "ProLimitInfo[E_EU].acFreqDn3.dnLimit", "paraMacroName": "ID_EU_AC_FREQ_DN3_DN_LIMIT", "paraDescription": "AC欠频3下限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1338", "paraNameCn": "AC欠频3滤波时间(欧洲区)", "paraNameEn": "AcFreqDn3 filterTime[EU]", "paraVarName": "ProLimitInfo[E_EU].acFreqDn3.filter", "paraMacroName": "ID_EU_AC_FREQ_DN3_FILTER", "paraDescription": "AC欠频3滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}], "时间版本类型": [{"paraId": "450", "paraNameCn": "年", "paraNameEn": "year", "paraVarName": "SysTime.year", "paraMacroName": "ID_SYS_TIME_YEAR", "paraDescription": "年", "paraUnit": "y", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I16", "saveAttr": "Y"}, {"paraId": "451", "paraNameCn": "月", "paraNameEn": "month", "paraVarName": "SysTime.month", "paraMacroName": "ID_SYS_TIME_MONTH", "paraDescription": "月", "paraUnit": "m", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "12", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I16", "saveAttr": "Y"}, {"paraId": "452", "paraNameCn": "日", "paraNameEn": "day ", "paraVarName": "SysTime.day ", "paraMacroName": "ID_SYS_TIME_DAY", "paraDescription": "日", "paraUnit": "d", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "31", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I16", "saveAttr": "Y"}, {"paraId": "453", "paraNameCn": "时", "paraNameEn": "hour", "paraVarName": "SysTime.hour", "paraMacroName": "ID_SYS_TIME_HOUR", "paraDescription": "时", "paraUnit": "h", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "24", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I16", "saveAttr": "Y"}, {"paraId": "454", "paraNameCn": "分", "paraNameEn": "min ", "paraVarName": "SysTime.min ", "paraMacroName": "ID_SYS_TIME_MIN", "paraDescription": "分", "paraUnit": "m", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "60", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I16", "saveAttr": "Y"}, {"paraId": "455", "paraNameCn": "秒", "paraNameEn": "sec ", "paraVarName": "SysTime.sec ", "paraMacroName": "ID_SYS_TIME_SEC", "paraDescription": "秒", "paraUnit": "s", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "60", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I16", "saveAttr": "Y"}, {"paraId": "456", "paraNameCn": "ms", "paraNameEn": "ms  ", "paraVarName": "SysTime.ms  ", "paraMacroName": "ID_SYS_TIME_MS", "paraDescription": "ms", "paraUnit": "ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I16", "saveAttr": "Y"}, {"paraId": "457", "paraNameCn": "软件版本", "paraNameEn": "Version soft", "paraVarName": "SysInfo.softVer.total", "paraMacroName": "ID_SOFT_VER", "paraDescription": "软件版本", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "200", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "458", "paraNameCn": "硬件版本", "paraNameEn": "Version hard", "paraVarName": "SysInfo.hardVer", "paraMacroName": "ID_HADR_VER", "paraDescription": "硬件版本", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "200", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I16", "saveAttr": "N"}, {"paraId": "459", "paraNameCn": "V版本", "paraNameEn": "Version V", "paraVarName": "SysInfo.softVer.bits.appV", "paraMacroName": "ID_SOFT_VER_APP_V", "paraDescription": "V版本", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "200", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I8", "saveAttr": "N"}, {"paraId": "460", "paraNameCn": "B版本", "paraNameEn": "Version B", "paraVarName": "SysInfo.softVer.bits.appB", "paraMacroName": "ID_SOFT_VER_APP_B", "paraDescription": "B版本", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "200", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I8", "saveAttr": "N"}, {"paraId": "461", "paraNameCn": "D版本", "paraNameEn": "Version D", "paraVarName": "SysInfo.softVer.bits.appD", "paraMacroName": "ID_SOFT_VER_APP_D", "paraDescription": "D版本", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "200", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I8", "saveAttr": "N"}, {"paraId": "462", "paraNameCn": "Boot版本", "paraNameEn": "Version D", "paraVarName": "SysInfo.softVer.bits.boot", "paraMacroName": "ID_SOFT_VER_BOOT", "paraDescription": "BOOT版本", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "200", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I8", "saveAttr": "N"}, {"paraId": "463", "paraNameCn": "通信型号", "paraNameEn": "Version CommType", "paraVarName": "SysInfo.commType", "paraMacroName": "ID_VER_COMM_TYPE", "paraDescription": "0:wifi 1:PLC 2:调试", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "3", "minValue": "0", "defaultValue": "1", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "464", "paraNameCn": "项目名称", "paraNameEn": "Version ProName", "paraVarName": "SysInfo.projectName", "paraMacroName": "ID_VER_PRO_NAME", "paraDescription": "1:HIM500 2:HIM1000", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "100", "minValue": "0", "defaultValue": "1", "dataType": "TYPE_I16", "saveAttr": "N"}, {"paraId": "465", "paraNameCn": "序列号位1_4", "paraNameEn": "SerialNum_1", "paraVarName": "SerNumInfo.serialNum[0]", "paraMacroName": "ID_SERIAL_PRODUCT_INFO_H", "paraDescription": "序列号位1_4", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "466", "paraNameCn": "序列号位4_8", "paraNameEn": "SerialNum_2", "paraVarName": "SerNumInfo.serialNum[1]", "paraMacroName": "ID_SERIAL_PRODUCT_INFO_L", "paraDescription": "序列号位4_8", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "467", "paraNameCn": "序列号位8_12", "paraNameEn": "SerialNum_3", "paraVarName": "SerNumInfo.serialNum[2]", "paraMacroName": "ID_SERIAL_MAC_HIGH", "paraDescription": "序列号位8_12", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "468", "paraNameCn": "序列号位12_16", "paraNameEn": "SerialNum_4", "paraVarName": "SerNumInfo.serialNum[3]", "paraMacroName": "ID_SERIAL_MAC_MIDDEM", "paraDescription": "序列号位12_16", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "469", "paraNameCn": "序列号位16_20", "paraNameEn": "SerialNum_5", "paraVarName": "SerNumInfo.serialNum[4]", "paraMacroName": "ID_SERIAL_MAC_LOW", "paraDescription": "序列号位16_20", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "470", "paraNameCn": "日运行时间", "paraNameEn": "DailyRunTime", "paraVarName": "DailyRunTimePara.min", "paraMacroName": "ID_DAILY_RUN_MIN", "paraDescription": "日运行时间", "paraUnit": "min", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}], "示波器": [{"paraId": "200", "paraNameCn": "示波器运行状态", "paraNameEn": "OscState", "paraVarName": "Osc.status", "paraMacroName": "ID_OSC_STATUS", "paraDescription": "示波器运行状态", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "201", "paraNameCn": "示波器控制命令", "paraNameEn": "OscCmd", "paraVarName": "Osc.cmd", "paraMacroName": "ID_OSC_CMD", "paraDescription": "示波器控制命令", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "202", "paraNameCn": "示波器采样频率", "paraNameEn": "OscSampleFreq", "paraVarName": "Osc.sampleFreq", "paraMacroName": "ID_OSC_SAMPL_FREQ", "paraDescription": "示波器采样频率", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "100000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U32", "saveAttr": "N"}, {"paraId": "203", "paraNameCn": "示波器采样间隔", "paraNameEn": "OscSampleRate", "paraVarName": "Osc.sampleRate", "paraMacroName": "ID_OSC_SAMPL_RATE", "paraDescription": "示波器采样间隔", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "10000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "204", "paraNameCn": "示波器通道0", "paraNameEn": "OscChannel0", "paraVarName": "Osc.channelId[0]", "paraMacroName": "ID_OSC_CHANNLE_0", "paraDescription": "示波器通道0", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "205", "paraNameCn": "示波器通道1", "paraNameEn": "OscChannel1", "paraVarName": "Osc.channelId[1]", "paraMacroName": "ID_OSC_CHANNLE_1", "paraDescription": "示波器通道1", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "206", "paraNameCn": "示波器通道2", "paraNameEn": "OscChannel2", "paraVarName": "Osc.channelId[2]", "paraMacroName": "ID_OSC_CHANNLE_2", "paraDescription": "示波器通道2", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "207", "paraNameCn": "示波器通道3", "paraNameEn": "OscChannel3", "paraVarName": "Osc.channelId[3]", "paraMacroName": "ID_OSC_CHANNLE_3", "paraDescription": "示波器通道3", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "60000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}], "温度采样校准": [{"paraId": "420", "paraNameCn": "绝缘阻抗实际值", "paraNameEn": "Iso Vol Real", "paraVarName": "AdPara.iso.real", "paraMacroName": "ID_AD_ISO_REAL", "paraDescription": "绝缘阻抗实际值", "paraUnit": "kOhm", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "421", "paraNameCn": "绝缘阻抗滤波值", "paraNameEn": "Iso Vol Filter", "paraVarName": "AdPara.iso.filter", "paraMacroName": "ID_AD_ISO_FILTER", "paraDescription": "绝缘阻抗滤波值", "paraUnit": "kOhm", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "422", "paraNameCn": "绝缘阻抗采样值", "paraNameEn": "Iso Vol reg", "paraVarName": "AdPara.iso.reg", "paraMacroName": "ID_AD_ISO_REG", "paraDescription": "绝缘阻抗采样值", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "4096", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I16", "saveAttr": "N"}, {"paraId": "423", "paraNameCn": "绝缘阻抗系数k", "paraNameEn": "Iso Vol k", "paraVarName": "AdPara.iso.k", "paraMacroName": "ID_AD_ISO_K", "paraDescription": "绝缘阻抗系数k", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "30000", "minValue": "-30000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "424", "paraNameCn": "绝缘阻抗偏置b", "paraNameEn": "Iso Vol b", "paraVarName": "AdPara.iso.b", "paraMacroName": "ID_AD_ISO_B", "paraDescription": "绝缘阻抗偏置b", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "30000", "minValue": "-30000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "425", "paraNameCn": "绝缘阻抗校准系数k", "paraNameEn": "Iso Vol adj k", "paraVarName": "AdPara.iso.adj_k", "paraMacroName": "ID_AD_ISO_ADJ_K", "paraDescription": "绝缘阻抗校准系数k", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "30000", "minValue": "-30000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "426", "paraNameCn": "绝缘阻抗校准偏置b", "paraNameEn": "Iso Vol adj b", "paraVarName": "AdPara.iso.adj_b", "paraMacroName": "ID_AD_ISO_ADJ_B", "paraDescription": "绝缘阻抗校准偏置b", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "30000", "minValue": "-30000", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "427", "paraNameCn": "AC MOS温度实际值", "paraNameEn": "Ac Mos Temp Real", "paraVarName": "AdPara.acMosTemp.real", "paraMacroName": "ID_AD_AC_MOS_TEMP_REAL", "paraDescription": "AC MOS温度实际值", "paraUnit": "度", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "150", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "428", "paraNameCn": "AC MOS温度滤波值", "paraNameEn": "Ac Mos Temp Filter", "paraVarName": "AdPara.acMosTemp.filter", "paraMacroName": "ID_AD_AC_MOS_TEMP_FILTER", "paraDescription": "AC MOS温度滤波值", "paraUnit": "度", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "150", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "429", "paraNameCn": "AC MOS温度采样值", "paraNameEn": "Ac Mos Temp reg", "paraVarName": "AdPara.acMosTemp.reg", "paraMacroName": "ID_AD_AC_MOS_TEMP_REG", "paraDescription": "AC MOS温度采样值", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "4096", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I16", "saveAttr": "N"}, {"paraId": "430", "paraNameCn": "PV MOS温度实际值", "paraNameEn": "PV Mos Temp Real", "paraVarName": "AdPara.pvMosTemp.real", "paraMacroName": "ID_AD_PV_MOS_TEMP_REAL", "paraDescription": "PV MOS温度实际值", "paraUnit": "度", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "150", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "431", "paraNameCn": "PV MOS温度滤波值", "paraNameEn": "PV Mos Temp Filter", "paraVarName": "AdPara.pvMosTemp.filter", "paraMacroName": "ID_AD_PV_MOS_TEMP_FILTER", "paraDescription": "PV MOS温度滤波值", "paraUnit": "度", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "150", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "432", "paraNameCn": "PV MOS温度采样值", "paraNameEn": "PV Mos Temp reg", "paraVarName": "AdPara.pvMosTemp.reg", "paraMacroName": "ID_AD_PV_MOS_TEMP_REG", "paraDescription": "PV MOS温度采样值", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "4096", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I16", "saveAttr": "N"}, {"paraId": "433", "paraNameCn": "硬件版本", "paraNameEn": "Haraware version", "paraVarName": "AdPara.hardwareVer", "paraMacroName": "ID_AD_HARD_VER", "paraDescription": "硬件版本", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "200", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I16", "saveAttr": "N"}, {"paraId": "434", "paraNameCn": "绝缘检测vac累加值", "paraNameEn": "Iso vac sum", "paraVarName": "IsoPara.vacSum ", "paraMacroName": "ID_ISO_VAC_SUM", "paraDescription": "绝缘检测vac累加值", "paraUnit": "V", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "435", "paraNameCn": "绝缘检测vac平均值", "paraNameEn": "Iso vac avg", "paraVarName": "IsoPara.vacAvg ", "paraMacroName": "ID_ISO_VAC_AVG", "paraDescription": "绝缘检测vac平均值", "paraUnit": "V", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "436", "paraNameCn": "绝缘检测vpe累加值", "paraNameEn": "Iso vpe sum", "paraVarName": "IsoPara.vpeSum ", "paraMacroName": "ID_ISO_VPE_SUM", "paraDescription": "绝缘检测vpe累加值", "paraUnit": "V", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "437", "paraNameCn": "绝缘检测vpe平均值", "paraNameEn": "Iso vpe avg", "paraVarName": "IsoPara.vpeAvg ", "paraMacroName": "ID_ISO_VPE_AVG", "paraDescription": "绝缘检测vpe平均值", "paraUnit": "V", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "438", "paraNameCn": "绝缘检测周期计数", "paraNameEn": "Iso periodCnt", "paraVarName": "IsoPara.periodCnt ", "paraMacroName": "ID_ISO_PERIOD_CNT", "paraDescription": "绝缘检测周期计数", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "439", "paraNameCn": "绝缘检测计数最大值", "paraNameEn": "Iso maxPeriodCnt", "paraVarName": "IsoPara.maxPeriodCnt ", "paraMacroName": "ID_ISO_MAX_PERIOD_CNT", "paraDescription": "绝缘检测计数最大值", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "440", "paraNameCn": "绝缘检测计数", "paraNameEn": "Iso cnt", "paraVarName": "IsoPara.cnt    ", "paraMacroName": "ID_ISO_CNT", "paraDescription": "绝缘检测计数", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U16", "saveAttr": "N"}, {"paraId": "441", "paraNameCn": "绝缘检测状态", "paraNameEn": "Iso status ", "paraVarName": "IsoPara.status ", "paraMacroName": "ID_ISO_STAUTS", "paraDescription": "绝缘检测状态", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "100", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}], "系统参数": [{"paraId": "1", "paraNameCn": "额定输出电压", "paraNameEn": "SysPara rateVol  ", "paraVarName": "SysPara.rateVol", "paraMacroName": "ID_RATE_VOL", "paraDescription": "额定输出电压", "paraUnit": "V", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "240", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "2", "paraNameCn": "额定输出电流", "paraNameEn": "SysPara rateCurr ", "paraVarName": "SysPara.rateCurr ", "paraMacroName": "ID_RATE_CURR", "paraDescription": "额定输出电流", "paraUnit": "A", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "3", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "3", "paraNameCn": "额定输出功率", "paraNameEn": "SysPara ratePower", "paraVarName": "SysPara.ratePower", "paraMacroName": "ID_RATE_POWER", "paraDescription": "额定输出功率", "paraUnit": "W", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "500", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "4", "paraNameCn": "峰值输出功率", "paraNameEn": "SysPara peakPower", "paraVarName": "SysPara.peakPower", "paraMacroName": "ID_PEAK_POWER", "paraDescription": "峰值输出功率", "paraUnit": "W", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "10000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "5", "paraNameCn": "额定频率", "paraNameEn": "SysPara rateFreq ", "paraVarName": "SysPara.rateFreq ", "paraMacroName": "ID_RATE_FREQ", "paraDescription": "额定频率", "paraUnit": "Hz", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "60", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "6", "paraNameCn": "电网频率", "paraNameEn": "SysPara gridFreq  ", "paraVarName": "SysPara.gridFreq  ", "paraMacroName": "ID_GRID_FREQ", "paraDescription": "电网频率", "paraUnit": "Hz", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "60", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "7", "paraNameCn": "区域选择", "paraNameEn": "SysPara areaSelect", "paraVarName": "SysPara.areaSelect", "paraMacroName": "ID_AREA_SELECT", "paraDescription": "0:自定义 1:中国 2:欧洲 3：澳大利亚", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "10", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "Y"}, {"paraId": "8", "paraNameCn": "启机标志", "paraNameEn": "SysPara startFlag ", "paraVarName": "SysPara.startFlag ", "paraMacroName": "ID_SYS_START_FLAG", "paraDescription": "启动标志", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "9", "paraNameCn": "启动电压", "paraNameEn": "SysPara startPvVol", "paraVarName": "SysPara.startPvVol", "paraMacroName": "ID_SYS_START_PV", "paraDescription": "启动电压", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "60", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "10", "paraNameCn": "关机电压", "paraNameEn": "SysPara stopPvVol ", "paraVarName": "SysPara.stopPvVol ", "paraMacroName": "ID_SYS_STOP_PV", "paraDescription": "关机电压", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "60", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "11", "paraNameCn": "变压器变比", "paraNameEn": "TR", "paraVarName": "CtrlPara.tr", "paraMacroName": "ID_CTRL_TR", "paraDescription": "变压器变比", "paraUnit": "null", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "60", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "12", "paraNameCn": "Mos导通电阻", "paraNameEn": "Rds On", "paraVarName": "CtrlPara.rdsOn", "paraMacroName": "ID_CTRL_RDS_ON", "paraDescription": "通态电阻", "paraUnit": "mOhm", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "500", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "13", "paraNameCn": "<PERSON><PERSON>寄生电容", "paraNameEn": "Coss", "paraVarName": "CtrlPara.coss", "paraMacroName": "ID_CTRL_COSS", "paraDescription": "<PERSON><PERSON>寄生电容", "paraUnit": "pF", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "10000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "14", "paraNameCn": "励磁电感", "paraNameEn": "Lm", "paraVarName": "CtrlPara.lm", "paraMacroName": "ID_CTRL_LM", "paraDescription": "励磁电感", "paraUnit": "uH", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "3000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "15", "paraNameCn": "谐振电感", "paraNameEn": "Lr", "paraVarName": "CtrlPara.lr", "paraMacroName": "ID_CTRL_LR", "paraDescription": "谐振电感", "paraUnit": "uH", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "300", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "16", "paraNameCn": "谐振电容", "paraNameEn": "Cr", "paraVarName": "CtrlPara.cr", "paraMacroName": "ID_CTRL_CR", "paraDescription": "谐振电容", "paraUnit": "nF", "r_wAttr": "W_O", "r_wOfRunAttr": "N", "maxValue": "500", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "17", "paraNameCn": "谐振频率", "paraNameEn": "Fr", "paraVarName": "CtrlPara.fr", "paraMacroName": "ID_CTRL_FR", "paraDescription": "谐振频率", "paraUnit": "kHz", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "500", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "18", "paraNameCn": "远程启机控制", "paraNameEn": "RemoteCtrlCmd", "paraVarName": "RemoteCtrlCmd", "paraMacroName": "ID_REMOTE_CTRL_CMD", "paraDescription": "0/远程停止 1/远程启动 2/远程无效", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "2", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I8", "saveAttr": "Y"}, {"paraId": "19", "paraNameCn": "启机控制", "paraNameEn": "SysCtrlCmd", "paraVarName": "SysCtrlCmd", "paraMacroName": "ID_SYS_CTRL_CMD", "paraDescription": "0/停止  1/启动", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I8", "saveAttr": "Y"}, {"paraId": "20", "paraNameCn": "系统状态字", "paraNameEn": "SysStatus", "paraVarName": "SysStatusWord.all", "paraMacroName": "ID_SYS_STATUS", "paraDescription": "B0:自检  B1:停机  B2:运行  B3:故障 \nB4:告警  B5:保留  B6:保留  B7:保留 \nB8:保留  B9:保留  B10:保留 B11:保留 \nB12:保留  B13:保留 B14:保留 B15:保留", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "2", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_BIT16", "saveAttr": "N"}, {"paraId": "21", "paraNameCn": "继电器状态", "paraNameEn": "RelayStatus", "paraVarName": "RelayStatus", "paraMacroName": "ID_RELAY_STATUS", "paraDescription": "0/断开  1/吸合", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "22", "paraNameCn": "系统复位命令", "paraNameEn": "SysResetCmd", "paraVarName": "SysResetCmd", "paraMacroName": "ID_SYS_RESET_CMD", "paraDescription": "0/停止  1/复位", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}], "中国故障保护门限": [{"paraId": "1200", "paraNameCn": "AC过压1上限(中国区)", "paraNameEn": "AcVolUp1 upLimit[CN]", "paraVarName": "ProLimitInfo[E_CN].acVolUp1.upLimit", "paraMacroName": "ID_CN_AC_VOL_UP1_UP_LIMIT", "paraDescription": "AC过压1上限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1201", "paraNameCn": "AC过压1下限(中国区)", "paraNameEn": "AcVolUp1 dnLimit[CN]", "paraVarName": "ProLimitInfo[E_CN].acVolUp1.dnLimit", "paraMacroName": "ID_CN_AC_VOL_UP1_DN_LIMIT", "paraDescription": "AC过压1下限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1202", "paraNameCn": "AC过压1滤波时间(中国区)", "paraNameEn": "AcVolUp1 filterTime[CN]", "paraVarName": "ProLimitInfo[E_CN].acVolUp1.filter", "paraMacroName": "ID_CN_AC_VOL_UP1_FILTER", "paraDescription": "AC过压1滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "1203", "paraNameCn": "AC过压2上限(中国区)", "paraNameEn": "AcVolUp2 upLimit[CN]", "paraVarName": "ProLimitInfo[E_CN].acVolUp2.upLimit", "paraMacroName": "ID_CN_AC_VOL_UP2_UP_LIMIT", "paraDescription": "AC过压2上限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1204", "paraNameCn": "AC过压2下限(中国区)", "paraNameEn": "AcVolUp2 dnLimit[CN]", "paraVarName": "ProLimitInfo[E_CN].acVolUp2.dnLimit", "paraMacroName": "ID_CN_AC_VOL_UP2_DN_LIMIT", "paraDescription": "AC过压2下限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1205", "paraNameCn": "AC过压2滤波时间(中国区)", "paraNameEn": "AcVolUp2 filterTime[CN]", "paraVarName": "ProLimitInfo[E_CN].acVolUp2.filter", "paraMacroName": "ID_CN_AC_VOL_UP2_FILTER", "paraDescription": "AC过压2滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "1206", "paraNameCn": "AC过压3上限(中国区)", "paraNameEn": "AcVolUp3 upLimit[CN]", "paraVarName": "ProLimitInfo[E_CN].acVolUp3.upLimit", "paraMacroName": "ID_CN_AC_VOL_UP3_UP_LIMIT", "paraDescription": "AC过压3上限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1207", "paraNameCn": "AC过压3下限(中国区)", "paraNameEn": "AcVolUp3 dnLimit[CN]", "paraVarName": "ProLimitInfo[E_CN].acVolUp3.dnLimit", "paraMacroName": "ID_CN_AC_VOL_UP3_DN_LIMIT", "paraDescription": "AC过压3下限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1208", "paraNameCn": "AC过压3滤波时间(中国区)", "paraNameEn": "AcVolUp3 filterTime[CN]", "paraVarName": "ProLimitInfo[E_CN].acVolUp3.filter", "paraMacroName": "ID_CN_AC_VOL_UP3_FILTER", "paraDescription": "AC过压3滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "1209", "paraNameCn": "AC10分钟平均过压上限(中国区)", "paraNameEn": "tenMinAvgVolUp upLimit[CN]", "paraVarName": "ProLimitInfo[E_CN].tenMinAvgVolUp.upLimit", "paraMacroName": "ID_CN_TEN_MIN_VOL_UP_UP_LIMIT", "paraDescription": "AC10分钟过压上限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1210", "paraNameCn": "AC10分钟平均过压下限(中国区)", "paraNameEn": "tenMinAvgVolUp dnLimit[CN]", "paraVarName": "ProLimitInfo[E_CN].tenMinAvgVolUp.dnLimit", "paraMacroName": "ID_CN_TEN_MIN_VOL_UP_DN_LIMIT", "paraDescription": "AC10分钟过压下限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1211", "paraNameCn": "AC10分钟平均过压滤波时间(中国区)", "paraNameEn": "tenMinAvgVolUp filterTime[CN]", "paraVarName": "ProLimitInfo[E_CN].tenMinAvgVolUp.filter", "paraMacroName": "ID_CN_TEN_MIN_VOL_UP_FILTER", "paraDescription": "AC10分钟过压滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "1212", "paraNameCn": "AC欠压1上限(中国区)", "paraNameEn": "AcVolDn1 upLimit[CN]", "paraVarName": "ProLimitInfo[E_CN].acVolDn1.upLimit", "paraMacroName": "ID_CN_AC_VOL_DN1_UP_LIMIT", "paraDescription": "AC欠压1上限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1213", "paraNameCn": "AC欠压1下限(中国区)", "paraNameEn": "AcVolDn1 dnLimit[CN]", "paraVarName": "ProLimitInfo[E_CN].acVolDn1.dnLimit", "paraMacroName": "ID_CN_AC_VOL_DN1_DN_LIMIT", "paraDescription": "AC欠压1下限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1214", "paraNameCn": "AC欠压1滤波时间(中国区)", "paraNameEn": "AcVolDn1 filterTime[CN]", "paraVarName": "ProLimitInfo[E_CN].acVolDn1.filter", "paraMacroName": "ID_CN_AC_VOL_DN1_FILTER", "paraDescription": "AC欠压1滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "1215", "paraNameCn": "AC欠压2上限(中国区)", "paraNameEn": "AcVolDn2 upLimit[CN]", "paraVarName": "ProLimitInfo[E_CN].acVolDn2.upLimit", "paraMacroName": "ID_CN_AC_VOL_DN2_UP_LIMIT", "paraDescription": "AC欠压2上限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1216", "paraNameCn": "AC欠压2下限(中国区)", "paraNameEn": "AcVolDn2 dnLimit[CN]", "paraVarName": "ProLimitInfo[E_CN].acVolDn2.dnLimit", "paraMacroName": "ID_CN_AC_VOL_DN2_DN_LIMIT", "paraDescription": "AC欠压2下限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1217", "paraNameCn": "AC欠压2滤波时间(中国区)", "paraNameEn": "AcVolDn2 filterTime[CN]", "paraVarName": "ProLimitInfo[E_CN].acVolDn2.filter", "paraMacroName": "ID_CN_AC_VOL_DN2_FILTER", "paraDescription": "AC欠压2滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "1218", "paraNameCn": "AC欠压3上限(中国区)", "paraNameEn": "AcVolDn3 upLimit[CN]", "paraVarName": "ProLimitInfo[E_CN].acVolDn3.upLimit", "paraMacroName": "ID_CN_AC_VOL_DN3_UP_LIMIT", "paraDescription": "AC欠压3上限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1219", "paraNameCn": "AC欠压3下限(中国区)", "paraNameEn": "AcVolDn3 dnLimit[CN]", "paraVarName": "ProLimitInfo[E_CN].acVolDn3.dnLimit", "paraMacroName": "ID_CN_AC_VOL_DN3_DN_LIMIT", "paraDescription": "AC欠压3下限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1220", "paraNameCn": "AC欠压3滤波时间(中国区)", "paraNameEn": "AcVolDn3 filterTime[CN]", "paraVarName": "ProLimitInfo[E_CN].acVolDn3.filter", "paraMacroName": "ID_CN_AC_VOL_DN3_FILTER", "paraDescription": "AC欠压3滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "1221", "paraNameCn": "AC过频1上限(中国区)", "paraNameEn": "AcFreqUp1 upLimit[CN]", "paraVarName": "ProLimitInfo[E_CN].acFreqUp1.upLimit", "paraMacroName": "ID_CN_AC_FREQ_UP1_UP_LIMIT", "paraDescription": "AC过频1上限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1222", "paraNameCn": "AC过频1下限(中国区)", "paraNameEn": "AcFreqUp1 dnLimit[CN]", "paraVarName": "ProLimitInfo[E_CN].acFreqUp1.dnLimit", "paraMacroName": "ID_CN_AC_FREQ_UP1_DN_LIMIT", "paraDescription": "AC过频1下限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1223", "paraNameCn": "AC过频1滤波时间(中国区)", "paraNameEn": "AcFreqUp1 filterTime[CN]", "paraVarName": "ProLimitInfo[E_CN].acFreqUp1.filter", "paraMacroName": "ID_CN_AC_FREQ_UP1_FILTER", "paraDescription": "AC过频1滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "1224", "paraNameCn": "AC过频2上限(中国区)", "paraNameEn": "AcFreqUp2 upLimit[CN]", "paraVarName": "ProLimitInfo[E_CN].acFreqUp2.upLimit", "paraMacroName": "ID_CN_AC_FREQ_UP2_UP_LIMIT", "paraDescription": "AC过频2上限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1225", "paraNameCn": "AC过频2下限(中国区)", "paraNameEn": "AcFreqUp2 dnLimit[CN]", "paraVarName": "ProLimitInfo[E_CN].acFreqUp2.dnLimit", "paraMacroName": "ID_CN_AC_FREQ_UP2_DN_LIMIT", "paraDescription": "AC过频2下限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1226", "paraNameCn": "AC过频2滤波时间(中国区)", "paraNameEn": "AcFreqUp2 filterTime[CN]", "paraVarName": "ProLimitInfo[E_CN].acFreqUp2.filter", "paraMacroName": "ID_CN_AC_FREQ_UP2_FILTER", "paraDescription": "AC过频2滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "1227", "paraNameCn": "AC过频3上限(中国区)", "paraNameEn": "AcFreqUp3 upLimit[CN]", "paraVarName": "ProLimitInfo[E_CN].acFreqUp3.upLimit", "paraMacroName": "ID_CN_AC_FREQ_UP3_UP_LIMIT", "paraDescription": "AC过频3上限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1228", "paraNameCn": "AC过频3下限(中国区)", "paraNameEn": "AcFreqUp3 dnLimit[CN]", "paraVarName": "ProLimitInfo[E_CN].acFreqUp3.dnLimit", "paraMacroName": "ID_CN_AC_FREQ_UP3_DN_LIMIT", "paraDescription": "AC过频3下限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1229", "paraNameCn": "AC过频3滤波时间(中国区)", "paraNameEn": "AcFreqUp3 filterTime[CN]", "paraVarName": "ProLimitInfo[E_CN].acFreqUp3.filter", "paraMacroName": "ID_CN_AC_FREQ_UP3_FILTER", "paraDescription": "AC过频3滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "1230", "paraNameCn": "AC欠频1上限(中国区)", "paraNameEn": "AcFreqDn1 upLimit[CN]", "paraVarName": "ProLimitInfo[E_CN].acFreqDn1.upLimit", "paraMacroName": "ID_CN_AC_FREQ_DN1_UP_LIMIT", "paraDescription": "AC欠频1上限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1231", "paraNameCn": "AC欠频1下限(中国区)", "paraNameEn": "AcFreqDn1 dnLimit[CN]", "paraVarName": "ProLimitInfo[E_CN].acFreqDn1.dnLimit", "paraMacroName": "ID_CN_AC_FREQ_DN1_DN_LIMIT", "paraDescription": "AC欠频1下限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1232", "paraNameCn": "AC欠频1滤波时间(中国区)", "paraNameEn": "AcFreqDn1 filterTime[CN]", "paraVarName": "ProLimitInfo[E_CN].acFreqDn1.filter", "paraMacroName": "ID_CN_AC_FREQ_DN1_FILTER", "paraDescription": "AC欠频1滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "1233", "paraNameCn": "AC欠频2上限(中国区)", "paraNameEn": "AcFreqDn2 upLimit[CN]", "paraVarName": "ProLimitInfo[E_CN].acFreqDn2.upLimit", "paraMacroName": "ID_CN_AC_FREQ_DN2_UP_LIMIT", "paraDescription": "AC欠频2上限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1234", "paraNameCn": "AC欠频2下限(中国区)", "paraNameEn": "AcFreqDn2 dnLimit[CN]", "paraVarName": "ProLimitInfo[E_CN].acFreqDn2.dnLimit", "paraMacroName": "ID_CN_AC_FREQ_DN2_DN_LIMIT", "paraDescription": "AC欠频2下限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1235", "paraNameCn": "AC欠频2滤波时间(中国区)", "paraNameEn": "AcFreqDn2 filterTime[CN]", "paraVarName": "ProLimitInfo[E_CN].acFreqDn2.filter", "paraMacroName": "ID_CN_AC_FREQ_DN2_FILTER", "paraDescription": "AC欠频2滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}, {"paraId": "1236", "paraNameCn": "AC欠频3上限(中国区)", "paraNameEn": "AcFreqDn3 upLimit[CN]", "paraVarName": "ProLimitInfo[E_CN].acFreqDn3.upLimit", "paraMacroName": "ID_CN_AC_FREQ_DN3_UP_LIMIT", "paraDescription": "AC欠频3上限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1237", "paraNameCn": "AC欠频3下限(中国区)", "paraNameEn": "AcFreqDn3 dnLimit[CN]", "paraVarName": "ProLimitInfo[E_CN].acFreqDn3.dnLimit", "paraMacroName": "ID_CN_AC_FREQ_DN3_DN_LIMIT", "paraDescription": "AC欠频3下限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "N"}, {"paraId": "1238", "paraNameCn": "AC欠频3滤波时间(中国区)", "paraNameEn": "AcFreqDn3 filterTime[CN]", "paraVarName": "ProLimitInfo[E_CN].acFreqDn3.filter", "paraMacroName": "ID_CN_AC_FREQ_DN3_FILTER", "paraDescription": "AC欠频3滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "N"}], "装备测试": [{"paraId": "1000", "paraNameCn": "装备使能命令", "paraNameEn": "Equipment Cmd", "paraVarName": "EquipmentPara.cmd", "paraMacroName": "ID_EQUIP_CMD", "paraDescription": "装备使能命令 0禁止 1使能", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "1001", "paraNameCn": "绿灯控制字", "paraNameEn": "Equipment LedCtrlG", "paraVarName": "EquipmentPara.ledCtrlG", "paraMacroName": "ID_EQUIP_LED_CTRL_G", "paraDescription": "绿灯控制 0 灭 1 亮", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "1002", "paraNameCn": "橙灯控制字", "paraNameEn": "Equipment LedCtrlR", "paraVarName": "EquipmentPara.ledCtrlR", "paraMacroName": "ID_EQUIP_LED_CTRL_R", "paraDescription": "橙灯控制 0 灭 1 亮", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "1003", "paraNameCn": "老化使能命令", "paraNameEn": "Aging Cmd", "paraVarName": "AgingPara.cmd", "paraMacroName": "ID_AGING_CND", "paraDescription": "老化使能命令 0禁止 1使能", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "1", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "1004", "paraNameCn": "老化状态", "paraNameEn": "Aging State", "paraVarName": "AgingPara.state", "paraMacroName": "ID_AGING_STATE", "paraDescription": "老化结果 0 老化前 1 老化中 2老化后", "paraUnit": "null", "r_wAttr": "R_O", "r_wOfRunAttr": "N", "maxValue": "2", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "1005", "paraNameCn": "老化结果", "paraNameEn": "A<PERSON>t", "paraVarName": "AgingPara.result", "paraMacroName": "ID_AGING_RESULT", "paraDescription": "老化结果 0 未老化 1 成功 2失败", "paraUnit": "null", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "2", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U8", "saveAttr": "N"}, {"paraId": "1006", "paraNameCn": "老化结束时间", "paraNameEn": "Aging EndTime", "paraVarName": "AgingPara.endTime", "paraMacroName": "ID_AGING_END_TIME", "paraDescription": "老化用时", "paraUnit": "min", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "10000000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U32", "saveAttr": "N"}, {"paraId": "1007", "paraNameCn": "老化限制时间", "paraNameEn": "Aging LimitTime", "paraVarName": "AgingPara.limitTime", "paraMacroName": "ID_AGING_LIMIT_TIME", "paraDescription": "老化限制时间", "paraUnit": "min", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "10000000", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_U32", "saveAttr": "N"}], "自定义故障保护门限": [{"paraId": "1100", "paraNameCn": "AC过压1上限(自定义)", "paraNameEn": "AcVolUp1 upLimit[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acVolUp1.upLimit", "paraMacroName": "ID_DIY_AC_VOL_UP1_UP_LIMIT", "paraDescription": "AC过压1上限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "1101", "paraNameCn": "AC过压1下限(自定义)", "paraNameEn": "AcVolUp1 dnLimit[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acVolUp1.dnLimit", "paraMacroName": "ID_DIY_AC_VOL_UP1_DN_LIMIT", "paraDescription": "AC过压1下限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "1102", "paraNameCn": "AC过压1滤波时间(自定义)", "paraNameEn": "AcVolUp1 filterTime[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acVolUp1.filter", "paraMacroName": "ID_DIY_AC_VOL_UP1_FILTER", "paraDescription": "AC过压1滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "Y"}, {"paraId": "1103", "paraNameCn": "AC过压2上限(自定义)", "paraNameEn": "AcVolUp2 upLimit[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acVolUp2.upLimit", "paraMacroName": "ID_DIY_AC_VOL_UP2_UP_LIMIT", "paraDescription": "AC过压2上限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "1104", "paraNameCn": "AC过压2下限(自定义)", "paraNameEn": "AcVolUp2 dnLimit[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acVolUp2.dnLimit", "paraMacroName": "ID_DIY_AC_VOL_UP2_DN_LIMIT", "paraDescription": "AC过压2下限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "1105", "paraNameCn": "AC过压2滤波时间(自定义)", "paraNameEn": "AcVolUp2 filterTime[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acVolUp2.filter", "paraMacroName": "ID_DIY_AC_VOL_UP2_FILTER", "paraDescription": "AC过压2滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "Y"}, {"paraId": "1106", "paraNameCn": "AC过压3上限(自定义)", "paraNameEn": "AcVolUp3 upLimit[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acVolUp3.upLimit", "paraMacroName": "ID_DIY_AC_VOL_UP3_UP_LIMIT", "paraDescription": "AC过压3上限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "1107", "paraNameCn": "AC过压3下限(自定义)", "paraNameEn": "AcVolUp3 dnLimit[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acVolUp3.dnLimit", "paraMacroName": "ID_DIY_AC_VOL_UP3_DN_LIMIT", "paraDescription": "AC过压3下限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "1108", "paraNameCn": "AC过压3滤波时间(自定义)", "paraNameEn": "AcVolUp3 filterTime[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acVolUp3.filter", "paraMacroName": "ID_DIY_AC_VOL_UP3_FILTER", "paraDescription": "AC过压3滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "Y"}, {"paraId": "1109", "paraNameCn": "AC10分钟平均过压上限(自定义)", "paraNameEn": "tenMinAvgVolUp upLimit[DIY]", "paraVarName": "ProLimitInfo[E_DIY].tenMinAvgVolUp.upLimit", "paraMacroName": "ID_DIY_TEN_MIN_VOL_UP_UP_LIMIT", "paraDescription": "AC10分钟过压上限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "1110", "paraNameCn": "AC10分钟平均过压下限(自定义)", "paraNameEn": "tenMinAvgVolUp dnLimit[DIY]", "paraVarName": "ProLimitInfo[E_DIY].tenMinAvgVolUp.dnLimit", "paraMacroName": "ID_DIY_TEN_MIN_VOL_UP_DN_LIMIT", "paraDescription": "AC10分钟过压下限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "1111", "paraNameCn": "AC10分钟平均过压滤波时间(自定义)", "paraNameEn": "tenMinAvgVolUp filterTime[DIY]", "paraVarName": "ProLimitInfo[E_DIY].tenMinAvgVolUp.filter", "paraMacroName": "ID_DIY_TEN_MIN_VOL_UP_FILTER", "paraDescription": "AC10分钟过压滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "Y"}, {"paraId": "1112", "paraNameCn": "AC欠压1上限(自定义)", "paraNameEn": "AcVolDn1 upLimit[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acVolDn1.upLimit", "paraMacroName": "ID_DIY_AC_VOL_DN1_UP_LIMIT", "paraDescription": "AC欠压1上限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "1113", "paraNameCn": "AC欠压1下限(自定义)", "paraNameEn": "AcVolDn1 dnLimit[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acVolDn1.dnLimit", "paraMacroName": "ID_DIY_AC_VOL_DN1_DN_LIMIT", "paraDescription": "AC欠压1下限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "1114", "paraNameCn": "AC欠压1滤波时间(自定义)", "paraNameEn": "AcVolDn1 filterTime[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acVolDn1.filter", "paraMacroName": "ID_DIY_AC_VOL_DN1_FILTER", "paraDescription": "AC欠压1滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "Y"}, {"paraId": "1115", "paraNameCn": "AC欠压2上限(自定义)", "paraNameEn": "AcVolDn2 upLimit[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acVolDn2.upLimit", "paraMacroName": "ID_DIY_AC_VOL_DN2_UP_LIMIT", "paraDescription": "AC欠压2上限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "1116", "paraNameCn": "AC欠压2下限(自定义)", "paraNameEn": "AcVolDn2 dnLimit[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acVolDn2.dnLimit", "paraMacroName": "ID_DIY_AC_VOL_DN2_DN_LIMIT", "paraDescription": "AC欠压2下限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "1117", "paraNameCn": "AC欠压2滤波时间(自定义)", "paraNameEn": "AcVolDn2 filterTime[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acVolDn2.filter", "paraMacroName": "ID_DIY_AC_VOL_DN2_FILTER", "paraDescription": "AC欠压2滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "Y"}, {"paraId": "1118", "paraNameCn": "AC欠压3上限(自定义)", "paraNameEn": "AcVolDn3 upLimit[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acVolDn3.upLimit", "paraMacroName": "ID_DIY_AC_VOL_DN3_UP_LIMIT", "paraDescription": "AC欠压3上限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "1119", "paraNameCn": "AC欠压3下限(自定义)", "paraNameEn": "AcVolDn3 dnLimit[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acVolDn3.dnLimit", "paraMacroName": "ID_DIY_AC_VOL_DN3_DN_LIMIT", "paraDescription": "AC欠压3下限", "paraUnit": "V", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "1120", "paraNameCn": "AC欠压3滤波时间(自定义)", "paraNameEn": "AcVolDn3 filterTime[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acVolDn3.filter", "paraMacroName": "ID_DIY_AC_VOL_DN3_FILTER", "paraDescription": "AC欠压3滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "Y"}, {"paraId": "1121", "paraNameCn": "AC过频1上限(自定义)", "paraNameEn": "AcFreqUp1 upLimit[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acFreqUp1.upLimit", "paraMacroName": "ID_DIY_AC_FREQ_UP1_UP_LIMIT", "paraDescription": "AC过频1上限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "1122", "paraNameCn": "AC过频1下限(自定义)", "paraNameEn": "AcFreqUp1 dnLimit[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acFreqUp1.dnLimit", "paraMacroName": "ID_DIY_AC_FREQ_UP1_DN_LIMIT", "paraDescription": "AC过频1下限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "1123", "paraNameCn": "AC过频1滤波时间(自定义)", "paraNameEn": "AcFreqUp1 filterTime[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acFreqUp1.filter", "paraMacroName": "ID_DIY_AC_FREQ_UP1_FILTER", "paraDescription": "AC过频1滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "Y"}, {"paraId": "1124", "paraNameCn": "AC过频2上限(自定义)", "paraNameEn": "AcFreqUp2 upLimit[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acFreqUp2.upLimit", "paraMacroName": "ID_DIY_AC_FREQ_UP2_UP_LIMIT", "paraDescription": "AC过频2上限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "1125", "paraNameCn": "AC过频2下限(自定义)", "paraNameEn": "AcFreqUp2 dnLimit[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acFreqUp2.dnLimit", "paraMacroName": "ID_DIY_AC_FREQ_UP2_DN_LIMIT", "paraDescription": "AC过频2下限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "1126", "paraNameCn": "AC过频2滤波时间(自定义)", "paraNameEn": "AcFreqUp2 filterTime[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acFreqUp2.filter", "paraMacroName": "ID_DIY_AC_FREQ_UP2_FILTER", "paraDescription": "AC过频2滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "Y"}, {"paraId": "1127", "paraNameCn": "AC过频3上限(自定义)", "paraNameEn": "AcFreqUp3 upLimit[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acFreqUp3.upLimit", "paraMacroName": "ID_DIY_AC_FREQ_UP3_UP_LIMIT", "paraDescription": "AC过频3上限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "1128", "paraNameCn": "AC过频3下限(自定义)", "paraNameEn": "AcFreqUp3 dnLimit[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acFreqUp3.dnLimit", "paraMacroName": "ID_DIY_AC_FREQ_UP3_DN_LIMIT", "paraDescription": "AC过频3下限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "1129", "paraNameCn": "AC过频3滤波时间(自定义)", "paraNameEn": "AcFreqUp3 filterTime[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acFreqUp3.filter", "paraMacroName": "ID_DIY_AC_FREQ_UP3_FILTER", "paraDescription": "AC过频3滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "Y"}, {"paraId": "1130", "paraNameCn": "AC欠频1上限(自定义)", "paraNameEn": "AcFreqDn1 upLimit[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acFreqDn1.upLimit", "paraMacroName": "ID_DIY_AC_FREQ_DN1_UP_LIMIT", "paraDescription": "AC欠频1上限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "1131", "paraNameCn": "AC欠频1下限(自定义)", "paraNameEn": "AcFreqDn1 dnLimit[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acFreqDn1.dnLimit", "paraMacroName": "ID_DIY_AC_FREQ_DN1_DN_LIMIT", "paraDescription": "AC欠频1下限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "1132", "paraNameCn": "AC欠频1滤波时间(自定义)", "paraNameEn": "AcFreqDn1 filterTime[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acFreqDn1.filter", "paraMacroName": "ID_DIY_AC_FREQ_DN1_FILTER", "paraDescription": "AC欠频1滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "Y"}, {"paraId": "1133", "paraNameCn": "AC欠频2上限(自定义)", "paraNameEn": "AcFreqDn2 upLimit[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acFreqDn2.upLimit", "paraMacroName": "ID_DIY_AC_FREQ_DN2_UP_LIMIT", "paraDescription": "AC欠频2上限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "1134", "paraNameCn": "AC欠频2下限(自定义)", "paraNameEn": "AcFreqDn2 dnLimit[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acFreqDn2.dnLimit", "paraMacroName": "ID_DIY_AC_FREQ_DN2_DN_LIMIT", "paraDescription": "AC欠频2下限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "1135", "paraNameCn": "AC欠频2滤波时间(自定义)", "paraNameEn": "AcFreqDn2 filterTime[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acFreqDn2.filter", "paraMacroName": "ID_DIY_AC_FREQ_DN2_FILTER", "paraDescription": "AC欠频2滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "Y"}, {"paraId": "1136", "paraNameCn": "AC欠频3上限(自定义)", "paraNameEn": "AcFreqDn3 upLimit[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acFreqDn3.upLimit", "paraMacroName": "ID_DIY_AC_FREQ_DN3_UP_LIMIT", "paraDescription": "AC欠频3上限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "1137", "paraNameCn": "AC欠频3下限(自定义)", "paraNameEn": "AcFreqDn3 dnLimit[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acFreqDn3.dnLimit", "paraMacroName": "ID_DIY_AC_FREQ_DN3_DN_LIMIT", "paraDescription": "AC欠频3下限", "paraUnit": "Hz", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_F32", "saveAttr": "Y"}, {"paraId": "1138", "paraNameCn": "AC欠频3滤波时间(自定义)", "paraNameEn": "AcFreqDn3 filterTime[DIY]", "paraVarName": "ProLimitInfo[E_DIY].acFreqDn3.filter", "paraMacroName": "ID_DIY_AC_FREQ_DN3_FILTER", "paraDescription": "AC欠频3滤波时间", "paraUnit": "Ms", "r_wAttr": "R_W", "r_wOfRunAttr": "Y", "maxValue": "65535", "minValue": "0", "defaultValue": "0", "dataType": "TYPE_I32", "saveAttr": "Y"}]}