## 参数定时上传

● 订阅方：云
● 发布方：Emu
● topic：Param/Emu/${Emu_SN}/EmuTimedUpload
● qos：0
● 保留消息：否
● 是否回复：否
● 发布消息格式

```json
{
  // 数据id
  "mid": 3342,
  // 时间戳
  "timeStamp": 1624982406963,
  "gateWayData": {
    // EMU序列号
    "sn": "10000123456789",
    // 运行状态，1：正常
    "runningStatus": 1,
    // 联网模式，0：WIFI,1:有线网，2：4G
    "commType":0,
    // 数据上传间隔时间
    "uploadInterval": 15,
    // 连接微逆数量
    "monitorNum": 200,
    // 网关软件版本号
    "softVer": 10000100,
    // 网关硬件版本号
    "hardVer": 12000001,
    // 时区
    "timeCode": "UTC-8"
  },
  "invData": [
    {
      // 微逆序列号
      "sn": "11000123456789",
      "data": {
        // PV瞬时功率
        "pvTempPower": 1,
        // AC瞬时功率
        "acTempPower": 1,
        // 电网频率
        "gridFreq": 230.5,
        // 系统状态,1自检ok 2停机 4运行 8故障 16告警
        "sysStatus": 40.2,
        //启机控制,0/停止  1/启动
        "sysCtrlCmd": 1,
        // PV电压1实际值
        "pvVolReal": 1,
        // PV电流1实际值
        "PvCurrReal": 230.5,
        // AC电压实际值
        "acVolReal": 1,
        // AC电流实际值
        "acCurrReal": 1,
        // AC MOS温度实际值
        "acMosTempReal": 230.5,
        // PV MOS温度实际值
        "pVMosTempReal": 1,
        // 日运行时间，小时
        "dayRunTime": 1,
        // 日发电量
        "dailyEnergy":200.5,
        // 微逆发电效率,%
        "efficiency":95.5
      },
      // 连接组件数量
      "pvCount": 2,
      // 和EMU的通信状态，0：通信异常，1：正常
      "commStatus": 1,
      // 故障码
      "faultCode": {
        "faultCode1": 3,
        "faultCode2": 3,
        "faultCode3": 5
      },
      // 微逆软件版本号
      "softVer": 10000100,
      // 微逆硬件版本号
      "hardVer": 12000001
    },
    {
      "sn": "11000123456790",
      "data": {
        "pvTempPower": 1,
        "acTempPower": 1,
        "gridFreq": 230.5,
        "sysStatus": 40.2,
        "sysCtrlCmd": 1,
        "pvVolReal": 1,
        "PvCurrReal": 230.5,
        "acVolReal": 1,
        "acCurrReal": 1,
        "acMosTempReal": 230.5,
        "pVMosTempReal": 1,
        "dayRunTime": 1,
        "dailyEnergy":200.5,
        "efficiency":95.5
      },
      "pvCount": 2,
      "commStatus": 1,
      // 故障码
      "faultCode": {
        "faultCode1": 3,
        "faultCode2": 3,
        "faultCode3": 5
      },
      // 微逆软件版本号
      "softVer": 10000100,
      // 微逆硬件版本号
      "hardVer": 12000001
    }
  ]
}
```



## 电表数据定时上传

● 订阅方：云
● 发布方：Emu
● topic：Param/Emu/${Emu_SN}/TimedUploadMeterData
● qos：0
● 保留消息：否
● 是否回复：否
● 发布消息格式

```json
{
  "mid": 3342,
  "timeStamp": 1624982406963,
  "EmuSn": "10000123456789",
  // 时区
  "timeCode": "UTC+8",
  "meterData": [
    {
      "sn": 13000123456789,
      "commStatus": 1,
      "power": 200.3,
      "reverseEnergy": 120.5,
      "forwardEnergy": 100.3

    },
    {
      "sn": 13000123456790,
      "commStatus": 1,
      "power": 200.3,
      "reverseEnergy": 120.5,
      "forwardEnergy": 100.3
    }
  ]
}
```

## 

## 设置Emu时区

● 订阅方：Emu
● 发布方：云
● topic：SetTime/Emu/${Emu_SN}/EmuTimeZone

● ResponseTopic：SetTime/Emu/${Emu_SN}/ResponseEmuTimeZone

● qos：0

● 保留消息：否
● 是否回复：否
● 发布消息格式

```json
{
  "mid": 3549,
  "timeStamp": 1624982416963,
  "sn": 10000123456789,
  "timeCode": "UTC-8",
  // 电站当地时间
  "time": 1624982416963
}
```

● 回复消息格式

```json
{
  "mid": 3550,
  "responseId": 3549,
  "timeStamp": 1624982416969,
  "sn": "10000123456789",
  "status": 1,
  // 错误码
  "errorCode" : 10,
}
```



## 读Inv参数

● 订阅方：Emu
● 发布方：云
● topic：ReadParam/Emu/${Emu_SN}/ReadInvOfEmuParam

● Response：ReadParam/Emu/${Emu_SN}/ResponseReadInvOfEmuParam

● qos：0
● 保留消息：否
● 是否回复：是
● 发布消息格式

```json
{
  "mid": 3350,
  "timeStamp": 1624982406918,
  "sn": "10000123456789",
  "invData": {
    "Inv" : ["11000123456789","11000123456790"]
  }
}
```

● 回复消息格式

```json
{
  "mid": 3356,
  "responseId": 3550,
  "timeStamp": 1624982406933,
  "sn": "10000123456789",
  // 错误码
  "errorCode" : 10,
  "invData": [
    {
      "sn": "11000123456789",
      "data": {
        "pvTempPower": 1,
        "acTempPower": 1,
        "gridFreq": 230.5,
        "sysStatus": 40.2,
        "sysCtrlCmd": 1,
        "pvVolReal": 1,
        "PvCurrReal": 230.5,
        "acVolReal": 1,
        "acCurrReal": 1,
        "acMosTempReal": 230.5,
        "pVMosTempReal": 1,
        "dayRunTime": 1,
        "dailyEnergy":200.5
      },
      "pvCount": 2,
      "commStatus": 1,
      "faultCode": 0,
      "softVer": 10000100,
      "hardVer": 12000001
    },
    {
      "sn": "11000123456790",
      "data": {
        "pvTempPower": 1,
        "acTempPower": 1,
        "gridFreq": 230.5,
        "sysStatus": 40.2,
        "sysCtrlCmd": 1,
        "pvVolReal": 1,
        "PvCurrReal": 230.5,
        "acVolReal": 1,
        "acCurrReal": 1,
        "acMosTempReal": 230.5,
        "pVMosTempReal": 1,
        "dayRunTime": 1,
        "dailyEnergy":200.5
      },
      "pvCount": 2,
      "commStatus": 1,
      "faultCode": 0,
      "softVer": 10000100,
      "hardVer": 12000001
    }
  ]
}
```





## 读Emu参数

● 订阅方：Emu
● 发布方：云
● topic：ReadParam/${Emu_SN}/ReadEmuParam

● Response：ReadParam/${Emu_SN}/ResponseReadEmuParam

● qos：0
● 保留消息：否
● 是否回复：是
● 发布消息格式

```json
{
  "mid": 3346,
  "timeStamp": 1624982406918,
  "sn": "10000123456789"
}
```

● 回复消息格式

```json
{
  "mid": 3349,
  "responseId": 3546,
  "timeStamp": 1624982406933,
  "sn": "10000123456789",
  // 错误码
  "errorCode" : 10,
  "gateWayData": {
    "sn": "10000123456789",
    "runningStatus": 1,
    "uploadInterval": 15,
    "monitorNum": 200,
    "softVer": 10000100,
    "hardVer": 12000001
  }
}
```



## 读全部参数

● 订阅方：Emu
● 发布方：云
● topic：ReadParam/Emu/${Emu_SN}/ReadAllParam

● Response：ReadParam/Emu/${Emu_SN}/ResponseReadAllParam

● qos：0
● 保留消息：否
● 是否回复：是
● 发布消息格式

```json
{
  "mid": 3566,
  "timeStamp": 1624982406918,
  "sn": "10000123456789"
}
```

● 回复消息格式

```json
{
  "mid": 3356,
  "responseId": 3550,
  "timeStamp": 1624982406933,
  "sn": "10000123456789",
  // 错误码
  "errorCode" : 10,
  "invData": [
    {
      "sn": "11000123456789",
      "data": {
        "pvTempPower": 1,
        "acTempPower": 1,
        "gridFreq": 230.5,
        "sysStatus": 40.2,
        "sysCtrlCmd": 1,
        "pvVolReal": 1,
        "PvCurrReal": 230.5,
        "acVolReal": 1,
        "acCurrReal": 1,
        "acMosTempReal": 230.5,
        "pVMosTempReal": 1,
        "dayRunTime": 1,
        "dailyEnergy":200.5
      },
      "pvCount": 2,
      "commStatus": 1,
      // 故障码
      "faultCode": {
        "faultCode1": 3,
        "faultCode2": 3,
        "faultCode3": 5
      },
      // 微逆软件版本号
      "softVer": 10000100,
      // 微逆硬件版本号
      "hardVer": 12000001
    },
    {
      "sn": "11000123456790",
      "data": {
        "pvTempPower": 1,
        "acTempPower": 1,
        "gridFreq": 230.5,
        "sysStatus": 40.2,
        "sysCtrlCmd": 1,
        "pvVolReal": 1,
        "PvCurrReal": 230.5,
        "acVolReal": 1,
        "acCurrReal": 1,
        "acMosTempReal": 230.5,
        "pVMosTempReal": 1,
        "dayRunTime": 1,
        "dailyEnergy":200.5
      },
      "pvCount": 2,
      "commStatus": 1,
      // 故障码
      "faultCode": {
        "faultCode1": 3,
        "faultCode2": 3,
        "faultCode3": 5
      },
      // 微逆软件版本号
      "softVer": 10000100,
      // 微逆硬件版本号
      "hardVer": 12000001
    }
  ]
}
```



## EMU自动组网

● 订阅方：Emu
● 发布方：云
● topic：ReadParam/Emu/${Emu_SN}/ReadInvNetworking

● Response：ReadParam/Emu/${Emu_SN}/ResponseReadInvNetworking

● qos：0
● 保留消息：否
● 是否回复：是
● 发布消息格式

```json
{
  "mid": 3346,
  "timeStamp": 1624982406918,
  "sn": "10000123456789",
  // 组网方式,0:手动组网，1：自动组网
  "type":0
}
```
● 回复消息格式

```json
{
  "mid": 3349,
  "responseId": 3546,
  "timeStamp": 1624982406933,
  "sn": "10000123456789",
  "type":1，
  // 0:失败，1成功
  "status" : 1,
  // 错误码
  "errorCode" : 10,
  "inv":["600050123456789698F1","600050123456789698F1","600050123456789698F1"]
}
```

手动组网回复消息格式

```json
{
  "mid": 3349,
  "responseId": 3546,
  "timeStamp": 1624982406933,
  "sn": "10000123456789",
  "type":0
  // 0:失败，1成功
  "status" : 1,
  // 错误码
  "errorCode" : 10
}
```





## 写微逆参数

● 订阅方：Emu
● 发布方：云
● topic：WriteParam/Emu/${Emu_SN}/WriteInvOfEmuParam

● Response：WriteParam/Emu/${Emu_SN}/ResponseWriteInvOfEmuParam

● qos：0
● 保留消息：否
● 是否回复：是
● 发布消息格式

```json
{
  "mid": 3349,
  "timeStamp": 1624982406963,
  "sn": 10000123456789,
  "invData": [
    {
      "sn": 11000123456789,
      "data": [
        {
          "addr": 9,
          "value": 1
        },
        {
          "addr": 10,
          "value": 18
        }
      ]
    },
    {
      "sn": 11000123456799,
      "data": [
        {
          "addr": 9,
          "value": 1
        },
        {
          "addr": 10,
          "value": 18
        }
      ]
    }
  ]
}
```

● 回复消息格式(写完毕后，将写的参数读上来完成组帧传递给服务器)

```json
{
  "mid": 3369,
  "responseId": 3349,
  "timeStamp": 1624982406983,
  "result":[
    {
      "sn": "10000123456789",
      "status" : 1,
      // 错误码
      "errorCode" : 10
    },
    {
      "sn": "10000123456789",
      "status" : 1,
      // 错误码
      "errorCode" : 10
    }
  ]

}
```

## 写Emu参数

● 订阅方：Emu
● 发布方：云
● topic：WriteParam/Emu/${Emu_SN}/WriteEmuParam

● Response：WriteParam/Emu/${Emu_SN}/ResponseWriteEmuParam

● qos：0
● 保留消息：否
● 是否回复：是
● 发布消息格式

```json
{
  "mid": 3349,
  "timeStamp": 1624982406963,
  "sn": "10000123456789",
  "gateWayData": [
    {
      "addr": 120,
      "value": 13
    },
    {
      "addr": 102,
      "value": 8
    }
  ]
}
```



● 回复消息格式(写完毕后，将写的参数读上来完成组帧传递给服务器)

```json
{
  "mid": 3359,
  "responseId": 3349,
  "timeStamp": 1624982406973,
  "sn": "10000123456789",
  "status" : 1,
  // 错误码
  "errorCode" : 10
}
```





## 升级EMU固件

● 订阅方：Emu
● 发布方：云
● topic：Upgrade/Emu/${Emu_SN}/UpgradeEmuFile

● response：Upgrade/Emu/${Emu_SN}/ResponseUpgradeEmuFile

● qos：0
● 保留消息：否
● 是否回复：是
● 发布消息格式

**备注**：发布消息中带了升级文件的下载链接，Emu收到后根据链接和HTTP协议下载对应的升级文件。

```json
{
  "mid": 3169,
  "timeStamp": 1624983407983,
  "sn": "10000123456789",
  "emu": {
    "url": "http://192.168.10.117/HttpEmuFileDown.bin",
    "md5": "2a56eaee5bd94ba9e12224e4cf04c11d"
  }
}
```

● 回复消息格式

```json
//升级微逆
{
  "mid": 3179,
  "responseId": 3169,
  "timeStamp": 1624983407986,
  "sn": "10000123456789",
  "status": 1,
  "progress":100,
  // 错误码
  "errorCode" : 10
}

```



## 升级微逆固件

● 订阅方：Emu
● 发布方：云
● topic：Upgrade/Emu/${Emu_SN}/UpgradeInvOfEmuFile

● response：Upgrade/Emu/${Emu_SN}/ResponseUpgradeInvOfEmuFile

● qos：0
● 保留消息：否
● 是否回复：是
● 发布消息格式

**备注**：发布消息中带了升级文件的下载链接，Emu收到后根据链接和HTTP协议下载对应的升级文件。

```json
//升级微逆-Inv
{
  "mid": 3169,
  "timeStamp": 1624983407983,
  "sn": "10000123456789",
  "inv": {
    "url": "http://192.168.10.117/HttpFileDown.bin",
    "md5": "2a56eaee5bd94ba9e12224e4cf04c11d",
    "sn": ["11000123456789","11000123456799"]
  }
}
```

● 回复消息格式

```json
{
  "mid": 3179,
  "responseId": 3169,
  "timeStamp": 1624983407986,
  "sn": "10000123456789",
  "result":{
    "sn":"11000123456789",
    "status": 1,
    "progress":100,
    // 错误码
    "errorCode" : 10
  }
}

```







## 配置电表

● 订阅方：Emu
● 发布方：云
● topic：Meter/Emu/${Emu_SN}/configMeter

● qos：0
● 保留消息：否
● 是否回复：否
● 发布消息格式

```json
{
  "mid": 3359,
  "timeStamp": 1624982406963,
  "sn": "10000123456789",
  // 并网测电表
  "gridMeter": {
    // 是否启用电表，0：不启用，1：启用
    "enable": 1,
    // 电表sn号
    "sn": "1224512131121",
    // 是否启用防逆流功能
    "enableReflux": 1,
    // 防逆流功率
    "power":10
  },
  // 负载测电表
  "loadMeter": {
    // 是否启用电表，0：不启用，1：启用
    "enable": 1,
    // 电表sn号
    "sn": "1224512131121"
  },
  // 光伏测电表
  "photovoltaicMeter": {
    // 是否启用电表，0：不启用，1：启用
    "enable": 1,
    // 电表sn号
    "sn": "1224512131121"
  }
}
```


## 给EMU下发组网信息

● 订阅方：Emu
● 发布方：云
● topic：WriteParam/Emu/${Emu_SN}/WriteEmuNetworking

● Response：WriteParam/Emu/${Emu_SN}/ResponseWriteEmuNetworking

● qos：0
● 保留消息：否
● 是否回复：是
● 发布消息格式

```json
{
  "mid": 3359,
  "timeStamp": 1624982406963,
  "sn": "10000123456789",
  // 组网方式,0:手动组网，1：自动组网
  "type":0
  "inv": [
    {
      "sn": "10000123456789",
      // 微逆链接的组件数
      "pcCount": 2,
      "pv": [
        {
          // 组件编号
          "num": 1,
          // 位置
          "location": "0,1",
          // 方向，0：水平，1：垂直
          "direction": 0,
          // 倾角
          "angle": 45,
          // 仰角
          "elevation": 30
        },
        {
          // 组件编号
          "num": 2,
          // 位置
          "location": "0,1",
          // 方向，0：水平，1：垂直
          "direction": 0,
          // 倾角
          "angle": 45,
          // 仰角
          "elevation": 30
        }
      ]
    },
    {
      "sn": "10000123456799",
      // 微逆链接的组件数
      "pcCount": 2,
      "pv": [
        {
          // 组件编号
          "num": 1,
          // 位置
          "location": "0,1",
          // 方向，0：水平，1：垂直
          "direction": 0,
          // 倾角
          "angle": 45,
          // 仰角
          "elevation": 30
        },
        {
          // 组件编号
          "num": 2,
          // 位置
          "location": "0,1",
          // 方向，0：水平，1：垂直
          "direction": 0,
          // 倾角
          "angle": 45,
          // 仰角
          "elevation": 30
        }
      ]
    }
  ]
}
```



## 下发并网文件

● 订阅方：Emu
● 发布方：云
● topic：WriteParam/Emu/${Emu_SN}/WriteGridParam

● Response：WriteParam/Emu/${Emu_SN}/ResponseWriteGridParam

● qos：0
● 保留消息：否
● 是否回复：是
● 发布消息格式

```json
{
  "mid": "3349",
  "timeStamp": 1624982406963,
  "sn": "10000123456789",
  // 并网参数,并网文件 id，固定地址 9
  "params": [
    {
      "addr": 9,
      "value": 100001
    },
    {
      "addr": 10,
      "value": 18
    },
    {
      "addr": 11,
      "value": 18
    }
  ],
  // 微逆sn
  "inv": [
    "10000123456789",
    "10000123456789",
    "10000123456789"
  ]
}
```

● 回复消息格式



```json
{
  "mid": "3369",
  "responseId": "3349",
  "timeStamp": 1624982406983,
  // emu sn
  "sn": "10000123456789",
  "result":[
    {
      "sn": "10000123456789",
      "status" : 1,
      // 错误码
      "errorCode" : 10
    },
    {
      "sn": "10000123456789",
      "status" : 1,
      // 错误码
      "errorCode" : 10
    }
  ]
}
```





## 并网文件查看

● 订阅方：Emu
● 发布方：云
● topic：WriteParam/Emu/${Emu_SN}/ReadGridParam

● Response：WriteParam/Emu/${Emu_SN}/ResponseReadGridParam

● qos：0
● 保留消息：否
● 是否回复：是
● 发布消息格式

```json
{
  "mid": "3349",
  "timeStamp": 1624982406963,
  "sn": "10000123456789",
  // 微逆sn
  "inv": "10000123456789",
  // 并网文件 fileId,固定地址 9
  "params" :[9,10,11]
}
```

● 回复消息格式(写完毕后，将写的参数读上来完成组帧传递给服务器)

```json
{
  "mid": "3369",
  "responseId": "3349",
  "timeStamp": 1624982406983,
  // emu sn
  "sn": "10000123456789",
  // 微逆 sn
  "inv": "10000123456789",
  // 并网参数
  "params": [
    {
      "addr": 9,
      "value": 1
    },
    {
      "addr": 10,
      "value": 18
    },
    {
      "addr": 11,
      "value": 18
    }
  ],
  // 0-失败，1成功
  "status" : 1,
  // 错误码
  "errorCode" : 10
}
```



## 读取故障录播参数配置

● 订阅方：Emu
● 发布方：云
● topic：ReadFile/Emu/${Emu_SN}/ReadFaultWaveConfig

● response：ReadFile/Emu/${Emu_SN}/ResponseReadFaultWaveConfig

● qos：0
● 保留消息：否
● 是否回复：是
● 发布消息格式

```json
{
    "mid": "cec464fa-359b-434b-ab9b-ddbe5f025aef",
	"timeStamp": 1624982406963,
	"sn": "10000123456789",
    "inv": "10000123456789"
}
```

● 回复消息格式

回复消息格式

```json
{
  "mid": "cec464fa-359b-434b-ab9b-ddbe5f025aef",
  "responseId": "caf39cda-65a8-5a1b-88b9-51db743e9b37",
  "timeStamp": 1624982407969,
  "sn": "10000123456789",
  "inv": "11000123456789",
  "status": 1,
  "errorCode" : 10,
  //采样频率
  "sampleFreq": 100,
  //采样间隔
  "sampleRate": 1000,
  //触发前百分比
  "trigPercent": 30.5,
  //触发类型
  "trigType": 1,
  //触发时间
  "year": 2021,
  "month": 1,
  "day": 1,
  "hour": 1,
  "minute": 1,
  "second": 1,
  //触发ID
  "trigId": 1,
  //触发Bit 位
  "trigBit":2,
  //触发门限
  "trigLimit": 20,
  //实际通道数量
  "channelCount": 6,
  //通道 ID
  "channelId": [10, 20, 30, 40, 50, 60]
}

```



## 写故障录播参数配置

● 订阅方：Emu
● 发布方：云
● topic：WriteParam/Emu/${Emu_SN}/WriteInvOfEmuParam

● Response：WriteParam/Emu/${Emu_SN}/ResponseWriteInvOfEmuParam

● qos：0
● 保留消息：否
● 是否回复：是
● 发布消息格式

```json
{
    "mid": "cec464fa-359b-434b-ab9b-ddbe5f025aef",
	"timeStamp": 1624982406963,
	"sn": 10000123456789,
	"invData": [
		{
			"sn": 11000123456789,
			"data": [
				{
					"addr": 9,
					"value": 1
				},
				{
					"addr": 10,
					"value": 18
				}
			]
		}
	]
}
```

● 回复消息格式(写完毕后，将写的参数读上来完成组帧传递给服务器)

```json
{
    "mid": "cec464fa-359b-434b-ab9b-ddbe5f025aef",
    "responseId": "caf39cda-65a8-5a1b-88b9-51db743e9b37",
	"timeStamp": 1624982406983,
    "result":[
      {
        "sn": "10000123456789",
     	 "status" : 1,
     	 // 错误码
      	"errorCode" : 10
      }
    ]
	
}
```

## 

## 读取故障录播

● 订阅方：Emu
● 发布方：云
● topic：ReadFile/Emu/${Emu_SN}/InvFaultWave

● response：ReadFile/Emu/${Emu_SN}/ResponseInvFaultWave

● qos：0
● 保留消息：否
● 是否回复：是
● 发布消息格式

```json
{
    "mid": "cec464fa-359b-434b-ab9b-ddbe5f025aef",
	"timeStamp": 1624982406963,
	"sn": "10000123456789",
    "inv": "10000123456789"
}
```

● 回复消息格式

```json
{
  "mid": "cec464fa-359b-434b-ab9b-ddbe5f025aef",
  "responseId": "caf39cda-65a8-5a1b-88b9-51db743e9b37",
  "timeStamp": 1624982407969,
  "sn": "10000123456789",
  "inv": "11000123456789",
  "status": 1,
  "errorCode" : 10,
  //采样频率
  "sampleFreq": 100,
  //采样间隔
  "sampleRate": 1000,
  //触发前百分比
  "trigPercent": 30.5,
  //触发类型
  "trigType": 1,
  //触发时间
  "year": 2021,
  "month": 1,
  "day": 1,
  "hour": 1,
  "minute": 1,
  "second": 1,
  //触发ID
  "trigId": 1,
  //触发Bit 位
  "trigBit":2,
  //触发门限
  "trigLimit": 20,
  //实际通道数量
  "channelCount": 6,
  //通道 ID
  "channelId": [10, 20, 30, 40, 50, 60],
  "faultWave": [
    {
      "channel": 1,
      "data": [10.0, 20.0, 30.0, 40.0, 50.0, 60.0, 70.0, 80.0, 90.0, 100.0, 110.0, 120.0, 130.0, 140.0, 150.0, 160.0, 170.0, 180.0, 190.0, 200.0, 210.0, 220.0, 230.0, 240.0, 250.0, 260.0, 270.0, 280.0, 290.0]
    },  {
      "channel": 2,
      "data": [10.0, 20.0, 30.0, 40.0, 50.0, 60.0, 70.0, 80.0, 90.0, 100.0, 110.0, 120.0, 130.0, 140.0, 150.0, 160.0, 170.0, 180.0, 190.0, 200.0, 210.0, 220.0, 230.0, 240.0, 250.0, 260.0, 270.0, 280.0, 290.0]
    },  {
      "channel": 3,
      "data": [10.0, 20.0, 30.0, 40.0, 50.0, 60.0, 70.0, 80.0, 90.0, 100.0, 110.0, 120.0, 130.0, 140.0, 150.0, 160.0, 170.0, 180.0, 190.0, 200.0, 210.0, 220.0, 230.0, 240.0, 250.0, 260.0, 270.0, 280.0, 290.0]
    },  {
      "channel": 4,
      "data": [10.0, 20.0, 30.0, 40.0, 50.0, 60.0, 70.0, 80.0, 90.0, 100.0, 110.0, 120.0, 130.0, 140.0, 150.0, 160.0, 170.0, 180.0, 190.0, 200.0, 210.0, 220.0, 230.0, 240.0, 250.0, 260.0, 270.0, 280.0, 290.0]
    },  {
      "channel": 5,
      "data": [10.0, 20.0, 30.0, 40.0, 50.0, 60.0, 70.0, 80.0, 90.0, 100.0, 110.0, 120.0, 130.0, 140.0, 150.0, 160.0, 170.0, 180.0, 190.0, 200.0, 210.0, 220.0, 230.0, 240.0, 250.0, 260.0, 270.0, 280.0, 290.0]
    },  {
      "channel": 6,
      "data": [10.0, 20.0, 30.0, 40.0, 50.0, 60.0, 70.0, 80.0, 90.0, 100.0, 110.0, 120.0, 130.0, 140.0, 150.0, 160.0, 170.0, 180.0, 190.0, 200.0, 210.0, 220.0, 230.0, 240.0, 250.0, 260.0, 270.0, 280.0, 290.0]
    }
  ]
}

```





## 读故障记录文件

● 订阅方：Emu
● 发布方：云
● topic：ReadFile/Emu/${Emu_SN}/ReadInvFaultLog

● response：ReadFile/Emu/${Emu_SN}/ResponseReadInvFaultLog

● qos：0
● 保留消息：否
● 是否回复：是
● 发布消息格式

```json
{
  "mid": "cec464fa-359b-434b-ab9b-ddbe5f025aef",
  "timeStamp": 1624982406963,
  "sn": "10000123456789",
  "inv": "10000123456789"
}
```

● 回复消息格式

```json
{
  "mid": "cec464fa-359b-434b-ab9b-ddbe5f025aef",
  "responseId": "caf39cda-65a8-5a1b-88b9-51db743e9b37",
  "timeStamp": 1624982407969,
  "sn": "10000123456789",
  "inv" : "11000123456789",
  "status": 1,
  "errorCode" : 10,
  "faultLog": [
    {
      "serialNum": 1,
      "year": 2021,
      "month": 1,
      "day": 1,
      "hour": 1,
      "minute": 1,
      "second": 1,
      "faultCode1": 10,
      "faultCode2": 10,
      "faultCode3": 10,
      "faultCode4": 10,
      "faultCode5": 10,
      "acMosTempReal": 230.5,
      "pVMosTempReal": 1,
      "pvVolReal": 1,
      "PvCurrReal": 230.5,
      "acVolEffective": 1,
      "acVolReal": 1,
      "acCurrEffective": 1,
      "acCurrReal": 1
    },
    {
      "serialNum": 2,
      "year": 2021,
      "month": 1,
      "day": 1,
      "hour": 1,
      "minute": 1,
      "second": 1,
      "faultCode1": 10,
      "faultCode2": 10,
      "faultCode3": 10,
      "faultCode4": 10,
      "faultCode5": 10,
      "acMosTempReal": 230.5,
      "pVMosTempReal": 1,
      "pvVolReal": 1,
      "PvCurrReal": 230.5,
      "acVolEffective": 1,
      "acVolReal": 1,
      "acCurrEffective": 1,
      "acCurrReal": 1
    }
  ]
}

```



