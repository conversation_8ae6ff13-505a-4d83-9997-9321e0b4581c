package com.ruoyi.gateway.config;

import com.ruoyi.gateway.handler.ValidateCodeHandler;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.server.*;

/**
 * 路由配置信息
 *
 * <AUTHOR>
 */
@Configuration
public class RouterFunctionConfiguration {

    @Resource
    private ValidateCodeHandler validateCodeHandler;

    @Bean
    public RouterFunction<ServerResponse> routerFunction() {
        RequestPredicate request = RequestPredicates.GET("/code").and(RequestPredicates.accept(MediaType.TEXT_PLAIN));
        return RouterFunctions.route(request, validateCodeHandler);
    }
}
