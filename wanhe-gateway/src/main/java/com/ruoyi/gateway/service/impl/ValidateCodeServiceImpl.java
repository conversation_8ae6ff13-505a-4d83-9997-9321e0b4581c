package com.ruoyi.gateway.service.impl;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.google.code.kaptcha.impl.DefaultKaptcha;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.enums.LanguageEnum;
import com.ruoyi.common.core.exception.CaptchaException;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.I18nUtil;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.sign.Base64;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.gateway.config.properties.CaptchaProperties;
import com.ruoyi.gateway.service.ValidateCodeService;
import com.wanhe.common.mail.MailUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.FastByteArrayOutputStream;
import org.springframework.web.reactive.function.server.ServerRequest;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.Locale;
import java.util.concurrent.TimeUnit;

/**
 * 验证码实现处理
 *
 * <AUTHOR>
 */
@Service
public class ValidateCodeServiceImpl implements ValidateCodeService {

//    @Resource(name = "captchaProducerMath")
//    @Resource(name = "captchaProducer")
//    private DefaultKaptcha captchaProducerMath;
    @Resource
    private RedisService redisService;
    @Resource
    private CaptchaProperties captchaProperties;

    /**
     * 生成验证码
     */
    @Override
    public AjaxResult createCaptcha(ServerRequest request) {
        Locale locale = LanguageEnum.getLocal(request.headers().firstHeader(SecurityConstants.LANGUAGE));
        AjaxResult ajax = AjaxResult.success();
        boolean captchaEnabled = captchaProperties.getEnabled();
        ajax.put("captchaEnabled", captchaEnabled);
        if (!captchaEnabled) {
            return ajax;
        }
        // 保存验证码信息
        String uuid = IdUtils.simpleUUID();
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + uuid;
        String code = "";
        String email = request.queryParams().getFirst("email");
        String type = request.queryParams().getFirst("type");
        if (StrUtil.isNotBlank(email)) {
            if (!Validator.isEmail(email)) {
                throw new ServiceException("请输入正确的邮箱地址");
            }
            String username = request.queryParams().getFirst("username");
            if (StrUtil.isBlank(username)) throw new ServiceException("用户名不能为空");
            code = RandomUtil.randomNumbers(6);
            String content = "";
            if ("1".equals(type) ) {
                content = I18nUtil.get("email.captcha", locale, username, I18nUtil.get("email.register", locale), code, Constants.CAPTCHA_EXPIRATION);
            } else {
                content = I18nUtil.get("email.captcha", locale, username, I18nUtil.get("email.forget", locale), code, Constants.CAPTCHA_EXPIRATION);
            }
            MailUtil.send(email, I18nUtil.get("万禾悦能邮箱验证", locale), content, true);

        } else {
            String clientType = request.headers().asHttpHeaders().getFirst(SecurityConstants.CLIENT_TYPE);
            // 生成验证码
            if ("1".equals(clientType)) {
                String captchaType = captchaProperties.getType();
                DefaultKaptcha captchaProducerMath = SpringUtils.getBean("captchaProducer" + StrUtil.upperFirst(captchaType));
                String capStr = "";
                if ("math".equals(captchaType)) {
                    String capText = captchaProducerMath.createText();
                    capStr = capText.substring(0, capText.lastIndexOf("@"));
                    code = capText.substring(capText.lastIndexOf("@") + 1);
                } else if ("char".equals(captchaType)) {
                    capStr = code = captchaProducerMath.createText();
                }
                BufferedImage image = captchaProducerMath.createImage(capStr);
//                String capText = captchaProducerMath.createText();
//                String capStr = capText.substring(0, capText.lastIndexOf("@"));
//                code = capText.substring(capText.lastIndexOf("@") + 1);
//                BufferedImage image = captchaProducerMath.createImage(capStr);
                // 转换流信息写出
                FastByteArrayOutputStream os = new FastByteArrayOutputStream();
                try {
                    ImageIO.write(image, "jpg", os);
                } catch (IOException e) {
                    return AjaxResult.error(e.getMessage());
                }
                ajax.put("img", Base64.encode(os.toByteArray()));
            } else {
                code = RandomUtil.randomNumbers(4);
                ajax.put("img", code);
            }
        }
        redisService.setCacheObject(verifyKey, code, Constants.CAPTCHA_EXPIRATION, TimeUnit.MINUTES);
        ajax.put("uuid", uuid);
        return ajax;
    }

    /**
     * 校验验证码
     */
    @Override
    public void checkCaptcha(String code, String uuid) {
        if (StringUtils.isEmpty(code)) {
            throw new CaptchaException("验证码不能为空");
        }
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(uuid, "");
        String captcha = redisService.getCacheObject(verifyKey);
        if (captcha == null) {
            throw new CaptchaException("验证码已失效");
        }
        if (!code.equalsIgnoreCase(captcha)) {
            throw new CaptchaException("验证码错误");
        }
        redisService.deleteObject(verifyKey);
    }
}
