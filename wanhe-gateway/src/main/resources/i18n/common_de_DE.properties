#i18n.common_de_DE.properties
#Wed Feb 12 18:37:55 CST 2025
请求地址不允许访问=Zugriff auf angeforderte Adresse nicht erlaubt
文件sheet不存在=Datei-Sheet existiert nicht
验证码不能为空=Bestätigungscode darf nicht leer sein
用户名不能为空=Benutzername darf nicht leer sein
参数不符合规范，不能进行查询=Parameter entspricht nicht den Spezifikationen, Abfrage nicht möglich
参数存在SQL注入风险=Parameter enthält SQL-Injektionsrisiko
未知设备类型=Unbekannter Gerätetyp
缺失数据或数据格式错误=Fehlende Daten oder falsches Datenformat
翻译失败=Übersetzung fehlgeschlagen
没有内部访问权限，不允许访问=Keine internen Zugriffsberechtigungen, Zugriff nicht erlaubt
验证码错误=Bestätigungscode falsch
令牌不能为空=Token darf nicht leer sein
序列号只能输入数字和大写字母=Seriennummer darf nur Zahlen und Großbuchstaben enthalten
令牌验证失败=Token-Überprüfung fehlgeschlagen
服务未找到=Dienst nicht gefunden
email.auth=Sehr geehrte(r) {0}: Hallo!\nSie führen gerade eine Wartung der Kontobasisinformationen durch. Bitte geben Sie den Prüfcode ein: {1}, um den Vorgang abzuschließen.
email.captcha=<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Security Verification Code</title>\n</head>\n<body style=\"margin: 0; padding: 0; font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;\">\n    <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"max-width: 600px; margin: 20px auto;\">\n        <tr>\n            <td style=\"padding: 30px; background-color: #ffffff;\">\n                <h2 style=\"color: #2d3436; margin: 0 0 25px 0;\">Security Code Verification</h2>\n                \n                <p style=\"color: #636e72; line-height: 1.6; margin: 0 0 15px 0;\">\n                    Dear {0}, {1}. Please use the following verification code:\n                </p>\n\n                <div style=\"background-color: #f8f9fa; padding: 20px; margin: 25px 0; border-radius: 4px; text-align: center;\">\n                    <div style=\"font-size: 32px; color: #2d3436; letter-spacing: 3px; font-weight: bold; margin: 10px 0;\">\n                        {2}\n                    </div>\n                    <div style=\"color: #e74c3c; font-size: 13px;\">\n                        (Valid for {3} minutes)\n                    </div>\n                </div>\n\n                <div style=\"border-left: 4px solid #e74c3c; padding: 15px; background-color: #fff5f5; margin: 25px 0;\">\n                    <h3 style=\"color: #c0392b; margin: 0 0 10px 0;\">Security Notice</h3>\n                    <ul style=\"color: #636e72; margin: 0; padding-left: 20px;\">\n                        <li>Do not share this code with anyone</li>\n                    </ul>\n                </div>\n            </td>\n        </tr>\n        <tr>\n            <td style=\"padding: 20px 30px; background-color: #f8f9fa; color: #636e72; font-size: 12px;\">\n                <p style=\"margin: 5px 0;\">This is an automated email, please do not reply</p>\n                <p style=\"margin: 5px 0;\">    © 2024 OneHope Energy Technology Co., Ltd. </p>\n            </td>\n        </tr>\n    </table>\n</body>\n</html>
email.register=you are registering an account
email.forget=you are changing your password
设备生产批次错误=Fehler in der Geräteproduktionscharge
没有设置用户信息，不允许访问=Keine Benutzerinformationen eingerichtet, Zugriff nicht erlaubt
内部服务器错误=Interner Serverfehler
验证码已失效=Bestätigungscode abgelaufen
万禾悦能邮箱验证=Wanhe Yueneng E-Mail-Verifizierung
数据不存在，编号为{0}=Daten existieren nicht, Nummer {0}
设备流水号错误=Geräteseriennummer falsch
请求超过最大数，请稍候再试=Anfrage überschreitet Höchstzahl, bitte später erneut versuchen
序列号长度必须为20位=Seriennummer muss 20 Stellen lang sein
用户名或密码错误=Benutzername oder Passwort falsch
参数已超过最大限制，不能进行查询=Parameter überschreitet Höchstgrenze, Abfrage nicht möglich
客户端类型错误=Client-Typ falsch
登录状态已过期=Anmeldestatus abgelaufen
没有访问权限，请联系管理员授权=Keine Zugriffsberechtigungen, bitte Administrator für Autorisierung kontaktieren
数据存在重复=Daten bereits vorhanden
令牌已过期或验证不正确=Token abgelaufen oder Überprüfung falsch
设备故障通知=Gerätefehlermeldung
请输入正确的邮箱地址=Bitte geben sie die richtige e-mail-adresse ein