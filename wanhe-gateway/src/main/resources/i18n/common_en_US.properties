#i18n.common_en_US.properties
#Wed Feb 12 18:37:55 CST 2025
请求地址不允许访问=Requested address cannot be accessed
文件sheet不存在=File sheet does not exist
验证码不能为空=Verification code cannot be empty
用户名不能为空=Username cannot be empty
参数不符合规范，不能进行查询=Parameter error. Query failed
参数存在SQL注入风险=Parameters have a risk of SQL injection
未知设备类型=Unknown device type
缺失数据或数据格式错误=Missing data or data format error
翻译失败=Translation failed
没有内部访问权限，不允许访问=No internal access permission. Access denied
验证码错误=Verification code error
令牌不能为空=Token cannot be empty
序列号只能输入数字和大写字母=SN can only contain numbers and uppercase letters
令牌验证失败=Token verification failed
服务未找到=Service not found
email.auth=Dear {0}: \n You are currently maintaining the basic account information. Please enter: {1} in the verification code input box to complete the operation.
email.captcha=<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Security Verification Code</title>\n</head>\n<body style=\"margin: 0; padding: 0; font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;\">\n    <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"max-width: 600px; margin: 20px auto;\">\n        <tr>\n            <td style=\"padding: 30px; background-color: #ffffff;\">\n                <h2 style=\"color: #2d3436; margin: 0 0 25px 0;\">Security Code Verification</h2>\n                \n                <p style=\"color: #636e72; line-height: 1.6; margin: 0 0 15px 0;\">\n                    Dear {0}, {1}. Please use the following verification code:\n                </p>\n\n                <div style=\"background-color: #f8f9fa; padding: 20px; margin: 25px 0; border-radius: 4px; text-align: center;\">\n                    <div style=\"font-size: 32px; color: #2d3436; letter-spacing: 3px; font-weight: bold; margin: 10px 0;\">\n                        {2}\n                    </div>\n                    <div style=\"color: #e74c3c; font-size: 13px;\">\n                        (Valid for {3} minutes)\n                    </div>\n                </div>\n\n                <div style=\"border-left: 4px solid #e74c3c; padding: 15px; background-color: #fff5f5; margin: 25px 0;\">\n                    <h3 style=\"color: #c0392b; margin: 0 0 10px 0;\">Security Notice</h3>\n                    <ul style=\"color: #636e72; margin: 0; padding-left: 20px;\">\n                        <li>Do not share this code with anyone</li>\n                    </ul>\n                </div>\n            </td>\n        </tr>\n        <tr>\n            <td style=\"padding: 20px 30px; background-color: #f8f9fa; color: #636e72; font-size: 12px;\">\n                <p style=\"margin: 5px 0;\">This is an automated email, please do not reply</p>\n                <p style=\"margin: 5px 0;\">    © 2024 OneHope Energy Technology Co., Ltd. </p>\n            </td>\n        </tr>\n    </table>\n</body>\n</html>
email.register=you are registering an account
email.forget=you are changing your password
设备生产批次错误=Device production batch error
没有设置用户信息，不允许访问=User info not set. Access denied
内部服务器错误=Internal server error
验证码已失效=Verification code expired
万禾悦能邮箱验证=OneHo email verification
数据不存在，编号为{0}=Data does not exist, number {0}
设备流水号错误=Device SN error
请求超过最大数，请稍候再试=Max requests exceeded. Try again later
序列号长度必须为20位=SN must be 20 digits in length
用户名或密码错误=Wrong username or password
参数已超过最大限制，不能进行查询=Parameters exceed max limit. Query failed
客户端类型错误=Client type error
登录状态已过期=Login status expired
没有访问权限，请联系管理员授权=No access permission. Contact admin
数据存在重复=Duplicate data
令牌已过期或验证不正确=Token expired or verification error
设备故障通知=Device fault notice
请输入正确的邮箱地址=Please enter the correct email address