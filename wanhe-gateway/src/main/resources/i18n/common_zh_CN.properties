#i18n.common_zh_CN.properties
#Wed Feb 12 18:37:55 CST 2025
请求地址不允许访问=请求地址不允许访问
文件sheet不存在=文件sheet不存在
验证码不能为空=验证码不能为空
用户名不能为空=用户名不能为空
参数不符合规范，不能进行查询=参数不符合规范，不能进行查询
参数存在SQL注入风险=参数存在SQL注入风险
未知设备类型=未知设备类型
缺失数据或数据格式错误=缺失数据或数据格式错误
翻译失败=翻译失败
没有内部访问权限，不允许访问=没有内部访问权限，不允许访问
验证码错误=验证码错误
令牌不能为空=令牌不能为空
序列号只能输入数字和大写字母=序列号只能输入数字和大写字母
令牌验证失败=令牌验证失败
服务未找到=服务未找到
email.auth=尊敬的{0}：您好！\n您正在进行帐号基础信息维护，请在校验码输入框中输入：{1}，以完成操作。
email.captcha=<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>安全验证码通知</title>\n</head>\n<body style=\"margin: 0; padding: 0; font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;\">\n    <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"max-width: 600px; margin: 20px auto;\">\n        <tr>\n            <td style=\"padding: 30px; background-color: #ffffff;\">\n                <h2 style=\"color: #2d3436; margin: 0 0 25px 0;\">安全验证码通知</h2>\n                \n                <p style=\"color: #636e72; line-height: 1.6; margin: 0 0 15px 0;\">\n                    尊敬的 {0}，您好！{1}，请使用以下验证码完成验证：\n                </p>\n\n                <div style=\"background-color: #f8f9fa; padding: 20px; margin: 25px 0; border-radius: 4px; text-align: center;\">\n                    <div style=\"font-size: 32px; color: #2d3436; letter-spacing: 3px; font-weight: bold; margin: 10px 0;\">\n                        {2}\n                    </div>\n                    <div style=\"color: #e74c3c; font-size: 13px;\">\n                        （有效期：{3}分钟）\n                    </div>\n                </div>\n\n                <div style=\"border-left: 4px solid #e74c3c; padding: 15px; background-color: #fff5f5; margin: 25px 0;\">\n                    <h3 style=\"color: #c0392b; margin: 0 0 10px 0;\">安全提醒</h3>\n                    <ul style=\"color: #636e72; margin: 0; padding-left: 20px;\">\n                        <li>请勿向任何人泄露验证码</li>\n                    </ul>\n                </div>\n            </td>\n        </tr>\n        <tr>\n            <td style=\"padding: 20px 30px; background-color: #f8f9fa; color: #636e72; font-size: 12px;\">\n                <p style=\"margin: 5px 0;\">本邮件为系统发送，请勿直接回复</p>\n                <p style=\"margin: 5px 0;\">© 2024 万禾悦能源科技有限公司 </p>\n            </td>\n        </tr>\n    </table>\n</body>\n</html>
email.register=您正在注册账号操作
email.forget=您正在修改密码操作
设备生产批次错误=设备生产批次错误
没有设置用户信息，不允许访问=没有设置用户信息，不允许访问
内部服务器错误=内部服务器错误
验证码已失效=验证码已失效
万禾悦能邮箱验证=OneHoPE邮箱验证
数据不存在=数据不存在
设备流水号错误=设备流水号错误
请求超过最大数，请稍候再试=请求超过最大数，请稍候再试
序列号长度必须为20位=序列号长度必须为20位
用户名或密码错误=用户名或密码错误
参数已超过最大限制，不能进行查询=参数已超过最大限制，不能进行查询
客户端类型错误=客户端类型错误
登录状态已过期=登录状态已过期
没有访问权限，请联系管理员授权=没有访问权限，请联系管理员授权
数据存在重复=数据存在重复
令牌已过期或验证不正确=令牌已过期或验证不正确
设备故障通知=设备故障通知
请输入正确的邮箱地址=请输入正确的邮箱地址
