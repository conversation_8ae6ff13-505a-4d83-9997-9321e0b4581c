## 参数定时上传

● 订阅方：云
● 发布方: 微逆
● topic：Param/Inv/${Inv_SN}/InvTimedUpload
● qos：0
● 保留消息：否
● 是否回复：否
● 发布消息格式 

```json
{
   // 数据id
	"mid": 3342,
   // 时间戳
	"timeStamp": 1624982406963,
  // 序列号
	"sn": "11000123456789",
    "wifi": {
       // 运行状态，1：正常
		"runningStatus": 1,
       // 数据上传间隔时间
		"uploadInterval": 15,
        // wifi软件版本号
        "softVer": 10000100,
       // wifi硬件版本号
        "hardVer": 12000001,
        "timeCode": "UTC-8"
	},
	"invData": {
	  // PV瞬时功率
      "pvTempPower": 1,
      // AC瞬时功率
      "acTempPower": 1,
      // 电网频率
      "gridFreq": 230.5,
      // 系统状态,1自检ok 2停机 4运行 8故障 16告警
      "sysStatus": 40.2,
      //启机控制,0/停止  1/启动
      "sysCtrlCmd": 1,
      // PV电压1实际值
      "pvVolReal": 1,
      // PV电流1实际值
      "PvCurrReal": 230.5,
      // AC电压实际值
      "acVolReal": 1,
      // AC电流实际值
      "acCurrReal": 1,
      // AC MOS温度实际值
      "acMosTempReal": 230.5,
      // PV MOS温度实际值
      "pVMosTempReal": 1,
      // 日运行时间，小时
      "dayRunTime": 1,
      // 日发电量
      "dailyEnergy":200.5,
      // 微逆发电效率,%
      "efficiency":95.5
       // 连接组件数量
        "pvCount": 2,
        "commStatus": 1,
        // 故障码
	 "faultCode": {
		"faultCode1": 3,
		"faultCode2": 3,
		"faultCode3": 5
	 },
        // 微逆软件版本号
        "softVer": 10000100,
        // 微逆硬件版本号
        "hardVer": 12000001
	}
}
```

## 设置微逆时区

● 订阅方：微逆
● 发布方：云
● topic：SetTime/Inv/${Inv_SN}/InvTimeZone

● ResponseTopic：SetTime/Inv/${Inv_SN}/ResponseInvTimeZone

● qos：0
● 保留消息：否
● 是否回复：否
● 发布消息格式

```json
{
	"mid": 3549,
	"timeStamp": 1624982416963,
	"sn": 10000123456789,
	"timeCode": "UTC-8",
     // 电站当地时间
    "time": 1624982416963
}
```

● 回复消息格式 

```json
{
	"mid": 3550,
	"ResponseId": 3549,
	"timeStamp": 1624982416969,
	"sn": "10000123456789",
  	"status": 1
}
```



## 读Inv参数

● 订阅方：EMU
● 发布方：云
● topic：ReadParam/Inv/${Inv_SN}/ReadInvParam

● Response：ReadParam/Inv/${Inv_SN}/ResponseReadInvParam

● qos：0
● 保留消息：否
● 是否回复：是
● 发布消息格式

```json
{
    "mid": 3350,
	"timeStamp": 1624982406918,
	"sn": "11000123456789"
}
```

● 回复消息格式 

```json
{
	"mid": 3342,
	"timeStamp": 1624982406963,
	"sn": "11000123456789",
    "wifi": {
		"runningStatus": 1,
		"uploadInterval": 15,
        "softVer": 10000100,
        "hardVer": 12000001,
	},
	"invData": {
		"pvTempPower": 1,
        "acTempPower": 1,
        "gridFreq": 230.5,
        "sysStatus": 40.2,
        "sysCtrlCmd": 1,
        "pvVolReal": 1,
        "PvCurrReal": 230.5,
        "acVolReal": 1,
        "acCurrReal": 1,
        "acMosTempReal": 230.5,
        "pVMosTempReal": 1,
        "dayRunTime": 1,
        "dailyEnergy":200.5,
        "pvCount": 2,
        "commStatus": 1,
        "faultCode": 0,
        "softVer": 10000100,
        "hardVer": 12000001
	}
}
```







## 写微逆参数

● 订阅方：微逆
● 发布方：云
● topic：WriteParam/Inv/${Inv_SN}/WriteInvParam

● Response：WriteParam/Inv/${Inv_SN}/ResponseWriteInvParam

● qos：0
● 保留消息：否
● 是否回复：是
● 发布消息格式

```json
{
	"mid": 3349,
	"timeStamp": 1624982406963,
	"sn": 10000123456789,
	"invData": [
		{
			"addr": 9,
			"value": 1
		},
		{
			"addr": 10,
			"value": 18
		}
	]
}
```

● 回复消息格式(写完毕后，将写的参数读上来完成组帧传递给服务器)

```json
{
	"mid": 3369,
	"ResponseId": 3349,
	"timeStamp": 1624982406983,
	"sn": "10000123456789",
	"status" : 1
}
```





## 读发电量文件

● 订阅方： 微逆
● 发布方：云
● topic：ReadFile/Inv/${Inv_SN}/ReadInvEnergy

● response：ReadFile/Inv/${Inv_SN}/ResponseReadInvEnergy

● qos：0
● 保留消息：否
● 是否回复：是
● 发布消息格式

```json
{
	"mid": 3549,
	"timeStamp": 1624982416998,
	"sn": "10000123456789"

}
```

回复消息(发电量文件内容需要再根据实际内容添加）

```json
{
	"mid": 3559,
	"ResponseId": 3549,
	"timeStamp": 1624982416999,
	"sn": "10000123456789",
	"invEnergy": {
      "sn": "11000123456789",
      "total" : 61,
      "todayTotal": 45.22,
	  "monthTotal": 52.4,
      "yearTotal": 59.3
    }
}
```



## 读故障录播文件

● 订阅方：微逆
● 发布方：云
● topic：ReadFile/Inv/${Inv_SN}/ReadInvFaultLog

● response：ReadFile/Inv/${Inv_SN}/ResponseReadInvFaultLog

● qos：0
● 保留消息：否
● 是否回复：是
● 发布消息格式

```json
{
	"mid": 3149,
	"timeStamp": 1624982406963,
	"sn": "10000123456789"
}
```

● 回复消息格式(故障录波文件内容需要再根据实际内容添加）

```json
{
	"mid": 3159,
	"ResponseId": 3149,
	"timeStamp": 1624982407969,
	"sn": "10000123456789",
	"FaultLogData": {
		......
	}
}
```



## 升级

● 订阅方：微逆
● 发布方：云
● topic：Upgrade/Inv/${Inv_SN}/UpgradeInvFile

● response：Upgrade/Inv/${Inv_SN}/ResponseUpgradeInvFile

● qos：0
● 保留消息：否
● 是否回复：是
● 发布消息格式

**备注**：发布消息中带了升级文件的下载链接，Wifi收到后根据链接和HTTP协议下载对应的升级文件。

```json
//升级微逆-Inv
{
	"mid": 3169,
	"timeStamp": 1624983407983,
	"sn": "11000123456789",
    // 0-微逆固件，1-微逆wifi文件
  	"type":0,
	"url": "http://192.168.10.117/HttpFileDown.bin",
	"md5": "2a56eaee5bd94ba9e12224e4cf04c11d"
}

```

● 回复消息格式

```json
//升级微逆
{
	"mid": 3179,
    "ResponseId": 3169,
	"timeStamp": 1624983407986,
	"sn": "10000123456789",
    "type":0,
     // 0-升级失败，1-升级中，2升级成功
    "status": 1,
 	 "progress":100 
}
```



## 给微逆下发组网信息

● 订阅方：微逆
● 发布方：云
● topic：WriteParam/Inv/${Inv_SN}/WriteInvNetworking

● Response：WriteParam/Inv/${Inv_SN}/ResponseWriteInvNetworking

● qos：0
● 保留消息：否
● 是否回复：是
● 发布消息格式

```json
{
	"mid": 3359,
	"timeStamp": 1624982406963,
    "sn": "10000123456789",
      // 微逆链接的组件数
      "pcCount": 2,
      "pv": [
        {
          // 组件编号
          "num": 1,
          // 位置
          "location": "0,1",
          // 方向，0：水平，1：垂直
          "direction": 0,
          // 倾角
          "angle": 45,
          // 仰角
          "elevation": 30
        },
        {
          // 组件编号
          "num": 2,
          // 位置
          "location": "0,1",
          // 方向，0：水平，1：垂直
          "direction": 0,
          // 倾角
          "angle": 45,
          // 仰角
          "elevation": 30
        }
      ]
}
```



## 下发并网文件

● 订阅方：微逆
● 发布方：云
● topic：WriteParam/Inv/${Inv_SN}/WriteInvGridParam

● Response：WriteParam/Inv/${Inv_SN}/ResponseWriteInvGridParam

● qos：0
● 保留消息：否
● 是否回复：是
● 发布消息格式

```json
{
	"mid": "3349",
	"timeStamp": 1624982406963,
	"sn": "10000123456789",
     // 并网参数,并网文件 id，固定地址 9
	"params": [
		{
			"addr": 9,
			"value": 100001
		},
		{
			"addr": 10,
			"value": 18
		},
		{
			"addr": 11,
			"value": 18
		}
	],
     // 微逆sn
	"inv": "10000123456789"
}
```

● 回复消息格式(写完毕后，将写的参数读上来完成组帧传递给服务器)

```json
{
	"mid": "3369",
	"responseId": "3349",
	"timeStamp": 1624982406983,   
    "sn": "10000123456789",
  	"inv": "10000123456789"
    "status" : 1,
     	 // 错误码
    "errorCode" : 10

}
```



## 并网文件查看

● 订阅方：微逆
● 发布方：云
● topic：WriteParam/Inv/${Inv_SN}/ReadInvGridParam

● Response：WriteParam/Inv/${Inv_SN}/ResponseReadInvGridParam

● qos：0
● 保留消息：否
● 是否回复：是
● 发布消息格式

```json
{
	"mid": "3349",
	"timeStamp": 1624982406963,
     // 微逆sn
	"sn": "10000123456789",
    // 并网文件 fileId,固定地址 9
    "params" :[9,10,11]
}
```

● 回复消息格式(写完毕后，将写的参数读上来完成组帧传递给服务器)

```json
{
	"mid": "3369",
	"responseId": "3349",
	"timeStamp": 1624982406983,
     // 微逆 sn
	"sn": "10000123456789",
     // 并网参数
	"params": [
		{
			"addr": 9,
			"value": 1
		},
		{
			"addr": 10,
			"value": 18
		},
		{
			"addr": 11,
			"value": 18
		}
	],
	// 0-失败，1成功
	 "status" : 1,
      // 错误码
     "errorCode" : 10
}
```

