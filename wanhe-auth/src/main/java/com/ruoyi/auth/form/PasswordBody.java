package com.ruoyi.auth.form;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @date 2025/1/5
 */
@Data
public class PasswordBody {

    /**
     * 用户名
     */
    @NotBlank
    private String username;

    /**
     * 邮箱
     */
    @NotBlank
    @Email
    private String email;

    /**
     * 用户密码
     */
    @NotBlank
    @Length(min = 5, max = 20)
    private String password;

    // 验证码
    @NotBlank
    private String code;

    // 唯一标识
    @NotBlank
    private String uuid;

}
