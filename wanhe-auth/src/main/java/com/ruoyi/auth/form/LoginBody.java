package com.ruoyi.auth.form;

import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

/**
 * 用户登录对象
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class LoginBody {
    /**
     * 用户名
     */
    @NotBlank
    @Length(min = 2, max = 20)
    private String username;

    /**
     * 用户密码
     */
    @NotBlank
    @Length(min = 5, max = 20)
    private String password;

    /**
     * 验证码UUID
     */
    @NotBlank
    private String uuid;

    /**
     * 验证码
     */
    @NotBlank
    private String code;

}
