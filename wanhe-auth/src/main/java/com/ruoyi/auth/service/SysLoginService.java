package com.ruoyi.auth.service;

import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.UserStatus;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.ip.IpUtils;
import com.ruoyi.common.redis.RedisUtil;
import com.ruoyi.common.security.service.BaseService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.model.LoginUser;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Component
public class SysLoginService {

    @Resource
    private RemoteUserService remoteUserService;
    @Resource
    private SysPasswordService passwordService;
    @Resource
    private SysRecordLogService recordLogService;

    /**
     * 登录
     */
    public LoginUser login(String username, String password, String clientType) {
        // 查询用户信息
        R<LoginUser> userResult = remoteUserService.getUserInfo(username, clientType, SecurityConstants.INNER);
        if (R.FAIL == userResult.getCode()) {
            throw new ServiceException(userResult.getMsg());
        }
        LoginUser userInfo = userResult.getData();
        SysUser user = userInfo.getSysUser();

        // IP黑名单校验
        String blackStr = RedisUtil.get(CacheConstants.SYS_LOGIN_BLACKIPLIST);
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr())) {
            recordLogService.recordLogininfor(user.getUserId(), user.getDeptId(), username, Constants.LOGIN_FAIL, user.getTenantId(), clientType, "访问IP已被列入系统黑名单");
            throw new ServiceException("访问IP已被列入系统黑名单");
        }

        if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
            recordLogService.recordLogininfor(user.getUserId(), user.getDeptId(), username, Constants.LOGIN_FAIL, user.getTenantId(), clientType, "账号已被删除");
            throw new ServiceException("账号已被删除");
        }
        if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            recordLogService.recordLogininfor(user.getUserId(), user.getDeptId(), username, Constants.LOGIN_FAIL, user.getTenantId(), clientType, "账号已停用，请联系管理员");
            throw new ServiceException("账号已停用，请联系管理员");
        }
        //校验密码
        passwordService.validate(user, password, clientType);
        //记录登录信息
        recordLogService.recordLogininfor(user.getUserId(), user.getDeptId(), username, Constants.LOGIN_SUCCESS, user.getTenantId(), clientType, "登录成功");
        //记录登录信息
        recordLoginInfo(user.getUserId());
        return userInfo;
    }

    /**
     * 记录登录信息
     *
     * @param userId 用户ID
     */
    public void recordLoginInfo(Long userId) {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        // 更新用户登录IP
        sysUser.setLoginIp(IpUtils.getIpAddr());
        // 更新用户登录时间
        sysUser.setLoginDate(LocalDateTime.now());
        remoteUserService.recordUserLogin(sysUser, SecurityConstants.INNER);
    }

    public void logout(Long userId, Long deptId, String loginName, Long tenantId, String clientType) {
        recordLogService.recordLogininfor(userId, deptId, loginName, Constants.LOGOUT, tenantId, clientType, "退出成功");
    }

    /**
     * 注册
     */
    public void register(String sn, String email, String username, String password, String clientType) {
        // 注册用户信息
        SysUser sysUser = new SysUser();
        sysUser.setUserType(SysUser.STAFF_TYPE);
        sysUser.setRemark(sn);
        sysUser.setEmail(email);
        sysUser.setUserName(username);
        sysUser.setNickName(username);
        sysUser.setCreateBy(username);
        sysUser.setUpdateBy(username);
        sysUser.setPassword(SecurityUtils.encryptPassword(password));
        R<SysUser> registerResult = remoteUserService.registerUserInfo(sysUser, clientType, SecurityConstants.INNER);
        if (R.FAIL == registerResult.getCode()) {
            throw new ServiceException(registerResult.getMsg());
        }
        SysUser user = registerResult.getData();
        recordLogService.recordLogininfor(user.getUserId(), user.getDeptId(), username, Constants.REGISTER, BaseService.DIY_TENANT_ID, clientType, "注册成功");
    }

    /**
     * 修改密码
     */
    public void updatePassword(String username, String email, String password, String clientType) {
        // 注册用户信息
        SysUser sysUser = new SysUser();
        sysUser.setUserName(username);
        sysUser.setEmail(email);
        sysUser.setPassword(SecurityUtils.encryptPassword(password));
        R<Boolean> registerResult = remoteUserService.updatePassword(sysUser, clientType, SecurityConstants.INNER);
        if (R.FAIL == registerResult.getCode()) {
            throw new ServiceException(registerResult.getMsg());
        }
    }
}
