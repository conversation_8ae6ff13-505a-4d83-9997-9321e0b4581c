package com.ruoyi.auth.controller;

import com.ruoyi.auth.form.LoginBody;
import com.ruoyi.auth.form.PasswordBody;
import com.ruoyi.auth.form.RegisterBody;
import com.ruoyi.auth.service.SysLoginService;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.ClientTypeEnum;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.auth.AuthUtil;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.model.LoginUser;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * token 控制
 *
 * <AUTHOR>
 */
@RestController
public class TokenController {

    @Resource
    private TokenService tokenService;
    @Resource
    private SysLoginService sysLoginService;

    /**
     * 登录
     */
    @PostMapping("/login")
    public R<Map<String, Object>> login(@Validated @RequestBody LoginBody form, HttpServletRequest request,
                                        @RequestHeader(SecurityConstants.CLIENT_TYPE) String clientType) {
        // 用户登录
        LoginUser userInfo = sysLoginService.login(form.getUsername(), form.getPassword(), ClientTypeEnum.getVal(request));
        // 获取登录token
        return R.ok(tokenService.createToken(userInfo, ClientTypeEnum.of(clientType)));
    }

    /**
     * 用户退出
     */
    @DeleteMapping("/logout")
    public R<Void> logout(HttpServletRequest request) {
        String token = SecurityUtils.getToken(request);
        if (StringUtils.isNotEmpty(token)) {
            LoginUser loginUser = tokenService.getLoginUser(token);
            if (loginUser != null) {
                // 删除用户缓存记录
                AuthUtil.logoutByToken(token);
                SysUser user = loginUser.getSysUser();
                // 记录用户退出日志
                sysLoginService.logout(user.getUserId(), user.getDeptId(), user.getUserName(), user.getTenantId(), ClientTypeEnum.getVal(request));
            }
        }
        return R.ok();
    }

    /**
     * 刷新令牌有效期
     */
    @PostMapping("/refresh")
    public R<Void> refresh(HttpServletRequest request, @RequestHeader(SecurityConstants.CLIENT_TYPE) String clientType) {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (StringUtils.isNotNull(loginUser)) {
            // 刷新令牌有效期
            tokenService.refreshToken(loginUser, ClientTypeEnum.of(clientType));
            return R.ok();
        }
        return R.ok();
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public R<Void> register(@Validated @RequestBody RegisterBody body, @RequestHeader(SecurityConstants.CLIENT_TYPE) String clientType) {
        sysLoginService.register(body.getSn(), body.getEmail(), body.getUsername(), body.getPassword(), clientType);
        return R.ok();
    }

    /**
     * 忘记密码 找回密码
     */
    @PostMapping("/password")
    public R<Void> password(@Validated @RequestBody PasswordBody body, @RequestHeader(SecurityConstants.CLIENT_TYPE) String clientType) {
        sysLoginService.updatePassword(body.getUsername(), body.getEmail(), body.getPassword(), clientType);
        return R.ok();
    }

}
